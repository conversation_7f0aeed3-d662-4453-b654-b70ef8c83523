// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

void* winrt_make_plexy_XamlMetaDataProvider()
{
    return winrt::detach_abi(winrt::make<winrt::plexy::factory_implementation::XamlMetaDataProvider>());
}
WINRT_EXPORT namespace winrt::plexy
{
    XamlMetaDataProvider::XamlMetaDataProvider() :
        XamlMetaDataProvider(make<plexy::implementation::XamlMetaDataProvider>())
    {
    }
}
