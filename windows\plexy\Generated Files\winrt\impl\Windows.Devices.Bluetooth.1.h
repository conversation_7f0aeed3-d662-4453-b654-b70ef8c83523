// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Bluetooth_1_H
#define WINRT_Windows_Devices_Bluetooth_1_H
#include "winrt/impl/Windows.Devices.Bluetooth.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Bluetooth
{
    struct WINRT_IMPL_EMPTY_BASES IBluetoothAdapter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothAdapter>
    {
        IBluetoothAdapter(std::nullptr_t = nullptr) noexcept {}
        IBluetoothAdapter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothAdapter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothAdapter2>
    {
        IBluetoothAdapter2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothAdapter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothAdapter3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothAdapter3>
    {
        IBluetoothAdapter3(std::nullptr_t = nullptr) noexcept {}
        IBluetoothAdapter3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothAdapterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothAdapterStatics>
    {
        IBluetoothAdapterStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothAdapterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothClassOfDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothClassOfDevice>
    {
        IBluetoothClassOfDevice(std::nullptr_t = nullptr) noexcept {}
        IBluetoothClassOfDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothClassOfDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothClassOfDeviceStatics>
    {
        IBluetoothClassOfDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothClassOfDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDevice>
    {
        IBluetoothDevice(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDevice2>
    {
        IBluetoothDevice2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDevice3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDevice3>
    {
        IBluetoothDevice3(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDevice3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDevice4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDevice4>
    {
        IBluetoothDevice4(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDevice4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDevice5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDevice5>
    {
        IBluetoothDevice5(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDevice5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDeviceId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDeviceId>
    {
        IBluetoothDeviceId(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDeviceId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDeviceIdStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDeviceIdStatics>
    {
        IBluetoothDeviceIdStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDeviceIdStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDeviceStatics>
    {
        IBluetoothDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothDeviceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothDeviceStatics2>
    {
        IBluetoothDeviceStatics2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothDeviceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAppearance :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAppearance>
    {
        IBluetoothLEAppearance(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAppearance(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAppearanceCategoriesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAppearanceCategoriesStatics>
    {
        IBluetoothLEAppearanceCategoriesStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAppearanceCategoriesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAppearanceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAppearanceStatics>
    {
        IBluetoothLEAppearanceStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAppearanceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAppearanceSubcategoriesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAppearanceSubcategoriesStatics>
    {
        IBluetoothLEAppearanceSubcategoriesStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAppearanceSubcategoriesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEConnectionParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEConnectionParameters>
    {
        IBluetoothLEConnectionParameters(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEConnectionParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEConnectionPhy :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEConnectionPhy>
    {
        IBluetoothLEConnectionPhy(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEConnectionPhy(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEConnectionPhyInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEConnectionPhyInfo>
    {
        IBluetoothLEConnectionPhyInfo(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEConnectionPhyInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice>
    {
        IBluetoothLEDevice(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice2>
    {
        IBluetoothLEDevice2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice3>
    {
        IBluetoothLEDevice3(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice4>
    {
        IBluetoothLEDevice4(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice5>
    {
        IBluetoothLEDevice5(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDevice6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDevice6>
    {
        IBluetoothLEDevice6(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDevice6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDeviceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDeviceStatics>
    {
        IBluetoothLEDeviceStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDeviceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEDeviceStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEDeviceStatics2>
    {
        IBluetoothLEDeviceStatics2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEDeviceStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEPreferredConnectionParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEPreferredConnectionParameters>
    {
        IBluetoothLEPreferredConnectionParameters(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEPreferredConnectionParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEPreferredConnectionParametersRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEPreferredConnectionParametersRequest>
    {
        IBluetoothLEPreferredConnectionParametersRequest(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEPreferredConnectionParametersRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEPreferredConnectionParametersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEPreferredConnectionParametersStatics>
    {
        IBluetoothLEPreferredConnectionParametersStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEPreferredConnectionParametersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothSignalStrengthFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothSignalStrengthFilter>
    {
        IBluetoothSignalStrengthFilter(std::nullptr_t = nullptr) noexcept {}
        IBluetoothSignalStrengthFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothUuidHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothUuidHelperStatics>
    {
        IBluetoothUuidHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothUuidHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
