// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_Web_WebView2_Core_1_H
#define WINRT_Microsoft_Web_WebView2_Core_1_H
#include "winrt/impl/Microsoft.Web.WebView2.Core.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Web::WebView2::Core
{
    struct WINRT_IMPL_EMPTY_BASES CoreWebView2Certificate_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<CoreWebView2Certificate_Manual>
    {
        CoreWebView2Certificate_Manual(std::nullptr_t = nullptr) noexcept {}
        CoreWebView2Certificate_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CoreWebView2ClientCertificate_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<CoreWebView2ClientCertificate_Manual>
    {
        CoreWebView2ClientCertificate_Manual(std::nullptr_t = nullptr) noexcept {}
        CoreWebView2ClientCertificate_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CoreWebView2Profile_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<CoreWebView2Profile_Manual>
    {
        CoreWebView2Profile_Manual(std::nullptr_t = nullptr) noexcept {}
        CoreWebView2Profile_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2>
    {
        ICoreWebView2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2AcceleratorKeyPressedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2AcceleratorKeyPressedEventArgs>
    {
        ICoreWebView2AcceleratorKeyPressedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2AcceleratorKeyPressedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2BasicAuthenticationRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2BasicAuthenticationRequestedEventArgs>
    {
        ICoreWebView2BasicAuthenticationRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2BasicAuthenticationRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2BasicAuthenticationResponse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2BasicAuthenticationResponse>
    {
        ICoreWebView2BasicAuthenticationResponse(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2BasicAuthenticationResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2BrowserProcessExitedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2BrowserProcessExitedEventArgs>
    {
        ICoreWebView2BrowserProcessExitedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2BrowserProcessExitedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Certificate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Certificate>
    {
        ICoreWebView2Certificate(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Certificate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ClientCertificate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ClientCertificate>
    {
        ICoreWebView2ClientCertificate(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ClientCertificate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ClientCertificateRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ClientCertificateRequestedEventArgs>
    {
        ICoreWebView2ClientCertificateRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ClientCertificateRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2CompositionController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2CompositionController>
    {
        ICoreWebView2CompositionController(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2CompositionController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2CompositionController2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2CompositionController2>
    {
        ICoreWebView2CompositionController2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2CompositionController2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2CompositionControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2CompositionControllerStatics>
    {
        ICoreWebView2CompositionControllerStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2CompositionControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ContentLoadingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ContentLoadingEventArgs>
    {
        ICoreWebView2ContentLoadingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ContentLoadingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ContextMenuItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ContextMenuItem>
    {
        ICoreWebView2ContextMenuItem(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ContextMenuItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ContextMenuRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ContextMenuRequestedEventArgs>
    {
        ICoreWebView2ContextMenuRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ContextMenuRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ContextMenuTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ContextMenuTarget>
    {
        ICoreWebView2ContextMenuTarget(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ContextMenuTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Controller :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Controller>
    {
        ICoreWebView2Controller(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Controller(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Controller2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Controller2>
    {
        ICoreWebView2Controller2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Controller2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Controller3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Controller3>
    {
        ICoreWebView2Controller3(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Controller3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Controller4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Controller4>
    {
        ICoreWebView2Controller4(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Controller4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ControllerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ControllerFactory>
    {
        ICoreWebView2ControllerFactory(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ControllerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ControllerOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ControllerOptions>
    {
        ICoreWebView2ControllerOptions(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ControllerOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ControllerWindowReference :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ControllerWindowReference>
    {
        ICoreWebView2ControllerWindowReference(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ControllerWindowReference(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ControllerWindowReferenceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ControllerWindowReferenceStatics>
    {
        ICoreWebView2ControllerWindowReferenceStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ControllerWindowReferenceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Cookie :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Cookie>
    {
        ICoreWebView2Cookie(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Cookie(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2CookieManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2CookieManager>
    {
        ICoreWebView2CookieManager(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2CookieManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2CookieManager_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2CookieManager_Manual>
    {
        ICoreWebView2CookieManager_Manual(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2CookieManager_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DOMContentLoadedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DOMContentLoadedEventArgs>
    {
        ICoreWebView2DOMContentLoadedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DOMContentLoadedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DevToolsProtocolEventReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DevToolsProtocolEventReceivedEventArgs>
    {
        ICoreWebView2DevToolsProtocolEventReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DevToolsProtocolEventReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DevToolsProtocolEventReceivedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>
    {
        ICoreWebView2DevToolsProtocolEventReceivedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DevToolsProtocolEventReceivedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DevToolsProtocolEventReceiver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DevToolsProtocolEventReceiver>
    {
        ICoreWebView2DevToolsProtocolEventReceiver(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DevToolsProtocolEventReceiver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DispatchAdapter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DispatchAdapter>
    {
        ICoreWebView2DispatchAdapter(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DispatchAdapter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DownloadOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DownloadOperation>
    {
        ICoreWebView2DownloadOperation(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DownloadOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2DownloadStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2DownloadStartingEventArgs>
    {
        ICoreWebView2DownloadStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2DownloadStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment>
    {
        ICoreWebView2Environment(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment10>
    {
        ICoreWebView2Environment10(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment2>
    {
        ICoreWebView2Environment2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment3>
    {
        ICoreWebView2Environment3(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment4>
    {
        ICoreWebView2Environment4(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment5>
    {
        ICoreWebView2Environment5(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment6>
    {
        ICoreWebView2Environment6(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment7>
    {
        ICoreWebView2Environment7(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment8>
    {
        ICoreWebView2Environment8(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment9>
    {
        ICoreWebView2Environment9(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2EnvironmentOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2EnvironmentOptions>
    {
        ICoreWebView2EnvironmentOptions(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2EnvironmentOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2EnvironmentOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2EnvironmentOptions2>
    {
        ICoreWebView2EnvironmentOptions2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2EnvironmentOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2EnvironmentOptions_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2EnvironmentOptions_Manual>
    {
        ICoreWebView2EnvironmentOptions_Manual(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2EnvironmentOptions_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2EnvironmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2EnvironmentStatics>
    {
        ICoreWebView2EnvironmentStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2EnvironmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Environment_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Environment_Manual>
    {
        ICoreWebView2Environment_Manual(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Environment_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Frame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Frame>
    {
        ICoreWebView2Frame(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Frame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Frame2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Frame2>
    {
        ICoreWebView2Frame2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Frame2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Frame3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Frame3>
    {
        ICoreWebView2Frame3(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Frame3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2FrameCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2FrameCreatedEventArgs>
    {
        ICoreWebView2FrameCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2FrameCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2FrameInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2FrameInfo>
    {
        ICoreWebView2FrameInfo(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2FrameInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2HttpHeadersCollectionIterator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2HttpHeadersCollectionIterator>
    {
        ICoreWebView2HttpHeadersCollectionIterator(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2HttpHeadersCollectionIterator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2HttpRequestHeaders :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2HttpRequestHeaders>
    {
        ICoreWebView2HttpRequestHeaders(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2HttpRequestHeaders(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2HttpResponseHeaders :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2HttpResponseHeaders>
    {
        ICoreWebView2HttpResponseHeaders(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2HttpResponseHeaders(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2MoveFocusRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2MoveFocusRequestedEventArgs>
    {
        ICoreWebView2MoveFocusRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2MoveFocusRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NavigationCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NavigationCompletedEventArgs>
    {
        ICoreWebView2NavigationCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NavigationCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NavigationCompletedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NavigationCompletedEventArgs2>
    {
        ICoreWebView2NavigationCompletedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NavigationCompletedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NavigationStartingEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NavigationStartingEventArgs>
    {
        ICoreWebView2NavigationStartingEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NavigationStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NavigationStartingEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NavigationStartingEventArgs2>
    {
        ICoreWebView2NavigationStartingEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NavigationStartingEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NewWindowRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NewWindowRequestedEventArgs>
    {
        ICoreWebView2NewWindowRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NewWindowRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2NewWindowRequestedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2NewWindowRequestedEventArgs2>
    {
        ICoreWebView2NewWindowRequestedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2NewWindowRequestedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2PermissionRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2PermissionRequestedEventArgs>
    {
        ICoreWebView2PermissionRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2PermissionRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2PermissionRequestedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2PermissionRequestedEventArgs2>
    {
        ICoreWebView2PermissionRequestedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2PermissionRequestedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2PointerInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2PointerInfo>
    {
        ICoreWebView2PointerInfo(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2PointerInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2PrintSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2PrintSettings>
    {
        ICoreWebView2PrintSettings(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2PrintSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ProcessFailedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ProcessFailedEventArgs>
    {
        ICoreWebView2ProcessFailedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ProcessFailedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ProcessFailedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ProcessFailedEventArgs2>
    {
        ICoreWebView2ProcessFailedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ProcessFailedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ProcessInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ProcessInfo>
    {
        ICoreWebView2ProcessInfo(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ProcessInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Profile :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Profile>
    {
        ICoreWebView2Profile(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Profile(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Profile2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Profile2>
    {
        ICoreWebView2Profile2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Profile2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ScriptDialogOpeningEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ScriptDialogOpeningEventArgs>
    {
        ICoreWebView2ScriptDialogOpeningEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ScriptDialogOpeningEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2ServerCertificateErrorDetectedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2ServerCertificateErrorDetectedEventArgs>
    {
        ICoreWebView2ServerCertificateErrorDetectedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2ServerCertificateErrorDetectedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings>
    {
        ICoreWebView2Settings(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings2>
    {
        ICoreWebView2Settings2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings3>
    {
        ICoreWebView2Settings3(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings4>
    {
        ICoreWebView2Settings4(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings5>
    {
        ICoreWebView2Settings5(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings6>
    {
        ICoreWebView2Settings6(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings7>
    {
        ICoreWebView2Settings7(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2Settings_Manual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2Settings_Manual>
    {
        ICoreWebView2Settings_Manual(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2Settings_Manual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2SourceChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2SourceChangedEventArgs>
    {
        ICoreWebView2SourceChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2SourceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebMessageReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebMessageReceivedEventArgs>
    {
        ICoreWebView2WebMessageReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebMessageReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebResourceRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebResourceRequest>
    {
        ICoreWebView2WebResourceRequest(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebResourceRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebResourceRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebResourceRequestedEventArgs>
    {
        ICoreWebView2WebResourceRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebResourceRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebResourceResponse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebResourceResponse>
    {
        ICoreWebView2WebResourceResponse(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebResourceResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebResourceResponseReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebResourceResponseReceivedEventArgs>
    {
        ICoreWebView2WebResourceResponseReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebResourceResponseReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WebResourceResponseView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WebResourceResponseView>
    {
        ICoreWebView2WebResourceResponseView(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WebResourceResponseView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2WindowFeatures :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2WindowFeatures>
    {
        ICoreWebView2WindowFeatures(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2WindowFeatures(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_10 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_10>
    {
        ICoreWebView2_10(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_10(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_11 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_11>
    {
        ICoreWebView2_11(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_11(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_12 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_12>
    {
        ICoreWebView2_12(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_12(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_13 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_13>
    {
        ICoreWebView2_13(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_13(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_14 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_14>
    {
        ICoreWebView2_14(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_14(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_2>
    {
        ICoreWebView2_2(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_3>
    {
        ICoreWebView2_3(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_4>
    {
        ICoreWebView2_4(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_5>
    {
        ICoreWebView2_5(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_6>
    {
        ICoreWebView2_6(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_7>
    {
        ICoreWebView2_7(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_8>
    {
        ICoreWebView2_8(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICoreWebView2_9 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreWebView2_9>
    {
        ICoreWebView2_9(std::nullptr_t = nullptr) noexcept {}
        ICoreWebView2_9(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
