// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_Primitives_H
#define WINRT_Microsoft_UI_Xaml_Controls_Primitives_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/Microsoft.UI.Xaml.Controls.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.2.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.Foundation.Numerics.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.2.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.2.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.Primitives.2.h"
#include "winrt/impl/Windows.UI.Xaml.Data.2.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.Primitives.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelperStatics<D>::KeepInteriorCornersSquareProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics)->get_KeepInteriorCornersSquareProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelperStatics<D>::SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics)->SetKeepInteriorCornersSquare(*(void**)(&autoSuggestBox), value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelperStatics<D>::GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics)->GetKeepInteriorCornersSquare(*(void**)(&autoSuggestBox), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSlider<D>::ColorChannel() const
    {
        winrt::Microsoft::UI::Xaml::Controls::ColorPickerHsvChannel value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider)->get_ColorChannel(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSlider<D>::ColorChannel(winrt::Microsoft::UI::Xaml::Controls::ColorPickerHsvChannel const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider)->put_ColorChannel(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderStatics<D>::ColorChannelProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics)->get_ColorChannelProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Color() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_Color(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Color(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_Color(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::HsvColor() const
    {
        winrt::Windows::Foundation::Numerics::float4 value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_HsvColor(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::HsvColor(winrt::Windows::Foundation::Numerics::float4 const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_HsvColor(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinHue() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MinHue(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinHue(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MinHue(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxHue() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MaxHue(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxHue(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MaxHue(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinSaturation() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MinSaturation(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinSaturation(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MinSaturation(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxSaturation() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MaxSaturation(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxSaturation(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MaxSaturation(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinValue() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MinValue(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MinValue(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MinValue(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxValue() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_MaxValue(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::MaxValue(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_MaxValue(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Shape() const
    {
        winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumShape value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_Shape(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Shape(winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumShape const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_Shape(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Components() const
    {
        winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumComponents value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->get_Components(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::Components(winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumComponents const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->put_Components(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::ColorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum, winrt::Microsoft::UI::Xaml::Controls::ColorChangedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->add_ColorChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::ColorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum, winrt::Microsoft::UI::Xaml::Controls::ColorChangedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ColorChanged_revoker>(this, ColorChanged(handler));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>::ColorChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum)->remove_ColorChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::ColorProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_ColorProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::HsvColorProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_HsvColorProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MinHueProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MinHueProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MaxHueProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MaxHueProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MinSaturationProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MinSaturationProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MaxSaturationProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MaxSaturationProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MinValueProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MinValueProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::MaxValueProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_MaxValueProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::ShapeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_ShapeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>::ComponentsProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics)->get_ComponentsProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::MaxColumns() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->get_MaxColumns(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::MaxColumns(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->put_MaxColumns(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::ColumnSpacing() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->get_ColumnSpacing(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::ColumnSpacing(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->put_ColumnSpacing(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::RowSpacing() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->get_RowSpacing(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>::RowSpacing(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout)->put_RowSpacing(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutStatics<D>::MaxColumnsProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics)->get_MaxColumnsProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutStatics<D>::ColumnSpacingProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics)->get_ColumnSpacingProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutStatics<D>::RowSpacingProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics)->get_RowSpacingProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelperStatics<D>::KeepInteriorCornersSquareProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics)->get_KeepInteriorCornersSquareProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelperStatics<D>::SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics)->SetKeepInteriorCornersSquare(*(void**)(&comboBox), value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelperStatics<D>::GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics)->GetKeepInteriorCornersSquare(*(void**)(&comboBox), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBar<D>::FlyoutTemplateSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar)->get_FlyoutTemplateSettings(&value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarAutomationPropertiesStatics<D>::ControlTypeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics)->get_ControlTypeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarAutomationPropertiesStatics<D>::GetControlType(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics)->GetControlType(*(void**)(&element), reinterpret_cast<int32_t*>(&result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarAutomationPropertiesStatics<D>::SetControlType(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics)->SetControlType(*(void**)(&element), static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::OpenAnimationStartPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_OpenAnimationStartPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::OpenAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_OpenAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::CloseAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_CloseAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::CurrentWidth() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_CurrentWidth(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandedWidth() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandedWidth(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::WidthExpansionDelta() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_WidthExpansionDelta(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::WidthExpansionAnimationStartPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_WidthExpansionAnimationStartPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::WidthExpansionAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_WidthExpansionAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::WidthExpansionMoreButtonAnimationStartPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_WidthExpansionMoreButtonAnimationStartPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::WidthExpansionMoreButtonAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_WidthExpansionMoreButtonAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandUpOverflowVerticalPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandUpOverflowVerticalPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandDownOverflowVerticalPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandDownOverflowVerticalPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandUpAnimationStartPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandUpAnimationStartPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandUpAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandUpAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandUpAnimationHoldPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandUpAnimationHoldPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandDownAnimationStartPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandDownAnimationStartPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandDownAnimationEndPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandDownAnimationEndPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ExpandDownAnimationHoldPosition() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ExpandDownAnimationHoldPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::ContentClipRect() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_ContentClipRect(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>::OverflowContentClipRect() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings)->get_OverflowContentClipRect(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter<D>::Filter() const
    {
        winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter)->get_Filter(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter<D>::Filter(winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter)->put_Filter(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter<D>::Scale() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter)->get_Scale(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter<D>::Scale(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter)->put_Scale(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverterStatics<D>::FilterProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics)->get_FilterProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverterStatics<D>::ScaleProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics)->get_ScaleProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter<D>::ConversionKind() const
    {
        winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter)->get_ConversionKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter<D>::ConversionKind(winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter)->put_ConversionKind(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter<D>::Multiplier() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter)->get_Multiplier(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter<D>::Multiplier(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter)->put_Multiplier(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverterStatics<D>::ConversionKindProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics)->get_ConversionKindProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverterStatics<D>::MultiplierProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics)->get_MultiplierProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel<D>::HorizontalOrientationPadding() const
    {
        winrt::Windows::UI::Xaml::Thickness value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel)->get_HorizontalOrientationPadding(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel<D>::HorizontalOrientationPadding(winrt::Windows::UI::Xaml::Thickness const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel)->put_HorizontalOrientationPadding(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel<D>::VerticalOrientationPadding() const
    {
        winrt::Windows::UI::Xaml::Thickness value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel)->get_VerticalOrientationPadding(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel<D>::VerticalOrientationPadding(winrt::Windows::UI::Xaml::Thickness const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel)->put_VerticalOrientationPadding(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::HorizontalOrientationPaddingProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->get_HorizontalOrientationPaddingProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::VerticalOrientationPaddingProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->get_VerticalOrientationPaddingProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::SetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->SetHorizontalOrientationMargin(*(void**)(&object), impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::GetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object) const
    {
        winrt::Windows::UI::Xaml::Thickness result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->GetHorizontalOrientationMargin(*(void**)(&object), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::HorizontalOrientationMarginProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->get_HorizontalOrientationMarginProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::SetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->SetVerticalOrientationMargin(*(void**)(&object), impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::GetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object) const
    {
        winrt::Windows::UI::Xaml::Thickness result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->GetVerticalOrientationMargin(*(void**)(&object), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>::VerticalOrientationMarginProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics)->get_VerticalOrientationMarginProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter<D>::SourceElement() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter)->get_SourceElement(&value));
        return winrt::Windows::UI::Xaml::UIElement{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter<D>::SourceElement(winrt::Windows::UI::Xaml::UIElement const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter)->put_SourceElement(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter<D>::ReplacementColor() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter)->get_ReplacementColor(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter<D>::ReplacementColor(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter)->put_ReplacementColor(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterStatics<D>::SourceElementProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics)->get_SourceElementProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterStatics<D>::ReplacementColorProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics)->get_ReplacementColorProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter<D>::Icon() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter)->get_Icon(&value));
        return winrt::Windows::UI::Xaml::Controls::IconElement{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter<D>::Icon(winrt::Windows::UI::Xaml::Controls::IconElement const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter)->put_Icon(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter<D>::TemplateSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter)->get_TemplateSettings(&value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter2<D>::InfoBadge() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2)->get_InfoBadge(&value));
        return winrt::Microsoft::UI::Xaml::Controls::InfoBadge{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter2<D>::InfoBadge(winrt::Microsoft::UI::Xaml::Controls::InfoBadge const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2)->put_InfoBadge(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics<D>::IconProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics)->get_IconProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics<D>::TemplateSettingsProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics)->get_TemplateSettingsProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics2<D>::InfoBadgeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2)->get_InfoBadgeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettings<D>::IconWidth() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings)->get_IconWidth(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettings<D>::SmallerIconWidth() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings)->get_SmallerIconWidth(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsStatics<D>::IconWidthProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics)->get_IconWidthProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsStatics<D>::SmallerIconWidthProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics)->get_SmallerIconWidthProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Controls_Primitives_ITabViewListViewFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView{ value, take_ownership_from_abi };
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics>
    {
        int32_t __stdcall get_KeepInteriorCornersSquareProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().KeepInteriorCornersSquareProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetKeepInteriorCornersSquare(void* autoSuggestBox, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetKeepInteriorCornersSquare(*reinterpret_cast<winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const*>(&autoSuggestBox), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetKeepInteriorCornersSquare(void* autoSuggestBox, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetKeepInteriorCornersSquare(*reinterpret_cast<winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const*>(&autoSuggestBox)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider>
    {
        int32_t __stdcall get_ColorChannel(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::ColorPickerHsvChannel>(this->shim().ColorChannel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ColorChannel(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ColorChannel(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ColorPickerHsvChannel const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics>
    {
        int32_t __stdcall get_ColorChannelProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ColorChannelProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>
    {
        int32_t __stdcall get_Color(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().Color());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Color(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Color(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HsvColor(winrt::Windows::Foundation::Numerics::float4* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Numerics::float4>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Numerics::float4>(this->shim().HsvColor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HsvColor(winrt::Windows::Foundation::Numerics::float4 value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HsvColor(*reinterpret_cast<winrt::Windows::Foundation::Numerics::float4 const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinHue(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MinHue());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MinHue(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MinHue(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxHue(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MaxHue());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxHue(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxHue(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinSaturation(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MinSaturation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MinSaturation(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MinSaturation(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxSaturation(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MaxSaturation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxSaturation(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxSaturation(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinValue(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MinValue());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MinValue(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MinValue(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxValue(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MaxValue());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxValue(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxValue(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Shape(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumShape>(this->shim().Shape());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Shape(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Shape(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumShape const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Components(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumComponents>(this->shim().Components());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Components(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Components(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumComponents const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_ColorChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ColorChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum, winrt::Microsoft::UI::Xaml::Controls::ColorChangedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ColorChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ColorChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics>
    {
        int32_t __stdcall get_ColorProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ColorProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HsvColorProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().HsvColorProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinHueProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MinHueProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxHueProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MaxHueProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinSaturationProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MinSaturationProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxSaturationProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MaxSaturationProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MinValueProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MinValueProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxValueProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MaxValueProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShapeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ShapeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ComponentsProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ComponentsProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout>
    {
        int32_t __stdcall get_MaxColumns(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().MaxColumns());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxColumns(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxColumns(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ColumnSpacing(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ColumnSpacing());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ColumnSpacing(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ColumnSpacing(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RowSpacing(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().RowSpacing());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RowSpacing(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RowSpacing(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics>
    {
        int32_t __stdcall get_MaxColumnsProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MaxColumnsProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ColumnSpacingProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ColumnSpacingProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RowSpacingProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().RowSpacingProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics>
    {
        int32_t __stdcall get_KeepInteriorCornersSquareProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().KeepInteriorCornersSquareProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetKeepInteriorCornersSquare(void* comboBox, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetKeepInteriorCornersSquare(*reinterpret_cast<winrt::Windows::UI::Xaml::Controls::ComboBox const*>(&comboBox), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetKeepInteriorCornersSquare(void* comboBox, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetKeepInteriorCornersSquare(*reinterpret_cast<winrt::Windows::UI::Xaml::Controls::ComboBox const*>(&comboBox)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar>
    {
        int32_t __stdcall get_FlyoutTemplateSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings>(this->shim().FlyoutTemplateSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics>
    {
        int32_t __stdcall get_ControlTypeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ControlTypeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetControlType(void* element, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType>(this->shim().GetControlType(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetControlType(void* element, int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetControlType(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings>
    {
        int32_t __stdcall get_OpenAnimationStartPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().OpenAnimationStartPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_OpenAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().OpenAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CloseAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().CloseAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CurrentWidth(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().CurrentWidth());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandedWidth(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandedWidth());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WidthExpansionDelta(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().WidthExpansionDelta());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WidthExpansionAnimationStartPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().WidthExpansionAnimationStartPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WidthExpansionAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().WidthExpansionAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WidthExpansionMoreButtonAnimationStartPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().WidthExpansionMoreButtonAnimationStartPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WidthExpansionMoreButtonAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().WidthExpansionMoreButtonAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandUpOverflowVerticalPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandUpOverflowVerticalPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandDownOverflowVerticalPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandDownOverflowVerticalPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandUpAnimationStartPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandUpAnimationStartPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandUpAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandUpAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandUpAnimationHoldPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandUpAnimationHoldPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandDownAnimationStartPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandDownAnimationStartPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandDownAnimationEndPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandDownAnimationEndPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExpandDownAnimationHoldPosition(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ExpandDownAnimationHoldPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ContentClipRect(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().ContentClipRect());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_OverflowContentClipRect(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().OverflowContentClipRect());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter>
    {
        int32_t __stdcall get_Filter(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind>(this->shim().Filter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Filter(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Filter(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Scale(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().Scale());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Scale(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Scale(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics>
    {
        int32_t __stdcall get_FilterProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().FilterProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ScaleProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ScaleProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter>
    {
        int32_t __stdcall get_ConversionKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind>(this->shim().ConversionKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ConversionKind(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ConversionKind(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Multiplier(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().Multiplier());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Multiplier(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Multiplier(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics>
    {
        int32_t __stdcall get_ConversionKindProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ConversionKindProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MultiplierProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MultiplierProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel>
    {
        int32_t __stdcall get_HorizontalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Thickness>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Thickness>(this->shim().HorizontalOrientationPadding());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HorizontalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HorizontalOrientationPadding(*reinterpret_cast<winrt::Windows::UI::Xaml::Thickness const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VerticalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Thickness>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Thickness>(this->shim().VerticalOrientationPadding());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_VerticalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().VerticalOrientationPadding(*reinterpret_cast<winrt::Windows::UI::Xaml::Thickness const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics>
    {
        int32_t __stdcall get_HorizontalOrientationPaddingProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().HorizontalOrientationPaddingProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VerticalOrientationPaddingProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().VerticalOrientationPaddingProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetHorizontalOrientationMargin(void* object, struct struct_Windows_UI_Xaml_Thickness value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetHorizontalOrientationMargin(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&object), *reinterpret_cast<winrt::Windows::UI::Xaml::Thickness const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHorizontalOrientationMargin(void* object, struct struct_Windows_UI_Xaml_Thickness* result) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Thickness>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::Thickness>(this->shim().GetHorizontalOrientationMargin(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&object)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HorizontalOrientationMarginProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().HorizontalOrientationMarginProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetVerticalOrientationMargin(void* object, struct struct_Windows_UI_Xaml_Thickness value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetVerticalOrientationMargin(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&object), *reinterpret_cast<winrt::Windows::UI::Xaml::Thickness const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetVerticalOrientationMargin(void* object, struct struct_Windows_UI_Xaml_Thickness* result) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Xaml::Thickness>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::Thickness>(this->shim().GetVerticalOrientationMargin(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&object)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VerticalOrientationMarginProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().VerticalOrientationMarginProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter>
    {
        int32_t __stdcall get_SourceElement(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::UIElement>(this->shim().SourceElement());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SourceElement(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SourceElement(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReplacementColor(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().ReplacementColor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ReplacementColor(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReplacementColor(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics>
    {
        int32_t __stdcall get_SourceElementProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().SourceElementProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReplacementColorProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ReplacementColorProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter>
    {
        int32_t __stdcall get_Icon(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::IconElement>(this->shim().Icon());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Icon(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Icon(*reinterpret_cast<winrt::Windows::UI::Xaml::Controls::IconElement const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TemplateSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings>(this->shim().TemplateSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2>
    {
        int32_t __stdcall get_InfoBadge(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::InfoBadge>(this->shim().InfoBadge());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InfoBadge(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InfoBadge(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::InfoBadge const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics>
    {
        int32_t __stdcall get_IconProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().IconProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TemplateSettingsProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TemplateSettingsProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2>
    {
        int32_t __stdcall get_InfoBadgeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().InfoBadgeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings>
    {
        int32_t __stdcall get_IconWidth(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().IconWidth());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SmallerIconWidth(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().SmallerIconWidth());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics>
    {
        int32_t __stdcall get_IconWidthProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().IconWidthProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SmallerIconWidthProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().SmallerIconWidthProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    inline auto AutoSuggestBoxHelper::KeepInteriorCornersSquareProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAutoSuggestBoxHelperStatics const&), AutoSuggestBoxHelper, IAutoSuggestBoxHelperStatics>([](IAutoSuggestBoxHelperStatics const& f) { return f.KeepInteriorCornersSquareProperty(); });
    }
    inline auto AutoSuggestBoxHelper::SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox, bool value)
    {
        impl::call_factory<AutoSuggestBoxHelper, IAutoSuggestBoxHelperStatics>([&](IAutoSuggestBoxHelperStatics const& f) { return f.SetKeepInteriorCornersSquare(autoSuggestBox, value); });
    }
    inline auto AutoSuggestBoxHelper::GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox)
    {
        return impl::call_factory<AutoSuggestBoxHelper, IAutoSuggestBoxHelperStatics>([&](IAutoSuggestBoxHelperStatics const& f) { return f.GetKeepInteriorCornersSquare(autoSuggestBox); });
    }
    inline ColorPickerSlider::ColorPickerSlider()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ColorPickerSlider, IColorPickerSliderFactory>([&](IColorPickerSliderFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto ColorPickerSlider::ColorChannelProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorPickerSliderStatics const&), ColorPickerSlider, IColorPickerSliderStatics>([](IColorPickerSliderStatics const& f) { return f.ColorChannelProperty(); });
    }
    inline ColorSpectrum::ColorSpectrum()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ColorSpectrum, IColorSpectrumFactory>([&](IColorSpectrumFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto ColorSpectrum::ColorProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.ColorProperty(); });
    }
    inline auto ColorSpectrum::HsvColorProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.HsvColorProperty(); });
    }
    inline auto ColorSpectrum::MinHueProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MinHueProperty(); });
    }
    inline auto ColorSpectrum::MaxHueProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MaxHueProperty(); });
    }
    inline auto ColorSpectrum::MinSaturationProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MinSaturationProperty(); });
    }
    inline auto ColorSpectrum::MaxSaturationProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MaxSaturationProperty(); });
    }
    inline auto ColorSpectrum::MinValueProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MinValueProperty(); });
    }
    inline auto ColorSpectrum::MaxValueProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.MaxValueProperty(); });
    }
    inline auto ColorSpectrum::ShapeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.ShapeProperty(); });
    }
    inline auto ColorSpectrum::ComponentsProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColorSpectrumStatics const&), ColorSpectrum, IColorSpectrumStatics>([](IColorSpectrumStatics const& f) { return f.ComponentsProperty(); });
    }
    inline ColumnMajorUniformToLargestGridLayout::ColumnMajorUniformToLargestGridLayout()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ColumnMajorUniformToLargestGridLayout, IColumnMajorUniformToLargestGridLayoutFactory>([&](IColumnMajorUniformToLargestGridLayoutFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto ColumnMajorUniformToLargestGridLayout::MaxColumnsProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColumnMajorUniformToLargestGridLayoutStatics const&), ColumnMajorUniformToLargestGridLayout, IColumnMajorUniformToLargestGridLayoutStatics>([](IColumnMajorUniformToLargestGridLayoutStatics const& f) { return f.MaxColumnsProperty(); });
    }
    inline auto ColumnMajorUniformToLargestGridLayout::ColumnSpacingProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColumnMajorUniformToLargestGridLayoutStatics const&), ColumnMajorUniformToLargestGridLayout, IColumnMajorUniformToLargestGridLayoutStatics>([](IColumnMajorUniformToLargestGridLayoutStatics const& f) { return f.ColumnSpacingProperty(); });
    }
    inline auto ColumnMajorUniformToLargestGridLayout::RowSpacingProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IColumnMajorUniformToLargestGridLayoutStatics const&), ColumnMajorUniformToLargestGridLayout, IColumnMajorUniformToLargestGridLayoutStatics>([](IColumnMajorUniformToLargestGridLayoutStatics const& f) { return f.RowSpacingProperty(); });
    }
    inline auto ComboBoxHelper::KeepInteriorCornersSquareProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IComboBoxHelperStatics const&), ComboBoxHelper, IComboBoxHelperStatics>([](IComboBoxHelperStatics const& f) { return f.KeepInteriorCornersSquareProperty(); });
    }
    inline auto ComboBoxHelper::SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox, bool value)
    {
        impl::call_factory<ComboBoxHelper, IComboBoxHelperStatics>([&](IComboBoxHelperStatics const& f) { return f.SetKeepInteriorCornersSquare(comboBox, value); });
    }
    inline auto ComboBoxHelper::GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox)
    {
        return impl::call_factory<ComboBoxHelper, IComboBoxHelperStatics>([&](IComboBoxHelperStatics const& f) { return f.GetKeepInteriorCornersSquare(comboBox); });
    }
    inline CommandBarFlyoutCommandBar::CommandBarFlyoutCommandBar()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<CommandBarFlyoutCommandBar, ICommandBarFlyoutCommandBarFactory>([&](ICommandBarFlyoutCommandBarFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto CommandBarFlyoutCommandBarAutomationProperties::ControlTypeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(ICommandBarFlyoutCommandBarAutomationPropertiesStatics const&), CommandBarFlyoutCommandBarAutomationProperties, ICommandBarFlyoutCommandBarAutomationPropertiesStatics>([](ICommandBarFlyoutCommandBarAutomationPropertiesStatics const& f) { return f.ControlTypeProperty(); });
    }
    inline auto CommandBarFlyoutCommandBarAutomationProperties::GetControlType(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<CommandBarFlyoutCommandBarAutomationProperties, ICommandBarFlyoutCommandBarAutomationPropertiesStatics>([&](ICommandBarFlyoutCommandBarAutomationPropertiesStatics const& f) { return f.GetControlType(element); });
    }
    inline auto CommandBarFlyoutCommandBarAutomationProperties::SetControlType(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType const& value)
    {
        impl::call_factory<CommandBarFlyoutCommandBarAutomationProperties, ICommandBarFlyoutCommandBarAutomationPropertiesStatics>([&](ICommandBarFlyoutCommandBarAutomationPropertiesStatics const& f) { return f.SetControlType(element, value); });
    }
    inline CornerRadiusFilterConverter::CornerRadiusFilterConverter() :
        CornerRadiusFilterConverter(impl::call_factory_cast<CornerRadiusFilterConverter(*)(winrt::Windows::Foundation::IActivationFactory const&), CornerRadiusFilterConverter>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<CornerRadiusFilterConverter>(); }))
    {
    }
    inline auto CornerRadiusFilterConverter::FilterProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(ICornerRadiusFilterConverterStatics const&), CornerRadiusFilterConverter, ICornerRadiusFilterConverterStatics>([](ICornerRadiusFilterConverterStatics const& f) { return f.FilterProperty(); });
    }
    inline auto CornerRadiusFilterConverter::ScaleProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(ICornerRadiusFilterConverterStatics const&), CornerRadiusFilterConverter, ICornerRadiusFilterConverterStatics>([](ICornerRadiusFilterConverterStatics const& f) { return f.ScaleProperty(); });
    }
    inline CornerRadiusToThicknessConverter::CornerRadiusToThicknessConverter() :
        CornerRadiusToThicknessConverter(impl::call_factory_cast<CornerRadiusToThicknessConverter(*)(winrt::Windows::Foundation::IActivationFactory const&), CornerRadiusToThicknessConverter>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<CornerRadiusToThicknessConverter>(); }))
    {
    }
    inline auto CornerRadiusToThicknessConverter::ConversionKindProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(ICornerRadiusToThicknessConverterStatics const&), CornerRadiusToThicknessConverter, ICornerRadiusToThicknessConverterStatics>([](ICornerRadiusToThicknessConverterStatics const& f) { return f.ConversionKindProperty(); });
    }
    inline auto CornerRadiusToThicknessConverter::MultiplierProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(ICornerRadiusToThicknessConverterStatics const&), CornerRadiusToThicknessConverter, ICornerRadiusToThicknessConverterStatics>([](ICornerRadiusToThicknessConverterStatics const& f) { return f.MultiplierProperty(); });
    }
    inline InfoBarPanel::InfoBarPanel()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<InfoBarPanel, IInfoBarPanelFactory>([&](IInfoBarPanelFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto InfoBarPanel::HorizontalOrientationPaddingProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IInfoBarPanelStatics const&), InfoBarPanel, IInfoBarPanelStatics>([](IInfoBarPanelStatics const& f) { return f.HorizontalOrientationPaddingProperty(); });
    }
    inline auto InfoBarPanel::VerticalOrientationPaddingProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IInfoBarPanelStatics const&), InfoBarPanel, IInfoBarPanelStatics>([](IInfoBarPanelStatics const& f) { return f.VerticalOrientationPaddingProperty(); });
    }
    inline auto InfoBarPanel::SetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value)
    {
        impl::call_factory<InfoBarPanel, IInfoBarPanelStatics>([&](IInfoBarPanelStatics const& f) { return f.SetHorizontalOrientationMargin(object, value); });
    }
    inline auto InfoBarPanel::GetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object)
    {
        return impl::call_factory<InfoBarPanel, IInfoBarPanelStatics>([&](IInfoBarPanelStatics const& f) { return f.GetHorizontalOrientationMargin(object); });
    }
    inline auto InfoBarPanel::HorizontalOrientationMarginProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IInfoBarPanelStatics const&), InfoBarPanel, IInfoBarPanelStatics>([](IInfoBarPanelStatics const& f) { return f.HorizontalOrientationMarginProperty(); });
    }
    inline auto InfoBarPanel::SetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value)
    {
        impl::call_factory<InfoBarPanel, IInfoBarPanelStatics>([&](IInfoBarPanelStatics const& f) { return f.SetVerticalOrientationMargin(object, value); });
    }
    inline auto InfoBarPanel::GetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object)
    {
        return impl::call_factory<InfoBarPanel, IInfoBarPanelStatics>([&](IInfoBarPanelStatics const& f) { return f.GetVerticalOrientationMargin(object); });
    }
    inline auto InfoBarPanel::VerticalOrientationMarginProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IInfoBarPanelStatics const&), InfoBarPanel, IInfoBarPanelStatics>([](IInfoBarPanelStatics const& f) { return f.VerticalOrientationMarginProperty(); });
    }
    inline MonochromaticOverlayPresenter::MonochromaticOverlayPresenter()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<MonochromaticOverlayPresenter, IMonochromaticOverlayPresenterFactory>([&](IMonochromaticOverlayPresenterFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto MonochromaticOverlayPresenter::SourceElementProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IMonochromaticOverlayPresenterStatics const&), MonochromaticOverlayPresenter, IMonochromaticOverlayPresenterStatics>([](IMonochromaticOverlayPresenterStatics const& f) { return f.SourceElementProperty(); });
    }
    inline auto MonochromaticOverlayPresenter::ReplacementColorProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IMonochromaticOverlayPresenterStatics const&), MonochromaticOverlayPresenter, IMonochromaticOverlayPresenterStatics>([](IMonochromaticOverlayPresenterStatics const& f) { return f.ReplacementColorProperty(); });
    }
    inline NavigationViewItemPresenter::NavigationViewItemPresenter()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<NavigationViewItemPresenter, INavigationViewItemPresenterFactory>([&](INavigationViewItemPresenterFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto NavigationViewItemPresenter::IconProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(INavigationViewItemPresenterStatics const&), NavigationViewItemPresenter, INavigationViewItemPresenterStatics>([](INavigationViewItemPresenterStatics const& f) { return f.IconProperty(); });
    }
    inline auto NavigationViewItemPresenter::TemplateSettingsProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(INavigationViewItemPresenterStatics const&), NavigationViewItemPresenter, INavigationViewItemPresenterStatics>([](INavigationViewItemPresenterStatics const& f) { return f.TemplateSettingsProperty(); });
    }
    inline auto NavigationViewItemPresenter::InfoBadgeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(INavigationViewItemPresenterStatics2 const&), NavigationViewItemPresenter, INavigationViewItemPresenterStatics2>([](INavigationViewItemPresenterStatics2 const& f) { return f.InfoBadgeProperty(); });
    }
    inline NavigationViewItemPresenterTemplateSettings::NavigationViewItemPresenterTemplateSettings()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<NavigationViewItemPresenterTemplateSettings, INavigationViewItemPresenterTemplateSettingsFactory>([&](INavigationViewItemPresenterTemplateSettingsFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto NavigationViewItemPresenterTemplateSettings::IconWidthProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(INavigationViewItemPresenterTemplateSettingsStatics const&), NavigationViewItemPresenterTemplateSettings, INavigationViewItemPresenterTemplateSettingsStatics>([](INavigationViewItemPresenterTemplateSettingsStatics const& f) { return f.IconWidthProperty(); });
    }
    inline auto NavigationViewItemPresenterTemplateSettings::SmallerIconWidthProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(INavigationViewItemPresenterTemplateSettingsStatics const&), NavigationViewItemPresenterTemplateSettings, INavigationViewItemPresenterTemplateSettingsStatics>([](INavigationViewItemPresenterTemplateSettingsStatics const& f) { return f.SmallerIconWidthProperty(); });
    }
    inline TabViewListView::TabViewListView()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TabViewListView, ITabViewListViewFactory>([&](ITabViewListViewFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    template <typename D, typename... Interfaces>
    struct ColorPickerSliderT :
        implements<D, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBaseOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider, winrt::Windows::UI::Xaml::Controls::ISlider, winrt::Windows::UI::Xaml::Controls::ISlider2, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBase, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ColorPickerSlider, winrt::Windows::UI::Xaml::Controls::Slider, winrt::Windows::UI::Xaml::Controls::Primitives::RangeBase, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBaseOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverrides6T<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = ColorPickerSlider;
    protected:
        ColorPickerSliderT()
        {
            impl::call_factory<ColorPickerSlider, IColorPickerSliderFactory>([&](IColorPickerSliderFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ColorSpectrumT :
        implements<D, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ColorSpectrum, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Controls::IControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverrides6T<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = ColorSpectrum;
    protected:
        ColorSpectrumT()
        {
            impl::call_factory<ColorSpectrum, IColorSpectrumFactory>([&](IColorSpectrumFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ColumnMajorUniformToLargestGridLayoutT :
        implements<D, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutOverrides, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ColumnMajorUniformToLargestGridLayout, winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutOverridesT<D>
    {
        using composable = ColumnMajorUniformToLargestGridLayout;
    protected:
        ColumnMajorUniformToLargestGridLayoutT()
        {
            impl::call_factory<ColumnMajorUniformToLargestGridLayout, IColumnMajorUniformToLargestGridLayoutFactory>([&](IColumnMajorUniformToLargestGridLayoutFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct CommandBarFlyoutCommandBarT :
        implements<D, winrt::Windows::UI::Xaml::Controls::IAppBarOverrides, winrt::Windows::UI::Xaml::Controls::IAppBarOverrides3, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar, winrt::Windows::UI::Xaml::Controls::ICommandBar, winrt::Windows::UI::Xaml::Controls::ICommandBar2, winrt::Windows::UI::Xaml::Controls::ICommandBar3, winrt::Windows::UI::Xaml::Controls::IAppBar, winrt::Windows::UI::Xaml::Controls::IAppBar2, winrt::Windows::UI::Xaml::Controls::IAppBar3, winrt::Windows::UI::Xaml::Controls::IAppBar4, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, CommandBarFlyoutCommandBar, winrt::Windows::UI::Xaml::Controls::CommandBar, winrt::Windows::UI::Xaml::Controls::AppBar, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Controls::IAppBarOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IAppBarOverrides3T<D>, winrt::Windows::UI::Xaml::Controls::IContentControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverrides6T<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = CommandBarFlyoutCommandBar;
    protected:
        CommandBarFlyoutCommandBarT()
        {
            impl::call_factory<CommandBarFlyoutCommandBar, ICommandBarFlyoutCommandBarFactory>([&](ICommandBarFlyoutCommandBarFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct InfoBarPanelT :
        implements<D, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, InfoBarPanel, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = InfoBarPanel;
    protected:
        InfoBarPanelT()
        {
            impl::call_factory<InfoBarPanel, IInfoBarPanelFactory>([&](IInfoBarPanelFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct MonochromaticOverlayPresenterT :
        implements<D, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter, winrt::Windows::UI::Xaml::Controls::IGrid, winrt::Windows::UI::Xaml::Controls::IGrid2, winrt::Windows::UI::Xaml::Controls::IGrid3, winrt::Windows::UI::Xaml::Controls::IGrid4, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, MonochromaticOverlayPresenter, winrt::Windows::UI::Xaml::Controls::Grid, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = MonochromaticOverlayPresenter;
    protected:
        MonochromaticOverlayPresenterT()
        {
            impl::call_factory<MonochromaticOverlayPresenter, IMonochromaticOverlayPresenterFactory>([&](IMonochromaticOverlayPresenterFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct NavigationViewItemPresenterT :
        implements<D, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, NavigationViewItemPresenter, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Controls::IContentControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverrides6T<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = NavigationViewItemPresenter;
    protected:
        NavigationViewItemPresenterT()
        {
            impl::call_factory<NavigationViewItemPresenter, INavigationViewItemPresenterFactory>([&](INavigationViewItemPresenterFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct NavigationViewItemPresenterTemplateSettingsT :
        implements<D, winrt::Windows::Foundation::IInspectable, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, NavigationViewItemPresenterTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>
    {
        using composable = NavigationViewItemPresenterTemplateSettings;
    protected:
        NavigationViewItemPresenterTemplateSettingsT()
        {
            impl::call_factory<NavigationViewItemPresenterTemplateSettings, INavigationViewItemPresenterTemplateSettingsFactory>([&](INavigationViewItemPresenterTemplateSettingsFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TabViewListViewT :
        implements<D, winrt::Windows::UI::Xaml::Controls::IItemsControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView, winrt::Windows::UI::Xaml::Controls::IListView, winrt::Windows::UI::Xaml::Controls::IListViewBase, winrt::Windows::UI::Xaml::Controls::IListViewBase2, winrt::Windows::UI::Xaml::Controls::IListViewBase3, winrt::Windows::UI::Xaml::Controls::IListViewBase4, winrt::Windows::UI::Xaml::Controls::IListViewBase5, winrt::Windows::UI::Xaml::Controls::IListViewBase6, winrt::Windows::UI::Xaml::Controls::ISemanticZoomInformation, winrt::Windows::UI::Xaml::Controls::Primitives::ISelector, winrt::Windows::UI::Xaml::Controls::IItemsControl, winrt::Windows::UI::Xaml::Controls::IItemsControl2, winrt::Windows::UI::Xaml::Controls::IItemsControl3, winrt::Windows::UI::Xaml::Controls::IItemContainerMapping, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TabViewListView, winrt::Windows::UI::Xaml::Controls::ListView, winrt::Windows::UI::Xaml::Controls::ListViewBase, winrt::Windows::UI::Xaml::Controls::Primitives::Selector, winrt::Windows::UI::Xaml::Controls::ItemsControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Controls::IItemsControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverridesT<D>, winrt::Windows::UI::Xaml::Controls::IControlOverrides6T<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverridesT<D>, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2T<D>, winrt::Windows::UI::Xaml::IUIElementOverridesT<D>, winrt::Windows::UI::Xaml::IUIElementOverrides7T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides8T<D>, winrt::Windows::UI::Xaml::IUIElementOverrides9T<D>
    {
        using composable = TabViewListView;
    protected:
        TabViewListViewT()
        {
            impl::call_factory<TabViewListView, ITabViewListViewFactory>([&](ITabViewListViewFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::AutoSuggestBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::ComboBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarAutomationProperties> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterConverter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
