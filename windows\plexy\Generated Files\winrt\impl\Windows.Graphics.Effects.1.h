// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Graphics_Effects_1_H
#define WINRT_Windows_Graphics_Effects_1_H
#include "winrt/impl/Windows.Graphics.Effects.0.h"
WINRT_EXPORT namespace winrt::Windows::Graphics::Effects
{
    struct WINRT_IMPL_EMPTY_BASES IGraphicsEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsEffect>,
        impl::require<winrt::Windows::Graphics::Effects::IGraphicsEffect, winrt::Windows::Graphics::Effects::IGraphicsEffectSource>
    {
        IGraphicsEffect(std::nullptr_t = nullptr) noexcept {}
        IGraphicsEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGraphicsEffectSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGraphicsEffectSource>
    {
        IGraphicsEffectSource(std::nullptr_t = nullptr) noexcept {}
        IGraphicsEffectSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
