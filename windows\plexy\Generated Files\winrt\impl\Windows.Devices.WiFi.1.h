// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_WiFi_1_H
#define WINRT_Windows_Devices_WiFi_1_H
#include "winrt/impl/Windows.Devices.WiFi.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::WiFi
{
    struct WINRT_IMPL_EMPTY_BASES IWiFiAdapter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiAdapter>
    {
        IWiFiAdapter(std::nullptr_t = nullptr) noexcept {}
        IWiFiAdapter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiAdapter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiAdapter2>
    {
        IWiFiAdapter2(std::nullptr_t = nullptr) noexcept {}
        IWiFiAdapter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiAdapterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiAdapterStatics>
    {
        IWiFiAdapterStatics(std::nullptr_t = nullptr) noexcept {}
        IWiFiAdapterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiAvailableNetwork :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiAvailableNetwork>
    {
        IWiFiAvailableNetwork(std::nullptr_t = nullptr) noexcept {}
        IWiFiAvailableNetwork(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiConnectionResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiConnectionResult>
    {
        IWiFiConnectionResult(std::nullptr_t = nullptr) noexcept {}
        IWiFiConnectionResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiNetworkReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiNetworkReport>
    {
        IWiFiNetworkReport(std::nullptr_t = nullptr) noexcept {}
        IWiFiNetworkReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiOnDemandHotspotConnectTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiOnDemandHotspotConnectTriggerDetails>
    {
        IWiFiOnDemandHotspotConnectTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IWiFiOnDemandHotspotConnectTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiOnDemandHotspotConnectionResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiOnDemandHotspotConnectionResult>
    {
        IWiFiOnDemandHotspotConnectionResult(std::nullptr_t = nullptr) noexcept {}
        IWiFiOnDemandHotspotConnectionResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiOnDemandHotspotNetwork :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiOnDemandHotspotNetwork>
    {
        IWiFiOnDemandHotspotNetwork(std::nullptr_t = nullptr) noexcept {}
        IWiFiOnDemandHotspotNetwork(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiOnDemandHotspotNetworkProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiOnDemandHotspotNetworkProperties>
    {
        IWiFiOnDemandHotspotNetworkProperties(std::nullptr_t = nullptr) noexcept {}
        IWiFiOnDemandHotspotNetworkProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiOnDemandHotspotNetworkStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiOnDemandHotspotNetworkStatics>
    {
        IWiFiOnDemandHotspotNetworkStatics(std::nullptr_t = nullptr) noexcept {}
        IWiFiOnDemandHotspotNetworkStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWiFiWpsConfigurationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWiFiWpsConfigurationResult>
    {
        IWiFiWpsConfigurationResult(std::nullptr_t = nullptr) noexcept {}
        IWiFiWpsConfigurationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
