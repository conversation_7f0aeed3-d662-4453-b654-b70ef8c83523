// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_Web_WebView2_Core_H
#define WINRT_Microsoft_Web_WebView2_Core_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.Foundation.Collections.2.h"
#include "winrt/impl/Windows.Security.Cryptography.Certificates.2.h"
#include "winrt/impl/Windows.Storage.Streams.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.UI.Core.2.h"
#include "winrt/impl/Microsoft.Web.WebView2.Core.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_CoreWebView2Certificate_Manual<D>::ToCertificate() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual)->ToCertificate(&result));
        return winrt::Windows::Security::Cryptography::Certificates::Certificate{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_CoreWebView2ClientCertificate_Manual<D>::ToCertificate() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual)->ToCertificate(&result));
        return winrt::Windows::Security::Cryptography::Certificates::Certificate{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_CoreWebView2Profile_Manual<D>::ClearBrowsingDataAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const& dataKinds, winrt::Windows::Foundation::DateTime const& startTime, winrt::Windows::Foundation::DateTime const& endTime) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual)->ClearBrowsingDataAsync(static_cast<uint32_t>(dataKinds), impl::bind_in(startTime), impl::bind_in(endTime), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_CoreWebView2Profile_Manual<D>::ClearBrowsingDataAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual)->ClearBrowsingDataAsync2(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::Settings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_Settings(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_Source(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::BrowserProcessId() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_BrowserProcessId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::CanGoBack() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_CanGoBack(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::CanGoForward() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_CanGoForward(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::DocumentTitle() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_DocumentTitle(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContainsFullScreenElement() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->get_ContainsFullScreenElement(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_NavigationStarting(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, NavigationStarting_revoker>(this, NavigationStarting(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationStarting(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_NavigationStarting(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContentLoading(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_ContentLoading(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContentLoading(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ContentLoading_revoker>(this, ContentLoading(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContentLoading(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_ContentLoading(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::SourceChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_SourceChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::SourceChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, SourceChanged_revoker>(this, SourceChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::SourceChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_SourceChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::HistoryChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_HistoryChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::HistoryChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, HistoryChanged_revoker>(this, HistoryChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::HistoryChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_HistoryChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_NavigationCompleted(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, NavigationCompleted_revoker>(this, NavigationCompleted(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigationCompleted(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_NavigationCompleted(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_FrameNavigationStarting(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, FrameNavigationStarting_revoker>(this, FrameNavigationStarting(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationStarting(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_FrameNavigationStarting(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_FrameNavigationCompleted(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, FrameNavigationCompleted_revoker>(this, FrameNavigationCompleted(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::FrameNavigationCompleted(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_FrameNavigationCompleted(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ScriptDialogOpening(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_ScriptDialogOpening(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ScriptDialogOpening(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ScriptDialogOpening_revoker>(this, ScriptDialogOpening(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ScriptDialogOpening(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_ScriptDialogOpening(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::PermissionRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_PermissionRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::PermissionRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, PermissionRequested_revoker>(this, PermissionRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::PermissionRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_PermissionRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ProcessFailed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_ProcessFailed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ProcessFailed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ProcessFailed_revoker>(this, ProcessFailed(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ProcessFailed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_ProcessFailed(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebMessageReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_WebMessageReceived(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebMessageReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, WebMessageReceived_revoker>(this, WebMessageReceived(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebMessageReceived(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_WebMessageReceived(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NewWindowRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_NewWindowRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NewWindowRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, NewWindowRequested_revoker>(this, NewWindowRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NewWindowRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_NewWindowRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::DocumentTitleChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_DocumentTitleChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::DocumentTitleChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, DocumentTitleChanged_revoker>(this, DocumentTitleChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::DocumentTitleChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_DocumentTitleChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContainsFullScreenElementChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_ContainsFullScreenElementChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContainsFullScreenElementChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, ContainsFullScreenElementChanged_revoker>(this, ContainsFullScreenElementChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ContainsFullScreenElementChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_ContainsFullScreenElementChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebResourceRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_WebResourceRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebResourceRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, WebResourceRequested_revoker>(this, WebResourceRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WebResourceRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_WebResourceRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WindowCloseRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->add_WindowCloseRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WindowCloseRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, WindowCloseRequested_revoker>(this, WindowCloseRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::WindowCloseRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->remove_WindowCloseRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::Navigate(param::hstring const& uri) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->Navigate(*(void**)(&uri)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::NavigateToString(param::hstring const& htmlContent) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->NavigateToString(*(void**)(&htmlContent)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::AddScriptToExecuteOnDocumentCreatedAsync(param::hstring const& javaScript) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->AddScriptToExecuteOnDocumentCreatedAsync(*(void**)(&javaScript), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<hstring>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::RemoveScriptToExecuteOnDocumentCreated(param::hstring const& id) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->RemoveScriptToExecuteOnDocumentCreated(*(void**)(&id)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::ExecuteScriptAsync(param::hstring const& javaScript) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->ExecuteScriptAsync(*(void**)(&javaScript), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<hstring>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::CapturePreviewAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2CapturePreviewImageFormat const& imageFormat, winrt::Windows::Storage::Streams::IRandomAccessStream const& imageStream) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->CapturePreviewAsync(static_cast<int32_t>(imageFormat), *(void**)(&imageStream), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::Reload() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->Reload());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::PostWebMessageAsJson(param::hstring const& webMessageAsJson) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->PostWebMessageAsJson(*(void**)(&webMessageAsJson)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::PostWebMessageAsString(param::hstring const& webMessageAsString) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->PostWebMessageAsString(*(void**)(&webMessageAsString)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::CallDevToolsProtocolMethodAsync(param::hstring const& methodName, param::hstring const& parametersAsJson) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->CallDevToolsProtocolMethodAsync(*(void**)(&methodName), *(void**)(&parametersAsJson), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<hstring>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::GoBack() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->GoBack());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::GoForward() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->GoForward());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::GetDevToolsProtocolEventReceiver(param::hstring const& eventName) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->GetDevToolsProtocolEventReceiver(*(void**)(&eventName), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::Stop() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->Stop());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::AddHostObjectToScript(param::hstring const& name, winrt::Windows::Foundation::IInspectable const& rawObject) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->AddHostObjectToScript(*(void**)(&name), *(void**)(&rawObject)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::RemoveHostObjectFromScript(param::hstring const& name) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->RemoveHostObjectFromScript(*(void**)(&name)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::OpenDevToolsWindow() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->OpenDevToolsWindow());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::AddWebResourceRequestedFilter(param::hstring const& uri, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const& ResourceContext) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->AddWebResourceRequestedFilter(*(void**)(&uri), static_cast<int32_t>(ResourceContext)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>::RemoveWebResourceRequestedFilter(param::hstring const& uri, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const& ResourceContext) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2)->RemoveWebResourceRequestedFilter(*(void**)(&uri), static_cast<int32_t>(ResourceContext)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::KeyEventKind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2KeyEventKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->get_KeyEventKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::VirtualKey() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->get_VirtualKey(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::KeyEventLParam() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->get_KeyEventLParam(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::PhysicalKeyStatus() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->get_PhysicalKeyStatus(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::Challenge() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->get_Challenge(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::Response() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->get_Response(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::Cancel() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->get_Cancel(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::Cancel(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->put_Cancel(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse<D>::UserName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse)->get_UserName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse<D>::UserName(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse)->put_UserName(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse<D>::Password() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse)->get_Password(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse<D>::Password(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse)->put_Password(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BrowserProcessExitedEventArgs<D>::BrowserProcessExitKind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs)->get_BrowserProcessExitKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2BrowserProcessExitedEventArgs<D>::BrowserProcessId() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs)->get_BrowserProcessId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::Subject() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_Subject(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::Issuer() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_Issuer(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::ValidFrom() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_ValidFrom(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::ValidTo() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_ValidTo(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::DerEncodedSerialNumber() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_DerEncodedSerialNumber(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::DisplayName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_DisplayName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::PemEncodedIssuerCertificateChain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->get_PemEncodedIssuerCertificateChain(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<hstring>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>::ToPemEncoding() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate)->ToPemEncoding(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::Subject() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_Subject(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::Issuer() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_Issuer(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::ValidFrom() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_ValidFrom(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::ValidTo() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_ValidTo(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::DerEncodedSerialNumber() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_DerEncodedSerialNumber(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::DisplayName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_DisplayName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::PemEncodedIssuerCertificateChain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_PemEncodedIssuerCertificateChain(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<hstring>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::Kind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>::ToPemEncoding() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate)->ToPemEncoding(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Host() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_Host(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Port() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_Port(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::IsProxy() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_IsProxy(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::AllowedCertificateAuthorities() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_AllowedCertificateAuthorities(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<hstring>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::MutuallyTrustedCertificates() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_MutuallyTrustedCertificates(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::SelectedCertificate() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_SelectedCertificate(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::SelectedCertificate(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->put_SelectedCertificate(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Cancel() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_Cancel(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Cancel(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->put_Cancel(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::RootVisualTarget() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->get_RootVisualTarget(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::RootVisualTarget(winrt::Windows::Foundation::IInspectable const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->put_RootVisualTarget(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::CursorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->add_CursorChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::CursorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, CursorChanged_revoker>(this, CursorChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::CursorChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->remove_CursorChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::SendMouseInput(winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventKind const& eventKind, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventVirtualKeys const& virtualKeys, uint32_t mouseData, winrt::Windows::Foundation::Point const& point) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->SendMouseInput(static_cast<int32_t>(eventKind), static_cast<uint32_t>(virtualKeys), mouseData, impl::bind_in(point)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::SendPointerInput(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerEventKind const& eventKind, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo const& pointerInfo) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->SendPointerInput(static_cast<int32_t>(eventKind), *(void**)(&pointerInfo)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>::Cursor() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController)->get_Cursor(&value));
        return winrt::Windows::UI::Core::CoreCursor{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContentLoadingEventArgs<D>::IsErrorPage() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs)->get_IsErrorPage(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContentLoadingEventArgs<D>::NavigationId() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs)->get_NavigationId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::Label() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_Label(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::CommandId() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_CommandId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::ShortcutKeyDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_ShortcutKeyDescription(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::Icon() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_Icon(&value));
        return winrt::Windows::Storage::Streams::IRandomAccessStream{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::Kind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::IsEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_IsEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::IsEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->put_IsEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::IsChecked() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_IsChecked(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::IsChecked(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->put_IsChecked(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::Children() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->get_Children(&value));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::CustomItemSelected(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->add_CustomItemSelected(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::CustomItemSelected(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, CustomItemSelected_revoker>(this, CustomItemSelected(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>::CustomItemSelected(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem)->remove_CustomItemSelected(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::MenuItems() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->get_MenuItems(&value));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::ContextMenuTarget() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->get_ContextMenuTarget(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::Location() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->get_Location(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::SelectedCommandId() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->get_SelectedCommandId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::SelectedCommandId(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->put_SelectedCommandId(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::Kind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTargetKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::IsEditable() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_IsEditable(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::IsRequestedForMainFrame() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_IsRequestedForMainFrame(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::PageUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_PageUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::FrameUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_FrameUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::HasLinkUri() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_HasLinkUri(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::LinkUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_LinkUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::HasLinkText() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_HasLinkText(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::LinkText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_LinkText(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::HasSourceUri() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_HasSourceUri(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::SourceUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_SourceUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::HasSelection() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_HasSelection(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>::SelectionText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget)->get_SelectionText(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::IsVisible() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->get_IsVisible(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::IsVisible(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->put_IsVisible(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::Bounds() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->get_Bounds(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::Bounds(winrt::Windows::Foundation::Rect const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->put_Bounds(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ZoomFactor() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->get_ZoomFactor(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ZoomFactor(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->put_ZoomFactor(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ParentWindow() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->get_ParentWindow(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ParentWindow(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->put_ParentWindow(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::CoreWebView2() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->get_CoreWebView2(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ZoomFactorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->add_ZoomFactorChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ZoomFactorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, ZoomFactorChanged_revoker>(this, ZoomFactorChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::ZoomFactorChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->remove_ZoomFactorChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::MoveFocusRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->add_MoveFocusRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::MoveFocusRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, MoveFocusRequested_revoker>(this, MoveFocusRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::MoveFocusRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->remove_MoveFocusRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::GotFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->add_GotFocus(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::GotFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, GotFocus_revoker>(this, GotFocus(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::GotFocus(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->remove_GotFocus(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::LostFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->add_LostFocus(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::LostFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, LostFocus_revoker>(this, LostFocus(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::LostFocus(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->remove_LostFocus(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::AcceleratorKeyPressed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->add_AcceleratorKeyPressed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::AcceleratorKeyPressed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, AcceleratorKeyPressed_revoker>(this, AcceleratorKeyPressed(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::AcceleratorKeyPressed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->remove_AcceleratorKeyPressed(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::SetBoundsAndZoomFactor(winrt::Windows::Foundation::Rect const& Bounds, double ZoomFactor) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->SetBoundsAndZoomFactor(impl::bind_in(Bounds), ZoomFactor));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::MoveFocus(winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason const& reason) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->MoveFocus(static_cast<int32_t>(reason)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::NotifyParentWindowPositionChanged() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->NotifyParentWindowPositionChanged());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>::Close() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller)->Close());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller2<D>::DefaultBackgroundColor() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2)->get_DefaultBackgroundColor(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller2<D>::DefaultBackgroundColor(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2)->put_DefaultBackgroundColor(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::RasterizationScale() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->get_RasterizationScale(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::RasterizationScale(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->put_RasterizationScale(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::ShouldDetectMonitorScaleChanges() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->get_ShouldDetectMonitorScaleChanges(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::ShouldDetectMonitorScaleChanges(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->put_ShouldDetectMonitorScaleChanges(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::BoundsMode() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->get_BoundsMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::BoundsMode(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->put_BoundsMode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::RasterizationScaleChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->add_RasterizationScaleChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::RasterizationScaleChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, RasterizationScaleChanged_revoker>(this, RasterizationScaleChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>::RasterizationScaleChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3)->remove_RasterizationScaleChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller4<D>::AllowExternalDrop() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4)->get_AllowExternalDrop(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller4<D>::AllowExternalDrop(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4)->put_AllowExternalDrop(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions<D>::ProfileName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions)->get_ProfileName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions<D>::ProfileName(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions)->put_ProfileName(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions<D>::IsInPrivateModeEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions)->get_IsInPrivateModeEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions<D>::IsInPrivateModeEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions)->put_IsInPrivateModeEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReference<D>::WindowHandle() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference)->get_WindowHandle(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReference<D>::CoreWindow() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference)->get_CoreWindow(&value));
        return winrt::Windows::UI::Core::CoreWindow{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReferenceStatics<D>::CreateFromWindowHandle(uint64_t windowHandle) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics)->CreateFromWindowHandle(windowHandle, &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReferenceStatics<D>::CreateFromCoreWindow(winrt::Windows::UI::Core::CoreWindow const& coreWindow) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics)->CreateFromCoreWindow(*(void**)(&coreWindow), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Value() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_Value(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Value(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->put_Value(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Domain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_Domain(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Path() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_Path(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Expires() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_Expires(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::Expires(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->put_Expires(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::IsHttpOnly() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_IsHttpOnly(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::IsHttpOnly(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->put_IsHttpOnly(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::SameSite() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_SameSite(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::SameSite(winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->put_SameSite(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::IsSecure() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_IsSecure(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::IsSecure(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->put_IsSecure(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>::IsSession() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie)->get_IsSession(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::CreateCookie(param::hstring const& name, param::hstring const& value, param::hstring const& Domain, param::hstring const& Path) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->CreateCookie(*(void**)(&name), *(void**)(&value), *(void**)(&Domain), *(void**)(&Path), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::CopyCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookieParam) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->CopyCookie(*(void**)(&cookieParam), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::AddOrUpdateCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookie) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->AddOrUpdateCookie(*(void**)(&cookie)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::DeleteCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookie) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->DeleteCookie(*(void**)(&cookie)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::DeleteCookies(param::hstring const& name, param::hstring const& uri) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->DeleteCookies(*(void**)(&name), *(void**)(&uri)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::DeleteCookiesWithDomainAndPath(param::hstring const& name, param::hstring const& Domain, param::hstring const& Path) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->DeleteCookiesWithDomainAndPath(*(void**)(&name), *(void**)(&Domain), *(void**)(&Path)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>::DeleteAllCookies() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager)->DeleteAllCookies());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager_Manual<D>::GetCookiesAsync(param::hstring const& uri) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual)->GetCookiesAsync(*(void**)(&uri), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DOMContentLoadedEventArgs<D>::NavigationId() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs)->get_NavigationId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs<D>::ParameterObjectAsJson() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs)->get_ParameterObjectAsJson(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs2<D>::SessionId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2)->get_SessionId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceiver<D>::DevToolsProtocolEventReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver)->add_DevToolsProtocolEventReceived(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceiver<D>::DevToolsProtocolEventReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, DevToolsProtocolEventReceived_revoker>(this, DevToolsProtocolEventReceived(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceiver<D>::DevToolsProtocolEventReceived(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver)->remove_DevToolsProtocolEventReceived(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter<D>::WrapNamedObject(param::hstring const& name, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& adapter) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter)->WrapNamedObject(*(void**)(&name), *(void**)(&adapter), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter<D>::WrapObject(winrt::Windows::Foundation::IInspectable const& unwrapped, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& adapter) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter)->WrapObject(*(void**)(&unwrapped), *(void**)(&adapter), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter<D>::UnwrapObject(winrt::Windows::Foundation::IInspectable const& wrapped) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter)->UnwrapObject(*(void**)(&wrapped), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter<D>::Clean() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter)->Clean());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::ContentDisposition() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_ContentDisposition(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::MimeType() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_MimeType(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::TotalBytesToReceive() const
    {
        int64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_TotalBytesToReceive(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::BytesReceived() const
    {
        int64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_BytesReceived(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::EstimatedEndTime() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_EstimatedEndTime(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::ResultFilePath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_ResultFilePath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::State() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadState value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_State(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::InterruptReason() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadInterruptReason value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_InterruptReason(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::CanResume() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->get_CanResume(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::BytesReceivedChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->add_BytesReceivedChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::BytesReceivedChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, BytesReceivedChanged_revoker>(this, BytesReceivedChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::BytesReceivedChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->remove_BytesReceivedChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::EstimatedEndTimeChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->add_EstimatedEndTimeChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::EstimatedEndTimeChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, EstimatedEndTimeChanged_revoker>(this, EstimatedEndTimeChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::EstimatedEndTimeChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->remove_EstimatedEndTimeChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::StateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->add_StateChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::StateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, StateChanged_revoker>(this, StateChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::StateChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->remove_StateChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::Cancel() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->Cancel());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::Pause() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->Pause());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>::Resume() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation)->Resume());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::DownloadOperation() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->get_DownloadOperation(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::Cancel() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->get_Cancel(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::Cancel(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->put_Cancel(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::ResultFilePath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->get_ResultFilePath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::ResultFilePath(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->put_ResultFilePath(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::BrowserVersionString() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment)->get_BrowserVersionString(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::NewBrowserVersionAvailable(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment)->add_NewBrowserVersionAvailable(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::NewBrowserVersionAvailable(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, NewBrowserVersionAvailable_revoker>(this, NewBrowserVersionAvailable(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::NewBrowserVersionAvailable(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment)->remove_NewBrowserVersionAvailable(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::CreateCoreWebView2ControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment)->CreateCoreWebView2ControllerAsync(*(void**)(&ParentWindow), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>::CreateWebResourceResponse(winrt::Windows::Storage::Streams::IRandomAccessStream const& Content, int32_t StatusCode, param::hstring const& ReasonPhrase, param::hstring const& Headers) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment)->CreateWebResourceResponse(*(void**)(&Content), StatusCode, *(void**)(&ReasonPhrase), *(void**)(&Headers), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment10<D>::CreateCoreWebView2ControllerOptions() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10)->CreateCoreWebView2ControllerOptions(&result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment2<D>::CreateWebResourceRequest(param::hstring const& uri, param::hstring const& Method, winrt::Windows::Storage::Streams::IRandomAccessStream const& postData, param::hstring const& Headers) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2)->CreateWebResourceRequest(*(void**)(&uri), *(void**)(&Method), *(void**)(&postData), *(void**)(&Headers), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment3<D>::CreateCoreWebView2CompositionControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3)->CreateCoreWebView2CompositionControllerAsync(*(void**)(&ParentWindow), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment3<D>::CreateCoreWebView2PointerInfo() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3)->CreateCoreWebView2PointerInfo(&result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment5<D>::BrowserProcessExited(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5)->add_BrowserProcessExited(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment5<D>::BrowserProcessExited(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, BrowserProcessExited_revoker>(this, BrowserProcessExited(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment5<D>::BrowserProcessExited(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5)->remove_BrowserProcessExited(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment6<D>::CreatePrintSettings() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6)->CreatePrintSettings(&result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment7<D>::UserDataFolder() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7)->get_UserDataFolder(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8<D>::ProcessInfosChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8)->add_ProcessInfosChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8<D>::ProcessInfosChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, ProcessInfosChanged_revoker>(this, ProcessInfosChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8<D>::ProcessInfosChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8)->remove_ProcessInfosChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8<D>::GetProcessInfos() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8)->GetProcessInfos(&result));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo>{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment9<D>::CreateContextMenuItem(param::hstring const& Label, winrt::Windows::Storage::Streams::IRandomAccessStream const& iconStream, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind const& Kind) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9)->CreateContextMenuItem(*(void**)(&Label), *(void**)(&iconStream), static_cast<int32_t>(Kind), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::AdditionalBrowserArguments() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->get_AdditionalBrowserArguments(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::AdditionalBrowserArguments(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->put_AdditionalBrowserArguments(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::Language() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->get_Language(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::Language(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->put_Language(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::TargetCompatibleBrowserVersion() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->get_TargetCompatibleBrowserVersion(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::TargetCompatibleBrowserVersion(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->put_TargetCompatibleBrowserVersion(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::AllowSingleSignOnUsingOSPrimaryAccount() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->get_AllowSingleSignOnUsingOSPrimaryAccount(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>::AllowSingleSignOnUsingOSPrimaryAccount(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions)->put_AllowSingleSignOnUsingOSPrimaryAccount(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions2<D>::ExclusiveUserDataFolderAccess() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2)->get_ExclusiveUserDataFolderAccess(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions2<D>::ExclusiveUserDataFolderAccess(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2)->put_ExclusiveUserDataFolderAccess(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>::CreateAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics)->CreateAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>::CreateWithOptionsAsync(param::hstring const& browserExecutableFolder, param::hstring const& userDataFolder, winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions const& options) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics)->CreateWithOptionsAsync(*(void**)(&browserExecutableFolder), *(void**)(&userDataFolder), *(void**)(&options), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>::GetAvailableBrowserVersionString() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics)->GetAvailableBrowserVersionString(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>::GetAvailableBrowserVersionString(param::hstring const& browserExecutableFolder) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics)->GetAvailableBrowserVersionString2(*(void**)(&browserExecutableFolder), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>::CompareBrowserVersionString(param::hstring const& browserVersionString1, param::hstring const& browserVersionString2) const
    {
        int32_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics)->CompareBrowserVersionString(*(void**)(&browserVersionString1), *(void**)(&browserVersionString2), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment_Manual<D>::CreateCoreWebView2ControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const& options) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual)->CreateCoreWebView2ControllerAsync(*(void**)(&ParentWindow), *(void**)(&options), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment_Manual<D>::CreateCoreWebView2CompositionControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const& options) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual)->CreateCoreWebView2CompositionControllerAsync(*(void**)(&ParentWindow), *(void**)(&options), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::NameChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->add_NameChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::NameChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, NameChanged_revoker>(this, NameChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::NameChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->remove_NameChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::Destroyed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->add_Destroyed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::Destroyed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, Destroyed_revoker>(this, Destroyed(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::Destroyed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->remove_Destroyed(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::RemoveHostObjectFromScript(param::hstring const& name) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->RemoveHostObjectFromScript(*(void**)(&name)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>::IsDestroyed() const
    {
        int32_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame)->IsDestroyed(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->add_NavigationStarting(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, NavigationStarting_revoker>(this, NavigationStarting(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationStarting(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->remove_NavigationStarting(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::ContentLoading(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->add_ContentLoading(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::ContentLoading(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ContentLoading_revoker>(this, ContentLoading(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::ContentLoading(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->remove_ContentLoading(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->add_NavigationCompleted(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, NavigationCompleted_revoker>(this, NavigationCompleted(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::NavigationCompleted(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->remove_NavigationCompleted(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::DOMContentLoaded(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->add_DOMContentLoaded(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::DOMContentLoaded(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, DOMContentLoaded_revoker>(this, DOMContentLoaded(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::DOMContentLoaded(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->remove_DOMContentLoaded(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::WebMessageReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->add_WebMessageReceived(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::WebMessageReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, WebMessageReceived_revoker>(this, WebMessageReceived(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::WebMessageReceived(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->remove_WebMessageReceived(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::ExecuteScriptAsync(param::hstring const& javaScript) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->ExecuteScriptAsync(*(void**)(&javaScript), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<hstring>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::PostWebMessageAsJson(param::hstring const& webMessageAsJson) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->PostWebMessageAsJson(*(void**)(&webMessageAsJson)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>::PostWebMessageAsString(param::hstring const& webMessageAsString) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2)->PostWebMessageAsString(*(void**)(&webMessageAsString)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame3<D>::PermissionRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3)->add_PermissionRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame3<D>::PermissionRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, PermissionRequested_revoker>(this, PermissionRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame3<D>::PermissionRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3)->remove_PermissionRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameCreatedEventArgs<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs)->get_Frame(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameInfo<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameInfo<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo)->get_Source(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>::GetHeader(param::hstring const& name) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders)->GetHeader(*(void**)(&name), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>::GetHeaders(param::hstring const& name) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders)->GetHeaders(*(void**)(&name), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>::Contains(param::hstring const& name) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders)->Contains(*(void**)(&name), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>::SetHeader(param::hstring const& name, param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders)->SetHeader(*(void**)(&name), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>::RemoveHeader(param::hstring const& name) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders)->RemoveHeader(*(void**)(&name)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders<D>::AppendHeader(param::hstring const& name, param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders)->AppendHeader(*(void**)(&name), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders<D>::Contains(param::hstring const& name) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders)->Contains(*(void**)(&name), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders<D>::GetHeader(param::hstring const& name) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders)->GetHeader(*(void**)(&name), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders<D>::GetHeaders(param::hstring const& name) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders)->GetHeaders(*(void**)(&name), &result));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2MoveFocusRequestedEventArgs<D>::Reason() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs)->get_Reason(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2MoveFocusRequestedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2MoveFocusRequestedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs<D>::IsSuccess() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs)->get_IsSuccess(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs<D>::WebErrorStatus() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs)->get_WebErrorStatus(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs<D>::NavigationId() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs)->get_NavigationId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs2<D>::HttpStatusCode() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2)->get_HttpStatusCode(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::IsUserInitiated() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_IsUserInitiated(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::IsRedirected() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_IsRedirected(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::RequestHeaders() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_RequestHeaders(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::Cancel() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_Cancel(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::Cancel(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->put_Cancel(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>::NavigationId() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs)->get_NavigationId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs2<D>::AdditionalAllowedFrameAncestors() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2)->get_AdditionalAllowedFrameAncestors(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs2<D>::AdditionalAllowedFrameAncestors(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2)->put_AdditionalAllowedFrameAncestors(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::NewWindow() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->get_NewWindow(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::NewWindow(winrt::Microsoft::Web::WebView2::Core::CoreWebView2 const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->put_NewWindow(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::IsUserInitiated() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->get_IsUserInitiated(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::WindowFeatures() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->get_WindowFeatures(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs2<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::PermissionKind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->get_PermissionKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::IsUserInitiated() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->get_IsUserInitiated(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::State() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->get_State(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::State(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->put_State(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs2<D>::Handled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2)->get_Handled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs2<D>::Handled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2)->put_Handled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerKind() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PointerKind(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerKind(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PointerKind(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerId() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PointerId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerId(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PointerId(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::FrameId() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_FrameId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::FrameId(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_FrameId(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerFlags() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PointerFlags(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerFlags(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PointerFlags(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerDeviceRect() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PointerDeviceRect(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PointerDeviceRect(winrt::Windows::Foundation::Rect const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PointerDeviceRect(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::DisplayRect() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_DisplayRect(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::DisplayRect(winrt::Windows::Foundation::Rect const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_DisplayRect(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PixelLocation() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PixelLocation(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PixelLocation(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PixelLocation(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HimetricLocation() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_HimetricLocation(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HimetricLocation(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_HimetricLocation(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PixelLocationRaw() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PixelLocationRaw(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PixelLocationRaw(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PixelLocationRaw(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HimetricLocationRaw() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_HimetricLocationRaw(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HimetricLocationRaw(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_HimetricLocationRaw(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::Time() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_Time(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::Time(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_Time(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HistoryCount() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_HistoryCount(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::HistoryCount(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_HistoryCount(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::InputData() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_InputData(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::InputData(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_InputData(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::KeyStates() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_KeyStates(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::KeyStates(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_KeyStates(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PerformanceCount() const
    {
        uint64_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PerformanceCount(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PerformanceCount(uint64_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PerformanceCount(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::ButtonChangeKind() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_ButtonChangeKind(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::ButtonChangeKind(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_ButtonChangeKind(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenFlags() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenFlags(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenFlags(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenFlags(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenMask() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenMask(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenMask(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenMask(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenPressure() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenPressure(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenPressure(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenPressure(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenRotation() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenRotation(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenRotation(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenRotation(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenTiltX() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenTiltX(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenTiltX(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenTiltX(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenTiltY() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_PenTiltY(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::PenTiltY(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_PenTiltY(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchFlags() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchFlags(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchFlags(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchFlags(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchMask() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchMask(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchMask(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchMask(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchContact() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchContact(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchContact(winrt::Windows::Foundation::Rect const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchContact(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchContactRaw() const
    {
        winrt::Windows::Foundation::Rect value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchContactRaw(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchContactRaw(winrt::Windows::Foundation::Rect const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchContactRaw(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchOrientation() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchOrientation(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchOrientation(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchOrientation(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchPressure() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->get_TouchPressure(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>::TouchPressure(uint32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo)->put_TouchPressure(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::Orientation() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_Orientation(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::Orientation(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_Orientation(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ScaleFactor() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_ScaleFactor(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ScaleFactor(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_ScaleFactor(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::PageWidth() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_PageWidth(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::PageWidth(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_PageWidth(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::PageHeight() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_PageHeight(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::PageHeight(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_PageHeight(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginTop() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_MarginTop(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginTop(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_MarginTop(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginBottom() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_MarginBottom(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginBottom(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_MarginBottom(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginLeft() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_MarginLeft(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginLeft(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_MarginLeft(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginRight() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_MarginRight(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::MarginRight(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_MarginRight(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintBackgrounds() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_ShouldPrintBackgrounds(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintBackgrounds(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_ShouldPrintBackgrounds(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintSelectionOnly() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_ShouldPrintSelectionOnly(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintSelectionOnly(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_ShouldPrintSelectionOnly(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintHeaderAndFooter() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_ShouldPrintHeaderAndFooter(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::ShouldPrintHeaderAndFooter(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_ShouldPrintHeaderAndFooter(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::HeaderTitle() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_HeaderTitle(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::HeaderTitle(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_HeaderTitle(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::FooterUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->get_FooterUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>::FooterUri(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings)->put_FooterUri(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs<D>::ProcessFailedKind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs)->get_ProcessFailedKind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2<D>::Reason() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedReason value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2)->get_Reason(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2<D>::ExitCode() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2)->get_ExitCode(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2<D>::ProcessDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2)->get_ProcessDescription(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2<D>::FrameInfosForFailedProcess() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2)->get_FrameInfosForFailedProcess(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessInfo<D>::ProcessId() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo)->get_ProcessId(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessInfo<D>::Kind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::ProfileName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->get_ProfileName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::IsInPrivateModeEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->get_IsInPrivateModeEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::ProfilePath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->get_ProfilePath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::DefaultDownloadFolderPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->get_DefaultDownloadFolderPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::DefaultDownloadFolderPath(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->put_DefaultDownloadFolderPath(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::PreferredColorScheme() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->get_PreferredColorScheme(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>::PreferredColorScheme(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile)->put_PreferredColorScheme(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile2<D>::ClearBrowsingDataAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const& dataKinds) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2)->ClearBrowsingDataAsync(static_cast<uint32_t>(dataKinds), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::Kind() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::Message() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->get_Message(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::DefaultText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->get_DefaultText(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::ResultText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->get_ResultText(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::ResultText(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->put_ResultText(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::Accept() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->Accept());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::ErrorStatus() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->get_ErrorStatus(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::RequestUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->get_RequestUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::ServerCertificate() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->get_ServerCertificate(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::Action() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->get_Action(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::Action(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->put_Action(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsScriptEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_IsScriptEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsScriptEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_IsScriptEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsWebMessageEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_IsWebMessageEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsWebMessageEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_IsWebMessageEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDefaultScriptDialogsEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_AreDefaultScriptDialogsEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDefaultScriptDialogsEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_AreDefaultScriptDialogsEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsStatusBarEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_IsStatusBarEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsStatusBarEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_IsStatusBarEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDevToolsEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_AreDevToolsEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDevToolsEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_AreDevToolsEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDefaultContextMenusEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_AreDefaultContextMenusEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreDefaultContextMenusEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_AreDefaultContextMenusEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreHostObjectsAllowed() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_AreHostObjectsAllowed(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::AreHostObjectsAllowed(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_AreHostObjectsAllowed(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsZoomControlEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_IsZoomControlEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsZoomControlEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_IsZoomControlEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsBuiltInErrorPageEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->get_IsBuiltInErrorPageEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>::IsBuiltInErrorPageEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings)->put_IsBuiltInErrorPageEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings2<D>::UserAgent() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2)->get_UserAgent(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings2<D>::UserAgent(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2)->put_UserAgent(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings3<D>::AreBrowserAcceleratorKeysEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3)->get_AreBrowserAcceleratorKeysEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings3<D>::AreBrowserAcceleratorKeysEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3)->put_AreBrowserAcceleratorKeysEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4<D>::IsPasswordAutosaveEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4)->get_IsPasswordAutosaveEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4<D>::IsPasswordAutosaveEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4)->put_IsPasswordAutosaveEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4<D>::IsGeneralAutofillEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4)->get_IsGeneralAutofillEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4<D>::IsGeneralAutofillEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4)->put_IsGeneralAutofillEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings5<D>::IsPinchZoomEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5)->get_IsPinchZoomEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings5<D>::IsPinchZoomEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5)->put_IsPinchZoomEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings6<D>::IsSwipeNavigationEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6)->get_IsSwipeNavigationEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings6<D>::IsSwipeNavigationEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6)->put_IsSwipeNavigationEnabled(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings7<D>::HiddenPdfToolbarItems() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7)->get_HiddenPdfToolbarItems(reinterpret_cast<uint32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings7<D>::HiddenPdfToolbarItems(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7)->put_HiddenPdfToolbarItems(static_cast<uint32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings_Manual<D>::HostObjectDispatchAdapter() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual)->get_HostObjectDispatchAdapter(&value));
        return winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings_Manual<D>::HostObjectDispatchAdapter(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual)->put_HostObjectDispatchAdapter(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2SourceChangedEventArgs<D>::IsNewDocument() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs)->get_IsNewDocument(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebMessageReceivedEventArgs<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs)->get_Source(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebMessageReceivedEventArgs<D>::WebMessageAsJson() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs)->get_WebMessageAsJson(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebMessageReceivedEventArgs<D>::TryGetWebMessageAsString() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs)->TryGetWebMessageAsString(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Uri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->get_Uri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Uri(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->put_Uri(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Method() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->get_Method(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Method(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->put_Method(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Content() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->get_Content(&value));
        return winrt::Windows::Storage::Streams::IRandomAccessStream{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Content(winrt::Windows::Storage::Streams::IRandomAccessStream const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->put_Content(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>::Headers() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest)->get_Headers(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>::Request() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs)->get_Request(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>::Response() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs)->get_Response(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>::Response(winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs)->put_Response(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>::ResourceContext() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs)->get_ResourceContext(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>::GetDeferral() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs)->GetDeferral(&result));
        return winrt::Windows::Foundation::Deferral{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::Content() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->get_Content(&value));
        return winrt::Windows::Storage::Streams::IRandomAccessStream{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::Content(winrt::Windows::Storage::Streams::IRandomAccessStream const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->put_Content(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::Headers() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->get_Headers(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::StatusCode() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->get_StatusCode(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::StatusCode(int32_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->put_StatusCode(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::ReasonPhrase() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->get_ReasonPhrase(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>::ReasonPhrase(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse)->put_ReasonPhrase(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseReceivedEventArgs<D>::Request() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs)->get_Request(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseReceivedEventArgs<D>::Response() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs)->get_Response(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView<D>::Headers() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView)->get_Headers(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView<D>::StatusCode() const
    {
        int32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView)->get_StatusCode(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView<D>::ReasonPhrase() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView)->get_ReasonPhrase(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView<D>::GetContentAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView)->GetContentAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::Streams::IRandomAccessStream>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::HasPosition() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_HasPosition(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::HasSize() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_HasSize(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::Left() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_Left(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::Top() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_Top(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::Height() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_Height(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::Width() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_Width(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::ShouldDisplayMenuBar() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_ShouldDisplayMenuBar(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::ShouldDisplayStatus() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_ShouldDisplayStatus(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::ShouldDisplayToolbar() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_ShouldDisplayToolbar(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>::ShouldDisplayScrollBars() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures)->get_ShouldDisplayScrollBars(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_10<D>::BasicAuthenticationRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10)->add_BasicAuthenticationRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_10<D>::BasicAuthenticationRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, BasicAuthenticationRequested_revoker>(this, BasicAuthenticationRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_10<D>::BasicAuthenticationRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10)->remove_BasicAuthenticationRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11<D>::ContextMenuRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11)->add_ContextMenuRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11<D>::ContextMenuRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ContextMenuRequested_revoker>(this, ContextMenuRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11<D>::ContextMenuRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11)->remove_ContextMenuRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11<D>::CallDevToolsProtocolMethodForSessionAsync(param::hstring const& sessionId, param::hstring const& methodName, param::hstring const& parametersAsJson) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11)->CallDevToolsProtocolMethodForSessionAsync(*(void**)(&sessionId), *(void**)(&methodName), *(void**)(&parametersAsJson), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<hstring>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12<D>::StatusBarText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12)->get_StatusBarText(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12<D>::StatusBarTextChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12)->add_StatusBarTextChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12<D>::StatusBarTextChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, StatusBarTextChanged_revoker>(this, StatusBarTextChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12<D>::StatusBarTextChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12)->remove_StatusBarTextChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_13<D>::Profile() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13)->get_Profile(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14<D>::ServerCertificateErrorDetected(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14)->add_ServerCertificateErrorDetected(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14<D>::ServerCertificateErrorDetected(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ServerCertificateErrorDetected_revoker>(this, ServerCertificateErrorDetected(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14<D>::ServerCertificateErrorDetected(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14)->remove_ServerCertificateErrorDetected(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14<D>::ClearServerCertificateErrorActionsAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14)->ClearServerCertificateErrorActionsAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::CookieManager() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->get_CookieManager(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::Environment() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->get_Environment(&value));
        return winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::WebResourceResponseReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->add_WebResourceResponseReceived(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::WebResourceResponseReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, WebResourceResponseReceived_revoker>(this, WebResourceResponseReceived(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::WebResourceResponseReceived(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->remove_WebResourceResponseReceived(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::DOMContentLoaded(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->add_DOMContentLoaded(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::DOMContentLoaded(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, DOMContentLoaded_revoker>(this, DOMContentLoaded(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::DOMContentLoaded(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->remove_DOMContentLoaded(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>::NavigateWithWebResourceRequest(winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest const& Request) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2)->NavigateWithWebResourceRequest(*(void**)(&Request)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>::IsSuspended() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3)->get_IsSuspended(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>::TrySuspendAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3)->TrySuspendAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<bool>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>::Resume() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3)->Resume());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>::SetVirtualHostNameToFolderMapping(param::hstring const& hostName, param::hstring const& folderPath, winrt::Microsoft::Web::WebView2::Core::CoreWebView2HostResourceAccessKind const& accessKind) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3)->SetVirtualHostNameToFolderMapping(*(void**)(&hostName), *(void**)(&folderPath), static_cast<int32_t>(accessKind)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>::ClearVirtualHostNameToFolderMapping(param::hstring const& hostName) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3)->ClearVirtualHostNameToFolderMapping(*(void**)(&hostName)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::FrameCreated(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4)->add_FrameCreated(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::FrameCreated(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, FrameCreated_revoker>(this, FrameCreated(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::FrameCreated(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4)->remove_FrameCreated(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::DownloadStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4)->add_DownloadStarting(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::DownloadStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, DownloadStarting_revoker>(this, DownloadStarting(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>::DownloadStarting(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4)->remove_DownloadStarting(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_5<D>::ClientCertificateRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5)->add_ClientCertificateRequested(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_5<D>::ClientCertificateRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, ClientCertificateRequested_revoker>(this, ClientCertificateRequested(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_5<D>::ClientCertificateRequested(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5)->remove_ClientCertificateRequested(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_6<D>::OpenTaskManagerWindow() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6)->OpenTaskManagerWindow());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_7<D>::PrintToPdfAsync(param::hstring const& ResultFilePath, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings const& printSettings) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7)->PrintToPdfAsync(*(void**)(&ResultFilePath), *(void**)(&printSettings), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<bool>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsMuted() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->get_IsMuted(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsMuted(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->put_IsMuted(value));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsDocumentPlayingAudio() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->get_IsDocumentPlayingAudio(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsMutedChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->add_IsMutedChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsMutedChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, IsMutedChanged_revoker>(this, IsMutedChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsMutedChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->remove_IsMutedChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsDocumentPlayingAudioChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->add_IsDocumentPlayingAudioChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsDocumentPlayingAudioChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, IsDocumentPlayingAudioChanged_revoker>(this, IsDocumentPlayingAudioChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>::IsDocumentPlayingAudioChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8)->remove_IsDocumentPlayingAudioChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::IsDefaultDownloadDialogOpen() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->get_IsDefaultDownloadDialogOpen(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::DefaultDownloadDialogCornerAlignment() const
    {
        winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->get_DefaultDownloadDialogCornerAlignment(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::DefaultDownloadDialogCornerAlignment(winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->put_DefaultDownloadDialogCornerAlignment(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::DefaultDownloadDialogMargin() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->get_DefaultDownloadDialogMargin(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->put_DefaultDownloadDialogMargin(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::IsDefaultDownloadDialogOpenChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->add_IsDefaultDownloadDialogOpenChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::IsDefaultDownloadDialogOpenChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, IsDefaultDownloadDialogOpenChanged_revoker>(this, IsDefaultDownloadDialogOpenChanged(handler));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::IsDefaultDownloadDialogOpenChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->remove_IsDefaultDownloadDialogOpenChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::OpenDefaultDownloadDialog() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->OpenDefaultDownloadDialog());
    }
    template <typename D> auto consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>::CloseDefaultDownloadDialog() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9)->CloseDefaultDownloadDialog());
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual>
    {
        int32_t __stdcall ToCertificate(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Security::Cryptography::Certificates::Certificate>(this->shim().ToCertificate());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual>
    {
        int32_t __stdcall ToCertificate(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Security::Cryptography::Certificates::Certificate>(this->shim().ToCertificate());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual>
    {
        int32_t __stdcall ClearBrowsingDataAsync(uint32_t dataKinds, int64_t startTime, int64_t endTime, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ClearBrowsingDataAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const*>(&dataKinds), *reinterpret_cast<winrt::Windows::Foundation::DateTime const*>(&startTime), *reinterpret_cast<winrt::Windows::Foundation::DateTime const*>(&endTime)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ClearBrowsingDataAsync2(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ClearBrowsingDataAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>
    {
        int32_t __stdcall get_Settings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings>(this->shim().Settings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BrowserProcessId(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().BrowserProcessId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CanGoBack(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().CanGoBack());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CanGoForward(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().CanGoForward());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DocumentTitle(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DocumentTitle());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ContainsFullScreenElement(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ContainsFullScreenElement());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_NavigationStarting(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NavigationStarting(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NavigationStarting(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigationStarting(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_ContentLoading(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ContentLoading(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ContentLoading(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ContentLoading(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_SourceChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().SourceChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_SourceChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SourceChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_HistoryChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().HistoryChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_HistoryChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HistoryChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_NavigationCompleted(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NavigationCompleted(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NavigationCompleted(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigationCompleted(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_FrameNavigationStarting(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().FrameNavigationStarting(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_FrameNavigationStarting(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FrameNavigationStarting(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_FrameNavigationCompleted(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().FrameNavigationCompleted(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_FrameNavigationCompleted(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FrameNavigationCompleted(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_ScriptDialogOpening(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ScriptDialogOpening(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ScriptDialogOpening(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ScriptDialogOpening(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_PermissionRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().PermissionRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_PermissionRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PermissionRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_ProcessFailed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ProcessFailed(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ProcessFailed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ProcessFailed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_WebMessageReceived(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().WebMessageReceived(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_WebMessageReceived(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WebMessageReceived(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_NewWindowRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NewWindowRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NewWindowRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NewWindowRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_DocumentTitleChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().DocumentTitleChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_DocumentTitleChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DocumentTitleChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_ContainsFullScreenElementChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ContainsFullScreenElementChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ContainsFullScreenElementChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ContainsFullScreenElementChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_WebResourceRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().WebResourceRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_WebResourceRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WebResourceRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_WindowCloseRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().WindowCloseRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_WindowCloseRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WindowCloseRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall Navigate(void* uri) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Navigate(*reinterpret_cast<hstring const*>(&uri));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall NavigateToString(void* htmlContent) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigateToString(*reinterpret_cast<hstring const*>(&htmlContent));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddScriptToExecuteOnDocumentCreatedAsync(void* javaScript, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<hstring>>(this->shim().AddScriptToExecuteOnDocumentCreatedAsync(*reinterpret_cast<hstring const*>(&javaScript)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveScriptToExecuteOnDocumentCreated(void* id) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveScriptToExecuteOnDocumentCreated(*reinterpret_cast<hstring const*>(&id));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ExecuteScriptAsync(void* javaScript, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<hstring>>(this->shim().ExecuteScriptAsync(*reinterpret_cast<hstring const*>(&javaScript)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CapturePreviewAsync(int32_t imageFormat, void* imageStream, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().CapturePreviewAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CapturePreviewImageFormat const*>(&imageFormat), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&imageStream)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Reload() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Reload();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PostWebMessageAsJson(void* webMessageAsJson) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PostWebMessageAsJson(*reinterpret_cast<hstring const*>(&webMessageAsJson));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PostWebMessageAsString(void* webMessageAsString) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PostWebMessageAsString(*reinterpret_cast<hstring const*>(&webMessageAsString));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CallDevToolsProtocolMethodAsync(void* methodName, void* parametersAsJson, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<hstring>>(this->shim().CallDevToolsProtocolMethodAsync(*reinterpret_cast<hstring const*>(&methodName), *reinterpret_cast<hstring const*>(&parametersAsJson)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GoBack() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GoBack();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GoForward() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GoForward();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDevToolsProtocolEventReceiver(void* eventName, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver>(this->shim().GetDevToolsProtocolEventReceiver(*reinterpret_cast<hstring const*>(&eventName)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Stop() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Stop();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddHostObjectToScript(void* name, void* rawObject) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddHostObjectToScript(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&rawObject));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveHostObjectFromScript(void* name) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveHostObjectFromScript(*reinterpret_cast<hstring const*>(&name));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall OpenDevToolsWindow() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OpenDevToolsWindow();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddWebResourceRequestedFilter(void* uri, int32_t ResourceContext) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddWebResourceRequestedFilter(*reinterpret_cast<hstring const*>(&uri), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const*>(&ResourceContext));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveWebResourceRequestedFilter(void* uri, int32_t ResourceContext) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveWebResourceRequestedFilter(*reinterpret_cast<hstring const*>(&uri), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const*>(&ResourceContext));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs>
    {
        int32_t __stdcall get_KeyEventKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2KeyEventKind>(this->shim().KeyEventKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VirtualKey(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().VirtualKey());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_KeyEventLParam(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().KeyEventLParam());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PhysicalKeyStatus(struct struct_Microsoft_Web_WebView2_Core_CoreWebView2PhysicalKeyStatus* value) noexcept final try
        {
            zero_abi<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus>(this->shim().PhysicalKeyStatus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Challenge(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Challenge());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Response(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse>(this->shim().Response());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Cancel(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Cancel(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse>
    {
        int32_t __stdcall get_UserName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().UserName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UserName(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UserName(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Password(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Password());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Password(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Password(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs>
    {
        int32_t __stdcall get_BrowserProcessExitKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitKind>(this->shim().BrowserProcessExitKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BrowserProcessId(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().BrowserProcessId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate>
    {
        int32_t __stdcall get_Subject(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Subject());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Issuer(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Issuer());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ValidFrom(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ValidFrom());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ValidTo(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ValidTo());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DerEncodedSerialNumber(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DerEncodedSerialNumber());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DisplayName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DisplayName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PemEncodedIssuerCertificateChain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<hstring>>(this->shim().PemEncodedIssuerCertificateChain());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ToPemEncoding(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().ToPemEncoding());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate>
    {
        int32_t __stdcall get_Subject(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Subject());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Issuer(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Issuer());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ValidFrom(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ValidFrom());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ValidTo(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ValidTo());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DerEncodedSerialNumber(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DerEncodedSerialNumber());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DisplayName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DisplayName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PemEncodedIssuerCertificateChain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<hstring>>(this->shim().PemEncodedIssuerCertificateChain());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ToPemEncoding(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().ToPemEncoding());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs>
    {
        int32_t __stdcall get_Host(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Host());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Port(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().Port());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsProxy(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsProxy());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AllowedCertificateAuthorities(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<hstring>>(this->shim().AllowedCertificateAuthorities());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MutuallyTrustedCertificates(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate>>(this->shim().MutuallyTrustedCertificates());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SelectedCertificate(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate>(this->shim().SelectedCertificate());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SelectedCertificate(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SelectedCertificate(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Cancel(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Cancel(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>
    {
        int32_t __stdcall get_RootVisualTarget(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().RootVisualTarget());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RootVisualTarget(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RootVisualTarget(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_CursorChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().CursorChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_CursorChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CursorChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall SendMouseInput(int32_t eventKind, uint32_t virtualKeys, uint32_t mouseData, winrt::Windows::Foundation::Point point) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SendMouseInput(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventKind const*>(&eventKind), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventVirtualKeys const*>(&virtualKeys), mouseData, *reinterpret_cast<winrt::Windows::Foundation::Point const*>(&point));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SendPointerInput(int32_t eventKind, void* pointerInfo) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SendPointerInput(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerEventKind const*>(&eventKind), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo const*>(&pointerInfo));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Cursor(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Core::CoreCursor>(this->shim().Cursor());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs>
    {
        int32_t __stdcall get_IsErrorPage(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsErrorPage());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationId(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().NavigationId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Label(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Label());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CommandId(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().CommandId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShortcutKeyDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ShortcutKeyDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Icon(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Storage::Streams::IRandomAccessStream>(this->shim().Icon());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsChecked(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsChecked());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsChecked(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsChecked(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Children(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>>(this->shim().Children());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_CustomItemSelected(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().CustomItemSelected(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_CustomItemSelected(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CustomItemSelected(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs>
    {
        int32_t __stdcall get_MenuItems(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>>(this->shim().MenuItems());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ContextMenuTarget(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget>(this->shim().ContextMenuTarget());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Location(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().Location());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SelectedCommandId(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().SelectedCommandId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SelectedCommandId(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SelectedCommandId(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget>
    {
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTargetKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsEditable(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsEditable());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsRequestedForMainFrame(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsRequestedForMainFrame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PageUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().PageUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FrameUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FrameUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HasLinkUri(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasLinkUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_LinkUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().LinkUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HasLinkText(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasLinkText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_LinkText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().LinkText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HasSourceUri(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasSourceUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SourceUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HasSelection(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasSelection());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SelectionText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SelectionText());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>
    {
        int32_t __stdcall get_IsVisible(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsVisible());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsVisible(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsVisible(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Bounds(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().Bounds());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Bounds(winrt::Windows::Foundation::Rect value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Bounds(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ZoomFactor(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ZoomFactor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ZoomFactor(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ZoomFactor(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ParentWindow(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference>(this->shim().ParentWindow());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ParentWindow(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ParentWindow(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CoreWebView2(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2>(this->shim().CoreWebView2());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_ZoomFactorChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ZoomFactorChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ZoomFactorChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ZoomFactorChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_MoveFocusRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().MoveFocusRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_MoveFocusRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MoveFocusRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_GotFocus(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().GotFocus(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_GotFocus(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GotFocus(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_LostFocus(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().LostFocus(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_LostFocus(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().LostFocus(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_AcceleratorKeyPressed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().AcceleratorKeyPressed(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_AcceleratorKeyPressed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AcceleratorKeyPressed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall SetBoundsAndZoomFactor(winrt::Windows::Foundation::Rect Bounds, double ZoomFactor) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetBoundsAndZoomFactor(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&Bounds), ZoomFactor);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall MoveFocus(int32_t reason) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MoveFocus(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason const*>(&reason));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall NotifyParentWindowPositionChanged() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NotifyParentWindowPositionChanged();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Close() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Close();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2>
    {
        int32_t __stdcall get_DefaultBackgroundColor(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().DefaultBackgroundColor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DefaultBackgroundColor(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DefaultBackgroundColor(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>
    {
        int32_t __stdcall get_RasterizationScale(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().RasterizationScale());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RasterizationScale(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RasterizationScale(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldDetectMonitorScaleChanges(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldDetectMonitorScaleChanges());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ShouldDetectMonitorScaleChanges(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ShouldDetectMonitorScaleChanges(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BoundsMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode>(this->shim().BoundsMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BoundsMode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BoundsMode(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_RasterizationScaleChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().RasterizationScaleChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_RasterizationScaleChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RasterizationScaleChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4>
    {
        int32_t __stdcall get_AllowExternalDrop(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AllowExternalDrop());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AllowExternalDrop(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllowExternalDrop(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions>
    {
        int32_t __stdcall get_ProfileName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ProfileName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ProfileName(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ProfileName(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsInPrivateModeEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsInPrivateModeEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsInPrivateModeEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsInPrivateModeEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference>
    {
        int32_t __stdcall get_WindowHandle(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().WindowHandle());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CoreWindow(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Core::CoreWindow>(this->shim().CoreWindow());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics>
    {
        int32_t __stdcall CreateFromWindowHandle(uint64_t windowHandle, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference>(this->shim().CreateFromWindowHandle(windowHandle));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateFromCoreWindow(void* coreWindow, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference>(this->shim().CreateFromCoreWindow(*reinterpret_cast<winrt::Windows::UI::Core::CoreWindow const*>(&coreWindow)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Value(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Value());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Value(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Value(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Domain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Domain());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Path(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Path());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Expires(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().Expires());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Expires(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Expires(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsHttpOnly(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsHttpOnly());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsHttpOnly(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsHttpOnly(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SameSite(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind>(this->shim().SameSite());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SameSite(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SameSite(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsSecure(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSecure());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsSecure(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsSecure(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsSession(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSession());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager>
    {
        int32_t __stdcall CreateCookie(void* name, void* value, void* Domain, void* Path, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>(this->shim().CreateCookie(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<hstring const*>(&value), *reinterpret_cast<hstring const*>(&Domain), *reinterpret_cast<hstring const*>(&Path)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CopyCookie(void* cookieParam, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>(this->shim().CopyCookie(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const*>(&cookieParam)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddOrUpdateCookie(void* cookie) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddOrUpdateCookie(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const*>(&cookie));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DeleteCookie(void* cookie) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeleteCookie(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const*>(&cookie));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DeleteCookies(void* name, void* uri) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeleteCookies(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<hstring const*>(&uri));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DeleteCookiesWithDomainAndPath(void* name, void* Domain, void* Path) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeleteCookiesWithDomainAndPath(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<hstring const*>(&Domain), *reinterpret_cast<hstring const*>(&Path));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DeleteAllCookies() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeleteAllCookies();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual>
    {
        int32_t __stdcall GetCookiesAsync(void* uri, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>>>(this->shim().GetCookiesAsync(*reinterpret_cast<hstring const*>(&uri)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs>
    {
        int32_t __stdcall get_NavigationId(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().NavigationId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs>
    {
        int32_t __stdcall get_ParameterObjectAsJson(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ParameterObjectAsJson());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>
    {
        int32_t __stdcall get_SessionId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SessionId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>
    {
        int32_t __stdcall add_DevToolsProtocolEventReceived(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().DevToolsProtocolEventReceived(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_DevToolsProtocolEventReceived(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DevToolsProtocolEventReceived(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>
    {
        int32_t __stdcall WrapNamedObject(void* name, void* adapter, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().WrapNamedObject(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const*>(&adapter)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WrapObject(void* unwrapped, void* adapter, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().WrapObject(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&unwrapped), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const*>(&adapter)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UnwrapObject(void* wrapped, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().UnwrapObject(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&wrapped)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Clean() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Clean();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ContentDisposition(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ContentDisposition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MimeType(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().MimeType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TotalBytesToReceive(int64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int64_t>(this->shim().TotalBytesToReceive());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BytesReceived(int64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int64_t>(this->shim().BytesReceived());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EstimatedEndTime(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().EstimatedEndTime());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ResultFilePath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ResultFilePath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_State(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadState>(this->shim().State());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InterruptReason(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadInterruptReason>(this->shim().InterruptReason());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CanResume(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().CanResume());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_BytesReceivedChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().BytesReceivedChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_BytesReceivedChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BytesReceivedChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_EstimatedEndTimeChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().EstimatedEndTimeChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_EstimatedEndTimeChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EstimatedEndTimeChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_StateChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().StateChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_StateChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().StateChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall Cancel() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Pause() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Pause();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Resume() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Resume();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs>
    {
        int32_t __stdcall get_DownloadOperation(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation>(this->shim().DownloadOperation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Cancel(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Cancel(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ResultFilePath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ResultFilePath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ResultFilePath(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ResultFilePath(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>
    {
        int32_t __stdcall get_BrowserVersionString(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BrowserVersionString());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_NewBrowserVersionAvailable(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NewBrowserVersionAvailable(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NewBrowserVersionAvailable(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NewBrowserVersionAvailable(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall CreateCoreWebView2ControllerAsync(void* ParentWindow, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>>(this->shim().CreateCoreWebView2ControllerAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const*>(&ParentWindow)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateWebResourceResponse(void* Content, int32_t StatusCode, void* ReasonPhrase, void* Headers, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse>(this->shim().CreateWebResourceResponse(*reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&Content), StatusCode, *reinterpret_cast<hstring const*>(&ReasonPhrase), *reinterpret_cast<hstring const*>(&Headers)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10>
    {
        int32_t __stdcall CreateCoreWebView2ControllerOptions(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions>(this->shim().CreateCoreWebView2ControllerOptions());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2>
    {
        int32_t __stdcall CreateWebResourceRequest(void* uri, void* Method, void* postData, void* Headers, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest>(this->shim().CreateWebResourceRequest(*reinterpret_cast<hstring const*>(&uri), *reinterpret_cast<hstring const*>(&Method), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&postData), *reinterpret_cast<hstring const*>(&Headers)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3>
    {
        int32_t __stdcall CreateCoreWebView2CompositionControllerAsync(void* ParentWindow, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>>(this->shim().CreateCoreWebView2CompositionControllerAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const*>(&ParentWindow)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateCoreWebView2PointerInfo(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo>(this->shim().CreateCoreWebView2PointerInfo());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>
    {
        int32_t __stdcall add_BrowserProcessExited(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().BrowserProcessExited(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_BrowserProcessExited(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BrowserProcessExited(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6>
    {
        int32_t __stdcall CreatePrintSettings(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings>(this->shim().CreatePrintSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7>
    {
        int32_t __stdcall get_UserDataFolder(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().UserDataFolder());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>
    {
        int32_t __stdcall add_ProcessInfosChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ProcessInfosChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ProcessInfosChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ProcessInfosChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall GetProcessInfos(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo>>(this->shim().GetProcessInfos());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9>
    {
        int32_t __stdcall CreateContextMenuItem(void* Label, void* iconStream, int32_t Kind, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>(this->shim().CreateContextMenuItem(*reinterpret_cast<hstring const*>(&Label), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&iconStream), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind const*>(&Kind)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions>
    {
        int32_t __stdcall get_AdditionalBrowserArguments(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().AdditionalBrowserArguments());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AdditionalBrowserArguments(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AdditionalBrowserArguments(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Language(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Language());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Language(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Language(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TargetCompatibleBrowserVersion(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().TargetCompatibleBrowserVersion());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TargetCompatibleBrowserVersion(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TargetCompatibleBrowserVersion(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AllowSingleSignOnUsingOSPrimaryAccount(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AllowSingleSignOnUsingOSPrimaryAccount());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AllowSingleSignOnUsingOSPrimaryAccount(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllowSingleSignOnUsingOSPrimaryAccount(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2>
    {
        int32_t __stdcall get_ExclusiveUserDataFolderAccess(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ExclusiveUserDataFolderAccess());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ExclusiveUserDataFolderAccess(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ExclusiveUserDataFolderAccess(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics>
    {
        int32_t __stdcall CreateAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>>(this->shim().CreateAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateWithOptionsAsync(void* browserExecutableFolder, void* userDataFolder, void* options, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>>(this->shim().CreateWithOptionsAsync(*reinterpret_cast<hstring const*>(&browserExecutableFolder), *reinterpret_cast<hstring const*>(&userDataFolder), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions const*>(&options)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAvailableBrowserVersionString(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetAvailableBrowserVersionString());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAvailableBrowserVersionString2(void* browserExecutableFolder, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetAvailableBrowserVersionString(*reinterpret_cast<hstring const*>(&browserExecutableFolder)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CompareBrowserVersionString(void* browserVersionString1, void* browserVersionString2, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<int32_t>(this->shim().CompareBrowserVersionString(*reinterpret_cast<hstring const*>(&browserVersionString1), *reinterpret_cast<hstring const*>(&browserVersionString2)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual>
    {
        int32_t __stdcall CreateCoreWebView2ControllerAsync(void* ParentWindow, void* options, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>>(this->shim().CreateCoreWebView2ControllerAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const*>(&ParentWindow), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const*>(&options)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateCoreWebView2CompositionControllerAsync(void* ParentWindow, void* options, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>>(this->shim().CreateCoreWebView2CompositionControllerAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const*>(&ParentWindow), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const*>(&options)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_NameChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NameChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NameChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NameChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_Destroyed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().Destroyed(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_Destroyed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Destroyed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall RemoveHostObjectFromScript(void* name) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveHostObjectFromScript(*reinterpret_cast<hstring const*>(&name));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsDestroyed(int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<int32_t>(this->shim().IsDestroyed());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>
    {
        int32_t __stdcall add_NavigationStarting(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NavigationStarting(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NavigationStarting(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigationStarting(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_ContentLoading(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ContentLoading(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ContentLoading(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ContentLoading(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_NavigationCompleted(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().NavigationCompleted(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_NavigationCompleted(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigationCompleted(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_DOMContentLoaded(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().DOMContentLoaded(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_DOMContentLoaded(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DOMContentLoaded(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_WebMessageReceived(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().WebMessageReceived(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_WebMessageReceived(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WebMessageReceived(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall ExecuteScriptAsync(void* javaScript, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<hstring>>(this->shim().ExecuteScriptAsync(*reinterpret_cast<hstring const*>(&javaScript)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PostWebMessageAsJson(void* webMessageAsJson) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PostWebMessageAsJson(*reinterpret_cast<hstring const*>(&webMessageAsJson));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PostWebMessageAsString(void* webMessageAsString) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PostWebMessageAsString(*reinterpret_cast<hstring const*>(&webMessageAsString));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>
    {
        int32_t __stdcall add_PermissionRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().PermissionRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_PermissionRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PermissionRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders>
    {
        int32_t __stdcall GetHeader(void* name, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetHeader(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHeaders(void* name, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator>(this->shim().GetHeaders(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Contains(void* name, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().Contains(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetHeader(void* name, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetHeader(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveHeader(void* name) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveHeader(*reinterpret_cast<hstring const*>(&name));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders>
    {
        int32_t __stdcall AppendHeader(void* name, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AppendHeader(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Contains(void* name, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().Contains(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHeader(void* name, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetHeader(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHeaders(void* name, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator>(this->shim().GetHeaders(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs>
    {
        int32_t __stdcall get_Reason(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason>(this->shim().Reason());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs>
    {
        int32_t __stdcall get_IsSuccess(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSuccess());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WebErrorStatus(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus>(this->shim().WebErrorStatus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationId(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().NavigationId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2>
    {
        int32_t __stdcall get_HttpStatusCode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().HttpStatusCode());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsUserInitiated(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsUserInitiated());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsRedirected(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsRedirected());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestHeaders(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders>(this->shim().RequestHeaders());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Cancel(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Cancel(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Cancel(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NavigationId(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().NavigationId());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2>
    {
        int32_t __stdcall get_AdditionalAllowedFrameAncestors(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().AdditionalAllowedFrameAncestors());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AdditionalAllowedFrameAncestors(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AdditionalAllowedFrameAncestors(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NewWindow(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2>(this->shim().NewWindow());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_NewWindow(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NewWindow(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2 const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsUserInitiated(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsUserInitiated());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WindowFeatures(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures>(this->shim().WindowFeatures());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PermissionKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionKind>(this->shim().PermissionKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsUserInitiated(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsUserInitiated());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_State(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState>(this->shim().State());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_State(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().State(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2>
    {
        int32_t __stdcall get_Handled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Handled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Handled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Handled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo>
    {
        int32_t __stdcall get_PointerKind(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PointerKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PointerKind(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PointerKind(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PointerId(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PointerId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PointerId(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PointerId(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FrameId(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().FrameId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_FrameId(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FrameId(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PointerFlags(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PointerFlags());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PointerFlags(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PointerFlags(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PointerDeviceRect(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().PointerDeviceRect());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PointerDeviceRect(winrt::Windows::Foundation::Rect value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PointerDeviceRect(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DisplayRect(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().DisplayRect());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DisplayRect(winrt::Windows::Foundation::Rect value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DisplayRect(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PixelLocation(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().PixelLocation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PixelLocation(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PixelLocation(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HimetricLocation(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().HimetricLocation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HimetricLocation(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HimetricLocation(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PixelLocationRaw(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().PixelLocationRaw());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PixelLocationRaw(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PixelLocationRaw(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HimetricLocationRaw(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().HimetricLocationRaw());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HimetricLocationRaw(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HimetricLocationRaw(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Time(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Time());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Time(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Time(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HistoryCount(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().HistoryCount());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HistoryCount(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HistoryCount(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InputData(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().InputData());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InputData(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InputData(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_KeyStates(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().KeyStates());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_KeyStates(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().KeyStates(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PerformanceCount(uint64_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint64_t>(this->shim().PerformanceCount());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PerformanceCount(uint64_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PerformanceCount(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ButtonChangeKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().ButtonChangeKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ButtonChangeKind(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ButtonChangeKind(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenFlags(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PenFlags());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenFlags(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenFlags(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenMask(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PenMask());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenMask(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenMask(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenPressure(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PenPressure());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenPressure(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenPressure(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenRotation(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().PenRotation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenRotation(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenRotation(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenTiltX(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().PenTiltX());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenTiltX(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenTiltX(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PenTiltY(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().PenTiltY());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PenTiltY(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PenTiltY(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchFlags(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().TouchFlags());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchFlags(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchFlags(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchMask(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().TouchMask());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchMask(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchMask(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchContact(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().TouchContact());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchContact(winrt::Windows::Foundation::Rect value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchContact(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchContactRaw(winrt::Windows::Foundation::Rect* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Rect>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Rect>(this->shim().TouchContactRaw());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchContactRaw(winrt::Windows::Foundation::Rect value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchContactRaw(*reinterpret_cast<winrt::Windows::Foundation::Rect const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchOrientation(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().TouchOrientation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchOrientation(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchOrientation(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TouchPressure(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().TouchPressure());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TouchPressure(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TouchPressure(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings>
    {
        int32_t __stdcall get_Orientation(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation>(this->shim().Orientation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Orientation(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Orientation(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ScaleFactor(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().ScaleFactor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ScaleFactor(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ScaleFactor(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PageWidth(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().PageWidth());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PageWidth(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PageWidth(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PageHeight(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().PageHeight());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PageHeight(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PageHeight(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MarginTop(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().MarginTop());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MarginTop(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MarginTop(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MarginBottom(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().MarginBottom());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MarginBottom(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MarginBottom(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MarginLeft(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().MarginLeft());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MarginLeft(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MarginLeft(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MarginRight(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().MarginRight());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MarginRight(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MarginRight(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldPrintBackgrounds(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldPrintBackgrounds());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ShouldPrintBackgrounds(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ShouldPrintBackgrounds(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldPrintSelectionOnly(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldPrintSelectionOnly());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ShouldPrintSelectionOnly(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ShouldPrintSelectionOnly(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldPrintHeaderAndFooter(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldPrintHeaderAndFooter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ShouldPrintHeaderAndFooter(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ShouldPrintHeaderAndFooter(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HeaderTitle(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().HeaderTitle());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HeaderTitle(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HeaderTitle(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FooterUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().FooterUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_FooterUri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FooterUri(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs>
    {
        int32_t __stdcall get_ProcessFailedKind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedKind>(this->shim().ProcessFailedKind());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2>
    {
        int32_t __stdcall get_Reason(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedReason>(this->shim().Reason());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExitCode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().ExitCode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ProcessDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ProcessDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FrameInfosForFailedProcess(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo>>(this->shim().FrameInfosForFailedProcess());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo>
    {
        int32_t __stdcall get_ProcessId(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().ProcessId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile>
    {
        int32_t __stdcall get_ProfileName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ProfileName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsInPrivateModeEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsInPrivateModeEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ProfilePath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ProfilePath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DefaultDownloadFolderPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DefaultDownloadFolderPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DefaultDownloadFolderPath(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DefaultDownloadFolderPath(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PreferredColorScheme(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme>(this->shim().PreferredColorScheme());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PreferredColorScheme(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PreferredColorScheme(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2>
    {
        int32_t __stdcall ClearBrowsingDataAsync(uint32_t dataKinds, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ClearBrowsingDataAsync(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const*>(&dataKinds)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Message(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Message());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DefaultText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DefaultText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ResultText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ResultText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ResultText(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ResultText(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Accept() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Accept();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs>
    {
        int32_t __stdcall get_ErrorStatus(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus>(this->shim().ErrorStatus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().RequestUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ServerCertificate(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate>(this->shim().ServerCertificate());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Action(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction>(this->shim().Action());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Action(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Action(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings>
    {
        int32_t __stdcall get_IsScriptEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsScriptEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsScriptEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsScriptEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsWebMessageEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsWebMessageEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsWebMessageEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsWebMessageEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AreDefaultScriptDialogsEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AreDefaultScriptDialogsEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AreDefaultScriptDialogsEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AreDefaultScriptDialogsEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsStatusBarEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsStatusBarEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsStatusBarEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsStatusBarEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AreDevToolsEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AreDevToolsEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AreDevToolsEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AreDevToolsEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AreDefaultContextMenusEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AreDefaultContextMenusEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AreDefaultContextMenusEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AreDefaultContextMenusEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AreHostObjectsAllowed(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AreHostObjectsAllowed());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AreHostObjectsAllowed(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AreHostObjectsAllowed(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsZoomControlEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsZoomControlEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsZoomControlEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsZoomControlEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsBuiltInErrorPageEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsBuiltInErrorPageEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsBuiltInErrorPageEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsBuiltInErrorPageEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2>
    {
        int32_t __stdcall get_UserAgent(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().UserAgent());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UserAgent(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UserAgent(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3>
    {
        int32_t __stdcall get_AreBrowserAcceleratorKeysEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AreBrowserAcceleratorKeysEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AreBrowserAcceleratorKeysEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AreBrowserAcceleratorKeysEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4>
    {
        int32_t __stdcall get_IsPasswordAutosaveEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsPasswordAutosaveEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsPasswordAutosaveEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsPasswordAutosaveEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsGeneralAutofillEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsGeneralAutofillEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsGeneralAutofillEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsGeneralAutofillEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5>
    {
        int32_t __stdcall get_IsPinchZoomEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsPinchZoomEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsPinchZoomEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsPinchZoomEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6>
    {
        int32_t __stdcall get_IsSwipeNavigationEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSwipeNavigationEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsSwipeNavigationEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsSwipeNavigationEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7>
    {
        int32_t __stdcall get_HiddenPdfToolbarItems(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems>(this->shim().HiddenPdfToolbarItems());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HiddenPdfToolbarItems(uint32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HiddenPdfToolbarItems(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual>
    {
        int32_t __stdcall get_HostObjectDispatchAdapter(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>(this->shim().HostObjectDispatchAdapter());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_HostObjectDispatchAdapter(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().HostObjectDispatchAdapter(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs>
    {
        int32_t __stdcall get_IsNewDocument(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsNewDocument());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs>
    {
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WebMessageAsJson(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().WebMessageAsJson());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall TryGetWebMessageAsString(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().TryGetWebMessageAsString());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest>
    {
        int32_t __stdcall get_Uri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Uri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Uri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Uri(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Method(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Method());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Method(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Method(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Content(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Storage::Streams::IRandomAccessStream>(this->shim().Content());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Content(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Content(*reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Headers(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders>(this->shim().Headers());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs>
    {
        int32_t __stdcall get_Request(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest>(this->shim().Request());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Response(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse>(this->shim().Response());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Response(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Response(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ResourceContext(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext>(this->shim().ResourceContext());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDeferral(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Deferral>(this->shim().GetDeferral());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse>
    {
        int32_t __stdcall get_Content(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Storage::Streams::IRandomAccessStream>(this->shim().Content());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Content(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Content(*reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Headers(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders>(this->shim().Headers());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_StatusCode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().StatusCode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_StatusCode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().StatusCode(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReasonPhrase(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ReasonPhrase());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ReasonPhrase(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReasonPhrase(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs>
    {
        int32_t __stdcall get_Request(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest>(this->shim().Request());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Response(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView>(this->shim().Response());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView>
    {
        int32_t __stdcall get_Headers(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders>(this->shim().Headers());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_StatusCode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<int32_t>(this->shim().StatusCode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReasonPhrase(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ReasonPhrase());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetContentAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::Streams::IRandomAccessStream>>(this->shim().GetContentAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures>
    {
        int32_t __stdcall get_HasPosition(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasPosition());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_HasSize(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasSize());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Left(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Left());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Top(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Top());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Height(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Height());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Width(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Width());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldDisplayMenuBar(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldDisplayMenuBar());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldDisplayStatus(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldDisplayStatus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldDisplayToolbar(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldDisplayToolbar());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ShouldDisplayScrollBars(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ShouldDisplayScrollBars());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>
    {
        int32_t __stdcall add_BasicAuthenticationRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().BasicAuthenticationRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_BasicAuthenticationRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BasicAuthenticationRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>
    {
        int32_t __stdcall add_ContextMenuRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ContextMenuRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ContextMenuRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ContextMenuRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall CallDevToolsProtocolMethodForSessionAsync(void* sessionId, void* methodName, void* parametersAsJson, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<hstring>>(this->shim().CallDevToolsProtocolMethodForSessionAsync(*reinterpret_cast<hstring const*>(&sessionId), *reinterpret_cast<hstring const*>(&methodName), *reinterpret_cast<hstring const*>(&parametersAsJson)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>
    {
        int32_t __stdcall get_StatusBarText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().StatusBarText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_StatusBarTextChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().StatusBarTextChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_StatusBarTextChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().StatusBarTextChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13>
    {
        int32_t __stdcall get_Profile(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile>(this->shim().Profile());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>
    {
        int32_t __stdcall add_ServerCertificateErrorDetected(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ServerCertificateErrorDetected(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ServerCertificateErrorDetected(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ServerCertificateErrorDetected(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall ClearServerCertificateErrorActionsAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ClearServerCertificateErrorActionsAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>
    {
        int32_t __stdcall get_CookieManager(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager>(this->shim().CookieManager());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Environment(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>(this->shim().Environment());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_WebResourceResponseReceived(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().WebResourceResponseReceived(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_WebResourceResponseReceived(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WebResourceResponseReceived(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_DOMContentLoaded(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().DOMContentLoaded(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_DOMContentLoaded(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DOMContentLoaded(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall NavigateWithWebResourceRequest(void* Request) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NavigateWithWebResourceRequest(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest const*>(&Request));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3>
    {
        int32_t __stdcall get_IsSuspended(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSuspended());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall TrySuspendAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<bool>>(this->shim().TrySuspendAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Resume() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Resume();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetVirtualHostNameToFolderMapping(void* hostName, void* folderPath, int32_t accessKind) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetVirtualHostNameToFolderMapping(*reinterpret_cast<hstring const*>(&hostName), *reinterpret_cast<hstring const*>(&folderPath), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HostResourceAccessKind const*>(&accessKind));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ClearVirtualHostNameToFolderMapping(void* hostName) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ClearVirtualHostNameToFolderMapping(*reinterpret_cast<hstring const*>(&hostName));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>
    {
        int32_t __stdcall add_FrameCreated(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().FrameCreated(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_FrameCreated(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FrameCreated(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_DownloadStarting(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().DownloadStarting(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_DownloadStarting(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DownloadStarting(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>
    {
        int32_t __stdcall add_ClientCertificateRequested(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ClientCertificateRequested(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ClientCertificateRequested(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ClientCertificateRequested(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6>
    {
        int32_t __stdcall OpenTaskManagerWindow() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OpenTaskManagerWindow();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7>
    {
        int32_t __stdcall PrintToPdfAsync(void* ResultFilePath, void* printSettings, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<bool>>(this->shim().PrintToPdfAsync(*reinterpret_cast<hstring const*>(&ResultFilePath), *reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings const*>(&printSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>
    {
        int32_t __stdcall get_IsMuted(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsMuted());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsMuted(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsMuted(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsDocumentPlayingAudio(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsDocumentPlayingAudio());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_IsMutedChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().IsMutedChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_IsMutedChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsMutedChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_IsDocumentPlayingAudioChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().IsDocumentPlayingAudioChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_IsDocumentPlayingAudioChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsDocumentPlayingAudioChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9> : produce_base<D, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>
    {
        int32_t __stdcall get_IsDefaultDownloadDialogOpen(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsDefaultDownloadDialogOpen());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DefaultDownloadDialogCornerAlignment(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment>(this->shim().DefaultDownloadDialogCornerAlignment());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DefaultDownloadDialogCornerAlignment(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DefaultDownloadDialogCornerAlignment(*reinterpret_cast<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().DefaultDownloadDialogMargin());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DefaultDownloadDialogMargin(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_IsDefaultDownloadDialogOpenChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().IsDefaultDownloadDialogOpenChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_IsDefaultDownloadDialogOpenChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsDefaultDownloadDialogOpenChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall OpenDefaultDownloadDialog() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OpenDefaultDownloadDialog();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CloseDefaultDownloadDialog() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CloseDefaultDownloadDialog();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Microsoft::Web::WebView2::Core
{
    constexpr auto operator|(CoreWebView2BrowsingDataKinds const left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        return static_cast<CoreWebView2BrowsingDataKinds>(impl::to_underlying_type(left) | impl::to_underlying_type(right));
    }
    constexpr auto operator|=(CoreWebView2BrowsingDataKinds& left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        left = left | right;
        return left;
    }
    constexpr auto operator&(CoreWebView2BrowsingDataKinds const left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        return static_cast<CoreWebView2BrowsingDataKinds>(impl::to_underlying_type(left) & impl::to_underlying_type(right));
    }
    constexpr auto operator&=(CoreWebView2BrowsingDataKinds& left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        left = left & right;
        return left;
    }
    constexpr auto operator~(CoreWebView2BrowsingDataKinds const value) noexcept
    {
        return static_cast<CoreWebView2BrowsingDataKinds>(~impl::to_underlying_type(value));
    }
    constexpr auto operator^(CoreWebView2BrowsingDataKinds const left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        return static_cast<CoreWebView2BrowsingDataKinds>(impl::to_underlying_type(left) ^ impl::to_underlying_type(right));
    }
    constexpr auto operator^=(CoreWebView2BrowsingDataKinds& left, CoreWebView2BrowsingDataKinds const right) noexcept
    {
        left = left ^ right;
        return left;
    }
    constexpr auto operator|(CoreWebView2MouseEventVirtualKeys const left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        return static_cast<CoreWebView2MouseEventVirtualKeys>(impl::to_underlying_type(left) | impl::to_underlying_type(right));
    }
    constexpr auto operator|=(CoreWebView2MouseEventVirtualKeys& left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        left = left | right;
        return left;
    }
    constexpr auto operator&(CoreWebView2MouseEventVirtualKeys const left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        return static_cast<CoreWebView2MouseEventVirtualKeys>(impl::to_underlying_type(left) & impl::to_underlying_type(right));
    }
    constexpr auto operator&=(CoreWebView2MouseEventVirtualKeys& left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        left = left & right;
        return left;
    }
    constexpr auto operator~(CoreWebView2MouseEventVirtualKeys const value) noexcept
    {
        return static_cast<CoreWebView2MouseEventVirtualKeys>(~impl::to_underlying_type(value));
    }
    constexpr auto operator^(CoreWebView2MouseEventVirtualKeys const left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        return static_cast<CoreWebView2MouseEventVirtualKeys>(impl::to_underlying_type(left) ^ impl::to_underlying_type(right));
    }
    constexpr auto operator^=(CoreWebView2MouseEventVirtualKeys& left, CoreWebView2MouseEventVirtualKeys const right) noexcept
    {
        left = left ^ right;
        return left;
    }
    constexpr auto operator|(CoreWebView2PdfToolbarItems const left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        return static_cast<CoreWebView2PdfToolbarItems>(impl::to_underlying_type(left) | impl::to_underlying_type(right));
    }
    constexpr auto operator|=(CoreWebView2PdfToolbarItems& left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        left = left | right;
        return left;
    }
    constexpr auto operator&(CoreWebView2PdfToolbarItems const left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        return static_cast<CoreWebView2PdfToolbarItems>(impl::to_underlying_type(left) & impl::to_underlying_type(right));
    }
    constexpr auto operator&=(CoreWebView2PdfToolbarItems& left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        left = left & right;
        return left;
    }
    constexpr auto operator~(CoreWebView2PdfToolbarItems const value) noexcept
    {
        return static_cast<CoreWebView2PdfToolbarItems>(~impl::to_underlying_type(value));
    }
    constexpr auto operator^(CoreWebView2PdfToolbarItems const left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        return static_cast<CoreWebView2PdfToolbarItems>(impl::to_underlying_type(left) ^ impl::to_underlying_type(right));
    }
    constexpr auto operator^=(CoreWebView2PdfToolbarItems& left, CoreWebView2PdfToolbarItems const right) noexcept
    {
        left = left ^ right;
        return left;
    }
    inline auto CoreWebView2ControllerWindowReference::CreateFromWindowHandle(uint64_t windowHandle)
    {
        return impl::call_factory<CoreWebView2ControllerWindowReference, ICoreWebView2ControllerWindowReferenceStatics>([&](ICoreWebView2ControllerWindowReferenceStatics const& f) { return f.CreateFromWindowHandle(windowHandle); });
    }
    inline auto CoreWebView2ControllerWindowReference::CreateFromCoreWindow(winrt::Windows::UI::Core::CoreWindow const& coreWindow)
    {
        return impl::call_factory<CoreWebView2ControllerWindowReference, ICoreWebView2ControllerWindowReferenceStatics>([&](ICoreWebView2ControllerWindowReferenceStatics const& f) { return f.CreateFromCoreWindow(coreWindow); });
    }
    inline auto CoreWebView2Environment::CreateAsync()
    {
        return impl::call_factory_cast<winrt::Windows::Foundation::IAsyncOperation<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>(*)(ICoreWebView2EnvironmentStatics const&), CoreWebView2Environment, ICoreWebView2EnvironmentStatics>([](ICoreWebView2EnvironmentStatics const& f) { return f.CreateAsync(); });
    }
    inline auto CoreWebView2Environment::CreateWithOptionsAsync(param::hstring const& browserExecutableFolder, param::hstring const& userDataFolder, winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions const& options)
    {
        return impl::call_factory<CoreWebView2Environment, ICoreWebView2EnvironmentStatics>([&](ICoreWebView2EnvironmentStatics const& f) { return f.CreateWithOptionsAsync(browserExecutableFolder, userDataFolder, options); });
    }
    inline auto CoreWebView2Environment::GetAvailableBrowserVersionString()
    {
        return impl::call_factory_cast<hstring(*)(ICoreWebView2EnvironmentStatics const&), CoreWebView2Environment, ICoreWebView2EnvironmentStatics>([](ICoreWebView2EnvironmentStatics const& f) { return f.GetAvailableBrowserVersionString(); });
    }
    inline auto CoreWebView2Environment::GetAvailableBrowserVersionString(param::hstring const& browserExecutableFolder)
    {
        return impl::call_factory<CoreWebView2Environment, ICoreWebView2EnvironmentStatics>([&](ICoreWebView2EnvironmentStatics const& f) { return f.GetAvailableBrowserVersionString(browserExecutableFolder); });
    }
    inline auto CoreWebView2Environment::CompareBrowserVersionString(param::hstring const& browserVersionString1, param::hstring const& browserVersionString2)
    {
        return impl::call_factory<CoreWebView2Environment, ICoreWebView2EnvironmentStatics>([&](ICoreWebView2EnvironmentStatics const& f) { return f.CompareBrowserVersionString(browserVersionString1, browserVersionString2); });
    }
    inline CoreWebView2EnvironmentOptions::CoreWebView2EnvironmentOptions() :
        CoreWebView2EnvironmentOptions(impl::call_factory_cast<CoreWebView2EnvironmentOptions(*)(winrt::Windows::Foundation::IActivationFactory const&), CoreWebView2EnvironmentOptions>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<CoreWebView2EnvironmentOptions>(); }))
    {
    }
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
