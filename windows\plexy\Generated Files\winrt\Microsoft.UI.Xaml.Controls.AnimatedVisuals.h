// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_H
#define WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/Microsoft.UI.Xaml.Controls.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.2.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.AnimatedVisuals.2.h"
namespace winrt::impl
{
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals
{
    inline AnimatedAcceptVisualSource::AnimatedAcceptVisualSource() :
        AnimatedAcceptVisualSource(impl::call_factory_cast<AnimatedAcceptVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedAcceptVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedAcceptVisualSource>(); }))
    {
    }
    inline AnimatedBackVisualSource::AnimatedBackVisualSource() :
        AnimatedBackVisualSource(impl::call_factory_cast<AnimatedBackVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedBackVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedBackVisualSource>(); }))
    {
    }
    inline AnimatedChevronDownSmallVisualSource::AnimatedChevronDownSmallVisualSource() :
        AnimatedChevronDownSmallVisualSource(impl::call_factory_cast<AnimatedChevronDownSmallVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedChevronDownSmallVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedChevronDownSmallVisualSource>(); }))
    {
    }
    inline AnimatedChevronRightDownSmallVisualSource::AnimatedChevronRightDownSmallVisualSource() :
        AnimatedChevronRightDownSmallVisualSource(impl::call_factory_cast<AnimatedChevronRightDownSmallVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedChevronRightDownSmallVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedChevronRightDownSmallVisualSource>(); }))
    {
    }
    inline AnimatedChevronUpDownSmallVisualSource::AnimatedChevronUpDownSmallVisualSource() :
        AnimatedChevronUpDownSmallVisualSource(impl::call_factory_cast<AnimatedChevronUpDownSmallVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedChevronUpDownSmallVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedChevronUpDownSmallVisualSource>(); }))
    {
    }
    inline AnimatedFindVisualSource::AnimatedFindVisualSource() :
        AnimatedFindVisualSource(impl::call_factory_cast<AnimatedFindVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedFindVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedFindVisualSource>(); }))
    {
    }
    inline AnimatedGlobalNavigationButtonVisualSource::AnimatedGlobalNavigationButtonVisualSource() :
        AnimatedGlobalNavigationButtonVisualSource(impl::call_factory_cast<AnimatedGlobalNavigationButtonVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedGlobalNavigationButtonVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedGlobalNavigationButtonVisualSource>(); }))
    {
    }
    inline AnimatedSettingsVisualSource::AnimatedSettingsVisualSource() :
        AnimatedSettingsVisualSource(impl::call_factory_cast<AnimatedSettingsVisualSource(*)(winrt::Windows::Foundation::IActivationFactory const&), AnimatedSettingsVisualSource>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<AnimatedSettingsVisualSource>(); }))
    {
    }
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedAcceptVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedBackVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronDownSmallVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronRightDownSmallVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronUpDownSmallVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedFindVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedGlobalNavigationButtonVisualSource> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedSettingsVisualSource> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
