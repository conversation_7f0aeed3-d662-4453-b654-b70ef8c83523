// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_ReactNative_1_H
#define WINRT_Microsoft_ReactNative_1_H
#include "winrt/impl/Microsoft.ReactNative.0.h"
WINRT_EXPORT namespace winrt::Microsoft::ReactNative
{
    struct WINRT_IMPL_EMPTY_BASES IBorderEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBorderEffect>
    {
        IBorderEffect(std::nullptr_t = nullptr) noexcept {}
        IBorderEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICallInvoker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICallInvoker>
    {
        ICallInvoker(std::nullptr_t = nullptr) noexcept {}
        ICallInvoker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSourceEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSourceEffect>
    {
        IColorSourceEffect(std::nullptr_t = nullptr) noexcept {}
        IColorSourceEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositeStepEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositeStepEffect>
    {
        ICompositeStepEffect(std::nullptr_t = nullptr) noexcept {}
        ICompositeStepEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDevMenuControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDevMenuControl>
    {
        IDevMenuControl(std::nullptr_t = nullptr) noexcept {}
        IDevMenuControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicAutomationPeer>
    {
        IDynamicAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IDynamicAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicAutomationPeerFactory>
    {
        IDynamicAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IDynamicAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicAutomationProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicAutomationProperties>
    {
        IDynamicAutomationProperties(std::nullptr_t = nullptr) noexcept {}
        IDynamicAutomationProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicAutomationPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicAutomationPropertiesStatics>
    {
        IDynamicAutomationPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        IDynamicAutomationPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicValueProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicValueProvider>
    {
        IDynamicValueProvider(std::nullptr_t = nullptr) noexcept {}
        IDynamicValueProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDynamicValueProviderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDynamicValueProviderFactory>
    {
        IDynamicValueProviderFactory(std::nullptr_t = nullptr) noexcept {}
        IDynamicValueProviderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGaussianBlurEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGaussianBlurEffect>
    {
        IGaussianBlurEffect(std::nullptr_t = nullptr) noexcept {}
        IGaussianBlurEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpSettingsStatics>
    {
        IHttpSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInstanceCreatedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInstanceCreatedEventArgs>
    {
        IInstanceCreatedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInstanceCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInstanceDestroyedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInstanceDestroyedEventArgs>
    {
        IInstanceDestroyedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInstanceDestroyedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInstanceLoadedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInstanceLoadedEventArgs>
    {
        IInstanceLoadedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IInstanceLoadedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJSValueReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJSValueReader>
    {
        IJSValueReader(std::nullptr_t = nullptr) noexcept {}
        IJSValueReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJSValueWriter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJSValueWriter>
    {
        IJSValueWriter(std::nullptr_t = nullptr) noexcept {}
        IJSValueWriter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiByteBuffer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiByteBuffer>
    {
        IJsiByteBuffer(std::nullptr_t = nullptr) noexcept {}
        IJsiByteBuffer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiError :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiError>
    {
        IJsiError(std::nullptr_t = nullptr) noexcept {}
        IJsiError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiHostObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiHostObject>
    {
        IJsiHostObject(std::nullptr_t = nullptr) noexcept {}
        IJsiHostObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiPreparedJavaScript :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiPreparedJavaScript>
    {
        IJsiPreparedJavaScript(std::nullptr_t = nullptr) noexcept {}
        IJsiPreparedJavaScript(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiRuntime :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiRuntime>
    {
        IJsiRuntime(std::nullptr_t = nullptr) noexcept {}
        IJsiRuntime(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IJsiRuntimeStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IJsiRuntimeStatics>
    {
        IJsiRuntimeStatics(std::nullptr_t = nullptr) noexcept {}
        IJsiRuntimeStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILayoutService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayoutService>
    {
        ILayoutService(std::nullptr_t = nullptr) noexcept {}
        ILayoutService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILayoutServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayoutServiceStatics>
    {
        ILayoutServiceStatics(std::nullptr_t = nullptr) noexcept {}
        ILayoutServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuirkSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuirkSettings>
    {
        IQuirkSettings(std::nullptr_t = nullptr) noexcept {}
        IQuirkSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuirkSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuirkSettingsStatics>
    {
        IQuirkSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IQuirkSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactApplication :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactApplication>
    {
        IReactApplication(std::nullptr_t = nullptr) noexcept {}
        IReactApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactApplicationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactApplicationFactory>
    {
        IReactApplicationFactory(std::nullptr_t = nullptr) noexcept {}
        IReactApplicationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactContext>
    {
        IReactContext(std::nullptr_t = nullptr) noexcept {}
        IReactContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactCoreInjection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactCoreInjection>
    {
        IReactCoreInjection(std::nullptr_t = nullptr) noexcept {}
        IReactCoreInjection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactCoreInjectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactCoreInjectionStatics>
    {
        IReactCoreInjectionStatics(std::nullptr_t = nullptr) noexcept {}
        IReactCoreInjectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactDispatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactDispatcher>
    {
        IReactDispatcher(std::nullptr_t = nullptr) noexcept {}
        IReactDispatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactDispatcherHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactDispatcherHelperStatics>
    {
        IReactDispatcherHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IReactDispatcherHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactInstanceSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactInstanceSettings>
    {
        IReactInstanceSettings(std::nullptr_t = nullptr) noexcept {}
        IReactInstanceSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactModuleBuilder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactModuleBuilder>
    {
        IReactModuleBuilder(std::nullptr_t = nullptr) noexcept {}
        IReactModuleBuilder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNativeHost :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNativeHost>
    {
        IReactNativeHost(std::nullptr_t = nullptr) noexcept {}
        IReactNativeHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNativeHostStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNativeHostStatics>
    {
        IReactNativeHostStatics(std::nullptr_t = nullptr) noexcept {}
        IReactNativeHostStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNonAbiValue :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNonAbiValue>
    {
        IReactNonAbiValue(std::nullptr_t = nullptr) noexcept {}
        IReactNonAbiValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNotificationArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNotificationArgs>
    {
        IReactNotificationArgs(std::nullptr_t = nullptr) noexcept {}
        IReactNotificationArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNotificationService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNotificationService>
    {
        IReactNotificationService(std::nullptr_t = nullptr) noexcept {}
        IReactNotificationService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNotificationServiceHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNotificationServiceHelperStatics>
    {
        IReactNotificationServiceHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IReactNotificationServiceHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactNotificationSubscription :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactNotificationSubscription>
    {
        IReactNotificationSubscription(std::nullptr_t = nullptr) noexcept {}
        IReactNotificationSubscription(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPackageBuilder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPackageBuilder>
    {
        IReactPackageBuilder(std::nullptr_t = nullptr) noexcept {}
        IReactPackageBuilder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPackageProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPackageProvider>
    {
        IReactPackageProvider(std::nullptr_t = nullptr) noexcept {}
        IReactPackageProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPointerEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPointerEventArgs>
    {
        IReactPointerEventArgs(std::nullptr_t = nullptr) noexcept {}
        IReactPointerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPropertyBag :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPropertyBag>
    {
        IReactPropertyBag(std::nullptr_t = nullptr) noexcept {}
        IReactPropertyBag(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPropertyBagHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPropertyBagHelperStatics>
    {
        IReactPropertyBagHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IReactPropertyBagHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPropertyName :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPropertyName>
    {
        IReactPropertyName(std::nullptr_t = nullptr) noexcept {}
        IReactPropertyName(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactPropertyNamespace :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactPropertyNamespace>
    {
        IReactPropertyNamespace(std::nullptr_t = nullptr) noexcept {}
        IReactPropertyNamespace(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactRootView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactRootView>
    {
        IReactRootView(std::nullptr_t = nullptr) noexcept {}
        IReactRootView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactSettingsSnapshot :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactSettingsSnapshot>
    {
        IReactSettingsSnapshot(std::nullptr_t = nullptr) noexcept {}
        IReactSettingsSnapshot(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactViewHost :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactViewHost>
    {
        IReactViewHost(std::nullptr_t = nullptr) noexcept {}
        IReactViewHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactViewInstance :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactViewInstance>
    {
        IReactViewInstance(std::nullptr_t = nullptr) noexcept {}
        IReactViewInstance(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReactViewOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReactViewOptions>
    {
        IReactViewOptions(std::nullptr_t = nullptr) noexcept {}
        IReactViewOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedBoxErrorFrameInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedBoxErrorFrameInfo>
    {
        IRedBoxErrorFrameInfo(std::nullptr_t = nullptr) noexcept {}
        IRedBoxErrorFrameInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedBoxErrorInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedBoxErrorInfo>
    {
        IRedBoxErrorInfo(std::nullptr_t = nullptr) noexcept {}
        IRedBoxErrorInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedBoxHandler :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedBoxHandler>
    {
        IRedBoxHandler(std::nullptr_t = nullptr) noexcept {}
        IRedBoxHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedBoxHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedBoxHelper>
    {
        IRedBoxHelper(std::nullptr_t = nullptr) noexcept {}
        IRedBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedBoxHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedBoxHelperStatics>
    {
        IRedBoxHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IRedBoxHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimer>
    {
        ITimer(std::nullptr_t = nullptr) noexcept {}
        ITimer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimer2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimer2>
    {
        ITimer2(std::nullptr_t = nullptr) noexcept {}
        ITimer2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimerStatics>
    {
        ITimerStatics(std::nullptr_t = nullptr) noexcept {}
        ITimerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewControl :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewControl>
    {
        IViewControl(std::nullptr_t = nullptr) noexcept {}
        IViewControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManager>
    {
        IViewManager(std::nullptr_t = nullptr) noexcept {}
        IViewManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerCreateWithProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerCreateWithProperties>
    {
        IViewManagerCreateWithProperties(std::nullptr_t = nullptr) noexcept {}
        IViewManagerCreateWithProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerRequiresNativeLayout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerRequiresNativeLayout>
    {
        IViewManagerRequiresNativeLayout(std::nullptr_t = nullptr) noexcept {}
        IViewManagerRequiresNativeLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithChildren :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithChildren>
    {
        IViewManagerWithChildren(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithChildren(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithCommands :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithCommands>
    {
        IViewManagerWithCommands(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithCommands(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithDropViewInstance :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithDropViewInstance>
    {
        IViewManagerWithDropViewInstance(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithDropViewInstance(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithExportedEventTypeConstants :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithExportedEventTypeConstants>
    {
        IViewManagerWithExportedEventTypeConstants(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithExportedEventTypeConstants(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithExportedViewConstants :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithExportedViewConstants>
    {
        IViewManagerWithExportedViewConstants(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithExportedViewConstants(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithNativeProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithNativeProperties>
    {
        IViewManagerWithNativeProperties(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithNativeProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithOnLayout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithOnLayout>
    {
        IViewManagerWithOnLayout(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithOnLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithPointerEvents :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithPointerEvents>
    {
        IViewManagerWithPointerEvents(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithPointerEvents(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewManagerWithReactContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewManagerWithReactContext>
    {
        IViewManagerWithReactContext(std::nullptr_t = nullptr) noexcept {}
        IViewManagerWithReactContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewPanel>
    {
        IViewPanel(std::nullptr_t = nullptr) noexcept {}
        IViewPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IViewPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IViewPanelStatics>
    {
        IViewPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IViewPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlHelper>
    {
        IXamlHelper(std::nullptr_t = nullptr) noexcept {}
        IXamlHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlHelperStatics>
    {
        IXamlHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIService>
    {
        IXamlUIService(std::nullptr_t = nullptr) noexcept {}
        IXamlUIService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlUIServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlUIServiceStatics>
    {
        IXamlUIServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlUIServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
