﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="App.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="App.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="ReactPackageProvider.cpp" />
    <ClCompile Include="AutolinkedNativeModules.g.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="App.h" />
    <ClInclude Include="ReactPackageProvider.h" />
    <ClInclude Include="AutolinkedNativeModules.g.h" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\Wide310x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\StoreLogo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\LockScreenLogo.scale-200.png">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <AppxManifest Include="Package.appxmanifest" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{e48dc53e-40b1-40cb-970a-f89935452892}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="PropertySheet.props" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainPage.xaml" />
  </ItemGroup>
</Project>