// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Payments_Provider_2_H
#define WINRT_Windows_ApplicationModel_Payments_Provider_2_H
#include "winrt/impl/Windows.ApplicationModel.Payments.Provider.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Payments::Provider
{
    struct WINRT_IMPL_EMPTY_BASES PaymentAppCanMakePaymentTriggerDetails : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentAppCanMakePaymentTriggerDetails
    {
        PaymentAppCanMakePaymentTriggerDetails(std::nullptr_t) noexcept {}
        PaymentAppCanMakePaymentTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentAppCanMakePaymentTriggerDetails(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PaymentAppManager : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentAppManager
    {
        PaymentAppManager(std::nullptr_t) noexcept {}
        PaymentAppManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentAppManager(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto Current();
    };
    struct WINRT_IMPL_EMPTY_BASES PaymentTransaction : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentTransaction
    {
        PaymentTransaction(std::nullptr_t) noexcept {}
        PaymentTransaction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentTransaction(ptr, take_ownership_from_abi) {}
        static auto FromIdAsync(param::hstring const& id);
    };
    struct WINRT_IMPL_EMPTY_BASES PaymentTransactionAcceptResult : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentTransactionAcceptResult
    {
        PaymentTransactionAcceptResult(std::nullptr_t) noexcept {}
        PaymentTransactionAcceptResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::Payments::Provider::IPaymentTransactionAcceptResult(ptr, take_ownership_from_abi) {}
    };
}
#endif
