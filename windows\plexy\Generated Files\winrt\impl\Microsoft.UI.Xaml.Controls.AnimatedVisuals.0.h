// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_0_H
#define WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_0_H
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls
{
    struct IAnimatedVisualSource;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals
{
    struct AnimatedAcceptVisualSource;
    struct AnimatedBackVisualSource;
    struct AnimatedChevronDownSmallVisualSource;
    struct AnimatedChevronRightDownSmallVisualSource;
    struct AnimatedChevronUpDownSmallVisualSource;
    struct AnimatedFindVisualSource;
    struct AnimatedGlobalNavigationButtonVisualSource;
    struct AnimatedSettingsVisualSource;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedAcceptVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedBackVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronDownSmallVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronRightDownSmallVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronUpDownSmallVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedFindVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedGlobalNavigationButtonVisualSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedSettingsVisualSource>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedAcceptVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedBackVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronDownSmallVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronRightDownSmallVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronUpDownSmallVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedFindVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedGlobalNavigationButtonVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedSettingsVisualSource> = L"Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource";
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedAcceptVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedBackVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronDownSmallVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronRightDownSmallVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedChevronUpDownSmallVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedFindVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedGlobalNavigationButtonVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals::AnimatedSettingsVisualSource>{ using type = winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource; };
}
#endif
