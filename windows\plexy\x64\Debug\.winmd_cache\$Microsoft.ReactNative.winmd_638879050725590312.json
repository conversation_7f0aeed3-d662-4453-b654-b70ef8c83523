{"FullPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\target\\x64\\Debug\\Microsoft.ReactNative\\Microsoft.ReactNative.winmd", "InProcServers": {"Microsoft.ReactNative.dll": {"Path": "Microsoft.ReactNative.dll", "TypeToThreadingModel": {"Microsoft.ReactNative.DevMenuControl": "both", "Microsoft.ReactNative.QuirkSettings": "both", "Microsoft.ReactNative.DynamicAutomationPeer": "both", "Microsoft.ReactNative.ReactViewOptions": "both", "Microsoft.ReactNative.ReactApplication": "both", "Microsoft.ReactNative.JsiRuntime": "both", "Microsoft.ReactNative.XamlMetaDataProvider": "both", "Microsoft.ReactNative.ViewPanel": "both", "Microsoft.ReactNative.DynamicValueProvider": "both", "Microsoft.ReactNative.XamlHelper": "both", "Microsoft.ReactNative.HttpSettings": "both", "Microsoft.ReactNative.ReactCoreInjection": "both", "Microsoft.ReactNative.ReactInstanceSettings": "both", "Microsoft.ReactNative.ReactDispatcherHelper": "both", "Microsoft.ReactNative.ReactPropertyBagHelper": "both", "Microsoft.ReactNative.ViewControl": "both", "Microsoft.ReactNative.Timer": "both", "Microsoft.ReactNative.ReactNotificationServiceHelper": "both", "Microsoft.ReactNative.LayoutService": "both", "Microsoft.ReactNative.ReactRootView": "both", "Microsoft.ReactNative.XamlUIService": "both", "Microsoft.ReactNative.DynamicAutomationProperties": "both", "Microsoft.ReactNative.RedBoxHelper": "both", "Microsoft.ReactNative.ReactNativeHost": "both"}}}, "Namespaces": {"Microsoft.ReactNative.IRedBoxHelperStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNativeHostStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.DevMenuControl": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithExportedEventTypeConstants": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicAutomationPeer": "Microsoft.ReactNative", "Microsoft.ReactNative.QuirkSettings": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewPanelStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactDispatcherCallback": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactViewHost": "Microsoft.ReactNative", "Microsoft.ReactNative.DynamicAutomationPeer": "Microsoft.ReactNative", "Microsoft.ReactNative.IJSValueReader": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactViewManagerProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.DocDefaultAttribute": "Microsoft.ReactNative", "Microsoft.ReactNative.IXamlHelperStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.ILayoutServiceStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicAutomationProperties": "Microsoft.ReactNative", "Microsoft.ReactNative.JSValueArgWriter": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiSymbolRef": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactCoreInjectionStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactViewOptions": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPropertyName": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityValue": "Microsoft.ReactNative", "Microsoft.ReactNative.MethodReturnType": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactApplication": "Microsoft.ReactNative", "Microsoft.ReactNative.InitializerDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithReactContext": "Microsoft.ReactNative", "Microsoft.ReactNative.CallInvoker": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactDispatcherHelperStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiRuntime": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactViewOptions": "Microsoft.ReactNative", "Microsoft.ReactNative.IXamlUIService": "Microsoft.ReactNative", "Microsoft.ReactNative.CanvasEdgeBehavior": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiPreparedJavaScript": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiError": "Microsoft.ReactNative", "Microsoft.ReactNative.XamlMetaDataProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.ViewPanel": "Microsoft.ReactNative", "Microsoft.ReactNative.DesktopWindowMessage": "Microsoft.ReactNative", "Microsoft.ReactNative.InstanceLoadedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.DynamicValueProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.AriaRole": "Microsoft.ReactNative", "Microsoft.ReactNative.LogLevel": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicAutomationPropertiesStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactInstanceSettings": "Microsoft.ReactNative", "Microsoft.ReactNative.JSValueType": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactCoreInjection": "Microsoft.ReactNative", "Microsoft.ReactNative.ITimerStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiPreparedJavaScript": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiHostObject": "Microsoft.ReactNative", "Microsoft.ReactNative.XamlHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.IXamlUIServiceStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactDispatcher": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNotificationArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.IInstanceLoadedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiScopeState": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNotificationServiceHelperStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.EmitEventSetterDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactRootView": "Microsoft.ReactNative", "Microsoft.ReactNative.IColorSourceEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithOnLayout": "Microsoft.ReactNative", "Microsoft.ReactNative.HttpSettings": "Microsoft.ReactNative", "Microsoft.ReactNative.BorderEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactSettingsSnapshot": "Microsoft.ReactNative", "Microsoft.ReactNative.IRedBoxHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.MethodDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.ITimer": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactCoreInjection": "Microsoft.ReactNative", "Microsoft.ReactNative.BackNavigationHandlerKind": "Microsoft.ReactNative", "Microsoft.ReactNative.IQuirkSettingsStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiValueRef": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityAction": "Microsoft.ReactNative", "Microsoft.ReactNative.ILayoutService": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactInstanceSettings": "Microsoft.ReactNative", "Microsoft.ReactNative.CallFunc": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManager": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactContext": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiValueKind": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerRequiresNativeLayout": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiRuntimeStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicValueProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.IInstanceCreatedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPackageBuilder": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiBigIntRef": "Microsoft.ReactNative", "Microsoft.ReactNative.RedBoxErrorType": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPropertyNamespace": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactDispatcherHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.EventEmitterInitializerDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.IRedBoxHandler": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactViewInstance": "Microsoft.ReactNative", "Microsoft.ReactNative.JSIEngine": "Microsoft.ReactNative", "Microsoft.ReactNative.PointerEventKind": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityInvokeEventHandler": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactApplication": "Microsoft.ReactNative", "Microsoft.ReactNative.GaussianBlurEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactPropertyBagHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiHostFunction": "Microsoft.ReactNative", "Microsoft.ReactNative.LoadingState": "Microsoft.ReactNative", "Microsoft.ReactNative.CanvasComposite": "Microsoft.ReactNative", "Microsoft.ReactNative.EffectBorderMode": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithExportedViewConstants": "Microsoft.ReactNative", "Microsoft.ReactNative.TimerFactory": "Microsoft.ReactNative", "Microsoft.ReactNative.IDevMenuControl": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactModuleBuilder": "Microsoft.ReactNative", "Microsoft.ReactNative.IGaussianBlurEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.ViewControl": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNotificationService": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityStates": "Microsoft.ReactNative", "Microsoft.ReactNative.Timer": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPropertyBagHelperStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicAutomationPeerFactory": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactNotificationServiceHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.IDynamicValueProviderFactory": "Microsoft.ReactNative", "Microsoft.ReactNative.IHttpSettingsStatics": "Microsoft.ReactNative", "Microsoft.ReactNative.LayoutService": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiErrorType": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactApplicationFactory": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNotificationSubscription": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPackageProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.InstanceCreatedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.ICompositeStepEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactRootView": "Microsoft.ReactNative", "Microsoft.ReactNative.UIBatchCompleteCallback": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityRoles": "Microsoft.ReactNative", "Microsoft.ReactNative.IXamlHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityActionEventHandler": "Microsoft.ReactNative", "Microsoft.ReactNative.ViewManagerPropertyType": "Microsoft.ReactNative", "Microsoft.ReactNative.IJSValueWriter": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewControl": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewPanel": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiError": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithNativeProperties": "Microsoft.ReactNative", "Microsoft.ReactNative.DocStringAttribute": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerCreateWithProperties": "Microsoft.ReactNative", "Microsoft.ReactNative.SyncMethodDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPropertyBag": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiRuntime": "Microsoft.ReactNative", "Microsoft.ReactNative.LogHandler": "Microsoft.ReactNative", "Microsoft.ReactNative.XamlUIService": "Microsoft.ReactNative", "Microsoft.ReactNative.ConstantProviderDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactPointerEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.EffectOptimization": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithPointerEvents": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiObjectRef": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactPointerEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactNotificationHandler": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactModuleProvider": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiWeakObjectRef": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithChildren": "Microsoft.ReactNative", "Microsoft.ReactNative.IInstanceDestroyedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.IJsiByteBuffer": "Microsoft.ReactNative", "Microsoft.ReactNative.IRedBoxErrorFrameInfo": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithDropViewInstance": "Microsoft.ReactNative", "Microsoft.ReactNative.DynamicAutomationProperties": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactCreatePropertyValue": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNativeHost": "Microsoft.ReactNative", "Microsoft.ReactNative.IBorderEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.MethodResultCallback": "Microsoft.ReactNative", "Microsoft.ReactNative.ICallInvoker": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiByteArrayUser": "Microsoft.ReactNative", "Microsoft.ReactNative.AccessibilityStateCheckedValue": "Microsoft.ReactNative", "Microsoft.ReactNative.ITimer2": "Microsoft.ReactNative", "Microsoft.ReactNative.ColorSourceEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.RedBoxHelper": "Microsoft.ReactNative", "Microsoft.ReactNative.IReactNonAbiValue": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiPropertyIdRef": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiInitializerDelegate": "Microsoft.ReactNative", "Microsoft.ReactNative.IQuirkSettings": "Microsoft.ReactNative", "Microsoft.ReactNative.InstanceDestroyedEventArgs": "Microsoft.ReactNative", "Microsoft.ReactNative.CompositeStepEffect": "Microsoft.ReactNative", "Microsoft.ReactNative.ReactNativeHost": "Microsoft.ReactNative", "Microsoft.ReactNative.JsiStringRef": "Microsoft.ReactNative", "Microsoft.ReactNative.IRedBoxErrorInfo": "Microsoft.ReactNative", "Microsoft.ReactNative.IViewManagerWithCommands": "Microsoft.ReactNative"}, "RootNamespace": "Microsoft.ReactNative", "AssemblyVersion": "17.14.40364.64997"}