// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Media_Capture_H
#define WINRT_Windows_Media_Capture_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/Windows.Media.h"
#include "winrt/impl/Windows.Devices.Enumeration.2.h"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.Foundation.Collections.2.h"
#include "winrt/impl/Windows.Graphics.DirectX.Direct3D11.2.h"
#include "winrt/impl/Windows.Graphics.Imaging.2.h"
#include "winrt/impl/Windows.Media.2.h"
#include "winrt/impl/Windows.Media.Capture.Core.2.h"
#include "winrt/impl/Windows.Media.Capture.Frames.2.h"
#include "winrt/impl/Windows.Media.Core.2.h"
#include "winrt/impl/Windows.Media.Devices.2.h"
#include "winrt/impl/Windows.Media.Effects.2.h"
#include "winrt/impl/Windows.Media.MediaProperties.2.h"
#include "winrt/impl/Windows.Security.Credentials.2.h"
#include "winrt/impl/Windows.Storage.2.h"
#include "winrt/impl/Windows.Storage.Streams.2.h"
#include "winrt/impl/Windows.UI.WindowManagement.2.h"
#include "winrt/impl/Windows.Media.Capture.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedCapturedPhoto<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedCapturedPhoto)->get_Frame(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedCapturedPhoto<D>::Mode() const
    {
        winrt::Windows::Media::Devices::AdvancedPhotoMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedCapturedPhoto)->get_Mode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedCapturedPhoto<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedCapturedPhoto)->get_Context(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedCapturedPhoto2<D>::FrameBoundsRelativeToReferencePhoto() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2)->get_FrameBoundsRelativeToReferencePhoto(&value));
        return winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::CaptureAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->CaptureAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::CaptureAsync(winrt::Windows::Foundation::IInspectable const& context) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->CaptureWithContextAsync(*(void**)(&context), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::OptionalReferencePhotoCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->add_OptionalReferencePhotoCaptured(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::OptionalReferencePhotoCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, OptionalReferencePhotoCaptured_revoker>(this, OptionalReferencePhotoCaptured(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::OptionalReferencePhotoCaptured(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->remove_OptionalReferencePhotoCaptured(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::AllPhotosCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->add_AllPhotosCaptured(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::AllPhotosCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, AllPhotosCaptured_revoker>(this, AllPhotosCaptured(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::AllPhotosCaptured(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->remove_AllPhotosCaptured(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAdvancedPhotoCapture<D>::FinishAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAdvancedPhotoCapture)->FinishAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCapture<D>::IsCapturingAudio() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCapture)->get_IsCapturingAudio(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCapture<D>::IsCapturingVideo() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCapture)->get_IsCapturingVideo(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCapture<D>::CapturingChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AppCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCapture)->add_CapturingChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCapture<D>::CapturingChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AppCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, CapturingChanged_revoker>(this, CapturingChanged(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCapture<D>::CapturingChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCapture)->remove_CapturingChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCaptureStatics<D>::GetForCurrentView() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCaptureStatics)->GetForCurrentView(&value));
        return winrt::Windows::Media::Capture::AppCapture{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IAppCaptureStatics2<D>::SetAllowedAsync(bool allowed) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IAppCaptureStatics2)->SetAllowedAsync(allowed, &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUI<D>::PhotoSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUI)->get_PhotoSettings(&value));
        return winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUI<D>::VideoSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUI)->get_VideoSettings(&value));
        return winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUI<D>::CaptureFileAsync(winrt::Windows::Media::Capture::CameraCaptureUIMode const& mode) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUI)->CaptureFileAsync(static_cast<int32_t>(mode), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::StorageFile>{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::Format() const
    {
        winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->get_Format(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::Format(winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->put_Format(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::MaxResolution() const
    {
        winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->get_MaxResolution(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::MaxResolution(winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->put_MaxResolution(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::CroppedSizeInPixels() const
    {
        winrt::Windows::Foundation::Size value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->get_CroppedSizeInPixels(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::CroppedSizeInPixels(winrt::Windows::Foundation::Size const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->put_CroppedSizeInPixels(impl::bind_in(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::CroppedAspectRatio() const
    {
        winrt::Windows::Foundation::Size value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->get_CroppedAspectRatio(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::CroppedAspectRatio(winrt::Windows::Foundation::Size const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->put_CroppedAspectRatio(impl::bind_in(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::AllowCropping() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->get_AllowCropping(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIPhotoCaptureSettings<D>::AllowCropping(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings)->put_AllowCropping(value));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::Format() const
    {
        winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->get_Format(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::Format(winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->put_Format(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::MaxResolution() const
    {
        winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->get_MaxResolution(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::MaxResolution(winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->put_MaxResolution(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::MaxDurationInSeconds() const
    {
        float value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->get_MaxDurationInSeconds(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::MaxDurationInSeconds(float value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->put_MaxDurationInSeconds(value));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::AllowTrimming() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->get_AllowTrimming(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICameraCaptureUIVideoCaptureSettings<D>::AllowTrimming(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings)->put_AllowTrimming(value));
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrame<D>::Width() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrame)->get_Width(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrame<D>::Height() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrame)->get_Height(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrame2<D>::ControlValues() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrame2)->get_ControlValues(&value));
        return winrt::Windows::Media::Capture::CapturedFrameControlValues{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrame2<D>::BitmapProperties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrame2)->get_BitmapProperties(&value));
        return winrt::Windows::Graphics::Imaging::BitmapPropertySet{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::Exposure() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_Exposure(&value));
        return winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::TimeSpan>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::ExposureCompensation() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_ExposureCompensation(&value));
        return winrt::Windows::Foundation::IReference<float>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::IsoSpeed() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_IsoSpeed(&value));
        return winrt::Windows::Foundation::IReference<uint32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::Focus() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_Focus(&value));
        return winrt::Windows::Foundation::IReference<uint32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::SceneMode() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_SceneMode(&value));
        return winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::CaptureSceneMode>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::Flashed() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_Flashed(&value));
        return winrt::Windows::Foundation::IReference<bool>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::FlashPowerPercent() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_FlashPowerPercent(&value));
        return winrt::Windows::Foundation::IReference<float>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::WhiteBalance() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_WhiteBalance(&value));
        return winrt::Windows::Foundation::IReference<uint32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues<D>::ZoomFactor() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues)->get_ZoomFactor(&value));
        return winrt::Windows::Foundation::IReference<float>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>::FocusState() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues2)->get_FocusState(&value));
        return winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::MediaCaptureFocusState>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>::IsoDigitalGain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues2)->get_IsoDigitalGain(&value));
        return winrt::Windows::Foundation::IReference<double>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>::IsoAnalogGain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues2)->get_IsoAnalogGain(&value));
        return winrt::Windows::Foundation::IReference<double>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>::SensorFrameRate() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues2)->get_SensorFrameRate(&value));
        return winrt::Windows::Media::MediaProperties::MediaRatio{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameControlValues2<D>::WhiteBalanceGain() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameControlValues2)->get_WhiteBalanceGain(&value));
        return winrt::Windows::Foundation::IReference<winrt::Windows::Media::Capture::WhiteBalanceGain>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedFrameWithSoftwareBitmap<D>::SoftwareBitmap() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap)->get_SoftwareBitmap(&value));
        return winrt::Windows::Graphics::Imaging::SoftwareBitmap{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedPhoto<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedPhoto)->get_Frame(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ICapturedPhoto<D>::Thumbnail() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ICapturedPhoto)->get_Thumbnail(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording<D>::StartAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording)->StartAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording<D>::StopAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording)->StopAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording<D>::FinishAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording)->FinishAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording2<D>::PauseAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording2)->PauseAsync(static_cast<int32_t>(behavior), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording2<D>::ResumeAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording2)->ResumeAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording3<D>::PauseWithResultAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording3)->PauseWithResultAsync(static_cast<int32_t>(behavior), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagMediaRecording3<D>::StopWithResultAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagMediaRecording3)->StopWithResultAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoCapture<D>::CaptureAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoCapture)->CaptureAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::CapturedPhoto>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoCapture<D>::FinishAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoCapture)->FinishAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::StartAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture)->StartAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::StopAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture)->StopAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::FinishAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture)->FinishAsync(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::PhotoCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture, winrt::Windows::Media::Capture::PhotoCapturedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture)->add_PhotoCaptured(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::PhotoCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture, winrt::Windows::Media::Capture::PhotoCapturedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, PhotoCaptured_revoker>(this, PhotoCaptured(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_ILowLagPhotoSequenceCapture<D>::PhotoCaptured(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture)->remove_PhotoCaptured(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::InitializeAsync() const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->InitializeAsync(&asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::InitializeAsync(winrt::Windows::Media::Capture::MediaCaptureInitializationSettings const& mediaCaptureInitializationSettings) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->InitializeWithSettingsAsync(*(void**)(&mediaCaptureInitializationSettings), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::StartRecordToStorageFileAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::IStorageFile const& file) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->StartRecordToStorageFileAsync(*(void**)(&encodingProfile), *(void**)(&file), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::StartRecordToStreamAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->StartRecordToStreamAsync(*(void**)(&encodingProfile), *(void**)(&stream), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::StartRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->StartRecordToCustomSinkAsync(*(void**)(&encodingProfile), *(void**)(&customMediaSink), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::StartRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->StartRecordToCustomSinkIdAsync(*(void**)(&encodingProfile), *(void**)(&customSinkActivationId), *(void**)(&customSinkSettings), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::StopRecordAsync() const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->StopRecordAsync(&asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::CapturePhotoToStorageFileAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type, winrt::Windows::Storage::IStorageFile const& file) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->CapturePhotoToStorageFileAsync(*(void**)(&type), *(void**)(&file), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::CapturePhotoToStreamAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->CapturePhotoToStreamAsync(*(void**)(&type), *(void**)(&stream), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::AddEffectAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, param::hstring const& effectActivationID, winrt::Windows::Foundation::Collections::IPropertySet const& effectSettings) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->AddEffectAsync(static_cast<int32_t>(mediaStreamType), *(void**)(&effectActivationID), *(void**)(&effectSettings), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::ClearEffectsAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->ClearEffectsAsync(static_cast<int32_t>(mediaStreamType), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::SetEncoderProperty(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::guid const& propertyId, winrt::Windows::Foundation::IInspectable const& propertyValue) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->SetEncoderProperty(static_cast<int32_t>(mediaStreamType), impl::bind_in(propertyId), *(void**)(&propertyValue)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::GetEncoderProperty(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::guid const& propertyId) const
    {
        void* propertyValue{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->GetEncoderProperty(static_cast<int32_t>(mediaStreamType), impl::bind_in(propertyId), &propertyValue));
        return winrt::Windows::Foundation::IInspectable{ propertyValue, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::Failed(winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler const& errorEventHandler) const
    {
        winrt::event_token eventCookie{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->add_Failed(*(void**)(&errorEventHandler), put_abi(eventCookie)));
        return eventCookie;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::Failed(auto_revoke_t, winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler const& errorEventHandler) const
    {
        return impl::make_event_revoker<D, Failed_revoker>(this, Failed(errorEventHandler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::Failed(winrt::event_token const& eventCookie) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->remove_Failed(impl::bind_in(eventCookie));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::RecordLimitationExceeded(winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler const& recordLimitationExceededEventHandler) const
    {
        winrt::event_token eventCookie{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->add_RecordLimitationExceeded(*(void**)(&recordLimitationExceededEventHandler), put_abi(eventCookie)));
        return eventCookie;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::RecordLimitationExceeded(auto_revoke_t, winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler const& recordLimitationExceededEventHandler) const
    {
        return impl::make_event_revoker<D, RecordLimitationExceeded_revoker>(this, RecordLimitationExceeded(recordLimitationExceededEventHandler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::RecordLimitationExceeded(winrt::event_token const& eventCookie) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->remove_RecordLimitationExceeded(impl::bind_in(eventCookie));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::MediaCaptureSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->get_MediaCaptureSettings(&value));
        return winrt::Windows::Media::Capture::MediaCaptureSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::AudioDeviceController() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->get_AudioDeviceController(&value));
        return winrt::Windows::Media::Devices::AudioDeviceController{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::VideoDeviceController() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->get_VideoDeviceController(&value));
        return winrt::Windows::Media::Devices::VideoDeviceController{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::SetPreviewMirroring(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->SetPreviewMirroring(value));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::GetPreviewMirroring() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->GetPreviewMirroring(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::SetPreviewRotation(winrt::Windows::Media::Capture::VideoRotation const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->SetPreviewRotation(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::GetPreviewRotation() const
    {
        winrt::Windows::Media::Capture::VideoRotation value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->GetPreviewRotation(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::SetRecordRotation(winrt::Windows::Media::Capture::VideoRotation const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->SetRecordRotation(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture<D>::GetRecordRotation() const
    {
        winrt::Windows::Media::Capture::VideoRotation value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture)->GetRecordRotation(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagRecordToStorageFileAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::IStorageFile const& file) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagRecordToStorageFileAsync(*(void**)(&encodingProfile), *(void**)(&file), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagRecordToStreamAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Storage::Streams::IRandomAccessStream const& stream) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagRecordToStreamAsync(*(void**)(&encodingProfile), *(void**)(&stream), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagRecordToCustomSinkAsync(*(void**)(&encodingProfile), *(void**)(&customMediaSink), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagRecordToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagRecordToCustomSinkIdAsync(*(void**)(&encodingProfile), *(void**)(&customSinkActivationId), *(void**)(&customSinkSettings), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagPhotoCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagPhotoCaptureAsync(*(void**)(&type), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoCapture>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::PrepareLowLagPhotoSequenceCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->PrepareLowLagPhotoSequenceCaptureAsync(*(void**)(&type), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture2<D>::SetEncodingPropertiesAsync(winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType, winrt::Windows::Media::MediaProperties::IMediaEncodingProperties const& mediaEncodingProperties, winrt::Windows::Media::MediaProperties::MediaPropertySet const& encoderProperties) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture2)->SetEncodingPropertiesAsync(static_cast<int32_t>(mediaStreamType), *(void**)(&mediaEncodingProperties), *(void**)(&encoderProperties), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::PrepareVariablePhotoSequenceCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& type) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture3)->PrepareVariablePhotoSequenceCaptureAsync(*(void**)(&type), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Core::VariablePhotoSequenceCapture>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::FocusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture3)->add_FocusChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::FocusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, FocusChanged_revoker>(this, FocusChanged(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::FocusChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture3)->remove_FocusChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::PhotoConfirmationCaptured(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture3)->add_PhotoConfirmationCaptured(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::PhotoConfirmationCaptured(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, PhotoConfirmationCaptured_revoker>(this, PhotoConfirmationCaptured(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture3<D>::PhotoConfirmationCaptured(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture3)->remove_PhotoConfirmationCaptured(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::AddAudioEffectAsync(winrt::Windows::Media::Effects::IAudioEffectDefinition const& definition) const
    {
        void* op{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->AddAudioEffectAsync(*(void**)(&definition), &op));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>{ op, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::AddVideoEffectAsync(winrt::Windows::Media::Effects::IVideoEffectDefinition const& definition, winrt::Windows::Media::Capture::MediaStreamType const& mediaStreamType) const
    {
        void* op{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->AddVideoEffectAsync(*(void**)(&definition), static_cast<int32_t>(mediaStreamType), &op));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>{ op, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::PauseRecordAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->PauseRecordAsync(static_cast<int32_t>(behavior), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::ResumeRecordAsync() const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->ResumeRecordAsync(&asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::CameraStreamStateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->add_CameraStreamStateChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::CameraStreamStateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, CameraStreamStateChanged_revoker>(this, CameraStreamStateChanged(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::CameraStreamStateChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->remove_CameraStreamStateChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::CameraStreamState() const
    {
        winrt::Windows::Media::Devices::CameraStreamState streamState{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->get_CameraStreamState(reinterpret_cast<int32_t*>(&streamState)));
        return streamState;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::GetPreviewFrameAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->GetPreviewFrameAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::GetPreviewFrameAsync(winrt::Windows::Media::VideoFrame const& destination) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->GetPreviewFrameCopyAsync(*(void**)(&destination), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::ThermalStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->add_ThermalStatusChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::ThermalStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, ThermalStatusChanged_revoker>(this, ThermalStatusChanged(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::ThermalStatusChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->remove_ThermalStatusChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::ThermalStatus() const
    {
        winrt::Windows::Media::Capture::MediaCaptureThermalStatus value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->get_ThermalStatus(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture4<D>::PrepareAdvancedPhotoCaptureAsync(winrt::Windows::Media::MediaProperties::ImageEncodingProperties const& encodingProperties) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture4)->PrepareAdvancedPhotoCaptureAsync(*(void**)(&encodingProperties), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedPhotoCapture>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::RemoveEffectAsync(winrt::Windows::Media::IMediaExtension const& effect) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->RemoveEffectAsync(*(void**)(&effect), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::PauseRecordWithResultAsync(winrt::Windows::Media::Devices::MediaCapturePauseBehavior const& behavior) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->PauseRecordWithResultAsync(static_cast<int32_t>(behavior), &operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::StopRecordWithResultAsync() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->StopRecordWithResultAsync(&operation));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::FrameSources() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->get_FrameSources(&value));
        return winrt::Windows::Foundation::Collections::IMapView<hstring, winrt::Windows::Media::Capture::Frames::MediaFrameSource>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->CreateFrameReaderAsync(*(void**)(&inputSource), &value));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource, param::hstring const& outputSubtype) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->CreateFrameReaderWithSubtypeAsync(*(void**)(&inputSource), *(void**)(&outputSubtype), &value));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture5<D>::CreateFrameReaderAsync(winrt::Windows::Media::Capture::Frames::MediaFrameSource const& inputSource, param::hstring const& outputSubtype, winrt::Windows::Graphics::Imaging::BitmapSize const& outputSize) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture5)->CreateFrameReaderWithSubtypeAndSizeAsync(*(void**)(&inputSource), *(void**)(&outputSubtype), impl::bind_in(outputSize), &value));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture6<D>::CaptureDeviceExclusiveControlStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture6)->add_CaptureDeviceExclusiveControlStatusChanged(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture6<D>::CaptureDeviceExclusiveControlStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, CaptureDeviceExclusiveControlStatusChanged_revoker>(this, CaptureDeviceExclusiveControlStatusChanged(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture6<D>::CaptureDeviceExclusiveControlStatusChanged(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture6)->remove_CaptureDeviceExclusiveControlStatusChanged(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture6<D>::CreateMultiSourceFrameReaderAsync(param::async_iterable<winrt::Windows::Media::Capture::Frames::MediaFrameSource> const& inputSources) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture6)->CreateMultiSourceFrameReaderAsync(*(void**)(&inputSources), &value));
        return winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MultiSourceMediaFrameReader>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapture7<D>::CreateRelativePanelWatcher(winrt::Windows::Media::Capture::StreamingCaptureMode const& captureMode, winrt::Windows::UI::WindowManagement::DisplayRegion const& displayRegion) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapture7)->CreateRelativePanelWatcher(static_cast<int32_t>(captureMode), *(void**)(&displayRegion), &result));
        return winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs<D>::DeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs)->get_DeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs<D>::Status() const
    {
        winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatus value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs)->get_Status(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureFailedEventArgs<D>::Message() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs)->get_Message(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureFailedEventArgs<D>::Code() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs)->get_Code(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureFocusChangedEventArgs<D>::FocusState() const
    {
        winrt::Windows::Media::Devices::MediaCaptureFocusState value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs)->get_FocusState(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::AudioDeviceId(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->put_AudioDeviceId(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::AudioDeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->get_AudioDeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::VideoDeviceId(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->put_VideoDeviceId(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::VideoDeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->get_VideoDeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::StreamingCaptureMode(winrt::Windows::Media::Capture::StreamingCaptureMode const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->put_StreamingCaptureMode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::StreamingCaptureMode() const
    {
        winrt::Windows::Media::Capture::StreamingCaptureMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->get_StreamingCaptureMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::PhotoCaptureSource(winrt::Windows::Media::Capture::PhotoCaptureSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->put_PhotoCaptureSource(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings<D>::PhotoCaptureSource() const
    {
        winrt::Windows::Media::Capture::PhotoCaptureSource value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings)->get_PhotoCaptureSource(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2<D>::MediaCategory(winrt::Windows::Media::Capture::MediaCategory const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2)->put_MediaCategory(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2<D>::MediaCategory() const
    {
        winrt::Windows::Media::Capture::MediaCategory value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2)->get_MediaCategory(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2<D>::AudioProcessing(winrt::Windows::Media::AudioProcessing const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2)->put_AudioProcessing(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings2<D>::AudioProcessing() const
    {
        winrt::Windows::Media::AudioProcessing value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2)->get_AudioProcessing(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3<D>::AudioSource(winrt::Windows::Media::Core::IMediaSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3)->put_AudioSource(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3<D>::AudioSource() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3)->get_AudioSource(&value));
        return winrt::Windows::Media::Core::IMediaSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3<D>::VideoSource(winrt::Windows::Media::Core::IMediaSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3)->put_VideoSource(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings3<D>::VideoSource() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3)->get_VideoSource(&value));
        return winrt::Windows::Media::Core::IMediaSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::VideoProfile() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->get_VideoProfile(&value));
        return winrt::Windows::Media::Capture::MediaCaptureVideoProfile{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::VideoProfile(winrt::Windows::Media::Capture::MediaCaptureVideoProfile const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->put_VideoProfile(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::PreviewMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->get_PreviewMediaDescription(&value));
        return winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::PreviewMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->put_PreviewMediaDescription(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::RecordMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->get_RecordMediaDescription(&value));
        return winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::RecordMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->put_RecordMediaDescription(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::PhotoMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->get_PhotoMediaDescription(&value));
        return winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings4<D>::PhotoMediaDescription(winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4)->put_PhotoMediaDescription(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::SourceGroup() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->get_SourceGroup(&value));
        return winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::SourceGroup(winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->put_SourceGroup(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::SharingMode() const
    {
        winrt::Windows::Media::Capture::MediaCaptureSharingMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->get_SharingMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::SharingMode(winrt::Windows::Media::Capture::MediaCaptureSharingMode const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->put_SharingMode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::MemoryPreference() const
    {
        winrt::Windows::Media::Capture::MediaCaptureMemoryPreference value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->get_MemoryPreference(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings5<D>::MemoryPreference(winrt::Windows::Media::Capture::MediaCaptureMemoryPreference const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5)->put_MemoryPreference(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings6<D>::AlwaysPlaySystemShutterSound() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6)->get_AlwaysPlaySystemShutterSound(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings6<D>::AlwaysPlaySystemShutterSound(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6)->put_AlwaysPlaySystemShutterSound(value));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7<D>::DeviceUriPasswordCredential() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7)->get_DeviceUriPasswordCredential(&value));
        return winrt::Windows::Security::Credentials::PasswordCredential{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7<D>::DeviceUriPasswordCredential(winrt::Windows::Security::Credentials::PasswordCredential const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7)->put_DeviceUriPasswordCredential(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7<D>::DeviceUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7)->get_DeviceUri(&value));
        return winrt::Windows::Foundation::Uri{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureInitializationSettings7<D>::DeviceUri(winrt::Windows::Foundation::Uri const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7)->put_DeviceUri(*(void**)(&value)));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapturePauseResult<D>::LastFrame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapturePauseResult)->get_LastFrame(&value));
        return winrt::Windows::Media::VideoFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCapturePauseResult<D>::RecordDuration() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCapturePauseResult)->get_RecordDuration(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::RelativePanel() const
    {
        winrt::Windows::Devices::Enumeration::Panel value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher)->get_RelativePanel(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::Changed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher)->add_Changed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::Changed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher, winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, Changed_revoker>(this, Changed(handler));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::Changed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher)->remove_Changed(impl::bind_in(token));
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::Start() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher)->Start());
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureRelativePanelWatcher<D>::Stop() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher)->Stop());
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings<D>::AudioDeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings)->get_AudioDeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings<D>::VideoDeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings)->get_VideoDeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings<D>::StreamingCaptureMode() const
    {
        winrt::Windows::Media::Capture::StreamingCaptureMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings)->get_StreamingCaptureMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings<D>::PhotoCaptureSource() const
    {
        winrt::Windows::Media::Capture::PhotoCaptureSource value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings)->get_PhotoCaptureSource(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings<D>::VideoDeviceCharacteristic() const
    {
        winrt::Windows::Media::Capture::VideoDeviceCharacteristic value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings)->get_VideoDeviceCharacteristic(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::ConcurrentRecordAndPhotoSupported() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_ConcurrentRecordAndPhotoSupported(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::ConcurrentRecordAndPhotoSequenceSupported() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_ConcurrentRecordAndPhotoSequenceSupported(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::CameraSoundRequiredForRegion() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_CameraSoundRequiredForRegion(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::Horizontal35mmEquivalentFocalLength() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_Horizontal35mmEquivalentFocalLength(&value));
        return winrt::Windows::Foundation::IReference<uint32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::PitchOffsetDegrees() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_PitchOffsetDegrees(&value));
        return winrt::Windows::Foundation::IReference<int32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::Vertical35mmEquivalentFocalLength() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_Vertical35mmEquivalentFocalLength(&value));
        return winrt::Windows::Foundation::IReference<uint32_t>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::MediaCategory() const
    {
        winrt::Windows::Media::Capture::MediaCategory value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_MediaCategory(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings2<D>::AudioProcessing() const
    {
        winrt::Windows::Media::AudioProcessing value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings2)->get_AudioProcessing(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureSettings3<D>::Direct3D11Device() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureSettings3)->get_Direct3D11Device(&value));
        return winrt::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStatics<D>::IsVideoProfileSupported(param::hstring const& videoDeviceId) const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStatics)->IsVideoProfileSupported(*(void**)(&videoDeviceId), &value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStatics<D>::FindAllVideoProfiles(param::hstring const& videoDeviceId) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStatics)->FindAllVideoProfiles(*(void**)(&videoDeviceId), &value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStatics<D>::FindConcurrentProfiles(param::hstring const& videoDeviceId) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStatics)->FindConcurrentProfiles(*(void**)(&videoDeviceId), &value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStatics<D>::FindKnownVideoProfiles(param::hstring const& videoDeviceId, winrt::Windows::Media::Capture::KnownVideoProfile const& name) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStatics)->FindKnownVideoProfiles(*(void**)(&videoDeviceId), static_cast<int32_t>(name), &value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStopResult<D>::LastFrame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStopResult)->get_LastFrame(&value));
        return winrt::Windows::Media::VideoFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureStopResult<D>::RecordDuration() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureStopResult)->get_RecordDuration(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoPreview<D>::StartPreviewAsync() const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoPreview)->StartPreviewAsync(&asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoPreview<D>::StartPreviewToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, winrt::Windows::Media::IMediaExtension const& customMediaSink) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoPreview)->StartPreviewToCustomSinkAsync(*(void**)(&encodingProfile), *(void**)(&customMediaSink), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoPreview<D>::StartPreviewToCustomSinkAsync(winrt::Windows::Media::MediaProperties::MediaEncodingProfile const& encodingProfile, param::hstring const& customSinkActivationId, winrt::Windows::Foundation::Collections::IPropertySet const& customSinkSettings) const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoPreview)->StartPreviewToCustomSinkIdAsync(*(void**)(&encodingProfile), *(void**)(&customSinkActivationId), *(void**)(&customSinkSettings), &asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoPreview<D>::StopPreviewAsync() const
    {
        void* asyncInfo{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoPreview)->StopPreviewAsync(&asyncInfo));
        return winrt::Windows::Foundation::IAsyncAction{ asyncInfo, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::Id() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->get_Id(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::VideoDeviceId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->get_VideoDeviceId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::SupportedPreviewMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->get_SupportedPreviewMediaDescription(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::SupportedRecordMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->get_SupportedRecordMediaDescription(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::SupportedPhotoMediaDescription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->get_SupportedPhotoMediaDescription(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile<D>::GetConcurrency() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile)->GetConcurrency(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile2<D>::FrameSourceInfos() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2)->get_FrameSourceInfos(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::Frames::MediaFrameSourceInfo>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfile2<D>::Properties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2)->get_Properties(&value));
        return winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>::Width() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription)->get_Width(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>::Height() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription)->get_Height(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>::FrameRate() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription)->get_FrameRate(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>::IsVariablePhotoSequenceSupported() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription)->get_IsVariablePhotoSequenceSupported(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription<D>::IsHdrVideoSupported() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription)->get_IsHdrVideoSupported(&value));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription2<D>::Subtype() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2)->get_Subtype(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IMediaCaptureVideoProfileMediaDescription2<D>::Properties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2)->get_Properties(&value));
        return winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IOptionalReferencePhotoCapturedEventArgs<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs)->get_Frame(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IOptionalReferencePhotoCapturedEventArgs<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs)->get_Context(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IPhotoCapturedEventArgs<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IPhotoCapturedEventArgs)->get_Frame(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IPhotoCapturedEventArgs<D>::Thumbnail() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IPhotoCapturedEventArgs)->get_Thumbnail(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IPhotoCapturedEventArgs<D>::CaptureTimeOffset() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IPhotoCapturedEventArgs)->get_CaptureTimeOffset(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IPhotoConfirmationCapturedEventArgs<D>::Frame() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs)->get_Frame(&value));
        return winrt::Windows::Media::Capture::CapturedFrame{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IPhotoConfirmationCapturedEventArgs<D>::CaptureTimeOffset() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs)->get_CaptureTimeOffset(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Windows_Media_Capture_IVideoStreamConfiguration<D>::InputProperties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IVideoStreamConfiguration)->get_InputProperties(&value));
        return winrt::Windows::Media::MediaProperties::VideoEncodingProperties{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Windows_Media_Capture_IVideoStreamConfiguration<D>::OutputProperties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Windows::Media::Capture::IVideoStreamConfiguration)->get_OutputProperties(&value));
        return winrt::Windows::Media::MediaProperties::VideoEncodingProperties{ value, take_ownership_from_abi };
    }
    template <typename H> struct delegate<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler, H> final : implements_delegate<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* errorEventArgs) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCapture const*>(&sender), *reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs const*>(&errorEventArgs));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler, H> final : implements_delegate<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCapture const*>(&sender));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAdvancedCapturedPhoto> : produce_base<D, winrt::Windows::Media::Capture::IAdvancedCapturedPhoto>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Mode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Devices::AdvancedPhotoMode>(this->shim().Mode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2> : produce_base<D, winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2>
    {
        int32_t __stdcall get_FrameBoundsRelativeToReferencePhoto(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect>>(this->shim().FrameBoundsRelativeToReferencePhoto());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAdvancedPhotoCapture> : produce_base<D, winrt::Windows::Media::Capture::IAdvancedPhotoCapture>
    {
        int32_t __stdcall CaptureAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>>(this->shim().CaptureAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CaptureWithContextAsync(void* context, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedCapturedPhoto>>(this->shim().CaptureAsync(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&context)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_OptionalReferencePhotoCaptured(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().OptionalReferencePhotoCaptured(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_OptionalReferencePhotoCaptured(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OptionalReferencePhotoCaptured(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_AllPhotosCaptured(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().AllPhotosCaptured(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AdvancedPhotoCapture, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_AllPhotosCaptured(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllPhotosCaptured(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall FinishAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().FinishAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAppCapture> : produce_base<D, winrt::Windows::Media::Capture::IAppCapture>
    {
        int32_t __stdcall get_IsCapturingAudio(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsCapturingAudio());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsCapturingVideo(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsCapturingVideo());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_CapturingChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().CapturingChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::AppCapture, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_CapturingChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CapturingChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAppCaptureStatics> : produce_base<D, winrt::Windows::Media::Capture::IAppCaptureStatics>
    {
        int32_t __stdcall GetForCurrentView(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::AppCapture>(this->shim().GetForCurrentView());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IAppCaptureStatics2> : produce_base<D, winrt::Windows::Media::Capture::IAppCaptureStatics2>
    {
        int32_t __stdcall SetAllowedAsync(bool allowed, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().SetAllowedAsync(allowed));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICameraCaptureUI> : produce_base<D, winrt::Windows::Media::Capture::ICameraCaptureUI>
    {
        int32_t __stdcall get_PhotoSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings>(this->shim().PhotoSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings>(this->shim().VideoSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CaptureFileAsync(int32_t mode, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Storage::StorageFile>>(this->shim().CaptureFileAsync(*reinterpret_cast<winrt::Windows::Media::Capture::CameraCaptureUIMode const*>(&mode)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings> : produce_base<D, winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings>
    {
        int32_t __stdcall get_Format(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat>(this->shim().Format());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Format(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Format(*reinterpret_cast<winrt::Windows::Media::Capture::CameraCaptureUIPhotoFormat const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxResolution(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution>(this->shim().MaxResolution());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxResolution(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxResolution(*reinterpret_cast<winrt::Windows::Media::Capture::CameraCaptureUIMaxPhotoResolution const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CroppedSizeInPixels(winrt::Windows::Foundation::Size* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Size>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Size>(this->shim().CroppedSizeInPixels());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_CroppedSizeInPixels(winrt::Windows::Foundation::Size value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CroppedSizeInPixels(*reinterpret_cast<winrt::Windows::Foundation::Size const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CroppedAspectRatio(winrt::Windows::Foundation::Size* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Size>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Size>(this->shim().CroppedAspectRatio());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_CroppedAspectRatio(winrt::Windows::Foundation::Size value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CroppedAspectRatio(*reinterpret_cast<winrt::Windows::Foundation::Size const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AllowCropping(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AllowCropping());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AllowCropping(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllowCropping(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings> : produce_base<D, winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings>
    {
        int32_t __stdcall get_Format(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat>(this->shim().Format());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Format(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Format(*reinterpret_cast<winrt::Windows::Media::Capture::CameraCaptureUIVideoFormat const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxResolution(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution>(this->shim().MaxResolution());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxResolution(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxResolution(*reinterpret_cast<winrt::Windows::Media::Capture::CameraCaptureUIMaxVideoResolution const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MaxDurationInSeconds(float* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<float>(this->shim().MaxDurationInSeconds());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MaxDurationInSeconds(float value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MaxDurationInSeconds(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AllowTrimming(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AllowTrimming());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AllowTrimming(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllowTrimming(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedFrame> : produce_base<D, winrt::Windows::Media::Capture::ICapturedFrame>
    {
        int32_t __stdcall get_Width(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Width());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Height(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Height());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedFrame2> : produce_base<D, winrt::Windows::Media::Capture::ICapturedFrame2>
    {
        int32_t __stdcall get_ControlValues(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrameControlValues>(this->shim().ControlValues());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BitmapProperties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Imaging::BitmapPropertySet>(this->shim().BitmapProperties());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedFrameControlValues> : produce_base<D, winrt::Windows::Media::Capture::ICapturedFrameControlValues>
    {
        int32_t __stdcall get_Exposure(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::TimeSpan>>(this->shim().Exposure());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExposureCompensation(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<float>>(this->shim().ExposureCompensation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsoSpeed(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<uint32_t>>(this->shim().IsoSpeed());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Focus(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<uint32_t>>(this->shim().Focus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SceneMode(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::CaptureSceneMode>>(this->shim().SceneMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Flashed(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<bool>>(this->shim().Flashed());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FlashPowerPercent(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<float>>(this->shim().FlashPowerPercent());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WhiteBalance(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<uint32_t>>(this->shim().WhiteBalance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ZoomFactor(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<float>>(this->shim().ZoomFactor());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedFrameControlValues2> : produce_base<D, winrt::Windows::Media::Capture::ICapturedFrameControlValues2>
    {
        int32_t __stdcall get_FocusState(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<winrt::Windows::Media::Devices::MediaCaptureFocusState>>(this->shim().FocusState());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsoDigitalGain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<double>>(this->shim().IsoDigitalGain());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsoAnalogGain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<double>>(this->shim().IsoAnalogGain());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SensorFrameRate(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::MediaProperties::MediaRatio>(this->shim().SensorFrameRate());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_WhiteBalanceGain(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<winrt::Windows::Media::Capture::WhiteBalanceGain>>(this->shim().WhiteBalanceGain());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap> : produce_base<D, winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap>
    {
        int32_t __stdcall get_SoftwareBitmap(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Imaging::SoftwareBitmap>(this->shim().SoftwareBitmap());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ICapturedPhoto> : produce_base<D, winrt::Windows::Media::Capture::ICapturedPhoto>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Thumbnail(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Thumbnail());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ILowLagMediaRecording> : produce_base<D, winrt::Windows::Media::Capture::ILowLagMediaRecording>
    {
        int32_t __stdcall StartAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StopAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FinishAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().FinishAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ILowLagMediaRecording2> : produce_base<D, winrt::Windows::Media::Capture::ILowLagMediaRecording2>
    {
        int32_t __stdcall PauseAsync(int32_t behavior, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().PauseAsync(*reinterpret_cast<winrt::Windows::Media::Devices::MediaCapturePauseBehavior const*>(&behavior)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ResumeAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ResumeAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ILowLagMediaRecording3> : produce_base<D, winrt::Windows::Media::Capture::ILowLagMediaRecording3>
    {
        int32_t __stdcall PauseWithResultAsync(int32_t behavior, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>>(this->shim().PauseWithResultAsync(*reinterpret_cast<winrt::Windows::Media::Devices::MediaCapturePauseBehavior const*>(&behavior)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopWithResultAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>>(this->shim().StopWithResultAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ILowLagPhotoCapture> : produce_base<D, winrt::Windows::Media::Capture::ILowLagPhotoCapture>
    {
        int32_t __stdcall CaptureAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::CapturedPhoto>>(this->shim().CaptureAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FinishAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().FinishAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture> : produce_base<D, winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture>
    {
        int32_t __stdcall StartAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StopAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FinishAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().FinishAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_PhotoCaptured(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().PhotoCaptured(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture, winrt::Windows::Media::Capture::PhotoCapturedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_PhotoCaptured(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PhotoCaptured(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture>
    {
        int32_t __stdcall InitializeAsync(void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InitializeAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall InitializeWithSettingsAsync(void* mediaCaptureInitializationSettings, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().InitializeAsync(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureInitializationSettings const*>(&mediaCaptureInitializationSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartRecordToStorageFileAsync(void* encodingProfile, void* file, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartRecordToStorageFileAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Storage::IStorageFile const*>(&file)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartRecordToStreamAsync(void* encodingProfile, void* stream, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartRecordToStreamAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&stream)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartRecordToCustomSinkAsync(void* encodingProfile, void* customMediaSink, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartRecordToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Media::IMediaExtension const*>(&customMediaSink)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartRecordToCustomSinkIdAsync(void* encodingProfile, void* customSinkActivationId, void* customSinkSettings, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartRecordToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<hstring const*>(&customSinkActivationId), *reinterpret_cast<winrt::Windows::Foundation::Collections::IPropertySet const*>(&customSinkSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopRecordAsync(void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StopRecordAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CapturePhotoToStorageFileAsync(void* type, void* file, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().CapturePhotoToStorageFileAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&type), *reinterpret_cast<winrt::Windows::Storage::IStorageFile const*>(&file)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CapturePhotoToStreamAsync(void* type, void* stream, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().CapturePhotoToStreamAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&type), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&stream)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddEffectAsync(int32_t mediaStreamType, void* effectActivationID, void* effectSettings, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().AddEffectAsync(*reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType), *reinterpret_cast<hstring const*>(&effectActivationID), *reinterpret_cast<winrt::Windows::Foundation::Collections::IPropertySet const*>(&effectSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ClearEffectsAsync(int32_t mediaStreamType, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ClearEffectsAsync(*reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetEncoderProperty(int32_t mediaStreamType, winrt::guid propertyId, void* propertyValue) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetEncoderProperty(*reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType), *reinterpret_cast<winrt::guid const*>(&propertyId), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&propertyValue));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetEncoderProperty(int32_t mediaStreamType, winrt::guid propertyId, void** propertyValue) noexcept final try
        {
            clear_abi(propertyValue);
            typename D::abi_guard guard(this->shim());
            *propertyValue = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().GetEncoderProperty(*reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType), *reinterpret_cast<winrt::guid const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_Failed(void* errorEventHandler, winrt::event_token* eventCookie) noexcept final try
        {
            zero_abi<winrt::event_token>(eventCookie);
            typename D::abi_guard guard(this->shim());
            *eventCookie = detach_from<winrt::event_token>(this->shim().Failed(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureFailedEventHandler const*>(&errorEventHandler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_Failed(winrt::event_token eventCookie) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Failed(*reinterpret_cast<winrt::event_token const*>(&eventCookie));
            return 0;
        }
        int32_t __stdcall add_RecordLimitationExceeded(void* recordLimitationExceededEventHandler, winrt::event_token* eventCookie) noexcept final try
        {
            zero_abi<winrt::event_token>(eventCookie);
            typename D::abi_guard guard(this->shim());
            *eventCookie = detach_from<winrt::event_token>(this->shim().RecordLimitationExceeded(*reinterpret_cast<winrt::Windows::Media::Capture::RecordLimitationExceededEventHandler const*>(&recordLimitationExceededEventHandler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_RecordLimitationExceeded(winrt::event_token eventCookie) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RecordLimitationExceeded(*reinterpret_cast<winrt::event_token const*>(&eventCookie));
            return 0;
        }
        int32_t __stdcall get_MediaCaptureSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureSettings>(this->shim().MediaCaptureSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AudioDeviceController(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Devices::AudioDeviceController>(this->shim().AudioDeviceController());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoDeviceController(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Devices::VideoDeviceController>(this->shim().VideoDeviceController());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetPreviewMirroring(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetPreviewMirroring(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPreviewMirroring(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().GetPreviewMirroring());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetPreviewRotation(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetPreviewRotation(*reinterpret_cast<winrt::Windows::Media::Capture::VideoRotation const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPreviewRotation(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::VideoRotation>(this->shim().GetPreviewRotation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetRecordRotation(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetRecordRotation(*reinterpret_cast<winrt::Windows::Media::Capture::VideoRotation const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetRecordRotation(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::VideoRotation>(this->shim().GetRecordRotation());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture2> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture2>
    {
        int32_t __stdcall PrepareLowLagRecordToStorageFileAsync(void* encodingProfile, void* file, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>>(this->shim().PrepareLowLagRecordToStorageFileAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Storage::IStorageFile const*>(&file)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareLowLagRecordToStreamAsync(void* encodingProfile, void* stream, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>>(this->shim().PrepareLowLagRecordToStreamAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Storage::Streams::IRandomAccessStream const*>(&stream)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareLowLagRecordToCustomSinkAsync(void* encodingProfile, void* customMediaSink, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>>(this->shim().PrepareLowLagRecordToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Media::IMediaExtension const*>(&customMediaSink)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareLowLagRecordToCustomSinkIdAsync(void* encodingProfile, void* customSinkActivationId, void* customSinkSettings, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagMediaRecording>>(this->shim().PrepareLowLagRecordToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<hstring const*>(&customSinkActivationId), *reinterpret_cast<winrt::Windows::Foundation::Collections::IPropertySet const*>(&customSinkSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareLowLagPhotoCaptureAsync(void* type, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoCapture>>(this->shim().PrepareLowLagPhotoCaptureAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&type)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareLowLagPhotoSequenceCaptureAsync(void* type, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture>>(this->shim().PrepareLowLagPhotoSequenceCaptureAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&type)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetEncodingPropertiesAsync(int32_t mediaStreamType, void* mediaEncodingProperties, void* encoderProperties, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().SetEncodingPropertiesAsync(*reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType), *reinterpret_cast<winrt::Windows::Media::MediaProperties::IMediaEncodingProperties const*>(&mediaEncodingProperties), *reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaPropertySet const*>(&encoderProperties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture3> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture3>
    {
        int32_t __stdcall PrepareVariablePhotoSequenceCaptureAsync(void* type, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Core::VariablePhotoSequenceCapture>>(this->shim().PrepareVariablePhotoSequenceCaptureAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&type)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_FocusChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().FocusChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_FocusChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().FocusChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_PhotoConfirmationCaptured(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().PhotoConfirmationCaptured(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_PhotoConfirmationCaptured(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PhotoConfirmationCaptured(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture4> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture4>
    {
        int32_t __stdcall AddAudioEffectAsync(void* definition, void** op) noexcept final try
        {
            clear_abi(op);
            typename D::abi_guard guard(this->shim());
            *op = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>>(this->shim().AddAudioEffectAsync(*reinterpret_cast<winrt::Windows::Media::Effects::IAudioEffectDefinition const*>(&definition)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddVideoEffectAsync(void* definition, int32_t mediaStreamType, void** op) noexcept final try
        {
            clear_abi(op);
            typename D::abi_guard guard(this->shim());
            *op = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::IMediaExtension>>(this->shim().AddVideoEffectAsync(*reinterpret_cast<winrt::Windows::Media::Effects::IVideoEffectDefinition const*>(&definition), *reinterpret_cast<winrt::Windows::Media::Capture::MediaStreamType const*>(&mediaStreamType)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PauseRecordAsync(int32_t behavior, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().PauseRecordAsync(*reinterpret_cast<winrt::Windows::Media::Devices::MediaCapturePauseBehavior const*>(&behavior)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ResumeRecordAsync(void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ResumeRecordAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_CameraStreamStateChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().CameraStreamStateChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_CameraStreamStateChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CameraStreamStateChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall get_CameraStreamState(int32_t* streamState) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *streamState = detach_from<winrt::Windows::Media::Devices::CameraStreamState>(this->shim().CameraStreamState());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPreviewFrameAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>>(this->shim().GetPreviewFrameAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPreviewFrameCopyAsync(void* destination, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::VideoFrame>>(this->shim().GetPreviewFrameAsync(*reinterpret_cast<winrt::Windows::Media::VideoFrame const*>(&destination)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_ThermalStatusChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().ThermalStatusChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_ThermalStatusChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ThermalStatusChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall get_ThermalStatus(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureThermalStatus>(this->shim().ThermalStatus());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareAdvancedPhotoCaptureAsync(void* encodingProperties, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::AdvancedPhotoCapture>>(this->shim().PrepareAdvancedPhotoCaptureAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::ImageEncodingProperties const*>(&encodingProperties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture5> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture5>
    {
        int32_t __stdcall RemoveEffectAsync(void* effect, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().RemoveEffectAsync(*reinterpret_cast<winrt::Windows::Media::IMediaExtension const*>(&effect)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PauseRecordWithResultAsync(int32_t behavior, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCapturePauseResult>>(this->shim().PauseRecordWithResultAsync(*reinterpret_cast<winrt::Windows::Media::Devices::MediaCapturePauseBehavior const*>(&behavior)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopRecordWithResultAsync(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::MediaCaptureStopResult>>(this->shim().StopRecordWithResultAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FrameSources(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IMapView<hstring, winrt::Windows::Media::Capture::Frames::MediaFrameSource>>(this->shim().FrameSources());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateFrameReaderAsync(void* inputSource, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>>(this->shim().CreateFrameReaderAsync(*reinterpret_cast<winrt::Windows::Media::Capture::Frames::MediaFrameSource const*>(&inputSource)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateFrameReaderWithSubtypeAsync(void* inputSource, void* outputSubtype, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>>(this->shim().CreateFrameReaderAsync(*reinterpret_cast<winrt::Windows::Media::Capture::Frames::MediaFrameSource const*>(&inputSource), *reinterpret_cast<hstring const*>(&outputSubtype)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateFrameReaderWithSubtypeAndSizeAsync(void* inputSource, void* outputSubtype, struct struct_Windows_Graphics_Imaging_BitmapSize outputSize, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MediaFrameReader>>(this->shim().CreateFrameReaderAsync(*reinterpret_cast<winrt::Windows::Media::Capture::Frames::MediaFrameSource const*>(&inputSource), *reinterpret_cast<hstring const*>(&outputSubtype), *reinterpret_cast<winrt::Windows::Graphics::Imaging::BitmapSize const*>(&outputSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture6> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture6>
    {
        int32_t __stdcall add_CaptureDeviceExclusiveControlStatusChanged(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().CaptureDeviceExclusiveControlStatusChanged(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCapture, winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_CaptureDeviceExclusiveControlStatusChanged(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CaptureDeviceExclusiveControlStatusChanged(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall CreateMultiSourceFrameReaderAsync(void* inputSources, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IAsyncOperation<winrt::Windows::Media::Capture::Frames::MultiSourceMediaFrameReader>>(this->shim().CreateMultiSourceFrameReaderAsync(*reinterpret_cast<winrt::Windows::Foundation::Collections::IIterable<winrt::Windows::Media::Capture::Frames::MediaFrameSource> const*>(&inputSources)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapture7> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapture7>
    {
        int32_t __stdcall CreateRelativePanelWatcher(int32_t captureMode, void* displayRegion, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher>(this->shim().CreateRelativePanelWatcher(*reinterpret_cast<winrt::Windows::Media::Capture::StreamingCaptureMode const*>(&captureMode), *reinterpret_cast<winrt::Windows::UI::WindowManagement::DisplayRegion const*>(&displayRegion)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs>
    {
        int32_t __stdcall get_DeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Status(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatus>(this->shim().Status());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs>
    {
        int32_t __stdcall get_Message(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Message());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Code(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Code());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs>
    {
        int32_t __stdcall get_FocusState(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Devices::MediaCaptureFocusState>(this->shim().FocusState());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings>
    {
        int32_t __stdcall put_AudioDeviceId(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AudioDeviceId(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AudioDeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().AudioDeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_VideoDeviceId(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().VideoDeviceId(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoDeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().VideoDeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_StreamingCaptureMode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().StreamingCaptureMode(*reinterpret_cast<winrt::Windows::Media::Capture::StreamingCaptureMode const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_StreamingCaptureMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::StreamingCaptureMode>(this->shim().StreamingCaptureMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PhotoCaptureSource(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PhotoCaptureSource(*reinterpret_cast<winrt::Windows::Media::Capture::PhotoCaptureSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PhotoCaptureSource(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::PhotoCaptureSource>(this->shim().PhotoCaptureSource());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2>
    {
        int32_t __stdcall put_MediaCategory(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MediaCategory(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCategory const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MediaCategory(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCategory>(this->shim().MediaCategory());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AudioProcessing(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AudioProcessing(*reinterpret_cast<winrt::Windows::Media::AudioProcessing const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AudioProcessing(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::AudioProcessing>(this->shim().AudioProcessing());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3>
    {
        int32_t __stdcall put_AudioSource(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AudioSource(*reinterpret_cast<winrt::Windows::Media::Core::IMediaSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AudioSource(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Core::IMediaSource>(this->shim().AudioSource());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_VideoSource(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().VideoSource(*reinterpret_cast<winrt::Windows::Media::Core::IMediaSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoSource(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Core::IMediaSource>(this->shim().VideoSource());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4>
    {
        int32_t __stdcall get_VideoProfile(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>(this->shim().VideoProfile());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_VideoProfile(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().VideoProfile(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureVideoProfile const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PreviewMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>(this->shim().PreviewMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PreviewMediaDescription(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PreviewMediaDescription(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RecordMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>(this->shim().RecordMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RecordMediaDescription(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RecordMediaDescription(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PhotoMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>(this->shim().PhotoMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_PhotoMediaDescription(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PhotoMediaDescription(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5>
    {
        int32_t __stdcall get_SourceGroup(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup>(this->shim().SourceGroup());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SourceGroup(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SourceGroup(*reinterpret_cast<winrt::Windows::Media::Capture::Frames::MediaFrameSourceGroup const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SharingMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureSharingMode>(this->shim().SharingMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SharingMode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SharingMode(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureSharingMode const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MemoryPreference(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCaptureMemoryPreference>(this->shim().MemoryPreference());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MemoryPreference(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MemoryPreference(*reinterpret_cast<winrt::Windows::Media::Capture::MediaCaptureMemoryPreference const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6>
    {
        int32_t __stdcall get_AlwaysPlaySystemShutterSound(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AlwaysPlaySystemShutterSound());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AlwaysPlaySystemShutterSound(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AlwaysPlaySystemShutterSound(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7>
    {
        int32_t __stdcall get_DeviceUriPasswordCredential(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Security::Credentials::PasswordCredential>(this->shim().DeviceUriPasswordCredential());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DeviceUriPasswordCredential(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeviceUriPasswordCredential(*reinterpret_cast<winrt::Windows::Security::Credentials::PasswordCredential const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DeviceUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Uri>(this->shim().DeviceUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DeviceUri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DeviceUri(*reinterpret_cast<winrt::Windows::Foundation::Uri const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCapturePauseResult> : produce_base<D, winrt::Windows::Media::Capture::IMediaCapturePauseResult>
    {
        int32_t __stdcall get_LastFrame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::VideoFrame>(this->shim().LastFrame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RecordDuration(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().RecordDuration());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher>
    {
        int32_t __stdcall get_RelativePanel(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Devices::Enumeration::Panel>(this->shim().RelativePanel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_Changed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().Changed(*reinterpret_cast<winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher, winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_Changed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Changed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall Start() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Start();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Stop() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Stop();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureSettings> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureSettings>
    {
        int32_t __stdcall get_AudioDeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().AudioDeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoDeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().VideoDeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_StreamingCaptureMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::StreamingCaptureMode>(this->shim().StreamingCaptureMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PhotoCaptureSource(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::PhotoCaptureSource>(this->shim().PhotoCaptureSource());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoDeviceCharacteristic(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::VideoDeviceCharacteristic>(this->shim().VideoDeviceCharacteristic());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureSettings2> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureSettings2>
    {
        int32_t __stdcall get_ConcurrentRecordAndPhotoSupported(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ConcurrentRecordAndPhotoSupported());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ConcurrentRecordAndPhotoSequenceSupported(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().ConcurrentRecordAndPhotoSequenceSupported());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CameraSoundRequiredForRegion(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().CameraSoundRequiredForRegion());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Horizontal35mmEquivalentFocalLength(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<uint32_t>>(this->shim().Horizontal35mmEquivalentFocalLength());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PitchOffsetDegrees(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<int32_t>>(this->shim().PitchOffsetDegrees());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Vertical35mmEquivalentFocalLength(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<uint32_t>>(this->shim().Vertical35mmEquivalentFocalLength());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MediaCategory(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::MediaCategory>(this->shim().MediaCategory());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AudioProcessing(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::AudioProcessing>(this->shim().AudioProcessing());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureSettings3> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureSettings3>
    {
        int32_t __stdcall get_Direct3D11Device(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice>(this->shim().Direct3D11Device());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureStatics> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureStatics>
    {
        int32_t __stdcall IsVideoProfileSupported(void* videoDeviceId, bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsVideoProfileSupported(*reinterpret_cast<hstring const*>(&videoDeviceId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FindAllVideoProfiles(void* videoDeviceId, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>>(this->shim().FindAllVideoProfiles(*reinterpret_cast<hstring const*>(&videoDeviceId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FindConcurrentProfiles(void* videoDeviceId, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>>(this->shim().FindConcurrentProfiles(*reinterpret_cast<hstring const*>(&videoDeviceId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall FindKnownVideoProfiles(void* videoDeviceId, int32_t name, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>>(this->shim().FindKnownVideoProfiles(*reinterpret_cast<hstring const*>(&videoDeviceId), *reinterpret_cast<winrt::Windows::Media::Capture::KnownVideoProfile const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureStopResult> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureStopResult>
    {
        int32_t __stdcall get_LastFrame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::VideoFrame>(this->shim().LastFrame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RecordDuration(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().RecordDuration());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureVideoPreview> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureVideoPreview>
    {
        int32_t __stdcall StartPreviewAsync(void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartPreviewAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartPreviewToCustomSinkAsync(void* encodingProfile, void* customMediaSink, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartPreviewToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<winrt::Windows::Media::IMediaExtension const*>(&customMediaSink)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StartPreviewToCustomSinkIdAsync(void* encodingProfile, void* customSinkActivationId, void* customSinkSettings, void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StartPreviewToCustomSinkAsync(*reinterpret_cast<winrt::Windows::Media::MediaProperties::MediaEncodingProfile const*>(&encodingProfile), *reinterpret_cast<hstring const*>(&customSinkActivationId), *reinterpret_cast<winrt::Windows::Foundation::Collections::IPropertySet const*>(&customSinkSettings)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StopPreviewAsync(void** asyncInfo) noexcept final try
        {
            clear_abi(asyncInfo);
            typename D::abi_guard guard(this->shim());
            *asyncInfo = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().StopPreviewAsync());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfile> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfile>
    {
        int32_t __stdcall get_Id(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Id());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_VideoDeviceId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().VideoDeviceId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SupportedPreviewMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>>(this->shim().SupportedPreviewMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SupportedRecordMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>>(this->shim().SupportedRecordMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SupportedPhotoMediaDescription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription>>(this->shim().SupportedPhotoMediaDescription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetConcurrency(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::MediaCaptureVideoProfile>>(this->shim().GetConcurrency());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2>
    {
        int32_t __stdcall get_FrameSourceInfos(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Windows::Media::Capture::Frames::MediaFrameSourceInfo>>(this->shim().FrameSourceInfos());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Properties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>>(this->shim().Properties());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription>
    {
        int32_t __stdcall get_Width(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Width());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Height(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Height());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FrameRate(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().FrameRate());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsVariablePhotoSequenceSupported(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsVariablePhotoSequenceSupported());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsHdrVideoSupported(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsHdrVideoSupported());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2> : produce_base<D, winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2>
    {
        int32_t __stdcall get_Subtype(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Subtype());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Properties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IMapView<winrt::guid, winrt::Windows::Foundation::IInspectable>>(this->shim().Properties());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IPhotoCapturedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IPhotoCapturedEventArgs>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Thumbnail(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Thumbnail());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CaptureTimeOffset(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().CaptureTimeOffset());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs> : produce_base<D, winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs>
    {
        int32_t __stdcall get_Frame(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::Capture::CapturedFrame>(this->shim().Frame());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CaptureTimeOffset(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().CaptureTimeOffset());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Windows::Media::Capture::IVideoStreamConfiguration> : produce_base<D, winrt::Windows::Media::Capture::IVideoStreamConfiguration>
    {
        int32_t __stdcall get_InputProperties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::MediaProperties::VideoEncodingProperties>(this->shim().InputProperties());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_OutputProperties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Media::MediaProperties::VideoEncodingProperties>(this->shim().OutputProperties());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Windows::Media::Capture
{
    inline auto AppCapture::GetForCurrentView()
    {
        return impl::call_factory_cast<winrt::Windows::Media::Capture::AppCapture(*)(IAppCaptureStatics const&), AppCapture, IAppCaptureStatics>([](IAppCaptureStatics const& f) { return f.GetForCurrentView(); });
    }
    inline auto AppCapture::SetAllowedAsync(bool allowed)
    {
        return impl::call_factory<AppCapture, IAppCaptureStatics2>([&](IAppCaptureStatics2 const& f) { return f.SetAllowedAsync(allowed); });
    }
    inline CameraCaptureUI::CameraCaptureUI() :
        CameraCaptureUI(impl::call_factory_cast<CameraCaptureUI(*)(winrt::Windows::Foundation::IActivationFactory const&), CameraCaptureUI>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<CameraCaptureUI>(); }))
    {
    }
    inline MediaCapture::MediaCapture() :
        MediaCapture(impl::call_factory_cast<MediaCapture(*)(winrt::Windows::Foundation::IActivationFactory const&), MediaCapture>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<MediaCapture>(); }))
    {
    }
    inline auto MediaCapture::IsVideoProfileSupported(param::hstring const& videoDeviceId)
    {
        return impl::call_factory<MediaCapture, IMediaCaptureStatics>([&](IMediaCaptureStatics const& f) { return f.IsVideoProfileSupported(videoDeviceId); });
    }
    inline auto MediaCapture::FindAllVideoProfiles(param::hstring const& videoDeviceId)
    {
        return impl::call_factory<MediaCapture, IMediaCaptureStatics>([&](IMediaCaptureStatics const& f) { return f.FindAllVideoProfiles(videoDeviceId); });
    }
    inline auto MediaCapture::FindConcurrentProfiles(param::hstring const& videoDeviceId)
    {
        return impl::call_factory<MediaCapture, IMediaCaptureStatics>([&](IMediaCaptureStatics const& f) { return f.FindConcurrentProfiles(videoDeviceId); });
    }
    inline auto MediaCapture::FindKnownVideoProfiles(param::hstring const& videoDeviceId, winrt::Windows::Media::Capture::KnownVideoProfile const& name)
    {
        return impl::call_factory<MediaCapture, IMediaCaptureStatics>([&](IMediaCaptureStatics const& f) { return f.FindKnownVideoProfiles(videoDeviceId, name); });
    }
    inline MediaCaptureInitializationSettings::MediaCaptureInitializationSettings() :
        MediaCaptureInitializationSettings(impl::call_factory_cast<MediaCaptureInitializationSettings(*)(winrt::Windows::Foundation::IActivationFactory const&), MediaCaptureInitializationSettings>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<MediaCaptureInitializationSettings>(); }))
    {
    }
    template <typename L> MediaCaptureFailedEventHandler::MediaCaptureFailedEventHandler(L handler) :
        MediaCaptureFailedEventHandler(impl::make_delegate<MediaCaptureFailedEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> MediaCaptureFailedEventHandler::MediaCaptureFailedEventHandler(F* handler) :
        MediaCaptureFailedEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> MediaCaptureFailedEventHandler::MediaCaptureFailedEventHandler(O* object, M method) :
        MediaCaptureFailedEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> MediaCaptureFailedEventHandler::MediaCaptureFailedEventHandler(com_ptr<O>&& object, M method) :
        MediaCaptureFailedEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> MediaCaptureFailedEventHandler::MediaCaptureFailedEventHandler(weak_ref<O>&& object, M method) :
        MediaCaptureFailedEventHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto MediaCaptureFailedEventHandler::operator()(winrt::Windows::Media::Capture::MediaCapture const& sender, winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs const& errorEventArgs) const
    {
        check_hresult((*(impl::abi_t<MediaCaptureFailedEventHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&errorEventArgs)));
    }
    template <typename L> RecordLimitationExceededEventHandler::RecordLimitationExceededEventHandler(L handler) :
        RecordLimitationExceededEventHandler(impl::make_delegate<RecordLimitationExceededEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> RecordLimitationExceededEventHandler::RecordLimitationExceededEventHandler(F* handler) :
        RecordLimitationExceededEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> RecordLimitationExceededEventHandler::RecordLimitationExceededEventHandler(O* object, M method) :
        RecordLimitationExceededEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> RecordLimitationExceededEventHandler::RecordLimitationExceededEventHandler(com_ptr<O>&& object, M method) :
        RecordLimitationExceededEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> RecordLimitationExceededEventHandler::RecordLimitationExceededEventHandler(weak_ref<O>&& object, M method) :
        RecordLimitationExceededEventHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto RecordLimitationExceededEventHandler::operator()(winrt::Windows::Media::Capture::MediaCapture const& sender) const
    {
        check_hresult((*(impl::abi_t<RecordLimitationExceededEventHandler>**)this)->Invoke(*(void**)(&sender)));
    }
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IAdvancedCapturedPhoto2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IAdvancedPhotoCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IAppCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IAppCaptureStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IAppCaptureStatics2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICameraCaptureUI> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICameraCaptureUIPhotoCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICameraCaptureUIVideoCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedFrame> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedFrame2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedFrameControlValues> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedFrameControlValues2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedFrameWithSoftwareBitmap> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ICapturedPhoto> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ILowLagMediaRecording> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ILowLagMediaRecording2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ILowLagMediaRecording3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ILowLagPhotoCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::ILowLagPhotoSequenceCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture5> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture6> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapture7> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureDeviceExclusiveControlStatusChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureFocusChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings4> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings5> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings6> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureInitializationSettings7> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCapturePauseResult> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureRelativePanelWatcher> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureSettings2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureSettings3> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureStopResult> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureVideoPreview> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureVideoProfile2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IMediaCaptureVideoProfileMediaDescription2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IOptionalReferencePhotoCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IPhotoCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IPhotoConfirmationCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::IVideoStreamConfiguration> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::AdvancedCapturedPhoto> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::AdvancedPhotoCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::AppCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CameraCaptureUI> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CameraCaptureUIPhotoCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CameraCaptureUIVideoCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CapturedFrame> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CapturedFrameControlValues> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::CapturedPhoto> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::LowLagMediaRecording> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::LowLagPhotoCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::LowLagPhotoSequenceCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCapture> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureDeviceExclusiveControlStatusChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureFailedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureFocusChangedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureInitializationSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCapturePauseResult> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureRelativePanelWatcher> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureStopResult> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureVideoProfile> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::MediaCaptureVideoProfileMediaDescription> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::OptionalReferencePhotoCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::PhotoCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::PhotoConfirmationCapturedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Windows::Media::Capture::VideoStreamConfiguration> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
