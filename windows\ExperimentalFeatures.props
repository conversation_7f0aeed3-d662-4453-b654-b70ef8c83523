<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!--
    This file contains some important settings that will apply globally for
    your app and *all* native modules your app consumes. These values were
    set when you created the app project, and in some cases cannot be
    simply changed here without recreating a new project.
  -->

  <PropertyGroup Label="Microsoft.ReactNative Experimental Features">
    <!--
      Enables default usage of Hermes.
      
      See https://microsoft.github.io/react-native-windows/docs/hermes
    -->
    <UseHermes>true</UseHermes>

    <!--
      Changes compilation to assume use of Microsoft.ReactNative NuGet packages
      instead of building the framework from source.
      Requires creation of new project.

      See https://microsoft.github.io/react-native-windows/docs/nuget
    -->
    <UseExperimentalNuget>false</UseExperimentalNuget>

    <ReactExperimentalFeaturesSet>true</ReactExperimentalFeaturesSet>
  
  </PropertyGroup>

</Project>
