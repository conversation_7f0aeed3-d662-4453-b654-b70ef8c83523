﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.windows.cppwinrt\2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.cppwinrt\2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.web.webview2\1.0.1264.42\build\native\Microsoft.Web.WebView2.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.web.webview2\1.0.1264.42\build\native\Microsoft.Web.WebView2.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ui.xaml\2.8.0\build\native\Microsoft.UI.Xaml.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ui.xaml\2.8.0\build\native\Microsoft.UI.Xaml.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9\build\native\Microsoft.JavaScript.Hermes.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9\build\native\Microsoft.JavaScript.Hermes.targets')" />
  </ImportGroup>
</Project>