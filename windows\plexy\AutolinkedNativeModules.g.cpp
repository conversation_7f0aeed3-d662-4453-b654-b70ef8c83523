// AutolinkedNativeModules.g.cpp contents generated by "npx @react-native-community/cli autolink-windows"
// clang-format off
#include "pch.h"
#include "AutolinkedNativeModules.g.h"

namespace winrt::Microsoft::ReactNative
{

void RegisterAutolinkedNativeModulePackages(winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider> const& packageProviders)
{ 
    UNREFERENCED_PARAMETER(packageProviders);
}

}
