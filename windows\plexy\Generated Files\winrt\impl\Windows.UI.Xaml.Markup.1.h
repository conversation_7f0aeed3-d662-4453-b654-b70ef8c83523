// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Markup_1_H
#define WINRT_Windows_UI_Xaml_Markup_1_H
#include "winrt/impl/Windows.UI.Xaml.Markup.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Markup
{
    struct WINRT_IMPL_EMPTY_BASES IComponentConnector :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComponentConnector>
    {
        IComponentConnector(std::nullptr_t = nullptr) noexcept {}
        IComponentConnector(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IComponentConnector2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComponentConnector2>
    {
        IComponentConnector2(std::nullptr_t = nullptr) noexcept {}
        IComponentConnector2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDataTemplateComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDataTemplateComponent>
    {
        IDataTemplateComponent(std::nullptr_t = nullptr) noexcept {}
        IDataTemplateComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMarkupExtension :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMarkupExtension>
    {
        IMarkupExtension(std::nullptr_t = nullptr) noexcept {}
        IMarkupExtension(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMarkupExtensionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMarkupExtensionFactory>
    {
        IMarkupExtensionFactory(std::nullptr_t = nullptr) noexcept {}
        IMarkupExtensionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMarkupExtensionOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMarkupExtensionOverrides>
    {
        IMarkupExtensionOverrides(std::nullptr_t = nullptr) noexcept {}
        IMarkupExtensionOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlBinaryWriter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlBinaryWriter>
    {
        IXamlBinaryWriter(std::nullptr_t = nullptr) noexcept {}
        IXamlBinaryWriter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlBinaryWriterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlBinaryWriterStatics>
    {
        IXamlBinaryWriterStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlBinaryWriterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlBindScopeDiagnostics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlBindScopeDiagnostics>
    {
        IXamlBindScopeDiagnostics(std::nullptr_t = nullptr) noexcept {}
        IXamlBindScopeDiagnostics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlBindingHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlBindingHelper>
    {
        IXamlBindingHelper(std::nullptr_t = nullptr) noexcept {}
        IXamlBindingHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlBindingHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlBindingHelperStatics>
    {
        IXamlBindingHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlBindingHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlMarkupHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlMarkupHelper>
    {
        IXamlMarkupHelper(std::nullptr_t = nullptr) noexcept {}
        IXamlMarkupHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlMarkupHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlMarkupHelperStatics>
    {
        IXamlMarkupHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlMarkupHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlMember :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlMember>
    {
        IXamlMember(std::nullptr_t = nullptr) noexcept {}
        IXamlMember(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlMetadataProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlMetadataProvider>
    {
        IXamlMetadataProvider(std::nullptr_t = nullptr) noexcept {}
        IXamlMetadataProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlReader :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlReader>
    {
        IXamlReader(std::nullptr_t = nullptr) noexcept {}
        IXamlReader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlReaderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlReaderStatics>
    {
        IXamlReaderStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlReaderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlType :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlType>
    {
        IXamlType(std::nullptr_t = nullptr) noexcept {}
        IXamlType(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlType2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlType2>,
        impl::require<winrt::Windows::UI::Xaml::Markup::IXamlType2, winrt::Windows::UI::Xaml::Markup::IXamlType>
    {
        IXamlType2(std::nullptr_t = nullptr) noexcept {}
        IXamlType2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
