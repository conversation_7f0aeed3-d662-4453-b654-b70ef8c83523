// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_2_H
#define WINRT_Microsoft_UI_Xaml_Controls_AnimatedVisuals_2_H
#include "winrt/impl/Microsoft.UI.Xaml.Controls.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.AnimatedVisuals.1.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::AnimatedVisuals
{
    struct WINRT_IMPL_EMPTY_BASES AnimatedAcceptVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedAcceptVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedAcceptVisualSource(std::nullptr_t) noexcept {}
        AnimatedAcceptVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedAcceptVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedBackVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedBackVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedBackVisualSource(std::nullptr_t) noexcept {}
        AnimatedBackVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedBackVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedChevronDownSmallVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedChevronDownSmallVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedChevronDownSmallVisualSource(std::nullptr_t) noexcept {}
        AnimatedChevronDownSmallVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedChevronDownSmallVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedChevronRightDownSmallVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedChevronRightDownSmallVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedChevronRightDownSmallVisualSource(std::nullptr_t) noexcept {}
        AnimatedChevronRightDownSmallVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedChevronRightDownSmallVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedChevronUpDownSmallVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedChevronUpDownSmallVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedChevronUpDownSmallVisualSource(std::nullptr_t) noexcept {}
        AnimatedChevronUpDownSmallVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedChevronUpDownSmallVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedFindVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedFindVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedFindVisualSource(std::nullptr_t) noexcept {}
        AnimatedFindVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedFindVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedGlobalNavigationButtonVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedGlobalNavigationButtonVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedGlobalNavigationButtonVisualSource(std::nullptr_t) noexcept {}
        AnimatedGlobalNavigationButtonVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedGlobalNavigationButtonVisualSource();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedSettingsVisualSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource,
        impl::require<AnimatedSettingsVisualSource, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource2>
    {
        AnimatedSettingsVisualSource(std::nullptr_t) noexcept {}
        AnimatedSettingsVisualSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualSource(ptr, take_ownership_from_abi) {}
        AnimatedSettingsVisualSource();
    };
}
#endif
