// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_2_H
#define WINRT_Microsoft_UI_Xaml_Controls_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Windows.Foundation.Collections.1.h"
#include "winrt/impl/Windows.UI.Composition.1.h"
#include "winrt/impl/Windows.UI.Xaml.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.Primitives.1.h"
#include "winrt/impl/Windows.UI.Xaml.Interop.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.1.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls
{
    struct WINRT_IMPL_EMPTY_BASES AnimatedIcon : winrt::Microsoft::UI::Xaml::Controls::IAnimatedIcon,
        impl::base<AnimatedIcon, winrt::Windows::UI::Xaml::Controls::IconElement, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AnimatedIcon, winrt::Windows::UI::Xaml::Controls::IIconElement, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AnimatedIcon(std::nullptr_t) noexcept {}
        AnimatedIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedIcon(ptr, take_ownership_from_abi) {}
        AnimatedIcon();
        [[nodiscard]] static auto StateProperty();
        static auto SetState(winrt::Windows::UI::Xaml::DependencyObject const& object, param::hstring const& value);
        static auto GetState(winrt::Windows::UI::Xaml::DependencyObject const& object);
        [[nodiscard]] static auto SourceProperty();
        [[nodiscard]] static auto FallbackIconSourceProperty();
        [[nodiscard]] static auto MirroredWhenRightToLeftProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedIconSource : winrt::Microsoft::UI::Xaml::Controls::IAnimatedIconSource,
        impl::base<AnimatedIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AnimatedIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AnimatedIconSource(std::nullptr_t) noexcept {}
        AnimatedIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedIconSource(ptr, take_ownership_from_abi) {}
        AnimatedIconSource();
        [[nodiscard]] static auto SourceProperty();
        [[nodiscard]] static auto FallbackIconSourceProperty();
        [[nodiscard]] static auto MirroredWhenRightToLeftProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES AnimatedVisualPlayer : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualPlayer,
        impl::base<AnimatedVisualPlayer, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AnimatedVisualPlayer, winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualPlayer2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AnimatedVisualPlayer(std::nullptr_t) noexcept {}
        AnimatedVisualPlayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IAnimatedVisualPlayer(ptr, take_ownership_from_abi) {}
        AnimatedVisualPlayer();
        [[nodiscard]] static auto AutoPlayProperty();
        [[nodiscard]] static auto DiagnosticsProperty();
        [[nodiscard]] static auto DurationProperty();
        [[nodiscard]] static auto FallbackContentProperty();
        [[nodiscard]] static auto IsAnimatedVisualLoadedProperty();
        [[nodiscard]] static auto IsPlayingProperty();
        [[nodiscard]] static auto PlaybackRateProperty();
        [[nodiscard]] static auto SourceProperty();
        [[nodiscard]] static auto StretchProperty();
        [[nodiscard]] static auto AnimationOptimizationProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES BackdropMaterial : winrt::Microsoft::UI::Xaml::Controls::IBackdropMaterial,
        impl::base<BackdropMaterial, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<BackdropMaterial, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        BackdropMaterial(std::nullptr_t) noexcept {}
        BackdropMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IBackdropMaterial(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto ApplyToRootOrPageBackgroundProperty();
        static auto SetApplyToRootOrPageBackground(winrt::Windows::UI::Xaml::Controls::Control const& element, bool value);
        static auto GetApplyToRootOrPageBackground(winrt::Windows::UI::Xaml::Controls::Control const& element);
    };
    struct WINRT_IMPL_EMPTY_BASES BitmapIconSource : winrt::Microsoft::UI::Xaml::Controls::IBitmapIconSource,
        impl::base<BitmapIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<BitmapIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        BitmapIconSource(std::nullptr_t) noexcept {}
        BitmapIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IBitmapIconSource(ptr, take_ownership_from_abi) {}
        BitmapIconSource();
        [[nodiscard]] static auto UriSourceProperty();
        [[nodiscard]] static auto ShowAsMonochromeProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES BreadcrumbBar : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBar,
        impl::base<BreadcrumbBar, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<BreadcrumbBar, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        BreadcrumbBar(std::nullptr_t) noexcept {}
        BreadcrumbBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBar(ptr, take_ownership_from_abi) {}
        BreadcrumbBar();
        [[nodiscard]] static auto ItemsSourceProperty();
        [[nodiscard]] static auto ItemTemplateProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES BreadcrumbBarItem : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBarItem,
        impl::base<BreadcrumbBarItem, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<BreadcrumbBarItem, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        BreadcrumbBarItem(std::nullptr_t) noexcept {}
        BreadcrumbBarItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBarItem(ptr, take_ownership_from_abi) {}
        BreadcrumbBarItem();
    };
    struct WINRT_IMPL_EMPTY_BASES BreadcrumbBarItemClickedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBarItemClickedEventArgs
    {
        BreadcrumbBarItemClickedEventArgs(std::nullptr_t) noexcept {}
        BreadcrumbBarItemClickedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IBreadcrumbBarItemClickedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ColorChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IColorChangedEventArgs
    {
        ColorChangedEventArgs(std::nullptr_t) noexcept {}
        ColorChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IColorChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ColorPicker : winrt::Microsoft::UI::Xaml::Controls::IColorPicker,
        impl::base<ColorPicker, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorPicker, winrt::Microsoft::UI::Xaml::Controls::IColorPicker2, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorPicker(std::nullptr_t) noexcept {}
        ColorPicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IColorPicker(ptr, take_ownership_from_abi) {}
        ColorPicker();
        [[nodiscard]] static auto ColorProperty();
        [[nodiscard]] static auto PreviousColorProperty();
        [[nodiscard]] static auto IsAlphaEnabledProperty();
        [[nodiscard]] static auto IsColorSpectrumVisibleProperty();
        [[nodiscard]] static auto IsColorPreviewVisibleProperty();
        [[nodiscard]] static auto IsColorSliderVisibleProperty();
        [[nodiscard]] static auto IsAlphaSliderVisibleProperty();
        [[nodiscard]] static auto IsMoreButtonVisibleProperty();
        [[nodiscard]] static auto IsColorChannelTextInputVisibleProperty();
        [[nodiscard]] static auto IsAlphaTextInputVisibleProperty();
        [[nodiscard]] static auto IsHexInputVisibleProperty();
        [[nodiscard]] static auto MinHueProperty();
        [[nodiscard]] static auto MaxHueProperty();
        [[nodiscard]] static auto MinSaturationProperty();
        [[nodiscard]] static auto MaxSaturationProperty();
        [[nodiscard]] static auto MinValueProperty();
        [[nodiscard]] static auto MaxValueProperty();
        [[nodiscard]] static auto ColorSpectrumShapeProperty();
        [[nodiscard]] static auto ColorSpectrumComponentsProperty();
        [[nodiscard]] static auto OrientationProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES CommandBarFlyout : winrt::Microsoft::UI::Xaml::Controls::ICommandBarFlyout,
        impl::base<CommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::FlyoutBase, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CommandBarFlyout, winrt::Microsoft::UI::Xaml::Controls::ICommandBarFlyout2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase3, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase4, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase6, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides4, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CommandBarFlyout(std::nullptr_t) noexcept {}
        CommandBarFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ICommandBarFlyout(ptr, take_ownership_from_abi) {}
        CommandBarFlyout();
        using impl::consume_t<CommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase>::ShowAt;
        using impl::consume_t<CommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5>::ShowAt;
    };
    struct WINRT_IMPL_EMPTY_BASES CoreWebView2InitializedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ICoreWebView2InitializedEventArgs
    {
        CoreWebView2InitializedEventArgs(std::nullptr_t) noexcept {}
        CoreWebView2InitializedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ICoreWebView2InitializedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DropDownButton : winrt::Microsoft::UI::Xaml::Controls::IDropDownButton,
        impl::base<DropDownButton, winrt::Windows::UI::Xaml::Controls::Button, winrt::Windows::UI::Xaml::Controls::Primitives::ButtonBase, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DropDownButton, winrt::Windows::UI::Xaml::Controls::IButton, winrt::Windows::UI::Xaml::Controls::IButtonWithFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IButtonBase, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DropDownButton(std::nullptr_t) noexcept {}
        DropDownButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IDropDownButton(ptr, take_ownership_from_abi) {}
        DropDownButton();
    };
    struct WINRT_IMPL_EMPTY_BASES ElementFactoryGetArgs : winrt::Microsoft::UI::Xaml::Controls::IElementFactoryGetArgs
    {
        ElementFactoryGetArgs(std::nullptr_t) noexcept {}
        ElementFactoryGetArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IElementFactoryGetArgs(ptr, take_ownership_from_abi) {}
        ElementFactoryGetArgs();
    };
    struct WINRT_IMPL_EMPTY_BASES ElementFactoryRecycleArgs : winrt::Microsoft::UI::Xaml::Controls::IElementFactoryRecycleArgs
    {
        ElementFactoryRecycleArgs(std::nullptr_t) noexcept {}
        ElementFactoryRecycleArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IElementFactoryRecycleArgs(ptr, take_ownership_from_abi) {}
        ElementFactoryRecycleArgs();
    };
    struct WINRT_IMPL_EMPTY_BASES Expander : winrt::Microsoft::UI::Xaml::Controls::IExpander,
        impl::base<Expander, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<Expander, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        Expander(std::nullptr_t) noexcept {}
        Expander(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IExpander(ptr, take_ownership_from_abi) {}
        Expander();
        [[nodiscard]] static auto HeaderProperty();
        [[nodiscard]] static auto HeaderTemplateProperty();
        [[nodiscard]] static auto HeaderTemplateSelectorProperty();
        [[nodiscard]] static auto IsExpandedProperty();
        [[nodiscard]] static auto ExpandDirectionProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ExpanderCollapsedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IExpanderCollapsedEventArgs
    {
        ExpanderCollapsedEventArgs(std::nullptr_t) noexcept {}
        ExpanderCollapsedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IExpanderCollapsedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ExpanderExpandingEventArgs : winrt::Microsoft::UI::Xaml::Controls::IExpanderExpandingEventArgs
    {
        ExpanderExpandingEventArgs(std::nullptr_t) noexcept {}
        ExpanderExpandingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IExpanderExpandingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ExpanderTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IExpanderTemplateSettings,
        impl::base<ExpanderTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ExpanderTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ExpanderTemplateSettings(std::nullptr_t) noexcept {}
        ExpanderTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IExpanderTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FontIconSource : winrt::Microsoft::UI::Xaml::Controls::IFontIconSource,
        impl::base<FontIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<FontIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        FontIconSource(std::nullptr_t) noexcept {}
        FontIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IFontIconSource(ptr, take_ownership_from_abi) {}
        FontIconSource();
        [[nodiscard]] static auto GlyphProperty();
        [[nodiscard]] static auto FontSizeProperty();
        [[nodiscard]] static auto FontFamilyProperty();
        [[nodiscard]] static auto FontWeightProperty();
        [[nodiscard]] static auto FontStyleProperty();
        [[nodiscard]] static auto IsTextScaleFactorEnabledProperty();
        [[nodiscard]] static auto MirroredWhenRightToLeftProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES IconSource : winrt::Microsoft::UI::Xaml::Controls::IIconSource,
        impl::base<IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<IconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        IconSource(std::nullptr_t) noexcept {}
        IconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IIconSource(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto ForegroundProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ImageIcon : winrt::Microsoft::UI::Xaml::Controls::IImageIcon,
        impl::base<ImageIcon, winrt::Windows::UI::Xaml::Controls::IconElement, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ImageIcon, winrt::Windows::UI::Xaml::Controls::IIconElement, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ImageIcon(std::nullptr_t) noexcept {}
        ImageIcon(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IImageIcon(ptr, take_ownership_from_abi) {}
        ImageIcon();
        [[nodiscard]] static auto SourceProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ImageIconSource : winrt::Microsoft::UI::Xaml::Controls::IImageIconSource,
        impl::base<ImageIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ImageIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ImageIconSource(std::nullptr_t) noexcept {}
        ImageIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IImageIconSource(ptr, take_ownership_from_abi) {}
        ImageIconSource();
        [[nodiscard]] static auto ImageSourceProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBadge : winrt::Microsoft::UI::Xaml::Controls::IInfoBadge,
        impl::base<InfoBadge, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBadge, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBadge(std::nullptr_t) noexcept {}
        InfoBadge(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBadge(ptr, take_ownership_from_abi) {}
        InfoBadge();
        [[nodiscard]] static auto ValueProperty();
        [[nodiscard]] static auto IconSourceProperty();
        [[nodiscard]] static auto TemplateSettingsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBadgeTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IInfoBadgeTemplateSettings,
        impl::base<InfoBadgeTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBadgeTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBadgeTemplateSettings(std::nullptr_t) noexcept {}
        InfoBadgeTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBadgeTemplateSettings(ptr, take_ownership_from_abi) {}
        InfoBadgeTemplateSettings();
        [[nodiscard]] static auto InfoBadgeCornerRadiusProperty();
        [[nodiscard]] static auto IconElementProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBar : winrt::Microsoft::UI::Xaml::Controls::IInfoBar,
        impl::base<InfoBar, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBar, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBar(std::nullptr_t) noexcept {}
        InfoBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBar(ptr, take_ownership_from_abi) {}
        InfoBar();
        [[nodiscard]] static auto IsOpenProperty();
        [[nodiscard]] static auto TitleProperty();
        [[nodiscard]] static auto MessageProperty();
        [[nodiscard]] static auto SeverityProperty();
        [[nodiscard]] static auto IconSourceProperty();
        [[nodiscard]] static auto IsIconVisibleProperty();
        [[nodiscard]] static auto IsClosableProperty();
        [[nodiscard]] static auto CloseButtonStyleProperty();
        [[nodiscard]] static auto CloseButtonCommandProperty();
        [[nodiscard]] static auto CloseButtonCommandParameterProperty();
        [[nodiscard]] static auto ActionButtonProperty();
        [[nodiscard]] static auto ContentProperty();
        [[nodiscard]] static auto ContentTemplateProperty();
        [[nodiscard]] static auto TemplateSettingsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBarClosedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IInfoBarClosedEventArgs
    {
        InfoBarClosedEventArgs(std::nullptr_t) noexcept {}
        InfoBarClosedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBarClosedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBarClosingEventArgs : winrt::Microsoft::UI::Xaml::Controls::IInfoBarClosingEventArgs
    {
        InfoBarClosingEventArgs(std::nullptr_t) noexcept {}
        InfoBarClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBarClosingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBarTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IInfoBarTemplateSettings,
        impl::base<InfoBarTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBarTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBarTemplateSettings(std::nullptr_t) noexcept {}
        InfoBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IInfoBarTemplateSettings(ptr, take_ownership_from_abi) {}
        InfoBarTemplateSettings();
        [[nodiscard]] static auto IconElementProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsRepeater : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeater,
        impl::base<ItemsRepeater, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ItemsRepeater, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ItemsRepeater(std::nullptr_t) noexcept {}
        ItemsRepeater(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeater(ptr, take_ownership_from_abi) {}
        ItemsRepeater();
        [[nodiscard]] static auto ItemsSourceProperty();
        [[nodiscard]] static auto ItemTemplateProperty();
        [[nodiscard]] static auto LayoutProperty();
        [[nodiscard]] static auto HorizontalCacheLengthProperty();
        [[nodiscard]] static auto VerticalCacheLengthProperty();
        [[nodiscard]] static auto BackgroundProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsRepeaterElementClearingEventArgs : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementClearingEventArgs
    {
        ItemsRepeaterElementClearingEventArgs(std::nullptr_t) noexcept {}
        ItemsRepeaterElementClearingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementClearingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsRepeaterElementIndexChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementIndexChangedEventArgs
    {
        ItemsRepeaterElementIndexChangedEventArgs(std::nullptr_t) noexcept {}
        ItemsRepeaterElementIndexChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementIndexChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsRepeaterElementPreparedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementPreparedEventArgs
    {
        ItemsRepeaterElementPreparedEventArgs(std::nullptr_t) noexcept {}
        ItemsRepeaterElementPreparedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterElementPreparedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsRepeaterScrollHost : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterScrollHost,
        impl::base<ItemsRepeaterScrollHost, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ItemsRepeaterScrollHost, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ItemsRepeaterScrollHost(std::nullptr_t) noexcept {}
        ItemsRepeaterScrollHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsRepeaterScrollHost(ptr, take_ownership_from_abi) {}
        ItemsRepeaterScrollHost();
    };
    struct WINRT_IMPL_EMPTY_BASES ItemsSourceView : winrt::Microsoft::UI::Xaml::Controls::IItemsSourceView,
        impl::require<ItemsSourceView, winrt::Windows::UI::Xaml::Interop::INotifyCollectionChanged>
    {
        ItemsSourceView(std::nullptr_t) noexcept {}
        ItemsSourceView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IItemsSourceView(ptr, take_ownership_from_abi) {}
        explicit ItemsSourceView(winrt::Windows::Foundation::IInspectable const& source);
    };
    struct WINRT_IMPL_EMPTY_BASES Layout : winrt::Microsoft::UI::Xaml::Controls::ILayout,
        impl::base<Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<Layout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        Layout(std::nullptr_t) noexcept {}
        Layout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ILayout(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES LayoutContext : winrt::Microsoft::UI::Xaml::Controls::ILayoutContext,
        impl::base<LayoutContext, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<LayoutContext, winrt::Microsoft::UI::Xaml::Controls::ILayoutContextOverrides, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        LayoutContext(std::nullptr_t) noexcept {}
        LayoutContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ILayoutContext(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBar : winrt::Microsoft::UI::Xaml::Controls::IMenuBar,
        impl::base<MenuBar, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBar, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBar(std::nullptr_t) noexcept {}
        MenuBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IMenuBar(ptr, take_ownership_from_abi) {}
        MenuBar();
        [[nodiscard]] static auto ItemsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarItem : winrt::Microsoft::UI::Xaml::Controls::IMenuBarItem,
        impl::base<MenuBarItem, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarItem, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarItem(std::nullptr_t) noexcept {}
        MenuBarItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IMenuBarItem(ptr, take_ownership_from_abi) {}
        MenuBarItem();
        [[nodiscard]] static auto TitleProperty();
        [[nodiscard]] static auto ItemsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarItemFlyout : winrt::Microsoft::UI::Xaml::Controls::IMenuBarItemFlyout,
        impl::base<MenuBarItemFlyout, winrt::Windows::UI::Xaml::Controls::MenuFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::FlyoutBase, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarItemFlyout, winrt::Windows::UI::Xaml::Controls::IMenuFlyout, winrt::Windows::UI::Xaml::Controls::IMenuFlyout2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase3, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase4, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase6, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides4, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarItemFlyout(std::nullptr_t) noexcept {}
        MenuBarItemFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IMenuBarItemFlyout(ptr, take_ownership_from_abi) {}
        MenuBarItemFlyout();
        using impl::consume_t<MenuBarItemFlyout, winrt::Windows::UI::Xaml::Controls::IMenuFlyout2>::ShowAt;
        using impl::consume_t<MenuBarItemFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase>::ShowAt;
        using impl::consume_t<MenuBarItemFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5>::ShowAt;
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationView : winrt::Microsoft::UI::Xaml::Controls::INavigationView,
        impl::base<NavigationView, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationView, winrt::Microsoft::UI::Xaml::Controls::INavigationView2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationView(std::nullptr_t) noexcept {}
        NavigationView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationView(ptr, take_ownership_from_abi) {}
        NavigationView();
        [[nodiscard]] static auto IsPaneOpenProperty();
        [[nodiscard]] static auto CompactModeThresholdWidthProperty();
        [[nodiscard]] static auto ExpandedModeThresholdWidthProperty();
        [[nodiscard]] static auto FooterMenuItemsProperty();
        [[nodiscard]] static auto FooterMenuItemsSourceProperty();
        [[nodiscard]] static auto PaneFooterProperty();
        [[nodiscard]] static auto HeaderProperty();
        [[nodiscard]] static auto HeaderTemplateProperty();
        [[nodiscard]] static auto DisplayModeProperty();
        [[nodiscard]] static auto IsSettingsVisibleProperty();
        [[nodiscard]] static auto IsPaneToggleButtonVisibleProperty();
        [[nodiscard]] static auto AlwaysShowHeaderProperty();
        [[nodiscard]] static auto CompactPaneLengthProperty();
        [[nodiscard]] static auto OpenPaneLengthProperty();
        [[nodiscard]] static auto PaneToggleButtonStyleProperty();
        [[nodiscard]] static auto MenuItemsProperty();
        [[nodiscard]] static auto MenuItemsSourceProperty();
        [[nodiscard]] static auto SelectedItemProperty();
        [[nodiscard]] static auto SettingsItemProperty();
        [[nodiscard]] static auto AutoSuggestBoxProperty();
        [[nodiscard]] static auto MenuItemTemplateProperty();
        [[nodiscard]] static auto MenuItemTemplateSelectorProperty();
        [[nodiscard]] static auto MenuItemContainerStyleProperty();
        [[nodiscard]] static auto MenuItemContainerStyleSelectorProperty();
        [[nodiscard]] static auto IsTitleBarAutoPaddingEnabledProperty();
        [[nodiscard]] static auto IsBackButtonVisibleProperty();
        [[nodiscard]] static auto IsBackEnabledProperty();
        [[nodiscard]] static auto PaneTitleProperty();
        [[nodiscard]] static auto PaneDisplayModeProperty();
        [[nodiscard]] static auto PaneHeaderProperty();
        [[nodiscard]] static auto PaneCustomContentProperty();
        [[nodiscard]] static auto ContentOverlayProperty();
        [[nodiscard]] static auto IsPaneVisibleProperty();
        [[nodiscard]] static auto SelectionFollowsFocusProperty();
        [[nodiscard]] static auto TemplateSettingsProperty();
        [[nodiscard]] static auto ShoulderNavigationEnabledProperty();
        [[nodiscard]] static auto OverflowLabelModeProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewBackRequestedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewBackRequestedEventArgs
    {
        NavigationViewBackRequestedEventArgs(std::nullptr_t) noexcept {}
        NavigationViewBackRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewBackRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewDisplayModeChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewDisplayModeChangedEventArgs
    {
        NavigationViewDisplayModeChangedEventArgs(std::nullptr_t) noexcept {}
        NavigationViewDisplayModeChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewDisplayModeChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItem : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItem,
        impl::base<NavigationViewItem, winrt::Microsoft::UI::Xaml::Controls::NavigationViewItemBase, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItem, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItem2, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItem3, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItem(std::nullptr_t) noexcept {}
        NavigationViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItem(ptr, take_ownership_from_abi) {}
        NavigationViewItem();
        [[nodiscard]] static auto IconProperty();
        [[nodiscard]] static auto CompactPaneLengthProperty();
        [[nodiscard]] static auto SelectsOnInvokedProperty();
        [[nodiscard]] static auto IsExpandedProperty();
        [[nodiscard]] static auto HasUnrealizedChildrenProperty();
        [[nodiscard]] static auto IsChildSelectedProperty();
        [[nodiscard]] static auto MenuItemsProperty();
        [[nodiscard]] static auto MenuItemsSourceProperty();
        [[nodiscard]] static auto InfoBadgeProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemBase : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase,
        impl::base<NavigationViewItemBase, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemBase, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemBase(std::nullptr_t) noexcept {}
        NavigationViewItemBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto IsSelectedProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemCollapsedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemCollapsedEventArgs
    {
        NavigationViewItemCollapsedEventArgs(std::nullptr_t) noexcept {}
        NavigationViewItemCollapsedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemCollapsedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemExpandingEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemExpandingEventArgs
    {
        NavigationViewItemExpandingEventArgs(std::nullptr_t) noexcept {}
        NavigationViewItemExpandingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemExpandingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemHeader : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemHeader,
        impl::base<NavigationViewItemHeader, winrt::Microsoft::UI::Xaml::Controls::NavigationViewItemBase, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemHeader, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemHeader(std::nullptr_t) noexcept {}
        NavigationViewItemHeader(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemHeader(ptr, take_ownership_from_abi) {}
        NavigationViewItemHeader();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemInvokedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemInvokedEventArgs,
        impl::require<NavigationViewItemInvokedEventArgs, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemInvokedEventArgs2>
    {
        NavigationViewItemInvokedEventArgs(std::nullptr_t) noexcept {}
        NavigationViewItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemInvokedEventArgs(ptr, take_ownership_from_abi) {}
        NavigationViewItemInvokedEventArgs();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemSeparator : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemSeparator,
        impl::base<NavigationViewItemSeparator, winrt::Microsoft::UI::Xaml::Controls::NavigationViewItemBase, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemSeparator, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase, winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemBase2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemSeparator(std::nullptr_t) noexcept {}
        NavigationViewItemSeparator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewItemSeparator(ptr, take_ownership_from_abi) {}
        NavigationViewItemSeparator();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewPaneClosingEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewPaneClosingEventArgs
    {
        NavigationViewPaneClosingEventArgs(std::nullptr_t) noexcept {}
        NavigationViewPaneClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewPaneClosingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewSelectionChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INavigationViewSelectionChangedEventArgs,
        impl::require<NavigationViewSelectionChangedEventArgs, winrt::Microsoft::UI::Xaml::Controls::INavigationViewSelectionChangedEventArgs2>
    {
        NavigationViewSelectionChangedEventArgs(std::nullptr_t) noexcept {}
        NavigationViewSelectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewSelectionChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::INavigationViewTemplateSettings,
        impl::base<NavigationViewTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewTemplateSettings, winrt::Microsoft::UI::Xaml::Controls::INavigationViewTemplateSettings2, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewTemplateSettings(std::nullptr_t) noexcept {}
        NavigationViewTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INavigationViewTemplateSettings(ptr, take_ownership_from_abi) {}
        NavigationViewTemplateSettings();
        [[nodiscard]] static auto TopPaddingProperty();
        [[nodiscard]] static auto OverflowButtonVisibilityProperty();
        [[nodiscard]] static auto PaneToggleButtonVisibilityProperty();
        [[nodiscard]] static auto BackButtonVisibilityProperty();
        [[nodiscard]] static auto TopPaneVisibilityProperty();
        [[nodiscard]] static auto LeftPaneVisibilityProperty();
        [[nodiscard]] static auto SingleSelectionFollowsFocusProperty();
        [[nodiscard]] static auto PaneToggleButtonWidthProperty();
        [[nodiscard]] static auto SmallerPaneToggleButtonWidthProperty();
        [[nodiscard]] static auto OpenPaneLengthProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NonVirtualizingLayout : winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayout,
        impl::base<NonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NonVirtualizingLayout(std::nullptr_t) noexcept {}
        NonVirtualizingLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayout(ptr, take_ownership_from_abi) {}
        NonVirtualizingLayout();
    };
    struct WINRT_IMPL_EMPTY_BASES NonVirtualizingLayoutContext : winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutContext,
        impl::base<NonVirtualizingLayoutContext, winrt::Microsoft::UI::Xaml::Controls::LayoutContext, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NonVirtualizingLayoutContext, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutContextOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayoutContext, winrt::Microsoft::UI::Xaml::Controls::ILayoutContextOverrides, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NonVirtualizingLayoutContext(std::nullptr_t) noexcept {}
        NonVirtualizingLayoutContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutContext(ptr, take_ownership_from_abi) {}
        NonVirtualizingLayoutContext();
    };
    struct WINRT_IMPL_EMPTY_BASES NumberBox : winrt::Microsoft::UI::Xaml::Controls::INumberBox,
        impl::base<NumberBox, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NumberBox, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NumberBox(std::nullptr_t) noexcept {}
        NumberBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INumberBox(ptr, take_ownership_from_abi) {}
        NumberBox();
        [[nodiscard]] static auto MinimumProperty();
        [[nodiscard]] static auto MaximumProperty();
        [[nodiscard]] static auto ValueProperty();
        [[nodiscard]] static auto SmallChangeProperty();
        [[nodiscard]] static auto LargeChangeProperty();
        [[nodiscard]] static auto TextProperty();
        [[nodiscard]] static auto HeaderProperty();
        [[nodiscard]] static auto HeaderTemplateProperty();
        [[nodiscard]] static auto PlaceholderTextProperty();
        [[nodiscard]] static auto SelectionFlyoutProperty();
        [[nodiscard]] static auto SelectionHighlightColorProperty();
        [[nodiscard]] static auto TextReadingOrderProperty();
        [[nodiscard]] static auto PreventKeyboardDisplayOnProgrammaticFocusProperty();
        [[nodiscard]] static auto DescriptionProperty();
        [[nodiscard]] static auto ValidationModeProperty();
        [[nodiscard]] static auto SpinButtonPlacementModeProperty();
        [[nodiscard]] static auto IsWrapEnabledProperty();
        [[nodiscard]] static auto AcceptsExpressionProperty();
        [[nodiscard]] static auto NumberFormatterProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NumberBoxValueChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::INumberBoxValueChangedEventArgs
    {
        NumberBoxValueChangedEventArgs(std::nullptr_t) noexcept {}
        NumberBoxValueChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::INumberBoxValueChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ParallaxView : winrt::Microsoft::UI::Xaml::Controls::IParallaxView,
        impl::base<ParallaxView, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ParallaxView, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ParallaxView(std::nullptr_t) noexcept {}
        ParallaxView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IParallaxView(ptr, take_ownership_from_abi) {}
        ParallaxView();
        [[nodiscard]] static auto ChildProperty();
        [[nodiscard]] static auto HorizontalSourceEndOffsetProperty();
        [[nodiscard]] static auto HorizontalSourceOffsetKindProperty();
        [[nodiscard]] static auto HorizontalSourceStartOffsetProperty();
        [[nodiscard]] static auto MaxHorizontalShiftRatioProperty();
        [[nodiscard]] static auto HorizontalShiftProperty();
        [[nodiscard]] static auto IsHorizontalShiftClampedProperty();
        [[nodiscard]] static auto IsVerticalShiftClampedProperty();
        [[nodiscard]] static auto SourceProperty();
        [[nodiscard]] static auto VerticalSourceEndOffsetProperty();
        [[nodiscard]] static auto VerticalSourceOffsetKindProperty();
        [[nodiscard]] static auto VerticalSourceStartOffsetProperty();
        [[nodiscard]] static auto MaxVerticalShiftRatioProperty();
        [[nodiscard]] static auto VerticalShiftProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES PathIconSource : winrt::Microsoft::UI::Xaml::Controls::IPathIconSource,
        impl::base<PathIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PathIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PathIconSource(std::nullptr_t) noexcept {}
        PathIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPathIconSource(ptr, take_ownership_from_abi) {}
        PathIconSource();
        [[nodiscard]] static auto DataProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES PersonPicture : winrt::Microsoft::UI::Xaml::Controls::IPersonPicture,
        impl::base<PersonPicture, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PersonPicture, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PersonPicture(std::nullptr_t) noexcept {}
        PersonPicture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPersonPicture(ptr, take_ownership_from_abi) {}
        PersonPicture();
        [[nodiscard]] static auto BadgeNumberProperty();
        [[nodiscard]] static auto BadgeGlyphProperty();
        [[nodiscard]] static auto BadgeImageSourceProperty();
        [[nodiscard]] static auto BadgeTextProperty();
        [[nodiscard]] static auto IsGroupProperty();
        [[nodiscard]] static auto ContactProperty();
        [[nodiscard]] static auto DisplayNameProperty();
        [[nodiscard]] static auto InitialsProperty();
        [[nodiscard]] static auto PreferSmallImageProperty();
        [[nodiscard]] static auto ProfilePictureProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES PersonPictureTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IPersonPictureTemplateSettings,
        impl::base<PersonPictureTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PersonPictureTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PersonPictureTemplateSettings(std::nullptr_t) noexcept {}
        PersonPictureTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPersonPictureTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PipsPager : winrt::Microsoft::UI::Xaml::Controls::IPipsPager,
        impl::base<PipsPager, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PipsPager, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PipsPager(std::nullptr_t) noexcept {}
        PipsPager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPipsPager(ptr, take_ownership_from_abi) {}
        PipsPager();
        [[nodiscard]] static auto NumberOfPagesProperty();
        [[nodiscard]] static auto SelectedPageIndexProperty();
        [[nodiscard]] static auto MaxVisiblePipsProperty();
        [[nodiscard]] static auto OrientationProperty();
        [[nodiscard]] static auto PreviousButtonVisibilityProperty();
        [[nodiscard]] static auto NextButtonVisibilityProperty();
        [[nodiscard]] static auto PreviousButtonStyleProperty();
        [[nodiscard]] static auto NextButtonStyleProperty();
        [[nodiscard]] static auto SelectedPipStyleProperty();
        [[nodiscard]] static auto NormalPipStyleProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES PipsPagerSelectedIndexChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IPipsPagerSelectedIndexChangedEventArgs
    {
        PipsPagerSelectedIndexChangedEventArgs(std::nullptr_t) noexcept {}
        PipsPagerSelectedIndexChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPipsPagerSelectedIndexChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PipsPagerTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IPipsPagerTemplateSettings,
        impl::base<PipsPagerTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PipsPagerTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PipsPagerTemplateSettings(std::nullptr_t) noexcept {}
        PipsPagerTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IPipsPagerTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressBar : winrt::Microsoft::UI::Xaml::Controls::IProgressBar,
        impl::base<ProgressBar, winrt::Windows::UI::Xaml::Controls::Primitives::RangeBase, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressBar, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBase, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBaseOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressBar(std::nullptr_t) noexcept {}
        ProgressBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IProgressBar(ptr, take_ownership_from_abi) {}
        ProgressBar();
        [[nodiscard]] static auto IsIndeterminateProperty();
        [[nodiscard]] static auto ShowErrorProperty();
        [[nodiscard]] static auto ShowPausedProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressBarTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IProgressBarTemplateSettings,
        impl::base<ProgressBarTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressBarTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressBarTemplateSettings(std::nullptr_t) noexcept {}
        ProgressBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IProgressBarTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressRing : winrt::Microsoft::UI::Xaml::Controls::IProgressRing,
        impl::base<ProgressRing, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressRing, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressRing(std::nullptr_t) noexcept {}
        ProgressRing(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IProgressRing(ptr, take_ownership_from_abi) {}
        ProgressRing();
        [[nodiscard]] static auto IsActiveProperty();
        [[nodiscard]] static auto IsIndeterminateProperty();
        [[nodiscard]] static auto ValueProperty();
        [[nodiscard]] static auto MinimumProperty();
        [[nodiscard]] static auto MaximumProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressRingTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::IProgressRingTemplateSettings,
        impl::base<ProgressRingTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressRingTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressRingTemplateSettings(std::nullptr_t) noexcept {}
        ProgressRingTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IProgressRingTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RadioButtons : winrt::Microsoft::UI::Xaml::Controls::IRadioButtons,
        impl::base<RadioButtons, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RadioButtons, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RadioButtons(std::nullptr_t) noexcept {}
        RadioButtons(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRadioButtons(ptr, take_ownership_from_abi) {}
        RadioButtons();
        [[nodiscard]] static auto ItemsSourceProperty();
        [[nodiscard]] static auto ItemsProperty();
        [[nodiscard]] static auto ItemTemplateProperty();
        [[nodiscard]] static auto SelectedIndexProperty();
        [[nodiscard]] static auto SelectedItemProperty();
        [[nodiscard]] static auto MaxColumnsProperty();
        [[nodiscard]] static auto HeaderProperty();
        [[nodiscard]] static auto HeaderTemplateProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RadioMenuFlyoutItem : winrt::Microsoft::UI::Xaml::Controls::IRadioMenuFlyoutItem,
        impl::base<RadioMenuFlyoutItem, winrt::Windows::UI::Xaml::Controls::MenuFlyoutItem, winrt::Windows::UI::Xaml::Controls::MenuFlyoutItemBase, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RadioMenuFlyoutItem, winrt::Windows::UI::Xaml::Controls::IMenuFlyoutItem, winrt::Windows::UI::Xaml::Controls::IMenuFlyoutItem2, winrt::Windows::UI::Xaml::Controls::IMenuFlyoutItem3, winrt::Windows::UI::Xaml::Controls::IMenuFlyoutItemBase, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RadioMenuFlyoutItem(std::nullptr_t) noexcept {}
        RadioMenuFlyoutItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRadioMenuFlyoutItem(ptr, take_ownership_from_abi) {}
        RadioMenuFlyoutItem();
        [[nodiscard]] static auto IsCheckedProperty();
        [[nodiscard]] static auto GroupNameProperty();
        [[nodiscard]] static auto AreCheckStatesEnabledProperty();
        static auto SetAreCheckStatesEnabled(winrt::Windows::UI::Xaml::Controls::MenuFlyoutSubItem const& object, bool value);
        static auto GetAreCheckStatesEnabled(winrt::Windows::UI::Xaml::Controls::MenuFlyoutSubItem const& object);
    };
    struct WINRT_IMPL_EMPTY_BASES RatingControl : winrt::Microsoft::UI::Xaml::Controls::IRatingControl,
        impl::base<RatingControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingControl, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingControl(std::nullptr_t) noexcept {}
        RatingControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRatingControl(ptr, take_ownership_from_abi) {}
        RatingControl();
        [[nodiscard]] static auto CaptionProperty();
        [[nodiscard]] static auto InitialSetValueProperty();
        [[nodiscard]] static auto IsClearEnabledProperty();
        [[nodiscard]] static auto IsReadOnlyProperty();
        [[nodiscard]] static auto MaxRatingProperty();
        [[nodiscard]] static auto PlaceholderValueProperty();
        [[nodiscard]] static auto ItemInfoProperty();
        [[nodiscard]] static auto ValueProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RatingItemFontInfo : winrt::Microsoft::UI::Xaml::Controls::IRatingItemFontInfo,
        impl::base<RatingItemFontInfo, winrt::Microsoft::UI::Xaml::Controls::RatingItemInfo, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingItemFontInfo, winrt::Microsoft::UI::Xaml::Controls::IRatingItemInfo, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingItemFontInfo(std::nullptr_t) noexcept {}
        RatingItemFontInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRatingItemFontInfo(ptr, take_ownership_from_abi) {}
        RatingItemFontInfo();
        [[nodiscard]] static auto DisabledGlyphProperty();
        [[nodiscard]] static auto GlyphProperty();
        [[nodiscard]] static auto PlaceholderGlyphProperty();
        [[nodiscard]] static auto PointerOverGlyphProperty();
        [[nodiscard]] static auto PointerOverPlaceholderGlyphProperty();
        [[nodiscard]] static auto UnsetGlyphProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RatingItemImageInfo : winrt::Microsoft::UI::Xaml::Controls::IRatingItemImageInfo,
        impl::base<RatingItemImageInfo, winrt::Microsoft::UI::Xaml::Controls::RatingItemInfo, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingItemImageInfo, winrt::Microsoft::UI::Xaml::Controls::IRatingItemInfo, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingItemImageInfo(std::nullptr_t) noexcept {}
        RatingItemImageInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRatingItemImageInfo(ptr, take_ownership_from_abi) {}
        RatingItemImageInfo();
        [[nodiscard]] static auto DisabledImageProperty();
        [[nodiscard]] static auto ImageProperty();
        [[nodiscard]] static auto PlaceholderImageProperty();
        [[nodiscard]] static auto PointerOverImageProperty();
        [[nodiscard]] static auto PointerOverPlaceholderImageProperty();
        [[nodiscard]] static auto UnsetImageProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RatingItemInfo : winrt::Microsoft::UI::Xaml::Controls::IRatingItemInfo,
        impl::base<RatingItemInfo, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingItemInfo, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingItemInfo(std::nullptr_t) noexcept {}
        RatingItemInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRatingItemInfo(ptr, take_ownership_from_abi) {}
        RatingItemInfo();
    };
    struct WINRT_IMPL_EMPTY_BASES RefreshContainer : winrt::Microsoft::UI::Xaml::Controls::IRefreshContainer,
        impl::base<RefreshContainer, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RefreshContainer, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RefreshContainer(std::nullptr_t) noexcept {}
        RefreshContainer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRefreshContainer(ptr, take_ownership_from_abi) {}
        RefreshContainer();
        [[nodiscard]] static auto VisualizerProperty();
        [[nodiscard]] static auto PullDirectionProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RefreshInteractionRatioChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IRefreshInteractionRatioChangedEventArgs
    {
        RefreshInteractionRatioChangedEventArgs(std::nullptr_t) noexcept {}
        RefreshInteractionRatioChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRefreshInteractionRatioChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RefreshRequestedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IRefreshRequestedEventArgs
    {
        RefreshRequestedEventArgs(std::nullptr_t) noexcept {}
        RefreshRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRefreshRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RefreshStateChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IRefreshStateChangedEventArgs
    {
        RefreshStateChangedEventArgs(std::nullptr_t) noexcept {}
        RefreshStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRefreshStateChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES RefreshVisualizer : winrt::Microsoft::UI::Xaml::Controls::IRefreshVisualizer,
        impl::base<RefreshVisualizer, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RefreshVisualizer, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RefreshVisualizer(std::nullptr_t) noexcept {}
        RefreshVisualizer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRefreshVisualizer(ptr, take_ownership_from_abi) {}
        RefreshVisualizer();
        [[nodiscard]] static auto InfoProviderProperty();
        [[nodiscard]] static auto OrientationProperty();
        [[nodiscard]] static auto ContentProperty();
        [[nodiscard]] static auto StateProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RevealListViewItemPresenter : winrt::Microsoft::UI::Xaml::Controls::IRevealListViewItemPresenter,
        impl::base<RevealListViewItemPresenter, winrt::Windows::UI::Xaml::Controls::Primitives::ListViewItemPresenter, winrt::Windows::UI::Xaml::Controls::ContentPresenter, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RevealListViewItemPresenter, winrt::Windows::UI::Xaml::Controls::Primitives::IListViewItemPresenter, winrt::Windows::UI::Xaml::Controls::Primitives::IListViewItemPresenter2, winrt::Windows::UI::Xaml::Controls::Primitives::IListViewItemPresenter3, winrt::Windows::UI::Xaml::Controls::Primitives::IListViewItemPresenter4, winrt::Windows::UI::Xaml::Controls::IContentPresenter, winrt::Windows::UI::Xaml::Controls::IContentPresenter2, winrt::Windows::UI::Xaml::Controls::IContentPresenter3, winrt::Windows::UI::Xaml::Controls::IContentPresenter4, winrt::Windows::UI::Xaml::Controls::IContentPresenter5, winrt::Windows::UI::Xaml::Controls::IContentPresenterOverrides, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RevealListViewItemPresenter(std::nullptr_t) noexcept {}
        RevealListViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IRevealListViewItemPresenter(ptr, take_ownership_from_abi) {}
        RevealListViewItemPresenter();
    };
    struct WINRT_IMPL_EMPTY_BASES SplitButton : winrt::Microsoft::UI::Xaml::Controls::ISplitButton,
        impl::base<SplitButton, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SplitButton, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SplitButton(std::nullptr_t) noexcept {}
        SplitButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISplitButton(ptr, take_ownership_from_abi) {}
        SplitButton();
        [[nodiscard]] static auto FlyoutProperty();
        [[nodiscard]] static auto CommandProperty();
        [[nodiscard]] static auto CommandParameterProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES SplitButtonClickEventArgs : winrt::Microsoft::UI::Xaml::Controls::ISplitButtonClickEventArgs
    {
        SplitButtonClickEventArgs(std::nullptr_t) noexcept {}
        SplitButtonClickEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISplitButtonClickEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES StackLayout : winrt::Microsoft::UI::Xaml::Controls::IStackLayout,
        impl::base<StackLayout, winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<StackLayout, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        StackLayout(std::nullptr_t) noexcept {}
        StackLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IStackLayout(ptr, take_ownership_from_abi) {}
        StackLayout();
        [[nodiscard]] static auto OrientationProperty();
        [[nodiscard]] static auto SpacingProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES SwipeControl : winrt::Microsoft::UI::Xaml::Controls::ISwipeControl,
        impl::base<SwipeControl, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SwipeControl, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SwipeControl(std::nullptr_t) noexcept {}
        SwipeControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISwipeControl(ptr, take_ownership_from_abi) {}
        SwipeControl();
        [[nodiscard]] static auto LeftItemsProperty();
        [[nodiscard]] static auto RightItemsProperty();
        [[nodiscard]] static auto TopItemsProperty();
        [[nodiscard]] static auto BottomItemsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES SwipeItem : winrt::Microsoft::UI::Xaml::Controls::ISwipeItem,
        impl::base<SwipeItem, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SwipeItem, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SwipeItem(std::nullptr_t) noexcept {}
        SwipeItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISwipeItem(ptr, take_ownership_from_abi) {}
        SwipeItem();
        [[nodiscard]] static auto IconSourceProperty();
        [[nodiscard]] static auto TextProperty();
        [[nodiscard]] static auto BackgroundProperty();
        [[nodiscard]] static auto ForegroundProperty();
        [[nodiscard]] static auto CommandProperty();
        [[nodiscard]] static auto CommandParameterProperty();
        [[nodiscard]] static auto BehaviorOnInvokedProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES SwipeItemInvokedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ISwipeItemInvokedEventArgs
    {
        SwipeItemInvokedEventArgs(std::nullptr_t) noexcept {}
        SwipeItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISwipeItemInvokedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES SwipeItems : winrt::Microsoft::UI::Xaml::Controls::ISwipeItems,
        impl::base<SwipeItems, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SwipeItems, winrt::Windows::Foundation::Collections::IIterable<winrt::Microsoft::UI::Xaml::Controls::SwipeItem>, winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::UI::Xaml::Controls::SwipeItem>, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SwipeItems(std::nullptr_t) noexcept {}
        SwipeItems(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISwipeItems(ptr, take_ownership_from_abi) {}
        SwipeItems();
        [[nodiscard]] static auto ModeProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES SymbolIconSource : winrt::Microsoft::UI::Xaml::Controls::ISymbolIconSource,
        impl::base<SymbolIconSource, winrt::Microsoft::UI::Xaml::Controls::IconSource, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SymbolIconSource, winrt::Microsoft::UI::Xaml::Controls::IIconSource, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SymbolIconSource(std::nullptr_t) noexcept {}
        SymbolIconSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ISymbolIconSource(ptr, take_ownership_from_abi) {}
        SymbolIconSource();
        [[nodiscard]] static auto SymbolProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TabView : winrt::Microsoft::UI::Xaml::Controls::ITabView,
        impl::base<TabView, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabView, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabView(std::nullptr_t) noexcept {}
        TabView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabView(ptr, take_ownership_from_abi) {}
        TabView();
        [[nodiscard]] static auto TabWidthModeProperty();
        [[nodiscard]] static auto TabStripHeaderProperty();
        [[nodiscard]] static auto TabStripHeaderTemplateProperty();
        [[nodiscard]] static auto TabStripFooterProperty();
        [[nodiscard]] static auto TabStripFooterTemplateProperty();
        [[nodiscard]] static auto IsAddTabButtonVisibleProperty();
        [[nodiscard]] static auto AddTabButtonCommandProperty();
        [[nodiscard]] static auto AddTabButtonCommandParameterProperty();
        [[nodiscard]] static auto TabItemsSourceProperty();
        [[nodiscard]] static auto TabItemsProperty();
        [[nodiscard]] static auto TabItemTemplateProperty();
        [[nodiscard]] static auto TabItemTemplateSelectorProperty();
        [[nodiscard]] static auto CloseButtonOverlayModeProperty();
        [[nodiscard]] static auto CanDragTabsProperty();
        [[nodiscard]] static auto CanReorderTabsProperty();
        [[nodiscard]] static auto AllowDropTabsProperty();
        [[nodiscard]] static auto SelectedIndexProperty();
        [[nodiscard]] static auto SelectedItemProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewItem : winrt::Microsoft::UI::Xaml::Controls::ITabViewItem,
        impl::base<TabViewItem, winrt::Windows::UI::Xaml::Controls::ListViewItem, winrt::Windows::UI::Xaml::Controls::Primitives::SelectorItem, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabViewItem, winrt::Windows::UI::Xaml::Controls::IListViewItem, winrt::Windows::UI::Xaml::Controls::Primitives::ISelectorItem, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabViewItem(std::nullptr_t) noexcept {}
        TabViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewItem(ptr, take_ownership_from_abi) {}
        TabViewItem();
        [[nodiscard]] static auto HeaderProperty();
        [[nodiscard]] static auto HeaderTemplateProperty();
        [[nodiscard]] static auto IconSourceProperty();
        [[nodiscard]] static auto IsClosableProperty();
        [[nodiscard]] static auto TabViewTemplateSettingsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewItemTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::ITabViewItemTemplateSettings,
        impl::base<TabViewItemTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabViewItemTemplateSettings, winrt::Microsoft::UI::Xaml::Controls::ITabViewItemTemplateSettings2, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabViewItemTemplateSettings(std::nullptr_t) noexcept {}
        TabViewItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewItemTemplateSettings(ptr, take_ownership_from_abi) {}
        TabViewItemTemplateSettings();
        [[nodiscard]] static auto IconElementProperty();
        [[nodiscard]] static auto TabGeometryProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewTabCloseRequestedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabCloseRequestedEventArgs
    {
        TabViewTabCloseRequestedEventArgs(std::nullptr_t) noexcept {}
        TabViewTabCloseRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabCloseRequestedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewTabDragCompletedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDragCompletedEventArgs
    {
        TabViewTabDragCompletedEventArgs(std::nullptr_t) noexcept {}
        TabViewTabDragCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDragCompletedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewTabDragStartingEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDragStartingEventArgs
    {
        TabViewTabDragStartingEventArgs(std::nullptr_t) noexcept {}
        TabViewTabDragStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDragStartingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewTabDroppedOutsideEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDroppedOutsideEventArgs
    {
        TabViewTabDroppedOutsideEventArgs(std::nullptr_t) noexcept {}
        TabViewTabDroppedOutsideEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITabViewTabDroppedOutsideEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TeachingTip : winrt::Microsoft::UI::Xaml::Controls::ITeachingTip,
        impl::base<TeachingTip, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TeachingTip, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TeachingTip(std::nullptr_t) noexcept {}
        TeachingTip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITeachingTip(ptr, take_ownership_from_abi) {}
        TeachingTip();
        [[nodiscard]] static auto IsOpenProperty();
        [[nodiscard]] static auto TargetProperty();
        [[nodiscard]] static auto TailVisibilityProperty();
        [[nodiscard]] static auto TitleProperty();
        [[nodiscard]] static auto SubtitleProperty();
        [[nodiscard]] static auto ActionButtonContentProperty();
        [[nodiscard]] static auto ActionButtonStyleProperty();
        [[nodiscard]] static auto ActionButtonCommandProperty();
        [[nodiscard]] static auto ActionButtonCommandParameterProperty();
        [[nodiscard]] static auto CloseButtonContentProperty();
        [[nodiscard]] static auto CloseButtonStyleProperty();
        [[nodiscard]] static auto CloseButtonCommandProperty();
        [[nodiscard]] static auto CloseButtonCommandParameterProperty();
        [[nodiscard]] static auto PlacementMarginProperty();
        [[nodiscard]] static auto ShouldConstrainToRootBoundsProperty();
        [[nodiscard]] static auto IsLightDismissEnabledProperty();
        [[nodiscard]] static auto PreferredPlacementProperty();
        [[nodiscard]] static auto HeroContentPlacementProperty();
        [[nodiscard]] static auto HeroContentProperty();
        [[nodiscard]] static auto IconSourceProperty();
        [[nodiscard]] static auto TemplateSettingsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TeachingTipClosedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipClosedEventArgs
    {
        TeachingTipClosedEventArgs(std::nullptr_t) noexcept {}
        TeachingTipClosedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipClosedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TeachingTipClosingEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipClosingEventArgs
    {
        TeachingTipClosingEventArgs(std::nullptr_t) noexcept {}
        TeachingTipClosingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipClosingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TeachingTipTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipTemplateSettings,
        impl::base<TeachingTipTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TeachingTipTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TeachingTipTemplateSettings(std::nullptr_t) noexcept {}
        TeachingTipTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITeachingTipTemplateSettings(ptr, take_ownership_from_abi) {}
        TeachingTipTemplateSettings();
        [[nodiscard]] static auto TopRightHighlightMarginProperty();
        [[nodiscard]] static auto TopLeftHighlightMarginProperty();
        [[nodiscard]] static auto IconElementProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TextCommandBarFlyout : winrt::Microsoft::UI::Xaml::Controls::ITextCommandBarFlyout,
        impl::base<TextCommandBarFlyout, winrt::Microsoft::UI::Xaml::Controls::CommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::FlyoutBase, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TextCommandBarFlyout, winrt::Microsoft::UI::Xaml::Controls::ICommandBarFlyout, winrt::Microsoft::UI::Xaml::Controls::ICommandBarFlyout2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase2, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase3, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase4, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase6, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBaseOverrides4, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TextCommandBarFlyout(std::nullptr_t) noexcept {}
        TextCommandBarFlyout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITextCommandBarFlyout(ptr, take_ownership_from_abi) {}
        TextCommandBarFlyout();
        using impl::consume_t<TextCommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase>::ShowAt;
        using impl::consume_t<TextCommandBarFlyout, winrt::Windows::UI::Xaml::Controls::Primitives::IFlyoutBase5>::ShowAt;
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleSplitButton : winrt::Microsoft::UI::Xaml::Controls::IToggleSplitButton,
        impl::base<ToggleSplitButton, winrt::Microsoft::UI::Xaml::Controls::SplitButton, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ToggleSplitButton, winrt::Microsoft::UI::Xaml::Controls::ISplitButton, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ToggleSplitButton(std::nullptr_t) noexcept {}
        ToggleSplitButton(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IToggleSplitButton(ptr, take_ownership_from_abi) {}
        ToggleSplitButton();
        [[nodiscard]] static auto IsCheckedProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleSplitButtonIsCheckedChangedEventArgs : winrt::Microsoft::UI::Xaml::Controls::IToggleSplitButtonIsCheckedChangedEventArgs
    {
        ToggleSplitButtonIsCheckedChangedEventArgs(std::nullptr_t) noexcept {}
        ToggleSplitButtonIsCheckedChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IToggleSplitButtonIsCheckedChangedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeView : winrt::Microsoft::UI::Xaml::Controls::ITreeView,
        impl::base<TreeView, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeView, winrt::Microsoft::UI::Xaml::Controls::ITreeView2, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeView(std::nullptr_t) noexcept {}
        TreeView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeView(ptr, take_ownership_from_abi) {}
        TreeView();
        [[nodiscard]] static auto SelectedItemProperty();
        [[nodiscard]] static auto SelectionModeProperty();
        [[nodiscard]] static auto CanDragItemsProperty();
        [[nodiscard]] static auto CanReorderItemsProperty();
        [[nodiscard]] static auto ItemTemplateProperty();
        [[nodiscard]] static auto ItemTemplateSelectorProperty();
        [[nodiscard]] static auto ItemContainerStyleProperty();
        [[nodiscard]] static auto ItemContainerStyleSelectorProperty();
        [[nodiscard]] static auto ItemContainerTransitionsProperty();
        [[nodiscard]] static auto ItemsSourceProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewCollapsedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITreeViewCollapsedEventArgs,
        impl::require<TreeViewCollapsedEventArgs, winrt::Microsoft::UI::Xaml::Controls::ITreeViewCollapsedEventArgs2>
    {
        TreeViewCollapsedEventArgs(std::nullptr_t) noexcept {}
        TreeViewCollapsedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewCollapsedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewDragItemsCompletedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITreeViewDragItemsCompletedEventArgs,
        impl::require<TreeViewDragItemsCompletedEventArgs, winrt::Microsoft::UI::Xaml::Controls::ITreeViewDragItemsCompletedEventArgs2>
    {
        TreeViewDragItemsCompletedEventArgs(std::nullptr_t) noexcept {}
        TreeViewDragItemsCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewDragItemsCompletedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewDragItemsStartingEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITreeViewDragItemsStartingEventArgs
    {
        TreeViewDragItemsStartingEventArgs(std::nullptr_t) noexcept {}
        TreeViewDragItemsStartingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewDragItemsStartingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewExpandingEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITreeViewExpandingEventArgs,
        impl::require<TreeViewExpandingEventArgs, winrt::Microsoft::UI::Xaml::Controls::ITreeViewExpandingEventArgs2>
    {
        TreeViewExpandingEventArgs(std::nullptr_t) noexcept {}
        TreeViewExpandingEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewExpandingEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItem : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItem,
        impl::base<TreeViewItem, winrt::Windows::UI::Xaml::Controls::ListViewItem, winrt::Windows::UI::Xaml::Controls::Primitives::SelectorItem, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewItem, winrt::Microsoft::UI::Xaml::Controls::ITreeViewItem2, winrt::Windows::UI::Xaml::Controls::IListViewItem, winrt::Windows::UI::Xaml::Controls::Primitives::ISelectorItem, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewItem(std::nullptr_t) noexcept {}
        TreeViewItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItem(ptr, take_ownership_from_abi) {}
        TreeViewItem();
        [[nodiscard]] static auto GlyphOpacityProperty();
        [[nodiscard]] static auto GlyphBrushProperty();
        [[nodiscard]] static auto ExpandedGlyphProperty();
        [[nodiscard]] static auto CollapsedGlyphProperty();
        [[nodiscard]] static auto GlyphSizeProperty();
        [[nodiscard]] static auto IsExpandedProperty();
        [[nodiscard]] static auto TreeViewItemTemplateSettingsProperty();
        [[nodiscard]] static auto HasUnrealizedChildrenProperty();
        [[nodiscard]] static auto ItemsSourceProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItemInvokedEventArgs : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItemInvokedEventArgs
    {
        TreeViewItemInvokedEventArgs(std::nullptr_t) noexcept {}
        TreeViewItemInvokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItemInvokedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItemTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItemTemplateSettings,
        impl::base<TreeViewItemTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewItemTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewItemTemplateSettings(std::nullptr_t) noexcept {}
        TreeViewItemTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewItemTemplateSettings(ptr, take_ownership_from_abi) {}
        TreeViewItemTemplateSettings();
        [[nodiscard]] static auto ExpandedGlyphVisibilityProperty();
        [[nodiscard]] static auto CollapsedGlyphVisibilityProperty();
        [[nodiscard]] static auto IndentationProperty();
        [[nodiscard]] static auto DragItemsCountProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewList : winrt::Microsoft::UI::Xaml::Controls::ITreeViewList,
        impl::base<TreeViewList, winrt::Windows::UI::Xaml::Controls::ListView, winrt::Windows::UI::Xaml::Controls::ListViewBase, winrt::Windows::UI::Xaml::Controls::Primitives::Selector, winrt::Windows::UI::Xaml::Controls::ItemsControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewList, winrt::Windows::UI::Xaml::Controls::IListView, winrt::Windows::UI::Xaml::Controls::IListViewBase, winrt::Windows::UI::Xaml::Controls::IListViewBase2, winrt::Windows::UI::Xaml::Controls::IListViewBase3, winrt::Windows::UI::Xaml::Controls::IListViewBase4, winrt::Windows::UI::Xaml::Controls::IListViewBase5, winrt::Windows::UI::Xaml::Controls::IListViewBase6, winrt::Windows::UI::Xaml::Controls::ISemanticZoomInformation, winrt::Windows::UI::Xaml::Controls::Primitives::ISelector, winrt::Windows::UI::Xaml::Controls::IItemsControl, winrt::Windows::UI::Xaml::Controls::IItemsControl2, winrt::Windows::UI::Xaml::Controls::IItemsControl3, winrt::Windows::UI::Xaml::Controls::IItemsControlOverrides, winrt::Windows::UI::Xaml::Controls::IItemContainerMapping, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewList(std::nullptr_t) noexcept {}
        TreeViewList(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewList(ptr, take_ownership_from_abi) {}
        TreeViewList();
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewNode : winrt::Microsoft::UI::Xaml::Controls::ITreeViewNode,
        impl::base<TreeViewNode, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewNode, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewNode(std::nullptr_t) noexcept {}
        TreeViewNode(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITreeViewNode(ptr, take_ownership_from_abi) {}
        TreeViewNode();
        [[nodiscard]] static auto ContentProperty();
        [[nodiscard]] static auto DepthProperty();
        [[nodiscard]] static auto IsExpandedProperty();
        [[nodiscard]] static auto HasChildrenProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TwoPaneView : winrt::Microsoft::UI::Xaml::Controls::ITwoPaneView,
        impl::base<TwoPaneView, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TwoPaneView, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TwoPaneView(std::nullptr_t) noexcept {}
        TwoPaneView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::ITwoPaneView(ptr, take_ownership_from_abi) {}
        TwoPaneView();
        [[nodiscard]] static auto Pane1Property();
        [[nodiscard]] static auto Pane2Property();
        [[nodiscard]] static auto Pane1LengthProperty();
        [[nodiscard]] static auto Pane2LengthProperty();
        [[nodiscard]] static auto PanePriorityProperty();
        [[nodiscard]] static auto ModeProperty();
        [[nodiscard]] static auto WideModeConfigurationProperty();
        [[nodiscard]] static auto TallModeConfigurationProperty();
        [[nodiscard]] static auto MinWideModeWidthProperty();
        [[nodiscard]] static auto MinTallModeHeightProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES UniformGridLayout : winrt::Microsoft::UI::Xaml::Controls::IUniformGridLayout,
        impl::base<UniformGridLayout, winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<UniformGridLayout, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        UniformGridLayout(std::nullptr_t) noexcept {}
        UniformGridLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IUniformGridLayout(ptr, take_ownership_from_abi) {}
        UniformGridLayout();
        [[nodiscard]] static auto OrientationProperty();
        [[nodiscard]] static auto MinItemWidthProperty();
        [[nodiscard]] static auto MinItemHeightProperty();
        [[nodiscard]] static auto MinRowSpacingProperty();
        [[nodiscard]] static auto MinColumnSpacingProperty();
        [[nodiscard]] static auto ItemsJustificationProperty();
        [[nodiscard]] static auto ItemsStretchProperty();
        [[nodiscard]] static auto MaximumRowsOrColumnsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES VirtualizingLayout : winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayout,
        impl::base<VirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<VirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        VirtualizingLayout(std::nullptr_t) noexcept {}
        VirtualizingLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayout(ptr, take_ownership_from_abi) {}
        VirtualizingLayout();
    };
    struct WINRT_IMPL_EMPTY_BASES VirtualizingLayoutContext : winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutContext,
        impl::base<VirtualizingLayoutContext, winrt::Microsoft::UI::Xaml::Controls::LayoutContext, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<VirtualizingLayoutContext, winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutContextOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayoutContext, winrt::Microsoft::UI::Xaml::Controls::ILayoutContextOverrides, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        VirtualizingLayoutContext(std::nullptr_t) noexcept {}
        VirtualizingLayoutContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutContext(ptr, take_ownership_from_abi) {}
        VirtualizingLayoutContext();
    };
    struct WINRT_IMPL_EMPTY_BASES WebView2 : winrt::Microsoft::UI::Xaml::Controls::IWebView2,
        impl::base<WebView2, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<WebView2, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        WebView2(std::nullptr_t) noexcept {}
        WebView2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IWebView2(ptr, take_ownership_from_abi) {}
        WebView2();
        [[nodiscard]] static auto SourceProperty();
        [[nodiscard]] static auto CanGoForwardProperty();
        [[nodiscard]] static auto CanGoBackProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES XamlControlsResources : winrt::Microsoft::UI::Xaml::Controls::IXamlControlsResources,
        impl::base<XamlControlsResources, winrt::Windows::UI::Xaml::ResourceDictionary, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<XamlControlsResources, winrt::Microsoft::UI::Xaml::Controls::IXamlControlsResources3, winrt::Windows::UI::Xaml::IResourceDictionary, winrt::Windows::Foundation::Collections::IIterable<winrt::Windows::Foundation::Collections::IKeyValuePair<winrt::Windows::Foundation::IInspectable, winrt::Windows::Foundation::IInspectable>>, winrt::Windows::Foundation::Collections::IMap<winrt::Windows::Foundation::IInspectable, winrt::Windows::Foundation::IInspectable>, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        XamlControlsResources(std::nullptr_t) noexcept {}
        XamlControlsResources(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::IXamlControlsResources(ptr, take_ownership_from_abi) {}
        XamlControlsResources();
        static auto EnsureRevealLights(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto ControlsResourcesVersionProperty();
    };
    template <typename D>
    class ILayoutContextOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using ILayoutContextOverrides = winrt::Microsoft::UI::Xaml::Controls::ILayoutContextOverrides;
        [[nodiscard]] auto LayoutStateCore() const;
        auto LayoutStateCore(winrt::Windows::Foundation::IInspectable const& value) const;
    };
    template <typename D>
    class INonVirtualizingLayoutOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using INonVirtualizingLayoutOverrides = winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutOverrides;
        auto InitializeForContextCore(winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayoutContext const& context) const;
        auto UninitializeForContextCore(winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayoutContext const& context) const;
        auto MeasureOverride(winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayoutContext const& context, winrt::Windows::Foundation::Size const& availableSize) const;
        auto ArrangeOverride(winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayoutContext const& context, winrt::Windows::Foundation::Size const& finalSize) const;
    };
    template <typename D>
    class INonVirtualizingLayoutContextOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using INonVirtualizingLayoutContextOverrides = winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutContextOverrides;
        [[nodiscard]] auto ChildrenCore() const;
    };
    template <typename D>
    class IVirtualizingLayoutOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IVirtualizingLayoutOverrides = winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutOverrides;
        auto InitializeForContextCore(winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayoutContext const& context) const;
        auto UninitializeForContextCore(winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayoutContext const& context) const;
        auto MeasureOverride(winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayoutContext const& context, winrt::Windows::Foundation::Size const& availableSize) const;
        auto ArrangeOverride(winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayoutContext const& context, winrt::Windows::Foundation::Size const& finalSize) const;
        auto OnItemsChangedCore(winrt::Microsoft::UI::Xaml::Controls::VirtualizingLayoutContext const& context, winrt::Windows::Foundation::IInspectable const& source, winrt::Windows::UI::Xaml::Interop::NotifyCollectionChangedEventArgs const& args) const;
    };
    template <typename D>
    class IVirtualizingLayoutContextOverridesT
    {
        D& shim() noexcept { return *static_cast<D*>(this); }
        D const& shim() const noexcept { return *static_cast<const D*>(this); }
    public:
        using IVirtualizingLayoutContextOverrides = winrt::Microsoft::UI::Xaml::Controls::IVirtualizingLayoutContextOverrides;
        auto ItemCountCore() const;
        auto GetItemAtCore(int32_t index) const;
        auto RealizationRectCore() const;
        auto GetOrCreateElementAtCore(int32_t index, winrt::Microsoft::UI::Xaml::Controls::ElementRealizationOptions const& options) const;
        auto RecycleElementCore(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto RecommendedAnchorIndexCore() const;
        [[nodiscard]] auto LayoutOriginCore() const;
        auto LayoutOriginCore(winrt::Windows::Foundation::Point const& value) const;
    };
}
#endif
