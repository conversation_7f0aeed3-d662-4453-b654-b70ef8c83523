﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><ReferenceAssemblyList><LocalAssembly PathName="c:\users\<USER>\desktop\plexy\windows\x64\debug\plexy\plexy.winmd" HashGuid="c797fba0-a00c-da2c-2e0c-1256cd1f0b96" /><ReferenceAssembly PathName="c:\users\<USER>\desktop\plexy\node_modules\react-native-windows\target\x64\debug\microsoft.reactnative\microsoft.reactnative.winmd" HashGuid="df683b5c-c128-702c-a982-01e5d3efd7a1" /><ReferenceAssembly PathName="c:\users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\lib\uap10.0\microsoft.ui.xaml.winmd" HashGuid="3a1d1bbd-6182-3650-39dd-d6230124c63e" /><ReferenceAssembly PathName="c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1264.42\lib\microsoft.web.webview2.core.winmd" HashGuid="854c6115-ab43-d147-f7bc-add56d625aa1" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="App.xaml" ClassFullName="plexy.App" GeneratedCodePathPrefix="C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\App" XamlFileTimeAtLastCompileInTicks="638879144689309999" HasBoundEventAssignments="False" /><XamlSourceFileData XamlFileName="MainPage.xaml" ClassFullName="plexy.MainPage" GeneratedCodePathPrefix="C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\MainPage" XamlFileTimeAtLastCompileInTicks="638879144689400011" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>