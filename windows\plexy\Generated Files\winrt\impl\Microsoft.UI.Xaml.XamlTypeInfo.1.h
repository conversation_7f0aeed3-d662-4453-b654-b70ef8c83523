// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_XamlTypeInfo_1_H
#define WINRT_Microsoft_UI_Xaml_XamlTypeInfo_1_H
#include "winrt/impl/Microsoft.UI.Xaml.XamlTypeInfo.0.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::XamlTypeInfo
{
    struct WINRT_IMPL_EMPTY_BASES IXamlControlsXamlMetaDataProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlControlsXamlMetaDataProvider>
    {
        IXamlControlsXamlMetaDataProvider(std::nullptr_t = nullptr) noexcept {}
        IXamlControlsXamlMetaDataProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IXamlControlsXamlMetaDataProviderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IXamlControlsXamlMetaDataProviderStatics>
    {
        IXamlControlsXamlMetaDataProviderStatics(std::nullptr_t = nullptr) noexcept {}
        IXamlControlsXamlMetaDataProviderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
