// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_Primitives_0_H
#define WINRT_Microsoft_UI_Xaml_Controls_Primitives_0_H
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls
{
    struct ColorChangedEventArgs;
    enum class ColorPickerHsvChannel : int32_t;
    enum class ColorSpectrumComponents : int32_t;
    enum class ColorSpectrumShape : int32_t;
    struct InfoBadge;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct EventRegistrationToken;
    struct Rect;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Numerics
{
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct Color;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml
{
    struct DependencyObject;
    struct DependencyProperty;
    struct Thickness;
    struct UIElement;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Peers
{
    enum class AutomationControlType : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Controls
{
    struct AutoSuggestBox;
    struct ComboBox;
    struct IconElement;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    enum class CornerRadiusFilterKind : int32_t
    {
        None = 0,
        Top = 1,
        Right = 2,
        Bottom = 3,
        Left = 4,
        TopLeftValue = 5,
        BottomRightValue = 6,
    };
    enum class CornerRadiusToThicknessConverterKind : int32_t
    {
        FilterTopAndBottomFromLeft = 0,
        FilterTopAndBottomFromRight = 1,
        FilterLeftAndRightFromTop = 2,
        FilterLeftAndRightFromBottom = 3,
        FilterTopFromTopLeft = 4,
        FilterTopFromTopRight = 5,
        FilterRightFromTopRight = 6,
        FilterRightFromBottomRight = 7,
        FilterBottomFromBottomRight = 8,
        FilterBottomFromBottomLeft = 9,
        FilterLeftFromBottomLeft = 10,
        FilterLeftFromTopLeft = 11,
    };
    struct IAutoSuggestBoxHelper;
    struct IAutoSuggestBoxHelperStatics;
    struct IColorPickerSlider;
    struct IColorPickerSliderFactory;
    struct IColorPickerSliderStatics;
    struct IColorSpectrum;
    struct IColorSpectrumFactory;
    struct IColorSpectrumStatics;
    struct IColumnMajorUniformToLargestGridLayout;
    struct IColumnMajorUniformToLargestGridLayoutFactory;
    struct IColumnMajorUniformToLargestGridLayoutStatics;
    struct IComboBoxHelper;
    struct IComboBoxHelperStatics;
    struct ICommandBarFlyoutCommandBar;
    struct ICommandBarFlyoutCommandBarAutomationPropertiesStatics;
    struct ICommandBarFlyoutCommandBarFactory;
    struct ICommandBarFlyoutCommandBarTemplateSettings;
    struct ICornerRadiusFilterConverter;
    struct ICornerRadiusFilterConverterStatics;
    struct ICornerRadiusToThicknessConverter;
    struct ICornerRadiusToThicknessConverterStatics;
    struct IInfoBarPanel;
    struct IInfoBarPanelFactory;
    struct IInfoBarPanelStatics;
    struct IMonochromaticOverlayPresenter;
    struct IMonochromaticOverlayPresenterFactory;
    struct IMonochromaticOverlayPresenterStatics;
    struct INavigationViewItemPresenter;
    struct INavigationViewItemPresenter2;
    struct INavigationViewItemPresenterFactory;
    struct INavigationViewItemPresenterStatics;
    struct INavigationViewItemPresenterStatics2;
    struct INavigationViewItemPresenterTemplateSettings;
    struct INavigationViewItemPresenterTemplateSettingsFactory;
    struct INavigationViewItemPresenterTemplateSettingsStatics;
    struct ITabViewListView;
    struct ITabViewListViewFactory;
    struct AutoSuggestBoxHelper;
    struct ColorPickerSlider;
    struct ColorSpectrum;
    struct ColumnMajorUniformToLargestGridLayout;
    struct ComboBoxHelper;
    struct CommandBarFlyoutCommandBar;
    struct CommandBarFlyoutCommandBarAutomationProperties;
    struct CommandBarFlyoutCommandBarTemplateSettings;
    struct CornerRadiusFilterConverter;
    struct CornerRadiusToThicknessConverter;
    struct InfoBarPanel;
    struct MonochromaticOverlayPresenter;
    struct NavigationViewItemPresenter;
    struct NavigationViewItemPresenterTemplateSettings;
    struct TabViewListView;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::AutoSuggestBoxHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::ComboBoxHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarAutomationProperties>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterConverter>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverter>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind>{ using type = enum_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::AutoSuggestBoxHelper> = L"Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider> = L"Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum> = L"Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout> = L"Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ComboBoxHelper> = L"Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar> = L"Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarAutomationProperties> = L"Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings> = L"Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterConverter> = L"Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverter> = L"Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel> = L"Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter> = L"Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter> = L"Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings> = L"Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView> = L"Microsoft.UI.Xaml.Controls.Primitives.TabViewListView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind> = L"Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind> = L"Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper> = L"Microsoft.UI.Xaml.Controls.Primitives.IAutoSuggestBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IAutoSuggestBoxHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorPickerSlider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorPickerSliderFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorPickerSliderStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorSpectrum";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorSpectrumFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IColorSpectrumStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout> = L"Microsoft.UI.Xaml.Controls.Primitives.IColumnMajorUniformToLargestGridLayout";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.IColumnMajorUniformToLargestGridLayoutFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IColumnMajorUniformToLargestGridLayoutStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper> = L"Microsoft.UI.Xaml.Controls.Primitives.IComboBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IComboBoxHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar> = L"Microsoft.UI.Xaml.Controls.Primitives.ICommandBarFlyoutCommandBar";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.ICommandBarFlyoutCommandBarAutomationPropertiesStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.ICommandBarFlyoutCommandBarFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings> = L"Microsoft.UI.Xaml.Controls.Primitives.ICommandBarFlyoutCommandBarTemplateSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter> = L"Microsoft.UI.Xaml.Controls.Primitives.ICornerRadiusFilterConverter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.ICornerRadiusFilterConverterStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter> = L"Microsoft.UI.Xaml.Controls.Primitives.ICornerRadiusToThicknessConverter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.ICornerRadiusToThicknessConverterStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel> = L"Microsoft.UI.Xaml.Controls.Primitives.IInfoBarPanel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.IInfoBarPanelFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IInfoBarPanelStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter> = L"Microsoft.UI.Xaml.Controls.Primitives.IMonochromaticOverlayPresenter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.IMonochromaticOverlayPresenterFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.IMonochromaticOverlayPresenterStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenter2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterStatics2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterTemplateSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterTemplateSettingsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics> = L"Microsoft.UI.Xaml.Controls.Primitives.INavigationViewItemPresenterTemplateSettingsStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView> = L"Microsoft.UI.Xaml.Controls.Primitives.ITabViewListView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory> = L"Microsoft.UI.Xaml.Controls.Primitives.ITabViewListViewFactory";
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper>{ 0x1E56736C,0x8248,0x57D9,{ 0xAC,0x04,0xE4,0xE7,0xDC,0xC3,0xF9,0xE1 } }; // 1E56736C-8248-57D9-AC04-E4E7DCC3F9E1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics>{ 0x1FD01B5A,0x71AB,0x5D3A,{ 0x93,0xE7,0x14,0x1B,0xFD,0x6E,0xDF,0x6A } }; // 1FD01B5A-71AB-5D3A-93E7-141BFD6EDF6A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider>{ 0x456231BB,0x5A4C,0x564B,{ 0x9B,0x3D,0x2F,0x15,0x70,0x61,0xA0,0xF8 } }; // 456231BB-5A4C-564B-9B3D-2F157061A0F8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory>{ 0xD0CB1F0E,0x0771,0x5C7D,{ 0xBA,0x14,0xAA,0x43,0x11,0x79,0xB2,0xAC } }; // D0CB1F0E-0771-5C7D-BA14-AA431179B2AC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics>{ 0x9792B197,0xC864,0x59F3,{ 0x9F,0xA0,0xE2,0x1C,0x7F,0xD4,0x6B,0x79 } }; // 9792B197-C864-59F3-9FA0-E21C7FD46B79
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>{ 0x75305916,0x882D,0x5667,{ 0xBF,0xD0,0x0A,0xF7,0x2D,0x50,0x2D,0x72 } }; // 75305916-882D-5667-BFD0-0AF72D502D72
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory>{ 0xEFECD442,0x8C2A,0x50A6,{ 0x88,0xA3,0x39,0x99,0xEA,0x01,0xF0,0x96 } }; // EFECD442-8C2A-50A6-88A3-3999EA01F096
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics>{ 0xAA1D251D,0x9E7B,0x5767,{ 0xAD,0xB9,0xFC,0xD3,0x68,0x55,0xD8,0x2E } }; // AA1D251D-9E7B-5767-ADB9-FCD36855D82E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout>{ 0xEE10A6AA,0xEFEB,0x51AC,{ 0xB7,0x91,0x71,0x91,0x3A,0xE8,0xC2,0x35 } }; // EE10A6AA-EFEB-51AC-B791-71913AE8C235
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory>{ 0x2F21AF59,0x1585,0x5325,{ 0x84,0x12,0x2B,0x83,0xBF,0x05,0xD3,0x45 } }; // 2F21AF59-1585-5325-8412-2B83BF05D345
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics>{ 0x29A1AC22,0xEE31,0x501F,{ 0x94,0xF8,0xA0,0x0D,0x33,0x8F,0x10,0x0A } }; // 29A1AC22-EE31-501F-94F8-A00D338F100A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper>{ 0x5A3C87AC,0xC399,0x5E5F,{ 0x87,0x3F,0xB9,0xD0,0xE8,0xBC,0xCE,0xB7 } }; // 5A3C87AC-C399-5E5F-873F-B9D0E8BCCEB7
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics>{ 0x59B4EB2D,0xBD79,0x59B2,{ 0xB3,0x43,0xFA,0x72,0xC7,0x47,0xD7,0x2A } }; // 59B4EB2D-BD79-59B2-B343-FA72C747D72A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar>{ 0x0F7120C5,0x6D00,0x5489,{ 0x91,0x71,0xBE,0xDD,0x2D,0x4E,0xF6,0x77 } }; // 0F7120C5-6D00-5489-9171-BEDD2D4EF677
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics>{ 0xA0D9D55F,0x090D,0x5F25,{ 0x89,0x53,0x0F,0xD1,0xE6,0x5F,0x9B,0x5A } }; // A0D9D55F-090D-5F25-8953-0FD1E65F9B5A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory>{ 0x58DBCDA9,0x38E4,0x5EFC,{ 0xB7,0x40,0x26,0xFD,0xA3,0xD0,0xA3,0xC6 } }; // 58DBCDA9-38E4-5EFC-B740-26FDA3D0A3C6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings>{ 0x533CC5CA,0xDCF7,0x5F9D,{ 0xA4,0x60,0x93,0x4A,0x88,0x3A,0xCD,0xC1 } }; // 533CC5CA-DCF7-5F9D-A460-934A883ACDC1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter>{ 0x6F1A3ED2,0xF965,0x545E,{ 0xBD,0x44,0x44,0x1D,0xB1,0x79,0x4F,0x5F } }; // 6F1A3ED2-F965-545E-BD44-441DB1794F5F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics>{ 0xF1A547F3,0x4C0E,0x5F23,{ 0x9F,0x1E,0xA4,0xC0,0x8C,0xAB,0xDE,0x88 } }; // F1A547F3-4C0E-5F23-9F1E-A4C08CABDE88
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter>{ 0xB36AA8AE,0x166E,0x5CA5,{ 0x93,0xD2,0x95,0xE9,0x90,0x7C,0x12,0x22 } }; // B36AA8AE-166E-5CA5-93D2-95E9907C1222
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics>{ 0xD6995649,0x8AD2,0x5C38,{ 0xBC,0x45,0xB8,0xEF,0x86,0xFF,0x5F,0x69 } }; // D6995649-8AD2-5C38-BC45-B8EF86FF5F69
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel>{ 0x44A0708C,0xAC3C,0x53B8,{ 0xBB,0xAA,0x3B,0x38,0x65,0x3E,0x84,0x3D } }; // 44A0708C-AC3C-53B8-BBAA-3B38653E843D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory>{ 0x00D1A8C5,0xF631,0x564A,{ 0x8E,0x9C,0x7C,0x5C,0xCA,0xD2,0x38,0xDE } }; // 00D1A8C5-F631-564A-8E9C-7C5CCAD238DE
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics>{ 0x335DDBFD,0x977A,0x58A2,{ 0xAF,0x17,0x4B,0x77,0x61,0x44,0xF2,0x45 } }; // 335DDBFD-977A-58A2-AF17-4B776144F245
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter>{ 0x4DBEF429,0x53B1,0x511A,{ 0x99,0x88,0x70,0xBB,0xCD,0x2E,0xE1,0x22 } }; // 4DBEF429-53B1-511A-9988-70BBCD2EE122
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory>{ 0x4997847B,0xB558,0x5C8C,{ 0x82,0x98,0xBE,0x15,0x32,0xE8,0x98,0xEC } }; // 4997847B-B558-5C8C-8298-BE1532E898EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics>{ 0xFA3915D4,0xCB66,0x522D,{ 0x83,0xE2,0x36,0xE9,0x88,0xFF,0xA0,0xA4 } }; // FA3915D4-CB66-522D-83E2-36E988FFA0A4
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter>{ 0x0489BF60,0xF630,0x510C,{ 0x98,0xDC,0xB1,0x23,0x22,0x26,0x0E,0x1F } }; // 0489BF60-F630-510C-98DC-B12322260E1F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2>{ 0x3F775730,0x3713,0x5B3B,{ 0x96,0x00,0x53,0xB7,0x9F,0xFF,0x2E,0x35 } }; // 3F775730-3713-5B3B-9600-53B79FFF2E35
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory>{ 0xB28B0160,0x022C,0x593C,{ 0xAB,0x9A,0x7B,0x3D,0xED,0x2C,0x07,0x54 } }; // B28B0160-022C-593C-AB9A-7B3DED2C0754
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics>{ 0xF0A5E6E7,0xF626,0x5430,{ 0x8E,0xF2,0xDE,0x75,0xAE,0x72,0x90,0x0F } }; // F0A5E6E7-F626-5430-8EF2-DE75AE72900F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2>{ 0x1F37F471,0x2F5C,0x5CE3,{ 0xAA,0x21,0xA5,0x0A,0xCB,0xFA,0x34,0xF9 } }; // 1F37F471-2F5C-5CE3-AA21-A50ACBFA34F9
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings>{ 0xA5119750,0xFA71,0x56F2,{ 0xBF,0xA4,0x79,0x9D,0x9F,0x30,0x4C,0xB8 } }; // A5119750-FA71-56F2-BFA4-799D9F304CB8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory>{ 0x19EF1328,0x52C7,0x55E3,{ 0xB1,0xBB,0x92,0x3F,0x2F,0x39,0xBD,0x6E } }; // 19EF1328-52C7-55E3-B1BB-923F2F39BD6E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics>{ 0xBA971CFD,0x5037,0x5B8B,{ 0xA9,0x5E,0x65,0x2C,0x65,0xEC,0xAA,0xBF } }; // BA971CFD-5037-5B8B-A95E-652C65ECAABF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView>{ 0xEC48EFB5,0x2CB3,0x562B,{ 0x92,0x1C,0xE5,0x54,0x92,0x3C,0xE1,0xD5 } }; // EC48EFB5-2CB3-562B-921C-E554923CE1D5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory>{ 0x8A084FDD,0x86F0,0x51EE,{ 0x98,0xDF,0x5F,0xBD,0x0B,0x56,0x69,0xBE } }; // 8A084FDD-86F0-51EE-98DF-5FBD0B5669BE
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::AutoSuggestBoxHelper>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColumnMajorUniformToLargestGridLayout>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::ComboBoxHelper>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBar>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::CommandBarFlyoutCommandBarTemplateSettings>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterConverter>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverter>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::InfoBarPanel>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::MonochromaticOverlayPresenter>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenter>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::NavigationViewItemPresenterTemplateSettings>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Controls::Primitives::TabViewListView>{ using type = winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView; };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_KeepInteriorCornersSquareProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetKeepInteriorCornersSquare(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetKeepInteriorCornersSquare(void*, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ColorChannel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ColorChannel(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ColorChannelProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Color(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_Color(struct struct_Windows_UI_Color) noexcept = 0;
            virtual int32_t __stdcall get_HsvColor(winrt::Windows::Foundation::Numerics::float4*) noexcept = 0;
            virtual int32_t __stdcall put_HsvColor(winrt::Windows::Foundation::Numerics::float4) noexcept = 0;
            virtual int32_t __stdcall get_MinHue(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MinHue(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxHue(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxHue(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MinSaturation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MinSaturation(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxSaturation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxSaturation(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MinValue(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MinValue(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_MaxValue(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxValue(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Shape(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Shape(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Components(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Components(int32_t) noexcept = 0;
            virtual int32_t __stdcall add_ColorChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ColorChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ColorProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_HsvColorProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinHueProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MaxHueProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinSaturationProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MaxSaturationProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinValueProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MaxValueProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ShapeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ComponentsProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MaxColumns(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MaxColumns(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ColumnSpacing(double*) noexcept = 0;
            virtual int32_t __stdcall put_ColumnSpacing(double) noexcept = 0;
            virtual int32_t __stdcall get_RowSpacing(double*) noexcept = 0;
            virtual int32_t __stdcall put_RowSpacing(double) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MaxColumnsProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ColumnSpacingProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RowSpacingProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_KeepInteriorCornersSquareProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetKeepInteriorCornersSquare(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetKeepInteriorCornersSquare(void*, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FlyoutTemplateSettings(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ControlTypeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall GetControlType(void*, int32_t*) noexcept = 0;
            virtual int32_t __stdcall SetControlType(void*, int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_OpenAnimationStartPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_OpenAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_CloseAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_CurrentWidth(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandedWidth(double*) noexcept = 0;
            virtual int32_t __stdcall get_WidthExpansionDelta(double*) noexcept = 0;
            virtual int32_t __stdcall get_WidthExpansionAnimationStartPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_WidthExpansionAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_WidthExpansionMoreButtonAnimationStartPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_WidthExpansionMoreButtonAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandUpOverflowVerticalPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandDownOverflowVerticalPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandUpAnimationStartPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandUpAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandUpAnimationHoldPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandDownAnimationStartPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandDownAnimationEndPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ExpandDownAnimationHoldPosition(double*) noexcept = 0;
            virtual int32_t __stdcall get_ContentClipRect(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall get_OverflowContentClipRect(winrt::Windows::Foundation::Rect*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Filter(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Filter(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Scale(double*) noexcept = 0;
            virtual int32_t __stdcall put_Scale(double) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FilterProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ScaleProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ConversionKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ConversionKind(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Multiplier(double*) noexcept = 0;
            virtual int32_t __stdcall put_Multiplier(double) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ConversionKindProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MultiplierProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HorizontalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_HorizontalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall get_VerticalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_VerticalOrientationPadding(struct struct_Windows_UI_Xaml_Thickness) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HorizontalOrientationPaddingProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_VerticalOrientationPaddingProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetHorizontalOrientationMargin(void*, struct struct_Windows_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall GetHorizontalOrientationMargin(void*, struct struct_Windows_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalOrientationMarginProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetVerticalOrientationMargin(void*, struct struct_Windows_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall GetVerticalOrientationMargin(void*, struct struct_Windows_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall get_VerticalOrientationMarginProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SourceElement(void**) noexcept = 0;
            virtual int32_t __stdcall put_SourceElement(void*) noexcept = 0;
            virtual int32_t __stdcall get_ReplacementColor(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_ReplacementColor(struct struct_Windows_UI_Color) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SourceElementProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ReplacementColorProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Icon(void**) noexcept = 0;
            virtual int32_t __stdcall put_Icon(void*) noexcept = 0;
            virtual int32_t __stdcall get_TemplateSettings(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_InfoBadge(void**) noexcept = 0;
            virtual int32_t __stdcall put_InfoBadge(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IconProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TemplateSettingsProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_InfoBadgeProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IconWidth(double*) noexcept = 0;
            virtual int32_t __stdcall get_SmallerIconWidth(double*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IconWidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_SmallerIconWidthProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelperStatics
    {
        [[nodiscard]] auto KeepInteriorCornersSquareProperty() const;
        auto SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox, bool value) const;
        auto GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IAutoSuggestBoxHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSlider
    {
        [[nodiscard]] auto ColorChannel() const;
        auto ColorChannel(winrt::Microsoft::UI::Xaml::Controls::ColorPickerHsvChannel const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSlider<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderStatics
    {
        [[nodiscard]] auto ColorChannelProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSliderStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorPickerSliderStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum
    {
        [[nodiscard]] auto Color() const;
        auto Color(winrt::Windows::UI::Color const& value) const;
        [[nodiscard]] auto HsvColor() const;
        auto HsvColor(winrt::Windows::Foundation::Numerics::float4 const& value) const;
        [[nodiscard]] auto MinHue() const;
        auto MinHue(int32_t value) const;
        [[nodiscard]] auto MaxHue() const;
        auto MaxHue(int32_t value) const;
        [[nodiscard]] auto MinSaturation() const;
        auto MinSaturation(int32_t value) const;
        [[nodiscard]] auto MaxSaturation() const;
        auto MaxSaturation(int32_t value) const;
        [[nodiscard]] auto MinValue() const;
        auto MinValue(int32_t value) const;
        [[nodiscard]] auto MaxValue() const;
        auto MaxValue(int32_t value) const;
        [[nodiscard]] auto Shape() const;
        auto Shape(winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumShape const& value) const;
        [[nodiscard]] auto Components() const;
        auto Components(winrt::Microsoft::UI::Xaml::Controls::ColorSpectrumComponents const& value) const;
        auto ColorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum, winrt::Microsoft::UI::Xaml::Controls::ColorChangedEventArgs> const& handler) const;
        using ColorChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum, &impl::abi_t<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>::remove_ColorChanged>;
        [[nodiscard]] auto ColorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum, winrt::Microsoft::UI::Xaml::Controls::ColorChangedEventArgs> const& handler) const;
        auto ColorChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrum<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics
    {
        [[nodiscard]] auto ColorProperty() const;
        [[nodiscard]] auto HsvColorProperty() const;
        [[nodiscard]] auto MinHueProperty() const;
        [[nodiscard]] auto MaxHueProperty() const;
        [[nodiscard]] auto MinSaturationProperty() const;
        [[nodiscard]] auto MaxSaturationProperty() const;
        [[nodiscard]] auto MinValueProperty() const;
        [[nodiscard]] auto MaxValueProperty() const;
        [[nodiscard]] auto ShapeProperty() const;
        [[nodiscard]] auto ComponentsProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrumStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColorSpectrumStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout
    {
        [[nodiscard]] auto MaxColumns() const;
        auto MaxColumns(int32_t value) const;
        [[nodiscard]] auto ColumnSpacing() const;
        auto ColumnSpacing(double value) const;
        [[nodiscard]] auto RowSpacing() const;
        auto RowSpacing(double value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayout<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutStatics
    {
        [[nodiscard]] auto MaxColumnsProperty() const;
        [[nodiscard]] auto ColumnSpacingProperty() const;
        [[nodiscard]] auto RowSpacingProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayoutStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IColumnMajorUniformToLargestGridLayoutStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelperStatics
    {
        [[nodiscard]] auto KeepInteriorCornersSquareProperty() const;
        auto SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox, bool value) const;
        auto GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IComboBoxHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBar
    {
        [[nodiscard]] auto FlyoutTemplateSettings() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBar<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarAutomationPropertiesStatics
    {
        [[nodiscard]] auto ControlTypeProperty() const;
        auto GetControlType(winrt::Windows::UI::Xaml::UIElement const& element) const;
        auto SetControlType(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarAutomationPropertiesStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarAutomationPropertiesStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings
    {
        [[nodiscard]] auto OpenAnimationStartPosition() const;
        [[nodiscard]] auto OpenAnimationEndPosition() const;
        [[nodiscard]] auto CloseAnimationEndPosition() const;
        [[nodiscard]] auto CurrentWidth() const;
        [[nodiscard]] auto ExpandedWidth() const;
        [[nodiscard]] auto WidthExpansionDelta() const;
        [[nodiscard]] auto WidthExpansionAnimationStartPosition() const;
        [[nodiscard]] auto WidthExpansionAnimationEndPosition() const;
        [[nodiscard]] auto WidthExpansionMoreButtonAnimationStartPosition() const;
        [[nodiscard]] auto WidthExpansionMoreButtonAnimationEndPosition() const;
        [[nodiscard]] auto ExpandUpOverflowVerticalPosition() const;
        [[nodiscard]] auto ExpandDownOverflowVerticalPosition() const;
        [[nodiscard]] auto ExpandUpAnimationStartPosition() const;
        [[nodiscard]] auto ExpandUpAnimationEndPosition() const;
        [[nodiscard]] auto ExpandUpAnimationHoldPosition() const;
        [[nodiscard]] auto ExpandDownAnimationStartPosition() const;
        [[nodiscard]] auto ExpandDownAnimationEndPosition() const;
        [[nodiscard]] auto ExpandDownAnimationHoldPosition() const;
        [[nodiscard]] auto ContentClipRect() const;
        [[nodiscard]] auto OverflowContentClipRect() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICommandBarFlyoutCommandBarTemplateSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter
    {
        [[nodiscard]] auto Filter() const;
        auto Filter(winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusFilterKind const& value) const;
        [[nodiscard]] auto Scale() const;
        auto Scale(double value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverter<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverterStatics
    {
        [[nodiscard]] auto FilterProperty() const;
        [[nodiscard]] auto ScaleProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverterStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusFilterConverterStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter
    {
        [[nodiscard]] auto ConversionKind() const;
        auto ConversionKind(winrt::Microsoft::UI::Xaml::Controls::Primitives::CornerRadiusToThicknessConverterKind const& value) const;
        [[nodiscard]] auto Multiplier() const;
        auto Multiplier(double value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverter<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverterStatics
    {
        [[nodiscard]] auto ConversionKindProperty() const;
        [[nodiscard]] auto MultiplierProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverterStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ICornerRadiusToThicknessConverterStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel
    {
        [[nodiscard]] auto HorizontalOrientationPadding() const;
        auto HorizontalOrientationPadding(winrt::Windows::UI::Xaml::Thickness const& value) const;
        [[nodiscard]] auto VerticalOrientationPadding() const;
        auto VerticalOrientationPadding(winrt::Windows::UI::Xaml::Thickness const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanel<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics
    {
        [[nodiscard]] auto HorizontalOrientationPaddingProperty() const;
        [[nodiscard]] auto VerticalOrientationPaddingProperty() const;
        auto SetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value) const;
        auto GetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object) const;
        [[nodiscard]] auto HorizontalOrientationMarginProperty() const;
        auto SetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value) const;
        auto GetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object) const;
        [[nodiscard]] auto VerticalOrientationMarginProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanelStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IInfoBarPanelStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter
    {
        [[nodiscard]] auto SourceElement() const;
        auto SourceElement(winrt::Windows::UI::Xaml::UIElement const& value) const;
        [[nodiscard]] auto ReplacementColor() const;
        auto ReplacementColor(winrt::Windows::UI::Color const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenter<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterStatics
    {
        [[nodiscard]] auto SourceElementProperty() const;
        [[nodiscard]] auto ReplacementColorProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenterStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_IMonochromaticOverlayPresenterStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter
    {
        [[nodiscard]] auto Icon() const;
        auto Icon(winrt::Windows::UI::Xaml::Controls::IconElement const& value) const;
        [[nodiscard]] auto TemplateSettings() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter2
    {
        [[nodiscard]] auto InfoBadge() const;
        auto InfoBadge(winrt::Microsoft::UI::Xaml::Controls::InfoBadge const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenter2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics
    {
        [[nodiscard]] auto IconProperty() const;
        [[nodiscard]] auto TemplateSettingsProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics2
    {
        [[nodiscard]] auto InfoBadgeProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterStatics2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterStatics2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettings
    {
        [[nodiscard]] auto IconWidth() const;
        [[nodiscard]] auto SmallerIconWidth() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsStatics
    {
        [[nodiscard]] auto IconWidthProperty() const;
        [[nodiscard]] auto SmallerIconWidthProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettingsStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_INavigationViewItemPresenterTemplateSettingsStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ITabViewListView
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ITabViewListView<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Controls_Primitives_ITabViewListViewFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListViewFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Controls_Primitives_ITabViewListViewFactory<D>;
    };
}
#endif
