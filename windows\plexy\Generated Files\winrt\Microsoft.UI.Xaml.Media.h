// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Media_H
#define WINRT_Microsoft_UI_Xaml_Media_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.Foundation.Collections.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Media.2.h"
#include "winrt/impl/Microsoft.UI.Xaml.Media.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::BackgroundSource() const
    {
        winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->get_BackgroundSource(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::BackgroundSource(winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->put_BackgroundSource(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintColor() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->get_TintColor(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintColor(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->put_TintColor(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintOpacity() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->get_TintOpacity(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintOpacity(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->put_TintOpacity(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintTransitionDuration() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->get_TintTransitionDuration(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::TintTransitionDuration(winrt::Windows::Foundation::TimeSpan const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->put_TintTransitionDuration(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::AlwaysUseFallback() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->get_AlwaysUseFallback(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>::AlwaysUseFallback(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush)->put_AlwaysUseFallback(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush2<D>::TintLuminosityOpacity() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2)->get_TintLuminosityOpacity(&value));
        return winrt::Windows::Foundation::IReference<double>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrush2<D>::TintLuminosityOpacity(winrt::Windows::Foundation::IReference<double> const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2)->put_TintLuminosityOpacity(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Media::AcrylicBrush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>::BackgroundSourceProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics)->get_BackgroundSourceProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>::TintColorProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics)->get_TintColorProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>::TintOpacityProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics)->get_TintOpacityProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>::TintTransitionDurationProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics)->get_TintTransitionDurationProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>::AlwaysUseFallbackProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics)->get_AlwaysUseFallbackProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics2<D>::TintLuminosityOpacityProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2)->get_TintLuminosityOpacityProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::Center() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_Center(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::Center(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_Center(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::RadiusX() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_RadiusX(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::RadiusX(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_RadiusX(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::RadiusY() const
    {
        double value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_RadiusY(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::RadiusY(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_RadiusY(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::GradientOrigin() const
    {
        winrt::Windows::Foundation::Point value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_GradientOrigin(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::GradientOrigin(winrt::Windows::Foundation::Point const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_GradientOrigin(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::MappingMode() const
    {
        winrt::Windows::UI::Xaml::Media::BrushMappingMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_MappingMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::MappingMode(winrt::Windows::UI::Xaml::Media::BrushMappingMode const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_MappingMode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::InterpolationSpace() const
    {
        winrt::Windows::UI::Composition::CompositionColorSpace value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_InterpolationSpace(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::InterpolationSpace(winrt::Windows::UI::Composition::CompositionColorSpace const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_InterpolationSpace(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::SpreadMethod() const
    {
        winrt::Windows::UI::Xaml::Media::GradientSpreadMethod value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_SpreadMethod(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::SpreadMethod(winrt::Windows::UI::Xaml::Media::GradientSpreadMethod const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->put_SpreadMethod(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>::GradientStops() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush)->get_GradientStops(&value));
        return winrt::Windows::Foundation::Collections::IObservableVector<winrt::Windows::UI::Xaml::Media::GradientStop>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::CenterProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_CenterProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::RadiusXProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_RadiusXProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::RadiusYProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_RadiusYProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::GradientOriginProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_GradientOriginProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::InterpolationSpaceProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_InterpolationSpaceProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::MappingModeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_MappingModeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>::SpreadMethodProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics)->get_SpreadMethodProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBackgroundBrushFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBorderBrushFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::Color() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->get_Color(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::Color(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->put_Color(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::TargetTheme() const
    {
        winrt::Windows::UI::Xaml::ApplicationTheme value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->get_TargetTheme(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::TargetTheme(winrt::Windows::UI::Xaml::ApplicationTheme const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->put_TargetTheme(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::AlwaysUseFallback() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->get_AlwaysUseFallback(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>::AlwaysUseFallback(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrush)->put_AlwaysUseFallback(value));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushProtectedFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Media::RevealBrush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::ColorProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->get_ColorProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::TargetThemeProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->get_TargetThemeProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::AlwaysUseFallbackProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->get_AlwaysUseFallbackProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::StateProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->get_StateProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::SetState(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::UI::Xaml::Media::RevealBrushState const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->SetState(*(void**)(&element), static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>::GetState(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        winrt::Microsoft::UI::Xaml::Media::RevealBrushState result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics)->GetState(*(void**)(&element), reinterpret_cast<int32_t*>(&result)));
        return result;
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush>
    {
        int32_t __stdcall get_BackgroundSource(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource>(this->shim().BackgroundSource());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BackgroundSource(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BackgroundSource(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintColor(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().TintColor());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TintColor(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TintColor(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintOpacity(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().TintOpacity());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TintOpacity(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TintOpacity(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintTransitionDuration(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().TintTransitionDuration());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TintTransitionDuration(int64_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TintTransitionDuration(*reinterpret_cast<winrt::Windows::Foundation::TimeSpan const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AlwaysUseFallback(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AlwaysUseFallback());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AlwaysUseFallback(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AlwaysUseFallback(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2>
    {
        int32_t __stdcall get_TintLuminosityOpacity(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IReference<double>>(this->shim().TintLuminosityOpacity());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TintLuminosityOpacity(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TintLuminosityOpacity(*reinterpret_cast<winrt::Windows::Foundation::IReference<double> const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::AcrylicBrush>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics>
    {
        int32_t __stdcall get_BackgroundSourceProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().BackgroundSourceProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintColorProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TintColorProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintOpacityProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TintOpacityProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TintTransitionDurationProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TintTransitionDurationProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AlwaysUseFallbackProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AlwaysUseFallbackProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2>
    {
        int32_t __stdcall get_TintLuminosityOpacityProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TintLuminosityOpacityProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush>
    {
        int32_t __stdcall get_Center(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().Center());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Center(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Center(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RadiusX(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().RadiusX());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RadiusX(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RadiusX(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RadiusY(double* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<double>(this->shim().RadiusY());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RadiusY(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RadiusY(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_GradientOrigin(winrt::Windows::Foundation::Point* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::Point>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Point>(this->shim().GradientOrigin());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_GradientOrigin(winrt::Windows::Foundation::Point value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GradientOrigin(*reinterpret_cast<winrt::Windows::Foundation::Point const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MappingMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::BrushMappingMode>(this->shim().MappingMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_MappingMode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MappingMode(*reinterpret_cast<winrt::Windows::UI::Xaml::Media::BrushMappingMode const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InterpolationSpace(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Composition::CompositionColorSpace>(this->shim().InterpolationSpace());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InterpolationSpace(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InterpolationSpace(*reinterpret_cast<winrt::Windows::UI::Composition::CompositionColorSpace const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SpreadMethod(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::GradientSpreadMethod>(this->shim().SpreadMethod());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SpreadMethod(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SpreadMethod(*reinterpret_cast<winrt::Windows::UI::Xaml::Media::GradientSpreadMethod const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_GradientStops(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IObservableVector<winrt::Windows::UI::Xaml::Media::GradientStop>>(this->shim().GradientStops());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics>
    {
        int32_t __stdcall get_CenterProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().CenterProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RadiusXProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().RadiusXProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RadiusYProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().RadiusYProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_GradientOriginProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().GradientOriginProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InterpolationSpaceProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().InterpolationSpaceProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_MappingModeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().MappingModeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SpreadMethodProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().SpreadMethodProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrush> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrush>
    {
        int32_t __stdcall get_Color(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().Color());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Color(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Color(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TargetTheme(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::ApplicationTheme>(this->shim().TargetTheme());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_TargetTheme(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().TargetTheme(*reinterpret_cast<winrt::Windows::UI::Xaml::ApplicationTheme const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AlwaysUseFallback(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AlwaysUseFallback());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AlwaysUseFallback(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AlwaysUseFallback(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Media::RevealBrush>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics> : produce_base<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics>
    {
        int32_t __stdcall get_ColorProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ColorProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TargetThemeProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TargetThemeProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AlwaysUseFallbackProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AlwaysUseFallbackProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_StateProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().StateProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetState(void* element, int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetState(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::UI::Xaml::Media::RevealBrushState const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetState(void* element, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::UI::Xaml::Media::RevealBrushState>(this->shim().GetState(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media
{
    inline AcrylicBrush::AcrylicBrush()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<AcrylicBrush, IAcrylicBrushFactory>([&](IAcrylicBrushFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto AcrylicBrush::BackgroundSourceProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics const&), AcrylicBrush, IAcrylicBrushStatics>([](IAcrylicBrushStatics const& f) { return f.BackgroundSourceProperty(); });
    }
    inline auto AcrylicBrush::TintColorProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics const&), AcrylicBrush, IAcrylicBrushStatics>([](IAcrylicBrushStatics const& f) { return f.TintColorProperty(); });
    }
    inline auto AcrylicBrush::TintOpacityProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics const&), AcrylicBrush, IAcrylicBrushStatics>([](IAcrylicBrushStatics const& f) { return f.TintOpacityProperty(); });
    }
    inline auto AcrylicBrush::TintTransitionDurationProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics const&), AcrylicBrush, IAcrylicBrushStatics>([](IAcrylicBrushStatics const& f) { return f.TintTransitionDurationProperty(); });
    }
    inline auto AcrylicBrush::AlwaysUseFallbackProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics const&), AcrylicBrush, IAcrylicBrushStatics>([](IAcrylicBrushStatics const& f) { return f.AlwaysUseFallbackProperty(); });
    }
    inline auto AcrylicBrush::TintLuminosityOpacityProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IAcrylicBrushStatics2 const&), AcrylicBrush, IAcrylicBrushStatics2>([](IAcrylicBrushStatics2 const& f) { return f.TintLuminosityOpacityProperty(); });
    }
    inline RadialGradientBrush::RadialGradientBrush()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RadialGradientBrush, IRadialGradientBrushFactory>([&](IRadialGradientBrushFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto RadialGradientBrush::CenterProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.CenterProperty(); });
    }
    inline auto RadialGradientBrush::RadiusXProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.RadiusXProperty(); });
    }
    inline auto RadialGradientBrush::RadiusYProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.RadiusYProperty(); });
    }
    inline auto RadialGradientBrush::GradientOriginProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.GradientOriginProperty(); });
    }
    inline auto RadialGradientBrush::InterpolationSpaceProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.InterpolationSpaceProperty(); });
    }
    inline auto RadialGradientBrush::MappingModeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.MappingModeProperty(); });
    }
    inline auto RadialGradientBrush::SpreadMethodProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRadialGradientBrushStatics const&), RadialGradientBrush, IRadialGradientBrushStatics>([](IRadialGradientBrushStatics const& f) { return f.SpreadMethodProperty(); });
    }
    inline RevealBackgroundBrush::RevealBackgroundBrush()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RevealBackgroundBrush, IRevealBackgroundBrushFactory>([&](IRevealBackgroundBrushFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline RevealBorderBrush::RevealBorderBrush()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RevealBorderBrush, IRevealBorderBrushFactory>([&](IRevealBorderBrushFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto RevealBrush::ColorProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRevealBrushStatics const&), RevealBrush, IRevealBrushStatics>([](IRevealBrushStatics const& f) { return f.ColorProperty(); });
    }
    inline auto RevealBrush::TargetThemeProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRevealBrushStatics const&), RevealBrush, IRevealBrushStatics>([](IRevealBrushStatics const& f) { return f.TargetThemeProperty(); });
    }
    inline auto RevealBrush::AlwaysUseFallbackProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRevealBrushStatics const&), RevealBrush, IRevealBrushStatics>([](IRevealBrushStatics const& f) { return f.AlwaysUseFallbackProperty(); });
    }
    inline auto RevealBrush::StateProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IRevealBrushStatics const&), RevealBrush, IRevealBrushStatics>([](IRevealBrushStatics const& f) { return f.StateProperty(); });
    }
    inline auto RevealBrush::SetState(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::UI::Xaml::Media::RevealBrushState const& value)
    {
        impl::call_factory<RevealBrush, IRevealBrushStatics>([&](IRevealBrushStatics const& f) { return f.SetState(element, value); });
    }
    inline auto RevealBrush::GetState(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<RevealBrush, IRevealBrushStatics>([&](IRevealBrushStatics const& f) { return f.GetState(element); });
    }
    template <typename D, typename... Interfaces>
    struct AcrylicBrushT :
        implements<D, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, AcrylicBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, winrt::Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using composable = AcrylicBrush;
    protected:
        AcrylicBrushT()
        {
            impl::call_factory<AcrylicBrush, IAcrylicBrushFactory>([&](IAcrylicBrushFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RadialGradientBrushT :
        implements<D, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RadialGradientBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, winrt::Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using composable = RadialGradientBrush;
    protected:
        RadialGradientBrushT()
        {
            impl::call_factory<RadialGradientBrush, IRadialGradientBrushFactory>([&](IRadialGradientBrushFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RevealBackgroundBrushT :
        implements<D, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush, winrt::Microsoft::UI::Xaml::Media::IRevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RevealBackgroundBrush, winrt::Microsoft::UI::Xaml::Media::RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, winrt::Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using composable = RevealBackgroundBrush;
    protected:
        RevealBackgroundBrushT()
        {
            impl::call_factory<RevealBackgroundBrush, IRevealBackgroundBrushFactory>([&](IRevealBackgroundBrushFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RevealBorderBrushT :
        implements<D, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush, winrt::Microsoft::UI::Xaml::Media::IRevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RevealBorderBrush, winrt::Microsoft::UI::Xaml::Media::RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, winrt::Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using composable = RevealBorderBrush;
    protected:
        RevealBorderBrushT()
        {
            impl::call_factory<RevealBorderBrush, IRevealBorderBrushFactory>([&](IRevealBorderBrushFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RevealBrushT :
        implements<D, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Media::IRevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverridesT<D>, winrt::Windows::UI::Xaml::Media::IBrushOverrides2T<D>
    {
        using composable = RevealBrush;
    protected:
        RevealBrushT()
        {
            impl::call_factory<RevealBrush, IRevealBrushProtectedFactory>([&](IRevealBrushProtectedFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::AcrylicBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Media::RevealBrush> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
