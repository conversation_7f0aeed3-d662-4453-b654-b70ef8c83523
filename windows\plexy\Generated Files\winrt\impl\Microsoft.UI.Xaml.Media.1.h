// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Media_1_H
#define WINRT_Microsoft_UI_Xaml_Media_1_H
#include "winrt/impl/Microsoft.UI.Xaml.Media.0.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media
{
    struct WINRT_IMPL_EMPTY_BASES IAcrylicBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrush>
    {
        IAcrylicBrush(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAcrylicBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrush2>
    {
        IAcrylicBrush2(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAcrylicBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushFactory>
    {
        IAcrylicBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAcrylicBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushStatics>
    {
        IAcrylicBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAcrylicBrushStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAcrylicBrushStatics2>
    {
        IAcrylicBrushStatics2(std::nullptr_t = nullptr) noexcept {}
        IAcrylicBrushStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialGradientBrush>
    {
        IRadialGradientBrush(std::nullptr_t = nullptr) noexcept {}
        IRadialGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialGradientBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialGradientBrushFactory>
    {
        IRadialGradientBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRadialGradientBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadialGradientBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadialGradientBrushStatics>
    {
        IRadialGradientBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IRadialGradientBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBackgroundBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBackgroundBrush>
    {
        IRevealBackgroundBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBackgroundBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBackgroundBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBackgroundBrushFactory>
    {
        IRevealBackgroundBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBackgroundBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBorderBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBorderBrush>
    {
        IRevealBorderBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBorderBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBorderBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBorderBrushFactory>
    {
        IRevealBorderBrushFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBorderBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrush>
    {
        IRevealBrush(std::nullptr_t = nullptr) noexcept {}
        IRevealBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBrushProtectedFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrushProtectedFactory>
    {
        IRevealBrushProtectedFactory(std::nullptr_t = nullptr) noexcept {}
        IRevealBrushProtectedFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRevealBrushStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRevealBrushStatics>
    {
        IRevealBrushStatics(std::nullptr_t = nullptr) noexcept {}
        IRevealBrushStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
