// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_UI_Xaml_Media_Animation_1_H
#define WINRT_Windows_UI_Xaml_Media_Animation_1_H
#include "winrt/impl/Windows.UI.Xaml.Media.Animation.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media::Animation
{
    struct WINRT_IMPL_EMPTY_BASES IAddDeleteThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAddDeleteThemeTransition>
    {
        IAddDeleteThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IAddDeleteThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackEase>
    {
        IBackEase(std::nullptr_t = nullptr) noexcept {}
        IBackEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackEaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackEaseStatics>
    {
        IBackEaseStatics(std::nullptr_t = nullptr) noexcept {}
        IBackEaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBasicConnectedAnimationConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBasicConnectedAnimationConfiguration>
    {
        IBasicConnectedAnimationConfiguration(std::nullptr_t = nullptr) noexcept {}
        IBasicConnectedAnimationConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBasicConnectedAnimationConfigurationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBasicConnectedAnimationConfigurationFactory>
    {
        IBasicConnectedAnimationConfigurationFactory(std::nullptr_t = nullptr) noexcept {}
        IBasicConnectedAnimationConfigurationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBeginStoryboard :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBeginStoryboard>
    {
        IBeginStoryboard(std::nullptr_t = nullptr) noexcept {}
        IBeginStoryboard(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBeginStoryboardStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBeginStoryboardStatics>
    {
        IBeginStoryboardStatics(std::nullptr_t = nullptr) noexcept {}
        IBeginStoryboardStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceEase>
    {
        IBounceEase(std::nullptr_t = nullptr) noexcept {}
        IBounceEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceEaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceEaseStatics>
    {
        IBounceEaseStatics(std::nullptr_t = nullptr) noexcept {}
        IBounceEaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICircleEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICircleEase>
    {
        ICircleEase(std::nullptr_t = nullptr) noexcept {}
        ICircleEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorAnimation>
    {
        IColorAnimation(std::nullptr_t = nullptr) noexcept {}
        IColorAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorAnimationStatics>
    {
        IColorAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IColorAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorAnimationUsingKeyFrames :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorAnimationUsingKeyFrames>
    {
        IColorAnimationUsingKeyFrames(std::nullptr_t = nullptr) noexcept {}
        IColorAnimationUsingKeyFrames(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorAnimationUsingKeyFramesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorAnimationUsingKeyFramesStatics>
    {
        IColorAnimationUsingKeyFramesStatics(std::nullptr_t = nullptr) noexcept {}
        IColorAnimationUsingKeyFramesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorKeyFrame>
    {
        IColorKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IColorKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorKeyFrameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorKeyFrameFactory>
    {
        IColorKeyFrameFactory(std::nullptr_t = nullptr) noexcept {}
        IColorKeyFrameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorKeyFrameStatics>
    {
        IColorKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IColorKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommonNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommonNavigationTransitionInfo>
    {
        ICommonNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        ICommonNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommonNavigationTransitionInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommonNavigationTransitionInfoStatics>
    {
        ICommonNavigationTransitionInfoStatics(std::nullptr_t = nullptr) noexcept {}
        ICommonNavigationTransitionInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimation>
    {
        IConnectedAnimation(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimation2>
    {
        IConnectedAnimation2(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimation3>
    {
        IConnectedAnimation3(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimationConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimationConfiguration>
    {
        IConnectedAnimationConfiguration(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimationConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimationConfigurationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimationConfigurationFactory>
    {
        IConnectedAnimationConfigurationFactory(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimationConfigurationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimationService :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimationService>
    {
        IConnectedAnimationService(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimationService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IConnectedAnimationServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IConnectedAnimationServiceStatics>
    {
        IConnectedAnimationServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IConnectedAnimationServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentThemeTransition>
    {
        IContentThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IContentThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentThemeTransitionStatics>
    {
        IContentThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        IContentThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContinuumNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContinuumNavigationTransitionInfo>
    {
        IContinuumNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        IContinuumNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContinuumNavigationTransitionInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContinuumNavigationTransitionInfoStatics>
    {
        IContinuumNavigationTransitionInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IContinuumNavigationTransitionInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICubicEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICubicEase>
    {
        ICubicEase(std::nullptr_t = nullptr) noexcept {}
        ICubicEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirectConnectedAnimationConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirectConnectedAnimationConfiguration>
    {
        IDirectConnectedAnimationConfiguration(std::nullptr_t = nullptr) noexcept {}
        IDirectConnectedAnimationConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDirectConnectedAnimationConfigurationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDirectConnectedAnimationConfigurationFactory>
    {
        IDirectConnectedAnimationConfigurationFactory(std::nullptr_t = nullptr) noexcept {}
        IDirectConnectedAnimationConfigurationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiscreteColorKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiscreteColorKeyFrame>
    {
        IDiscreteColorKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IDiscreteColorKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiscreteDoubleKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiscreteDoubleKeyFrame>
    {
        IDiscreteDoubleKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IDiscreteDoubleKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiscreteObjectKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiscreteObjectKeyFrame>
    {
        IDiscreteObjectKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IDiscreteObjectKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDiscretePointKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDiscretePointKeyFrame>
    {
        IDiscretePointKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IDiscretePointKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleAnimation>
    {
        IDoubleAnimation(std::nullptr_t = nullptr) noexcept {}
        IDoubleAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleAnimationStatics>
    {
        IDoubleAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDoubleAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleAnimationUsingKeyFrames :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleAnimationUsingKeyFrames>
    {
        IDoubleAnimationUsingKeyFrames(std::nullptr_t = nullptr) noexcept {}
        IDoubleAnimationUsingKeyFrames(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleAnimationUsingKeyFramesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleAnimationUsingKeyFramesStatics>
    {
        IDoubleAnimationUsingKeyFramesStatics(std::nullptr_t = nullptr) noexcept {}
        IDoubleAnimationUsingKeyFramesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleKeyFrame>
    {
        IDoubleKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IDoubleKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleKeyFrameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleKeyFrameFactory>
    {
        IDoubleKeyFrameFactory(std::nullptr_t = nullptr) noexcept {}
        IDoubleKeyFrameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDoubleKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDoubleKeyFrameStatics>
    {
        IDoubleKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IDoubleKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragItemThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragItemThemeAnimation>
    {
        IDragItemThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IDragItemThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragItemThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragItemThemeAnimationStatics>
    {
        IDragItemThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDragItemThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragOverThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragOverThemeAnimation>
    {
        IDragOverThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IDragOverThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDragOverThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDragOverThemeAnimationStatics>
    {
        IDragOverThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDragOverThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDrillInNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDrillInNavigationTransitionInfo>
    {
        IDrillInNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        IDrillInNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDrillInThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDrillInThemeAnimation>
    {
        IDrillInThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IDrillInThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDrillInThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDrillInThemeAnimationStatics>
    {
        IDrillInThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDrillInThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDrillOutThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDrillOutThemeAnimation>
    {
        IDrillOutThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IDrillOutThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDrillOutThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDrillOutThemeAnimationStatics>
    {
        IDrillOutThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDrillOutThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropTargetItemThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetItemThemeAnimation>
    {
        IDropTargetItemThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IDropTargetItemThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropTargetItemThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropTargetItemThemeAnimationStatics>
    {
        IDropTargetItemThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IDropTargetItemThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingColorKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingColorKeyFrame>
    {
        IEasingColorKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IEasingColorKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingColorKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingColorKeyFrameStatics>
    {
        IEasingColorKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IEasingColorKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingDoubleKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingDoubleKeyFrame>
    {
        IEasingDoubleKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IEasingDoubleKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingDoubleKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingDoubleKeyFrameStatics>
    {
        IEasingDoubleKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IEasingDoubleKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingFunctionBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingFunctionBase>
    {
        IEasingFunctionBase(std::nullptr_t = nullptr) noexcept {}
        IEasingFunctionBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingFunctionBaseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingFunctionBaseFactory>
    {
        IEasingFunctionBaseFactory(std::nullptr_t = nullptr) noexcept {}
        IEasingFunctionBaseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingFunctionBaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingFunctionBaseStatics>
    {
        IEasingFunctionBaseStatics(std::nullptr_t = nullptr) noexcept {}
        IEasingFunctionBaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingPointKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingPointKeyFrame>
    {
        IEasingPointKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IEasingPointKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEasingPointKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEasingPointKeyFrameStatics>
    {
        IEasingPointKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IEasingPointKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEdgeUIThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEdgeUIThemeTransition>
    {
        IEdgeUIThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IEdgeUIThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEdgeUIThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEdgeUIThemeTransitionStatics>
    {
        IEdgeUIThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        IEdgeUIThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElasticEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElasticEase>
    {
        IElasticEase(std::nullptr_t = nullptr) noexcept {}
        IElasticEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElasticEaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElasticEaseStatics>
    {
        IElasticEaseStatics(std::nullptr_t = nullptr) noexcept {}
        IElasticEaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEntranceNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEntranceNavigationTransitionInfo>
    {
        IEntranceNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        IEntranceNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEntranceNavigationTransitionInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEntranceNavigationTransitionInfoStatics>
    {
        IEntranceNavigationTransitionInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IEntranceNavigationTransitionInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEntranceThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEntranceThemeTransition>
    {
        IEntranceThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IEntranceThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEntranceThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEntranceThemeTransitionStatics>
    {
        IEntranceThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        IEntranceThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExponentialEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExponentialEase>
    {
        IExponentialEase(std::nullptr_t = nullptr) noexcept {}
        IExponentialEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExponentialEaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExponentialEaseStatics>
    {
        IExponentialEaseStatics(std::nullptr_t = nullptr) noexcept {}
        IExponentialEaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFadeInThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFadeInThemeAnimation>
    {
        IFadeInThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IFadeInThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFadeInThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFadeInThemeAnimationStatics>
    {
        IFadeInThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IFadeInThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFadeOutThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFadeOutThemeAnimation>
    {
        IFadeOutThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IFadeOutThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFadeOutThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFadeOutThemeAnimationStatics>
    {
        IFadeOutThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IFadeOutThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGravityConnectedAnimationConfiguration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGravityConnectedAnimationConfiguration>
    {
        IGravityConnectedAnimationConfiguration(std::nullptr_t = nullptr) noexcept {}
        IGravityConnectedAnimationConfiguration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGravityConnectedAnimationConfiguration2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGravityConnectedAnimationConfiguration2>
    {
        IGravityConnectedAnimationConfiguration2(std::nullptr_t = nullptr) noexcept {}
        IGravityConnectedAnimationConfiguration2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGravityConnectedAnimationConfigurationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGravityConnectedAnimationConfigurationFactory>
    {
        IGravityConnectedAnimationConfigurationFactory(std::nullptr_t = nullptr) noexcept {}
        IGravityConnectedAnimationConfigurationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeySpline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeySpline>
    {
        IKeySpline(std::nullptr_t = nullptr) noexcept {}
        IKeySpline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyTimeHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyTimeHelper>
    {
        IKeyTimeHelper(std::nullptr_t = nullptr) noexcept {}
        IKeyTimeHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyTimeHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyTimeHelperStatics>
    {
        IKeyTimeHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyTimeHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILinearColorKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearColorKeyFrame>
    {
        ILinearColorKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ILinearColorKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILinearDoubleKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearDoubleKeyFrame>
    {
        ILinearDoubleKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ILinearDoubleKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILinearPointKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearPointKeyFrame>
    {
        ILinearPointKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ILinearPointKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationThemeTransition>
    {
        INavigationThemeTransition(std::nullptr_t = nullptr) noexcept {}
        INavigationThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationThemeTransitionStatics>
    {
        INavigationThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationTransitionInfo>
    {
        INavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        INavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationTransitionInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationTransitionInfoFactory>
    {
        INavigationTransitionInfoFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationTransitionInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationTransitionInfoOverrides :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationTransitionInfoOverrides>
    {
        INavigationTransitionInfoOverrides(std::nullptr_t = nullptr) noexcept {}
        INavigationTransitionInfoOverrides(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IObjectAnimationUsingKeyFrames :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectAnimationUsingKeyFrames>
    {
        IObjectAnimationUsingKeyFrames(std::nullptr_t = nullptr) noexcept {}
        IObjectAnimationUsingKeyFrames(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IObjectAnimationUsingKeyFramesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectAnimationUsingKeyFramesStatics>
    {
        IObjectAnimationUsingKeyFramesStatics(std::nullptr_t = nullptr) noexcept {}
        IObjectAnimationUsingKeyFramesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IObjectKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectKeyFrame>
    {
        IObjectKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IObjectKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IObjectKeyFrameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectKeyFrameFactory>
    {
        IObjectKeyFrameFactory(std::nullptr_t = nullptr) noexcept {}
        IObjectKeyFrameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IObjectKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IObjectKeyFrameStatics>
    {
        IObjectKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IObjectKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPaneThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPaneThemeTransition>
    {
        IPaneThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IPaneThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPaneThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPaneThemeTransitionStatics>
    {
        IPaneThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        IPaneThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointAnimation>
    {
        IPointAnimation(std::nullptr_t = nullptr) noexcept {}
        IPointAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointAnimationStatics>
    {
        IPointAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IPointAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointAnimationUsingKeyFrames :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointAnimationUsingKeyFrames>
    {
        IPointAnimationUsingKeyFrames(std::nullptr_t = nullptr) noexcept {}
        IPointAnimationUsingKeyFrames(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointAnimationUsingKeyFramesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointAnimationUsingKeyFramesStatics>
    {
        IPointAnimationUsingKeyFramesStatics(std::nullptr_t = nullptr) noexcept {}
        IPointAnimationUsingKeyFramesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointKeyFrame>
    {
        IPointKeyFrame(std::nullptr_t = nullptr) noexcept {}
        IPointKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointKeyFrameFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointKeyFrameFactory>
    {
        IPointKeyFrameFactory(std::nullptr_t = nullptr) noexcept {}
        IPointKeyFrameFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointKeyFrameStatics>
    {
        IPointKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        IPointKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerDownThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerDownThemeAnimation>
    {
        IPointerDownThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IPointerDownThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerDownThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerDownThemeAnimationStatics>
    {
        IPointerDownThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IPointerDownThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerUpThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerUpThemeAnimation>
    {
        IPointerUpThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IPointerUpThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointerUpThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointerUpThemeAnimationStatics>
    {
        IPointerUpThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IPointerUpThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopInThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopInThemeAnimation>
    {
        IPopInThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IPopInThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopInThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopInThemeAnimationStatics>
    {
        IPopInThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IPopInThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopOutThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopOutThemeAnimation>
    {
        IPopOutThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IPopOutThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopOutThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopOutThemeAnimationStatics>
    {
        IPopOutThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IPopOutThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopupThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupThemeTransition>
    {
        IPopupThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IPopupThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPopupThemeTransitionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPopupThemeTransitionStatics>
    {
        IPopupThemeTransitionStatics(std::nullptr_t = nullptr) noexcept {}
        IPopupThemeTransitionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPowerEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerEase>
    {
        IPowerEase(std::nullptr_t = nullptr) noexcept {}
        IPowerEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPowerEaseStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerEaseStatics>
    {
        IPowerEaseStatics(std::nullptr_t = nullptr) noexcept {}
        IPowerEaseStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuadraticEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuadraticEase>
    {
        IQuadraticEase(std::nullptr_t = nullptr) noexcept {}
        IQuadraticEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuarticEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuarticEase>
    {
        IQuarticEase(std::nullptr_t = nullptr) noexcept {}
        IQuarticEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuinticEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuinticEase>
    {
        IQuinticEase(std::nullptr_t = nullptr) noexcept {}
        IQuinticEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IReorderThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IReorderThemeTransition>
    {
        IReorderThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IReorderThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepeatBehaviorHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatBehaviorHelper>
    {
        IRepeatBehaviorHelper(std::nullptr_t = nullptr) noexcept {}
        IRepeatBehaviorHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepeatBehaviorHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeatBehaviorHelperStatics>
    {
        IRepeatBehaviorHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IRepeatBehaviorHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepositionThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepositionThemeAnimation>
    {
        IRepositionThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        IRepositionThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepositionThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepositionThemeAnimationStatics>
    {
        IRepositionThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        IRepositionThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepositionThemeTransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepositionThemeTransition>
    {
        IRepositionThemeTransition(std::nullptr_t = nullptr) noexcept {}
        IRepositionThemeTransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepositionThemeTransition2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepositionThemeTransition2>
    {
        IRepositionThemeTransition2(std::nullptr_t = nullptr) noexcept {}
        IRepositionThemeTransition2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepositionThemeTransitionStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepositionThemeTransitionStatics2>
    {
        IRepositionThemeTransitionStatics2(std::nullptr_t = nullptr) noexcept {}
        IRepositionThemeTransitionStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISineEase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISineEase>
    {
        ISineEase(std::nullptr_t = nullptr) noexcept {}
        ISineEase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISlideNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISlideNavigationTransitionInfo>
    {
        ISlideNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        ISlideNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISlideNavigationTransitionInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISlideNavigationTransitionInfo2>
    {
        ISlideNavigationTransitionInfo2(std::nullptr_t = nullptr) noexcept {}
        ISlideNavigationTransitionInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISlideNavigationTransitionInfoStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISlideNavigationTransitionInfoStatics2>
    {
        ISlideNavigationTransitionInfoStatics2(std::nullptr_t = nullptr) noexcept {}
        ISlideNavigationTransitionInfoStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplineColorKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplineColorKeyFrame>
    {
        ISplineColorKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ISplineColorKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplineColorKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplineColorKeyFrameStatics>
    {
        ISplineColorKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        ISplineColorKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplineDoubleKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplineDoubleKeyFrame>
    {
        ISplineDoubleKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ISplineDoubleKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplineDoubleKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplineDoubleKeyFrameStatics>
    {
        ISplineDoubleKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        ISplineDoubleKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplinePointKeyFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplinePointKeyFrame>
    {
        ISplinePointKeyFrame(std::nullptr_t = nullptr) noexcept {}
        ISplinePointKeyFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplinePointKeyFrameStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplinePointKeyFrameStatics>
    {
        ISplinePointKeyFrameStatics(std::nullptr_t = nullptr) noexcept {}
        ISplinePointKeyFrameStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitCloseThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitCloseThemeAnimation>
    {
        ISplitCloseThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        ISplitCloseThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitCloseThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitCloseThemeAnimationStatics>
    {
        ISplitCloseThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        ISplitCloseThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitOpenThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitOpenThemeAnimation>
    {
        ISplitOpenThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        ISplitOpenThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitOpenThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitOpenThemeAnimationStatics>
    {
        ISplitOpenThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        ISplitOpenThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStoryboard :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoryboard>
    {
        IStoryboard(std::nullptr_t = nullptr) noexcept {}
        IStoryboard(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStoryboardStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoryboardStatics>
    {
        IStoryboardStatics(std::nullptr_t = nullptr) noexcept {}
        IStoryboardStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISuppressNavigationTransitionInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISuppressNavigationTransitionInfo>
    {
        ISuppressNavigationTransitionInfo(std::nullptr_t = nullptr) noexcept {}
        ISuppressNavigationTransitionInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISwipeBackThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeBackThemeAnimation>
    {
        ISwipeBackThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        ISwipeBackThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISwipeBackThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeBackThemeAnimationStatics>
    {
        ISwipeBackThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        ISwipeBackThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISwipeHintThemeAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeHintThemeAnimation>
    {
        ISwipeHintThemeAnimation(std::nullptr_t = nullptr) noexcept {}
        ISwipeHintThemeAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISwipeHintThemeAnimationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISwipeHintThemeAnimationStatics>
    {
        ISwipeHintThemeAnimationStatics(std::nullptr_t = nullptr) noexcept {}
        ISwipeHintThemeAnimationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimeline :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeline>
    {
        ITimeline(std::nullptr_t = nullptr) noexcept {}
        ITimeline(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimelineFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimelineFactory>
    {
        ITimelineFactory(std::nullptr_t = nullptr) noexcept {}
        ITimelineFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimelineStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimelineStatics>
    {
        ITimelineStatics(std::nullptr_t = nullptr) noexcept {}
        ITimelineStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransition>
    {
        ITransition(std::nullptr_t = nullptr) noexcept {}
        ITransition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITransitionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITransitionFactory>
    {
        ITransitionFactory(std::nullptr_t = nullptr) noexcept {}
        ITransitionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
