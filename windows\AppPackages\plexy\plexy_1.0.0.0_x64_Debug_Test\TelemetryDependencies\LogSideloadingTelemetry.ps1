﻿#
# This script handles common telemetry tasks for Install.ps1 and Add-AppDevPackage.ps1.
#

function IsVsTelemetryRegOptOutSet()
{
    $VsTelemetryRegOptOutKeys = @(
        "HKLM:\SOFTWARE\Policies\Microsoft\VisualStudio\SQM",
        "HKLM:\SOFTWARE\Wow6432Node\Microsoft\VSCommon\16.0\SQM",
        "HKLM:\SOFTWARE\Microsoft\VSCommon\16.0\SQM",
        "HKLM:\SOFTWARE\Wow6432Node\Microsoft\VSCommon\17.0\SQM",
        "HKLM:\SOFTWARE\Microsoft\VSCommon\17.0\SQM"
    )

    foreach ($optOutKey in $VsTelemetryRegOptOutKeys)
    {
        if (Test-Path $optOutKey)
        {
            # If any of these keys have the DWORD value OptIn set to 0 that means that the user
            # has explicitly opted out of logging telemetry from Visual Studio.
            $val = (Get-ItemProperty $optOutKey)."OptIn"
            if ($val -eq 0)
            {
                return $true
            }
        }
    }

    return $false
}

try
{
    $TelemetryOptedOut = IsVsTelemetryRegOptOutSet
    if (!$TelemetryOptedOut)
    {
        $TelemetryAssembliesFolder = $args[0]
        $EventName = $args[1]
        $ReturnCode = $args[2]
        $ProcessorArchitecture = $args[3]

        foreach ($file in Get-ChildItem $TelemetryAssembliesFolder -Filter "*.dll")
        {
            [Reflection.Assembly]::LoadFile((Join-Path $TelemetryAssembliesFolder $file)) | Out-Null
        }

        [Microsoft.VisualStudio.Telemetry.TelemetryService]::DefaultSession.IsOptedIn = $True
        [Microsoft.VisualStudio.Telemetry.TelemetryService]::DefaultSession.Start()

        $TelEvent = New-Object "Microsoft.VisualStudio.Telemetry.TelemetryEvent" -ArgumentList $EventName
        if ($null -ne $ReturnCode)
        {
            $TelEvent.Properties["VS.DesignTools.SideLoadingScript.ReturnCode"] = $ReturnCode
        }

        if ($null -ne $ProcessorArchitecture)
        {
            $TelEvent.Properties["VS.DesignTools.SideLoadingScript.ProcessorArchitecture"] = $ProcessorArchitecture
        }

        [Microsoft.VisualStudio.Telemetry.TelemetryService]::DefaultSession.PostEvent($TelEvent)
        [Microsoft.VisualStudio.Telemetry.TelemetryService]::DefaultSession.Dispose()
    }
}
catch
{
    # Ignore telemetry errors
}
# SIG # Begin signature block
# MIIphgYJKoZIhvcNAQcCoIIpdzCCKXMCAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBs5RUy/PKaA/U5
# /+cdRqsBJInUnLtOhcpeK3MamAG/SqCCDdYwgga9MIIEpaADAgECAhMzAAAAHEif
# gd+hsLd3AAAAAAAcMA0GCSqGSIb3DQEBDAUAMIGIMQswCQYDVQQGEwJVUzETMBEG
# A1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWlj
# cm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3NvZnQgUm9vdCBDZXJ0
# aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yNDA4MDgyMTM2MjNaFw0zNTA2MjMy
# MjA0MDFaMF8xCzAJBgNVBAYTAlVTMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y
# YXRpb24xMDAuBgNVBAMTJ01pY3Jvc29mdCBXaW5kb3dzIENvZGUgU2lnbmluZyBQ
# Q0EgMjAyNDCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAJp9a30nwXYq
# Lq7j1TT/zCtt7vxU+CCj+7BkifS/B2gXKGU7OV9SXRJGP1yFs5p6jpsYi4cYzF56
# AV0AEmmEjV8wT2lvPU5BhN3wV30HqYPIYEj5P3WXf0kXD9fvjUf1GAtXEriJ8w7A
# LNaVEm9Rs4ePA0ZsYHaCbU5kBUJQDXv76hafOcQgdFCA3I3zYtfzX2vOwx87uDOa
# CuyKORZih9c3zTf+TLC5QYLyhVMBnDXEHDOrvaw92DSyIqpdgRWpufzqDFy1egVj
# koXZhb+9pZ9heUzNXTXhOoXzexh6YzAL4flBWm+Bc1hQyESenEvBJznV+25u3h77
# jjgMUY44+WXQ4u9qddDe/U5SeAaKRvvibmi4z7QRpLvZsla0CPiOUGz00Do5sfkC
# 0EwlsSzfM3+8A9rsyFVOgWDVPzt98OJP2EoaEOq8GE9GCoN2i7/4C2FCwff1BSCT
# JWZO1Wcr2MteJE6UxGV+ihA8nN51YPKD2dYGoewrXvRzC/1HoUeSvlZf0mf9GHEt
# vvkbJVRRo6PBf0md5t87Vb1mM/fIp1eypyaxmXkgpcBwuylsOq2kSVOJ5wBPoaEs
# sJkeMcKnEuuu++UKdDHlS0DtsYjN0QnOucvTdSsdvhzKOSjJF3XVqr9f2C945LXT
# 5rxKIHUIEDBcNYU6BKDDH6rfpKOOCSilAgMBAAGjggFGMIIBQjAOBgNVHQ8BAf8E
# BAMCAYYwEAYJKwYBBAGCNxUBBAMCAQAwHQYDVR0OBBYEFB6C3w7XjLPXAjSDDtqr
# rWW5r7jsMBkGCSsGAQQBgjcUAgQMHgoAUwB1AGIAQwBBMA8GA1UdEwEB/wQFMAMB
# Af8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL
# oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv
# TWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr
# BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNS
# b29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQEMBQADggIBAENf+N8/
# u+mUjDtc9btoA52RBc0XVDSBMQBMqxu56hXHBwuctUWs1XBqDDMIFCHu9c6Y/UF+
# TN8EIgjnujApKYmHP4f4EM3ARSmlzrpF5ozOJx0BA5FUv1jmpdf/2ZbqpvCxlxv/
# G1R4KjrSmmqPHzs6igw3b7RTbj7BxIS8fOIkwYWQhB2fLjlg+3HSrDGPFIhpIJWV
# amMIR7a72OGonjdf45rspwqIHuynZU4avy9ruB/Rhhbwm+fMb8BMecIaTmkohx/E
# ZZ5GNWcN6oTYW3G2BM3B3YznWkl9t4shP60fMue+2ksdHGWSE8EVTdSmGUdj0jrU
# c46lGVFJISF3/MxcxnlNeP1Khyr+ZzT4Ets/I7mufpaLnLalzMR2zIuhGOAWWswe
# sbjtFzkVUFgDR2SW903I0XKlbPEA6q8epHGJ9roxh85nsEKcBNUw4Scp68KCqSpF
# BaKiyV1skd+l8U50WNePMb9Bzz0OfASal8v5sQG+DW01kN+I+RKUIbM5I50wJjiH
# ymQFNDsbobFx9I95mCEEPU7fUZ3VT/HOUVbkmX7ltIC/eQAu5GO8fu+ceETMybvb
# oxUM4dYNC+PzooUxfmC0DuKRwB21bX9+acuIBkxIm4Ed3O19w1VLoA7UNOUuJ7z6
# NQ2W/+q7cnfOPl2QVL4qlgCblUT2vmQpllV3MIIHETCCBPmgAwIBAgITMwAAAIe8
# gm6Foa5TqAAAAAAAhzANBgkqhkiG9w0BAQwFADBfMQswCQYDVQQGEwJVUzEeMBwG
# A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTAwLgYDVQQDEydNaWNyb3NvZnQg
# V2luZG93cyBDb2RlIFNpZ25pbmcgUENBIDIwMjQwHhcNMjUwNTA4MTgyNDU0WhcN
# MjYwNTA2MTgyNDU0WjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3Rv
# bjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0
# aW9uMR4wHAYDVQQDExVNaWNyb3NvZnQgQ29ycG9yYXRpb24wggIiMA0GCSqGSIb3
# DQEBAQUAA4ICDwAwggIKAoICAQC0JvvNCq9eE0sLt0WdArsItuEZpbuTI0W3EVMS
# i6PTkY1xniZhsm8rsEzr/Ngq77HknKV8GYYNkVxhppwdoQilh/R0KELXZi22Qqi4
# jEUsOYgkDHPIbnUEcQvbaG1R2ijOme/uGE9DX3RBx9Pkbi8TYec1d2iDhzF/xofy
# oZHPUTvL2PzDae7ncAjjRChG7kCzobShkcMpUZPyYMXKtqCMAx4OBYdeBF6PGfft
# 4Z16crWAmSEGMCcXQ7EVxWj1W7R5sOo4TilWQBQp9yYyvKYUObpiQwUjNZb4wALe
# Y6msmJogp7LC5N6warLYTbhwJJblmhZQIhaD8UABuP030nUofVkGDqK6xC/REnOE
# KTnsE+KaRb8JDOBXWSscNMQSR7wbX2NF+hK/S4dg6NUHzr0p0k20yY8LZg0OPOeb
# Hg5WdXUqkFHNB4Ck2aOT4rhu7YYiYBewfVZXF4/XF/BemkMKgnQToJvFJYLMPZM3
# tosN1IW0Ow3Ny3RvwQfHPGQ1kSqprOl14JFTI9CO/CZzhZbgeRB9Z3dWcXt1RYpu
# NIAkMWl7gDTRYy4gWhkgmzE1x1cz1hA8hKZIvD0VmNiwqN1HHRDHn2ryxmLgZgLh
# kth4K/i3CR+xSiptNnYnpMkE0Rre89r45MKUHytz0z0yqSsey/BsJ9RhnTvqiNrX
# Ay0TBwIDAQABo4IBrzCCAaswDgYDVR0PAQH/BAQDAgeAMB8GA1UdJQQYMBYGCisG
# AQQBgjc9BgEGCCsGAQUFBwMDMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFCYGR6ii
# PVV4VrV7+HKNkZBNf6SoMEUGA1UdEQQ+MDykOjA4MR4wHAYDVQQLExVNaWNyb3Nv
# ZnQgQ29ycG9yYXRpb24xFjAUBgNVBAUTDTIzMDg2NSs1MDQ1ODEwHwYDVR0jBBgw
# FoAUHoLfDteMs9cCNIMO2qutZbmvuOwwagYDVR0fBGMwYTBfoF2gW4ZZaHR0cDov
# L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jcmwvTWljcm9zb2Z0JTIwV2luZG93
# cyUyMENvZGUlMjBTaWduaW5nJTIwUENBJTIwMjAyNC5jcmwwdwYIKwYBBQUHAQEE
# azBpMGcGCCsGAQUFBzAChltodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz
# L2NlcnRzL01pY3Jvc29mdCUyMFdpbmRvd3MlMjBDb2RlJTIwU2lnbmluZyUyMFBD
# QSUyMDIwMjQuY3J0MA0GCSqGSIb3DQEBDAUAA4ICAQB+y/LNkTgCe/vYXjHxIrS/
# 73+FTugtMOG7l/fuiVFw3poLaGGtcn7LF5s8/h6soz4ST82QeZA3y/ADvl5VXeO1
# 5mROcELkFZ8kbdzqXTnyuWpDOTTm54DX2XVwxQfFIxYJqBij8pwvlNAB5r3JOBOA
# fegOad1NuCoP0/aA5hgu4ci3d0i+LaBVHm2RlHM44si1KyEg8Rs9g/SQPSijwRMt
# Da7TsWmz7F1J9P+UV36yOmwQ4jepF5h8hFUSrCJ3x7tEqA0ruiy2yCT15FcaJdnQ
# 3tZ8RRUdoCJ/pxtDI3YySMLA2XYLs4qHzoF/TPPNtaWDTvr4a+rBbUZm58pV+S14
# jhtkQAoO+275/a+ESeRSEeP9+Hde3Ez06U1MRKu+uicTPfDhUggSXzcaDImnRS6i
# FjEryJGHhRyZYoMphCjLn+PBJe58nvL/HWuP7kCMbSJOYz1ghVU/k5cgTYqq/V3E
# yzIo2K0MtwoUQcZygkTdCRUv6EtqFWbxtK6fyM6mbCoRgs2tKdMl8EN5nSMRXZEr
# sx6bG6M7BgTiSP56F//HvdLyGKfAR247Nu4/1nxgcDBZVcvCJyj6FmYKnvQEWixb
# aXFlpvCMvahUFV1b/1FKk2EF4ntMScCjyTKUXSTt8cgmVfS8FB1YkZm7IsCjBkua
# GWbGZBLKlwLys7wpFhqvOzGCGwYwghsCAgEBMHYwXzELMAkGA1UEBhMCVVMxHjAc
# BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEwMC4GA1UEAxMnTWljcm9zb2Z0
# IFdpbmRvd3MgQ29kZSBTaWduaW5nIFBDQSAyMDI0AhMzAAAAh7yCboWhrlOoAAAA
# AACHMA0GCWCGSAFlAwQCAQUAoIGuMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEE
# MBwGCisGAQQBgjcCAQsxDjAMBgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCAN
# SiN/A9m3PfXxWvXOr5Y6vf4QWtWaOKTWzL+fiYGStDBCBgorBgEEAYI3AgEMMTQw
# MqAUgBIATQBpAGMAcgBvAHMAbwBmAHShGoAYaHR0cDovL3d3dy5taWNyb3NvZnQu
# Y29tMA0GCSqGSIb3DQEBAQUABIICAIwLEyfZrWsOx+/XymrsWeLjZRxGB2Mi/hvv
# cZNPvf+0Vwpof/+UfSGx1X93PX4sBYSpqNj61f62bAzupYNGr8PJ9ysV5ZPmkyfo
# 0p1F8fHgrWpIge5tCcSyR+gMNIGtLCcooCSxMbxqMelynkPpMZi37KvK0VwJ8bOa
# JnTnpvMrLdNSzP3M5WP3LeolrzsWdFskTd1edXWpXp54tQ/gsCLc1XyIeucpiYar
# FnA58NnEIaLKstR/C17GyZmGlFRMwVc5vgZYaENrs9zlBBwV7+BDh/fHCoxQqmAf
# 4amiiAVjAT4XqQXh13fRbxX5I6oADtpqcud7AnBEOBJms2xa1sIgAaly6c6OB5ah
# Fbdo6iaxqYN9fBZ9bJviaSUMfOqqIS21+bPOl5Sdi5RG5vShZge5QAeNJKGTJoPz
# wJJHTbCzRh38w3lGhsif9E7I/GA6in3Ev2EZlnqjZJmdVbYMSRrHyVD8BJ88WPZf
# OkZwW5yJ/qDUbbWf/vhDXoOAHstHrdMS3Pod5O+KSJL001Q4HISfeOryJYaVbl9i
# s8SGzkxMCV9eKyiBvcEYFUmQ7UhgEhRjP47i+6hlBsInQ0mdpnoHRZzuV/Ym4GDv
# qoZGLzeBtP/k5pA+JwPCrQeOAUZEZ0UcvGA9KFyob86thtEK+gfpCPw6cvJdPEsn
# V6o5ht6LoYIXsDCCF6wGCisGAQQBgjcDAwExghecMIIXmAYJKoZIhvcNAQcCoIIX
# iTCCF4UCAQMxDzANBglghkgBZQMEAgEFADCCAVoGCyqGSIb3DQEJEAEEoIIBSQSC
# AUUwggFBAgEBBgorBgEEAYRZCgMBMDEwDQYJYIZIAWUDBAIBBQAEIPgIP7nq1OtX
# lpmWqxEG+P5Mj9cD5sPmOejNvks2QkadAgZoLkxWT9cYEzIwMjUwNjEyMTc0MTM1
# LjM0NFowBIACAfSggdmkgdYwgdMxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJhdGlvbnMg
# TGltaXRlZDEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjU1MUEtMDVFMC1EOTQ3
# MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloIIR/jCCBygw
# ggUQoAMCAQICEzMAAAIB0UVZmBDMQk8AAQAAAgEwDQYJKoZIhvcNAQELBQAwfDEL
# MAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
# bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWlj
# cm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwHhcNMjQwNzI1MTgzMTIyWhcNMjUx
# MDIyMTgzMTIyWjCB0zELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
# EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
# bjEtMCsGA1UECxMkTWljcm9zb2Z0IElyZWxhbmQgT3BlcmF0aW9ucyBMaW1pdGVk
# MScwJQYDVQQLEx5uU2hpZWxkIFRTUyBFU046NTUxQS0wNUUwLUQ5NDcxJTAjBgNV
# BAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZpY2UwggIiMA0GCSqGSIb3DQEB
# AQUAA4ICDwAwggIKAoICAQC1at/4fMO7uyTnTLlgeF4IgkbS7FFIUVwc16T5N31m
# bImPbQQSNpTwkMm7mlZzik8CwEjfw0QAnVv4oGeVK2pTy69cXiqcRKEN2Lyc+xll
# CxNkMpYCBQzaJlM6JYi5lwWzlkLz/4aWtUszmHVj2d8yHkHgOdRA5cyt6YBP0yS9
# SGDe5piCaouWZjI4OZtriVdkG7XThIsAWxc5+X9MuGlOhPjrLuUj2xsj26rf8B6u
# Hdo+LaSce8QRrOKVd6ihc0sLB274izqjyRAui5SfcrBRCbRvtpS2y/Vf86A+aw4m
# LrI3cthbIchK+s24isniJg2Ad0EG6ZBgrwuNmZBpMoVpzGGZcnSraDNoh/EXbIjA
# z5X2xCqQeSD9D6JIM2kyvqav87CSc4QiMjSDpkw7KaK+kKHMM2g/P2GQreBUdkpb
# s1Xz5QFc3gbRoFfr18pRvEEEvKTZwL4+E6hSOSXpQLz9zSG6qPnFfyb5hUiTzV7u
# 3oj5X8TjJdF55mCvQWFio2m9OMZxo7ZauQ/leaxhLsi8w8h/gMLIglRlqqgExOgA
# kkcZF74M+oIeDpuYY+b3sys5a/Xr8KjpL1xAORen28xJJFBZfLgq0mFl+a4PPa+v
# WPDg16LHC4gMbDWa1X9N1Ij6+ksl9SIuX9v3D+0kH3YEAtBPx7Vgfw2mF06jXELC
# RwIDAQABo4IBSTCCAUUwHQYDVR0OBBYEFLByr1uWoug8+JWvKb2YWYVZvLJSMB8G
# A1UdIwQYMBaAFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMF8GA1UdHwRYMFYwVKBSoFCG
# Tmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY3JsL01pY3Jvc29mdCUy
# MFRpbWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNybDBsBggrBgEFBQcBAQRgMF4w
# XAYIKwYBBQUHMAKGUGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2lvcHMvY2Vy
# dHMvTWljcm9zb2Z0JTIwVGltZS1TdGFtcCUyMFBDQSUyMDIwMTAoMSkuY3J0MAwG
# A1UdEwEB/wQCMAAwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwgwDgYDVR0PAQH/BAQD
# AgeAMA0GCSqGSIb3DQEBCwUAA4ICAQA6NADLPRxXO1MUapdfkktHEUr87+gx7nm4
# OoQLxV3WBtwzbwoFQ+C9Qg9eb+90M3YhUGRYebAKngAhzLh1m2S5SZ3R+e7ppP0y
# +jWd2wunZglwygUsS3dO2uIto76Lgau/RlQu1ZdQ8Bb8yflJyOCfTFl24Y8EP9ez
# cnv6B6337xm8GKmyD83umiKZg5WwfEtx6btXld0w2zK1Ob+4KiaEz/CBHkqUNhNU
# 0BcHFxIox4lqIPdXX4eE2RWWIyXlU4/6fDnFYXnm72Hp4XYbt4E+pP6pIVD6tAJB
# 0is3TIbVA308muiC4r4UlAl1DN18PdFZWxyIHKBthpmVPVwYkjUjJDvgNDRQF1Ol
# 94azKsRD08jxDKpUupvarsu0joMkw2mFi76Ov//SymvVRW/IM+25GdsZBE2LUI7A
# lyP05iaWQWAo14J9sNPtTe4Q69aiZ6RfrRj+bm61FxQ9V4A92GQH4PENp6/cnXLA
# M13K73XWcBU+BGTIqAwrdRIsbfsR2Vq0OTwXK4KvHi2IfKoc7fATrE/DfZDx7++a
# 5A+gFm5fepR6gUizJkR6cerZJwy6eFypbfZJRUCLmhnhned/t0CA1q7bU0Q/CBb7
# bCSs2oODsenzIfKg4puAQG7pERBu9J9nkqHg9X5LaDF/a6roahgOeWoAE4xjDPfT
# 0hKLRs8yHzCCB3EwggVZoAMCAQICEzMAAAAVxedrngKbSZkAAAAAABUwDQYJKoZI
# hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAw
# DgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24x
# MjAwBgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1dGhvcml0eSAy
# MDEwMB4XDTIxMDkzMDE4MjIyNVoXDTMwMDkzMDE4MzIyNVowfDELMAkGA1UEBhMC
# VVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
# BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRp
# bWUtU3RhbXAgUENBIDIwMTAwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoIC
# AQDk4aZM57RyIQt5osvXJHm9DtWC0/3unAcH0qlsTnXIyjVX9gF/bErg4r25Phdg
# M/9cT8dm95VTcVrifkpa/rg2Z4VGIwy1jRPPdzLAEBjoYH1qUoNEt6aORmsHFPPF
# dvWGUNzBRMhxXFExN6AKOG6N7dcP2CZTfDlhAnrEqv1yaa8dq6z2Nr41JmTamDu6
# GnszrYBbfowQHJ1S/rboYiXcag/PXfT+jlPP1uyFVk3v3byNpOORj7I5LFGc6XBp
# Dco2LXCOMcg1KL3jtIckw+DJj361VI/c+gVVmG1oO5pGve2krnopN6zL64NF50Zu
# yjLVwIYwXE8s4mKyzbnijYjklqwBSru+cakXW2dg3viSkR4dPf0gz3N9QZpGdc3E
# XzTdEonW/aUgfX782Z5F37ZyL9t9X4C626p+Nuw2TPYrbqgSUei/BQOj0XOmTTd0
# lBw0gg/wEPK3Rxjtp+iZfD9M269ewvPV2HM9Q07BMzlMjgK8QmguEOqEUUbi0b1q
# GFphAXPKZ6Je1yh2AuIzGHLXpyDwwvoSCtdjbwzJNmSLW6CmgyFdXzB0kZSU2LlQ
# +QuJYfM2BjUYhEfb3BvR/bLUHMVr9lxSUV0S2yW6r1AFemzFER1y7435UsSFF5PA
# PBXbGjfHCBUYP3irRbb1Hode2o+eFnJpxq57t7c+auIurQIDAQABo4IB3TCCAdkw
# EgYJKwYBBAGCNxUBBAUCAwEAATAjBgkrBgEEAYI3FQIEFgQUKqdS/mTEmr6CkTxG
# NSnPEP8vBO4wHQYDVR0OBBYEFJ+nFV0AXmJdg/Tl0mWnG1M1GelyMFwGA1UdIARV
# MFMwUQYMKwYBBAGCN0yDfQEBMEEwPwYIKwYBBQUHAgEWM2h0dHA6Ly93d3cubWlj
# cm9zb2Z0LmNvbS9wa2lvcHMvRG9jcy9SZXBvc2l0b3J5Lmh0bTATBgNVHSUEDDAK
# BggrBgEFBQcDCDAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
# AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV9lbLj+iiXGJo0T2UkFvX
# zpoYxDBWBgNVHR8ETzBNMEugSaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20v
# cGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5jcmwwWgYI
# KwYBBQUHAQEETjBMMEoGCCsGAQUFBzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5j
# b20vcGtpL2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNydDANBgkqhkiG
# 9w0BAQsFAAOCAgEAnVV9/Cqt4SwfZwExJFvhnnJL/Klv6lwUtj5OR2R4sQaTlz0x
# M7U518JxNj/aZGx80HU5bbsPMeTCj/ts0aGUGCLu6WZnOlNN3Zi6th542DYunKmC
# VgADsAW+iehp4LoJ7nvfam++Kctu2D9IdQHZGN5tggz1bSNU5HhTdSRXud2f8449
# xvNo32X2pFaq95W2KFUn0CS9QKC/GbYSEhFdPSfgQJY4rPf5KYnDvBewVIVCs/wM
# nosZiefwC2qBwoEZQhlSdYo2wh3DYXMuLGt7bj8sCXgU6ZGyqVvfSaN0DLzskYDS
# PeZKPmY7T7uG+jIa2Zb0j/aRAfbOxnT99kxybxCrdTDFNLB62FD+CljdQDzHVG2d
# Y3RILLFORy3BFARxv2T5JL5zbcqOCb2zAVdJVGTZc9d/HltEAY5aGZFrDZ+kKNxn
# GSgkujhLmm77IVRrakURR6nxt67I6IleT53S0Ex2tVdUCbFpAUR+fKFhbHP+Crvs
# QWY9af3LwUFJfn6Tvsv4O+S3Fb+0zj6lMVGEvL8CwYKiexcdFYmNcP7ntdAoGokL
# jzbaukz5m/8K6TT4JDVnK+ANuOaMmdbhIurwJ0I9JZTmdHRbatGePu1+oDEzfbzL
# 6Xu/OHBE0ZDxyKs6ijoIYn/ZcGNTTY3ugm2lBRDBcQZqELQdVTNYs6FwZvKhggNZ
# MIICQQIBATCCAQGhgdmkgdYwgdMxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo
# aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
# cG9yYXRpb24xLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJhdGlvbnMg
# TGltaXRlZDEnMCUGA1UECxMeblNoaWVsZCBUU1MgRVNOOjU1MUEtMDVFMC1EOTQ3
# MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBTZXJ2aWNloiMKAQEwBwYF
# Kw4DAhoDFQDX7bpxH/IfXTQOI0UZaG4C/atgGqCBgzCBgKR+MHwxCzAJBgNVBAYT
# AlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYD
# VQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBU
# aW1lLVN0YW1wIFBDQSAyMDEwMA0GCSqGSIb3DQEBCwUAAgUA6/Ug9jAiGA8yMDI1
# MDYxMjA5NDgzOFoYDzIwMjUwNjEzMDk0ODM4WjB3MD0GCisGAQQBhFkKBAExLzAt
# MAoCBQDr9SD2AgEAMAoCAQACAhpXAgH/MAcCAQACAhJXMAoCBQDr9nJ2AgEAMDYG
# CisGAQQBhFkKBAIxKDAmMAwGCisGAQQBhFkKAwKgCjAIAgEAAgMHoSChCjAIAgEA
# AgMBhqAwDQYJKoZIhvcNAQELBQADggEBABizOm8YcN4YtewyjLGRwVUqwqVGA8HV
# cF8Ggv/WytocQSxCMDObfYpcf+AkZ5/Jxhf0HZhzjip3L+3jAjz58DkBL1hcd/3F
# vp6iqAqExJ4Z6Yp7A7AyQUgnxOzU3o8nGGk7nFgTMsSpj0cPmphtD7W1D6Ge9iDD
# 8Cy03eBY8NbGKy4LzpDuiLw3HOycM9mcKPTsdLttQPyrMnNL6E5jPY7BIv6F4im0
# Lcjj2YwMxW96QLuFxFFXH4Zp39SZwcgKwVNAkR5QMW7UlxLf8XkIq5d1qCfVOuOx
# 0JdW+9yYqd5Bsd9plMaq9GjKL9FYQ1rO4zZUCnkWEzM143K/dlQRx8QxggQNMIIE
# CQIBATCBkzB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4G
# A1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYw
# JAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAxMAITMwAAAgHRRVmY
# EMxCTwABAAACATANBglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcNAQkDMQ0GCyqG
# SIb3DQEJEAEEMC8GCSqGSIb3DQEJBDEiBCBYcYFjVwXOuG4rRGs0ZjWttGK9wlcz
# H5YIGlb3iMCrTjCB+gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIFhrsjpMlBFy
# bHQdpJNZl0mCjB2uX35muvSkh2oe1zgjMIGYMIGApH4wfDELMAkGA1UEBhMCVVMx
# EzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
# FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUt
# U3RhbXAgUENBIDIwMTACEzMAAAIB0UVZmBDMQk8AAQAAAgEwIgQgDFbPfIGdp4oi
# H33rmUkqe802Gsb11jnTna1bMkFiZEwwDQYJKoZIhvcNAQELBQAEggIAB0FyDkA7
# 6pFvM/jioxT+rRJh6IjGKg7/iHibwPh+PoRWCoa8TamlL2ERB/kMoDtsyWX/hCht
# AO6TiP2SrTN+XO6UikVK4EFEzp14Z7iUB4toyzS8P3phYJqsfIjZBMXGYqKXTFtm
# Ek64MiaoXQjd48c/j48W1Ko2F/1VrcAirTEVCNwao8y4z2PuY83ZaWKktpfpTRH9
# vGZazPW/c1iegikfhtVMeokPJ0+FkGvyBc+SNy+ItdnJa9ix6CsNrv2tFR1C1qGv
# zQGCQJ8PWsSZSvkgV36IM/07EPb9d+o17EpbBYjhf8RBCIXPl2rOn5VtWViwBAUn
# bWEjCLrZuIeC4o+M2YHA53CuEyKcokWktQLe28KdrWZj6x0ddi+tbMOlzewvtPOY
# hVT8cKj6kkHQ1iZpJHCyFn7JFZx5pNIikTh+LMk75xveC/0zrXrU7cSaQ3k0mIa/
# e67bkAb9t4hJl4IwSTsmn4J9481g3HDWFxCk5OLnlZ2m6NngLI8r+MMICuZuBa49
# MvIgoH64DNwgOz/fzD3Irc5dXO25V3JNk0ekiJkbqJb8DvtRicL0SSKr7ngwfcl7
# iyTJX/syeOtPgSSUrfMXlM3dQuRC7j5ZpDnwYJWUMYokeQhmjSzUDoxLgUzfz8I3
# ST2mkI9m9sv5qAaSaS/gjtFBgBPnD/p8EIc=
# SIG # End signature block
