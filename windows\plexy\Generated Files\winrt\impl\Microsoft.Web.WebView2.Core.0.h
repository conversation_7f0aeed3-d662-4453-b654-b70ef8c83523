// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_Web_WebView2_Core_0_H
#define WINRT_Microsoft_Web_WebView2_Core_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Deferral;
    struct EventRegistrationToken;
    struct IAsyncAction;
    struct Point;
    struct Rect;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
}
WINRT_EXPORT namespace winrt::Windows::Security::Cryptography::Certificates
{
    struct Certificate;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IRandomAccessStream;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct Color;
}
WINRT_EXPORT namespace winrt::Windows::UI::Core
{
    struct CoreCursor;
    struct CoreWindow;
}
WINRT_EXPORT namespace winrt::Microsoft::Web::WebView2::Core
{
    enum class CoreWebView2BoundsMode : int32_t
    {
        UseRawPixels = 0,
        UseRasterizationScale = 1,
    };
    enum class CoreWebView2BrowserProcessExitKind : int32_t
    {
        Normal = 0,
        Failed = 1,
    };
    enum class CoreWebView2BrowsingDataKinds : uint32_t
    {
        FileSystems = 0x1,
        IndexedDb = 0x2,
        LocalStorage = 0x4,
        WebSql = 0x8,
        CacheStorage = 0x10,
        AllDomStorage = 0x20,
        Cookies = 0x40,
        AllSite = 0x80,
        DiskCache = 0x100,
        DownloadHistory = 0x200,
        GeneralAutofill = 0x400,
        PasswordAutosave = 0x800,
        BrowsingHistory = 0x1000,
        Settings = 0x2000,
        AllProfile = 0x4000,
    };
    enum class CoreWebView2CapturePreviewImageFormat : int32_t
    {
        Png = 0,
        Jpeg = 1,
    };
    enum class CoreWebView2ClientCertificateKind : int32_t
    {
        SmartCard = 0,
        Pin = 1,
        Other = 2,
    };
    enum class CoreWebView2ContextMenuItemKind : int32_t
    {
        Command = 0,
        CheckBox = 1,
        Radio = 2,
        Separator = 3,
        Submenu = 4,
    };
    enum class CoreWebView2ContextMenuTargetKind : int32_t
    {
        Page = 0,
        Image = 1,
        SelectedText = 2,
        Audio = 3,
        Video = 4,
    };
    enum class CoreWebView2CookieSameSiteKind : int32_t
    {
        None = 0,
        Lax = 1,
        Strict = 2,
    };
    enum class CoreWebView2DefaultDownloadDialogCornerAlignment : int32_t
    {
        TopLeft = 0,
        TopRight = 1,
        BottomLeft = 2,
        BottomRight = 3,
    };
    enum class CoreWebView2DownloadInterruptReason : int32_t
    {
        None = 0,
        FileFailed = 1,
        FileAccessDenied = 2,
        FileNoSpace = 3,
        FileNameTooLong = 4,
        FileTooLarge = 5,
        FileMalicious = 6,
        FileTransientError = 7,
        FileBlockedByPolicy = 8,
        FileSecurityCheckFailed = 9,
        FileTooShort = 10,
        FileHashMismatch = 11,
        NetworkFailed = 12,
        NetworkTimeout = 13,
        NetworkDisconnected = 14,
        NetworkServerDown = 15,
        NetworkInvalidRequest = 16,
        ServerFailed = 17,
        ServerNoRange = 18,
        ServerBadContent = 19,
        ServerUnauthorized = 20,
        ServerCertificateProblem = 21,
        ServerForbidden = 22,
        ServerUnexpectedResponse = 23,
        ServerContentLengthMismatch = 24,
        ServerCrossOriginRedirect = 25,
        UserCanceled = 26,
        UserShutdown = 27,
        UserPaused = 28,
        DownloadProcessCrashed = 29,
    };
    enum class CoreWebView2DownloadState : int32_t
    {
        InProgress = 0,
        Interrupted = 1,
        Completed = 2,
    };
    enum class CoreWebView2HostResourceAccessKind : int32_t
    {
        Deny = 0,
        Allow = 1,
        DenyCors = 2,
    };
    enum class CoreWebView2KeyEventKind : int32_t
    {
        KeyDown = 0,
        KeyUp = 1,
        SystemKeyDown = 2,
        SystemKeyUp = 3,
    };
    enum class CoreWebView2MouseEventKind : int32_t
    {
        HorizontalWheel = 526,
        LeftButtonDoubleClick = 515,
        LeftButtonDown = 513,
        LeftButtonUp = 514,
        Leave = 675,
        MiddleButtonDoubleClick = 521,
        MiddleButtonDown = 519,
        MiddleButtonUp = 520,
        Move = 512,
        RightButtonDoubleClick = 518,
        RightButtonDown = 516,
        RightButtonUp = 517,
        Wheel = 522,
        XButtonDoubleClick = 525,
        XButtonDown = 523,
        XButtonUp = 524,
    };
    enum class CoreWebView2MouseEventVirtualKeys : uint32_t
    {
        None = 0,
        LeftButton = 0x1,
        RightButton = 0x2,
        Shift = 0x4,
        Control = 0x8,
        MiddleButton = 0x10,
        XButton1 = 0x20,
        XButton2 = 0x40,
    };
    enum class CoreWebView2MoveFocusReason : int32_t
    {
        Programmatic = 0,
        Next = 1,
        Previous = 2,
    };
    enum class CoreWebView2PdfToolbarItems : uint32_t
    {
        None = 0,
        Save = 0x1,
        Print = 0x2,
        SaveAs = 0x4,
        ZoomIn = 0x8,
        ZoomOut = 0x10,
        Rotate = 0x20,
        FitPage = 0x40,
        PageLayout = 0x80,
        Bookmarks = 0x100,
        PageSelector = 0x200,
        Search = 0x400,
    };
    enum class CoreWebView2PermissionKind : int32_t
    {
        UnknownPermission = 0,
        Microphone = 1,
        Camera = 2,
        Geolocation = 3,
        Notifications = 4,
        OtherSensors = 5,
        ClipboardRead = 6,
    };
    enum class CoreWebView2PermissionState : int32_t
    {
        Default = 0,
        Allow = 1,
        Deny = 2,
    };
    enum class CoreWebView2PointerEventKind : int32_t
    {
        Activate = 587,
        Down = 582,
        Enter = 585,
        Leave = 586,
        Up = 583,
        Update = 581,
    };
    enum class CoreWebView2PreferredColorScheme : int32_t
    {
        Auto = 0,
        Light = 1,
        Dark = 2,
    };
    enum class CoreWebView2PrintOrientation : int32_t
    {
        Portrait = 0,
        Landscape = 1,
    };
    enum class CoreWebView2ProcessFailedKind : int32_t
    {
        BrowserProcessExited = 0,
        RenderProcessExited = 1,
        RenderProcessUnresponsive = 2,
        FrameRenderProcessExited = 3,
        UtilityProcessExited = 4,
        SandboxHelperProcessExited = 5,
        GpuProcessExited = 6,
        PpapiPluginProcessExited = 7,
        PpapiBrokerProcessExited = 8,
        UnknownProcessExited = 9,
    };
    enum class CoreWebView2ProcessFailedReason : int32_t
    {
        Unexpected = 0,
        Unresponsive = 1,
        Terminated = 2,
        Crashed = 3,
        LaunchFailed = 4,
        OutOfMemory = 5,
    };
    enum class CoreWebView2ProcessKind : int32_t
    {
        Browser = 0,
        Renderer = 1,
        Utility = 2,
        SandboxHelper = 3,
        Gpu = 4,
        PpapiPlugin = 5,
        PpapiBroker = 6,
    };
    enum class CoreWebView2ScriptDialogKind : int32_t
    {
        Alert = 0,
        Confirm = 1,
        Prompt = 2,
        Beforeunload = 3,
    };
    enum class CoreWebView2ServerCertificateErrorAction : int32_t
    {
        AlwaysAllow = 0,
        Cancel = 1,
        Default = 2,
    };
    enum class CoreWebView2WebErrorStatus : int32_t
    {
        Unknown = 0,
        CertificateCommonNameIsIncorrect = 1,
        CertificateExpired = 2,
        ClientCertificateContainsErrors = 3,
        CertificateRevoked = 4,
        CertificateIsInvalid = 5,
        ServerUnreachable = 6,
        Timeout = 7,
        ErrorHttpInvalidServerResponse = 8,
        ConnectionAborted = 9,
        ConnectionReset = 10,
        Disconnected = 11,
        CannotConnect = 12,
        HostNameNotResolved = 13,
        OperationCanceled = 14,
        RedirectFailed = 15,
        UnexpectedError = 16,
        ValidAuthenticationCredentialsRequired = 17,
        ValidProxyAuthenticationRequired = 18,
    };
    enum class CoreWebView2WebResourceContext : int32_t
    {
        All = 0,
        Document = 1,
        Stylesheet = 2,
        Image = 3,
        Media = 4,
        Font = 5,
        Script = 6,
        XmlHttpRequest = 7,
        Fetch = 8,
        TextTrack = 9,
        EventSource = 10,
        Websocket = 11,
        Manifest = 12,
        SignedExchange = 13,
        Ping = 14,
        CspViolationReport = 15,
        Other = 16,
    };
    struct CoreWebView2Certificate_Manual;
    struct CoreWebView2ClientCertificate_Manual;
    struct CoreWebView2Profile_Manual;
    struct ICoreWebView2;
    struct ICoreWebView2AcceleratorKeyPressedEventArgs;
    struct ICoreWebView2BasicAuthenticationRequestedEventArgs;
    struct ICoreWebView2BasicAuthenticationResponse;
    struct ICoreWebView2BrowserProcessExitedEventArgs;
    struct ICoreWebView2Certificate;
    struct ICoreWebView2ClientCertificate;
    struct ICoreWebView2ClientCertificateRequestedEventArgs;
    struct ICoreWebView2CompositionController;
    struct ICoreWebView2CompositionController2;
    struct ICoreWebView2CompositionControllerStatics;
    struct ICoreWebView2ContentLoadingEventArgs;
    struct ICoreWebView2ContextMenuItem;
    struct ICoreWebView2ContextMenuRequestedEventArgs;
    struct ICoreWebView2ContextMenuTarget;
    struct ICoreWebView2Controller;
    struct ICoreWebView2Controller2;
    struct ICoreWebView2Controller3;
    struct ICoreWebView2Controller4;
    struct ICoreWebView2ControllerFactory;
    struct ICoreWebView2ControllerOptions;
    struct ICoreWebView2ControllerWindowReference;
    struct ICoreWebView2ControllerWindowReferenceStatics;
    struct ICoreWebView2Cookie;
    struct ICoreWebView2CookieManager;
    struct ICoreWebView2CookieManager_Manual;
    struct ICoreWebView2DOMContentLoadedEventArgs;
    struct ICoreWebView2DevToolsProtocolEventReceivedEventArgs;
    struct ICoreWebView2DevToolsProtocolEventReceivedEventArgs2;
    struct ICoreWebView2DevToolsProtocolEventReceiver;
    struct ICoreWebView2DispatchAdapter;
    struct ICoreWebView2DownloadOperation;
    struct ICoreWebView2DownloadStartingEventArgs;
    struct ICoreWebView2Environment;
    struct ICoreWebView2Environment10;
    struct ICoreWebView2Environment2;
    struct ICoreWebView2Environment3;
    struct ICoreWebView2Environment4;
    struct ICoreWebView2Environment5;
    struct ICoreWebView2Environment6;
    struct ICoreWebView2Environment7;
    struct ICoreWebView2Environment8;
    struct ICoreWebView2Environment9;
    struct ICoreWebView2EnvironmentOptions;
    struct ICoreWebView2EnvironmentOptions2;
    struct ICoreWebView2EnvironmentOptions_Manual;
    struct ICoreWebView2EnvironmentStatics;
    struct ICoreWebView2Environment_Manual;
    struct ICoreWebView2Frame;
    struct ICoreWebView2Frame2;
    struct ICoreWebView2Frame3;
    struct ICoreWebView2FrameCreatedEventArgs;
    struct ICoreWebView2FrameInfo;
    struct ICoreWebView2HttpHeadersCollectionIterator;
    struct ICoreWebView2HttpRequestHeaders;
    struct ICoreWebView2HttpResponseHeaders;
    struct ICoreWebView2MoveFocusRequestedEventArgs;
    struct ICoreWebView2NavigationCompletedEventArgs;
    struct ICoreWebView2NavigationCompletedEventArgs2;
    struct ICoreWebView2NavigationStartingEventArgs;
    struct ICoreWebView2NavigationStartingEventArgs2;
    struct ICoreWebView2NewWindowRequestedEventArgs;
    struct ICoreWebView2NewWindowRequestedEventArgs2;
    struct ICoreWebView2PermissionRequestedEventArgs;
    struct ICoreWebView2PermissionRequestedEventArgs2;
    struct ICoreWebView2PointerInfo;
    struct ICoreWebView2PrintSettings;
    struct ICoreWebView2ProcessFailedEventArgs;
    struct ICoreWebView2ProcessFailedEventArgs2;
    struct ICoreWebView2ProcessInfo;
    struct ICoreWebView2Profile;
    struct ICoreWebView2Profile2;
    struct ICoreWebView2ScriptDialogOpeningEventArgs;
    struct ICoreWebView2ServerCertificateErrorDetectedEventArgs;
    struct ICoreWebView2Settings;
    struct ICoreWebView2Settings2;
    struct ICoreWebView2Settings3;
    struct ICoreWebView2Settings4;
    struct ICoreWebView2Settings5;
    struct ICoreWebView2Settings6;
    struct ICoreWebView2Settings7;
    struct ICoreWebView2Settings_Manual;
    struct ICoreWebView2SourceChangedEventArgs;
    struct ICoreWebView2WebMessageReceivedEventArgs;
    struct ICoreWebView2WebResourceRequest;
    struct ICoreWebView2WebResourceRequestedEventArgs;
    struct ICoreWebView2WebResourceResponse;
    struct ICoreWebView2WebResourceResponseReceivedEventArgs;
    struct ICoreWebView2WebResourceResponseView;
    struct ICoreWebView2WindowFeatures;
    struct ICoreWebView2_10;
    struct ICoreWebView2_11;
    struct ICoreWebView2_12;
    struct ICoreWebView2_13;
    struct ICoreWebView2_14;
    struct ICoreWebView2_2;
    struct ICoreWebView2_3;
    struct ICoreWebView2_4;
    struct ICoreWebView2_5;
    struct ICoreWebView2_6;
    struct ICoreWebView2_7;
    struct ICoreWebView2_8;
    struct ICoreWebView2_9;
    struct CoreWebView2;
    struct CoreWebView2AcceleratorKeyPressedEventArgs;
    struct CoreWebView2BasicAuthenticationRequestedEventArgs;
    struct CoreWebView2BasicAuthenticationResponse;
    struct CoreWebView2BrowserProcessExitedEventArgs;
    struct CoreWebView2Certificate;
    struct CoreWebView2ClientCertificate;
    struct CoreWebView2ClientCertificateRequestedEventArgs;
    struct CoreWebView2CompositionController;
    struct CoreWebView2ContentLoadingEventArgs;
    struct CoreWebView2ContextMenuItem;
    struct CoreWebView2ContextMenuRequestedEventArgs;
    struct CoreWebView2ContextMenuTarget;
    struct CoreWebView2Controller;
    struct CoreWebView2ControllerOptions;
    struct CoreWebView2ControllerWindowReference;
    struct CoreWebView2Cookie;
    struct CoreWebView2CookieManager;
    struct CoreWebView2DOMContentLoadedEventArgs;
    struct CoreWebView2DevToolsProtocolEventReceivedEventArgs;
    struct CoreWebView2DevToolsProtocolEventReceiver;
    struct CoreWebView2DownloadOperation;
    struct CoreWebView2DownloadStartingEventArgs;
    struct CoreWebView2Environment;
    struct CoreWebView2EnvironmentOptions;
    struct CoreWebView2Frame;
    struct CoreWebView2FrameCreatedEventArgs;
    struct CoreWebView2FrameInfo;
    struct CoreWebView2HttpHeadersCollectionIterator;
    struct CoreWebView2HttpRequestHeaders;
    struct CoreWebView2HttpResponseHeaders;
    struct CoreWebView2MoveFocusRequestedEventArgs;
    struct CoreWebView2NavigationCompletedEventArgs;
    struct CoreWebView2NavigationStartingEventArgs;
    struct CoreWebView2NewWindowRequestedEventArgs;
    struct CoreWebView2PermissionRequestedEventArgs;
    struct CoreWebView2PointerInfo;
    struct CoreWebView2PrintSettings;
    struct CoreWebView2ProcessFailedEventArgs;
    struct CoreWebView2ProcessInfo;
    struct CoreWebView2Profile;
    struct CoreWebView2ScriptDialogOpeningEventArgs;
    struct CoreWebView2ServerCertificateErrorDetectedEventArgs;
    struct CoreWebView2Settings;
    struct CoreWebView2SourceChangedEventArgs;
    struct CoreWebView2WebMessageReceivedEventArgs;
    struct CoreWebView2WebResourceRequest;
    struct CoreWebView2WebResourceRequestedEventArgs;
    struct CoreWebView2WebResourceResponse;
    struct CoreWebView2WebResourceResponseReceivedEventArgs;
    struct CoreWebView2WebResourceResponseView;
    struct CoreWebView2WindowFeatures;
    struct CoreWebView2PhysicalKeyStatus;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CapturePreviewImageFormat>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTargetKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadInterruptReason>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HostResourceAccessKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2KeyEventKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventVirtualKeys>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerEventKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedReason>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus>{ using type = struct_category<uint32_t, uint32_t, int32_t, int32_t, int32_t, int32_t>; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2> = L"Microsoft.Web.WebView2.Core.CoreWebView2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2AcceleratorKeyPressedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse> = L"Microsoft.Web.WebView2.Core.CoreWebView2BasicAuthenticationResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate> = L"Microsoft.Web.WebView2.Core.CoreWebView2Certificate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate> = L"Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController> = L"Microsoft.Web.WebView2.Core.CoreWebView2CompositionController";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContentLoadingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItem";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTarget";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller> = L"Microsoft.Web.WebView2.Core.CoreWebView2Controller";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions> = L"Microsoft.Web.WebView2.Core.CoreWebView2ControllerOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference> = L"Microsoft.Web.WebView2.Core.CoreWebView2ControllerWindowReference";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie> = L"Microsoft.Web.WebView2.Core.CoreWebView2Cookie";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager> = L"Microsoft.Web.WebView2.Core.CoreWebView2CookieManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2DOMContentLoadedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver> = L"Microsoft.Web.WebView2.Core.CoreWebView2DevToolsProtocolEventReceiver";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation> = L"Microsoft.Web.WebView2.Core.CoreWebView2DownloadOperation";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2DownloadStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment> = L"Microsoft.Web.WebView2.Core.CoreWebView2Environment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions> = L"Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame> = L"Microsoft.Web.WebView2.Core.CoreWebView2Frame";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2FrameCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo> = L"Microsoft.Web.WebView2.Core.CoreWebView2FrameInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator> = L"Microsoft.Web.WebView2.Core.CoreWebView2HttpHeadersCollectionIterator";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders> = L"Microsoft.Web.WebView2.Core.CoreWebView2HttpRequestHeaders";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders> = L"Microsoft.Web.WebView2.Core.CoreWebView2HttpResponseHeaders";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2NavigationStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2NewWindowRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2PermissionRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo> = L"Microsoft.Web.WebView2.Core.CoreWebView2PointerInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings> = L"Microsoft.Web.WebView2.Core.CoreWebView2PrintSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo> = L"Microsoft.Web.WebView2.Core.CoreWebView2ProcessInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile> = L"Microsoft.Web.WebView2.Core.CoreWebView2Profile";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogOpeningEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorDetectedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings> = L"Microsoft.Web.WebView2.Core.CoreWebView2Settings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2SourceChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebMessageReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceResponseView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures> = L"Microsoft.Web.WebView2.Core.CoreWebView2WindowFeatures";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode> = L"Microsoft.Web.WebView2.Core.CoreWebView2BoundsMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2BrowserProcessExitKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds> = L"Microsoft.Web.WebView2.Core.CoreWebView2BrowsingDataKinds";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CapturePreviewImageFormat> = L"Microsoft.Web.WebView2.Core.CoreWebView2CapturePreviewImageFormat";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificateKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuItemKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTargetKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ContextMenuTargetKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2CookieSameSiteKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment> = L"Microsoft.Web.WebView2.Core.CoreWebView2DefaultDownloadDialogCornerAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadInterruptReason> = L"Microsoft.Web.WebView2.Core.CoreWebView2DownloadInterruptReason";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadState> = L"Microsoft.Web.WebView2.Core.CoreWebView2DownloadState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HostResourceAccessKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2HostResourceAccessKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2KeyEventKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2KeyEventKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2MouseEventKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventVirtualKeys> = L"Microsoft.Web.WebView2.Core.CoreWebView2MouseEventVirtualKeys";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason> = L"Microsoft.Web.WebView2.Core.CoreWebView2MoveFocusReason";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems> = L"Microsoft.Web.WebView2.Core.CoreWebView2PdfToolbarItems";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2PermissionKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState> = L"Microsoft.Web.WebView2.Core.CoreWebView2PermissionState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerEventKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2PointerEventKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme> = L"Microsoft.Web.WebView2.Core.CoreWebView2PreferredColorScheme";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation> = L"Microsoft.Web.WebView2.Core.CoreWebView2PrintOrientation";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedReason> = L"Microsoft.Web.WebView2.Core.CoreWebView2ProcessFailedReason";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ProcessKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogKind> = L"Microsoft.Web.WebView2.Core.CoreWebView2ScriptDialogKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction> = L"Microsoft.Web.WebView2.Core.CoreWebView2ServerCertificateErrorAction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebErrorStatus> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebErrorStatus";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext> = L"Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus> = L"Microsoft.Web.WebView2.Core.CoreWebView2PhysicalKeyStatus";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual> = L"Microsoft.Web.WebView2.Core.CoreWebView2Certificate_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual> = L"Microsoft.Web.WebView2.Core.CoreWebView2ClientCertificate_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual> = L"Microsoft.Web.WebView2.Core.CoreWebView2Profile_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2AcceleratorKeyPressedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2BasicAuthenticationRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse> = L"Microsoft.Web.WebView2.Core.ICoreWebView2BasicAuthenticationResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2BrowserProcessExitedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Certificate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ClientCertificate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ClientCertificateRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController> = L"Microsoft.Web.WebView2.Core.ICoreWebView2CompositionController";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2CompositionController2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics> = L"Microsoft.Web.WebView2.Core.ICoreWebView2CompositionControllerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ContentLoadingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ContextMenuItem";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ContextMenuRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ContextMenuTarget";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Controller";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Controller2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Controller3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Controller4";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ControllerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ControllerOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ControllerWindowReference";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ControllerWindowReferenceStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Cookie";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager> = L"Microsoft.Web.WebView2.Core.ICoreWebView2CookieManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual> = L"Microsoft.Web.WebView2.Core.ICoreWebView2CookieManager_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DOMContentLoadedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DevToolsProtocolEventReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DevToolsProtocolEventReceivedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DevToolsProtocolEventReceiver";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DispatchAdapter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DownloadOperation";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2DownloadStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment10";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment4";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment5";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment6";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment7";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment8";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment9";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions> = L"Microsoft.Web.WebView2.Core.ICoreWebView2EnvironmentOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2EnvironmentOptions2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual> = L"Microsoft.Web.WebView2.Core.ICoreWebView2EnvironmentOptions_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics> = L"Microsoft.Web.WebView2.Core.ICoreWebView2EnvironmentStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Environment_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Frame";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Frame2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Frame3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2FrameCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo> = L"Microsoft.Web.WebView2.Core.ICoreWebView2FrameInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator> = L"Microsoft.Web.WebView2.Core.ICoreWebView2HttpHeadersCollectionIterator";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders> = L"Microsoft.Web.WebView2.Core.ICoreWebView2HttpRequestHeaders";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders> = L"Microsoft.Web.WebView2.Core.ICoreWebView2HttpResponseHeaders";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2MoveFocusRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NavigationCompletedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NavigationCompletedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NavigationStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NavigationStartingEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NewWindowRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2NewWindowRequestedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2PermissionRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2PermissionRequestedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo> = L"Microsoft.Web.WebView2.Core.ICoreWebView2PointerInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings> = L"Microsoft.Web.WebView2.Core.ICoreWebView2PrintSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ProcessFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ProcessFailedEventArgs2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ProcessInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Profile";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Profile2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ScriptDialogOpeningEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2ServerCertificateErrorDetectedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings4";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings5";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings6";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings7";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual> = L"Microsoft.Web.WebView2.Core.ICoreWebView2Settings_Manual";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2SourceChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebMessageReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebResourceRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebResourceResponseReceivedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WebResourceResponseView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures> = L"Microsoft.Web.WebView2.Core.ICoreWebView2WindowFeatures";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_10";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_11";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_12";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_13";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_14";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_4";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_5";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_6";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_7";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_8";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9> = L"Microsoft.Web.WebView2.Core.ICoreWebView2_9";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual>{ 0x4B9B0FE5,0x0AD9,0x5594,{ 0x81,0xE7,0xB1,0x8E,0xCC,0x06,0x36,0xDE } }; // 4B9B0FE5-0AD9-5594-81E7-B18ECC0636DE
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual>{ 0xFAEFEFC2,0x20C3,0x5D86,{ 0x8A,0x74,0xF6,0xD8,0x7D,0x6F,0xF8,0xFA } }; // FAEFEFC2-20C3-5D86-8A74-F6D87D6FF8FA
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual>{ 0xB42BFAB4,0xC4BF,0x5469,{ 0x89,0xAC,0xCA,0xDC,0x69,0xE3,0xB0,0xF5 } }; // B42BFAB4-C4BF-5469-89AC-CADC69E3B0F5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>{ 0x3A3F559A,0xE5E9,0x5338,{ 0xBB,0x67,0x4E,0xB0,0x50,0x4A,0x4F,0x14 } }; // 3A3F559A-E5E9-5338-BB67-4EB0504A4F14
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs>{ 0x41A56100,0x92A5,0x59D1,{ 0x9E,0x71,0x92,0x22,0xE3,0x3A,0xE3,0x8B } }; // 41A56100-92A5-59D1-9E71-9222E33AE38B
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs>{ 0x4B16330C,0x4CA5,0x555E,{ 0xAF,0x21,0x16,0x43,0x34,0x40,0x5F,0x63 } }; // 4B16330C-4CA5-555E-AF21-164334405F63
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse>{ 0x08DF33B9,0x6E38,0x5962,{ 0x9F,0xFD,0xCA,0xAB,0x3C,0x30,0xFB,0xC1 } }; // 08DF33B9-6E38-5962-9FFD-CAAB3C30FBC1
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs>{ 0x79963F77,0x1484,0x5A46,{ 0xB9,0x1F,0xDF,0xC5,0xC1,0xA0,0xCE,0x14 } }; // 79963F77-1484-5A46-B91F-DFC5C1A0CE14
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate>{ 0x414A3B75,0x1BC1,0x55E1,{ 0x99,0x26,0x26,0x8C,0x0A,0x34,0x62,0xC7 } }; // 414A3B75-1BC1-55E1-9926-268C0A3462C7
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate>{ 0x091B39F2,0x68DF,0x52B4,{ 0x8F,0xB0,0xFD,0x35,0x61,0xAF,0x41,0xF2 } }; // 091B39F2-68DF-52B4-8FB0-FD3561AF41F2
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs>{ 0x93287B55,0x31F9,0x55A0,{ 0xB6,0x8B,0xD9,0x84,0x1D,0x7E,0x1B,0xF4 } }; // 93287B55-31F9-55A0-B68B-D9841D7E1BF4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>{ 0x4FB8B7B3,0x4A2E,0x5787,{ 0x94,0xB9,0xCC,0x48,0xC4,0xD3,0x64,0xD7 } }; // 4FB8B7B3-4A2E-5787-94B9-CC48C4D364D7
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2>{ 0x8CEF61B9,0xFA55,0x547D,{ 0xAA,0xE6,0x7B,0xCA,0xED,0x42,0x49,0xA2 } }; // 8CEF61B9-FA55-547D-AAE6-7BCAED4249A2
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics>{ 0x4DF0AB1F,0x7F2A,0x573B,{ 0xB8,0x1A,0xB9,0xB5,0x31,0x22,0x47,0x36 } }; // 4DF0AB1F-7F2A-573B-B81A-B9B531224736
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs>{ 0x6CF95373,0x946C,0x5DAE,{ 0x9B,0x3E,0x0F,0xE2,0x3D,0x5A,0xA2,0x9F } }; // 6CF95373-946C-5DAE-9B3E-0FE23D5AA29F
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>{ 0x2A65706F,0x941A,0x52CD,{ 0x86,0x51,0xA1,0x65,0x58,0x6B,0x0A,0xBF } }; // 2A65706F-941A-52CD-8651-A165586B0ABF
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs>{ 0xD77BDD8C,0x9B3E,0x596E,{ 0xAE,0x80,0x32,0x0C,0x0D,0xF4,0xEC,0xBC } }; // D77BDD8C-9B3E-596E-AE80-320C0DF4ECBC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget>{ 0x41E24E6A,0x4612,0x5BD9,{ 0x8E,0x61,0xE9,0x28,0x06,0x15,0x20,0x5E } }; // 41E24E6A-4612-5BD9-8E61-E9280615205E
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>{ 0xA588121C,0x53BF,0x590E,{ 0x80,0xE5,0x29,0xD7,0x29,0xCB,0xD7,0x43 } }; // A588121C-53BF-590E-80E5-29D729CBD743
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2>{ 0x0069C40B,0x2E8A,0x513F,{ 0x9D,0x9D,0xE0,0xC2,0xB6,0x4F,0x72,0x00 } }; // 0069C40B-2E8A-513F-9D9D-E0C2B64F7200
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>{ 0xE5BAE214,0x791A,0x5D13,{ 0x9B,0x76,0xA2,0x57,0xD9,0xFD,0xA2,0xAC } }; // E5BAE214-791A-5D13-9B76-A257D9FDA2AC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4>{ 0x94E2862D,0x4638,0x54BA,{ 0x92,0xCF,0xE3,0x1A,0x31,0x49,0x9B,0x78 } }; // 94E2862D-4638-54BA-92CF-E31A31499B78
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory>{ 0x270B2C5B,0xC3A9,0x53D8,{ 0xA5,0xCA,0x26,0x2E,0xA9,0xEA,0x62,0xE8 } }; // 270B2C5B-C3A9-53D8-A5CA-262EA9EA62E8
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions>{ 0x3337E821,0x3606,0x5A0E,{ 0x8E,0x2F,0x0C,0x1E,0x57,0xD7,0x43,0xF7 } }; // 3337E821-3606-5A0E-8E2F-0C1E57D743F7
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference>{ 0x0FEDDAD4,0x48A3,0x5CC4,{ 0x9F,0x61,0xE7,0xAD,0xFD,0x1E,0x9C,0x76 } }; // 0FEDDAD4-48A3-5CC4-9F61-E7ADFD1E9C76
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics>{ 0xDDF6EBF1,0xEBC6,0x5A34,{ 0x90,0x08,0x66,0x1C,0x3A,0x2E,0xB7,0x67 } }; // DDF6EBF1-EBC6-5A34-9008-661C3A2EB767
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie>{ 0x52F670FE,0x8CA2,0x5AAD,{ 0xAE,0xDB,0x25,0xF7,0x90,0x3B,0x70,0x38 } }; // 52F670FE-8CA2-5AAD-AEDB-25F7903B7038
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager>{ 0x4098F516,0xADCA,0x5563,{ 0xAA,0xA5,0xD7,0xAF,0xFD,0x84,0x7A,0xA3 } }; // 4098F516-ADCA-5563-AAA5-D7AFFD847AA3
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual>{ 0x9BCCA0EA,0x7225,0x577A,{ 0xBB,0x23,0xC7,0xC9,0x80,0x23,0x15,0x4E } }; // 9BCCA0EA-7225-577A-BB23-C7C98023154E
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs>{ 0xC474D0A3,0x24AC,0x59FC,{ 0xB7,0x8B,0xDA,0x75,0x62,0xA6,0xA0,0x52 } }; // C474D0A3-24AC-59FC-B78B-DA7562A6A052
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs>{ 0xB6A4B41D,0xFD18,0x59FA,{ 0x92,0x3A,0xC5,0x75,0x55,0xD9,0x60,0xCE } }; // B6A4B41D-FD18-59FA-923A-C57555D960CE
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>{ 0x221728BA,0x635E,0x50D2,{ 0xBD,0x3C,0xFD,0x22,0xF4,0x11,0x39,0x78 } }; // 221728BA-635E-50D2-BD3C-FD22F4113978
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>{ 0xB2A2BE79,0x65FC,0x5537,{ 0x87,0x15,0x3D,0x92,0xBF,0x31,0x09,0x0B } }; // B2A2BE79-65FC-5537-8715-3D92BF31090B
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>{ 0x7888A42D,0x18F3,0x5966,{ 0x80,0xCB,0x8C,0xC2,0x53,0x51,0xBD,0x0A } }; // 7888A42D-18F3-5966-80CB-8CC25351BD0A
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>{ 0xAFE73E6B,0xE760,0x5A06,{ 0x9B,0xF6,0x1E,0x74,0x3C,0x13,0xCD,0x2D } }; // AFE73E6B-E760-5A06-9BF6-1E743C13CD2D
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs>{ 0x45D982BA,0x9256,0x5B35,{ 0xB0,0x23,0x26,0xA4,0x38,0x59,0x91,0x10 } }; // 45D982BA-9256-5B35-B023-26A438599110
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>{ 0xD8CC7831,0xB783,0x556B,{ 0xB9,0xCE,0x89,0x9C,0x1E,0x95,0xD5,0x85 } }; // D8CC7831-B783-556B-B9CE-899C1E95D585
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10>{ 0xC224E69C,0x1EFD,0x5ECC,{ 0xAD,0xC8,0x2B,0x52,0xE7,0xB9,0x7C,0xE5 } }; // C224E69C-1EFD-5ECC-ADC8-2B52E7B97CE5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2>{ 0x0B634668,0x1017,0x5FC7,{ 0x99,0x21,0xF1,0xF5,0x18,0x66,0xA8,0xC0 } }; // 0B634668-1017-5FC7-9921-F1F51866A8C0
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3>{ 0x5E33F46C,0xC0B9,0x5126,{ 0x88,0x40,0x17,0xF9,0xC1,0x1B,0x3A,0x8A } }; // 5E33F46C-C0B9-5126-8840-17F9C11B3A8A
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4>{ 0x6DB697DA,0xEEBD,0x5818,{ 0x87,0x90,0x1F,0xE5,0x7E,0xF3,0x19,0xE2 } }; // 6DB697DA-EEBD-5818-8790-1FE57EF319E2
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>{ 0xF33399AF,0xE4D3,0x59DC,{ 0xAC,0x38,0x83,0x97,0xAA,0xDC,0xED,0xB1 } }; // F33399AF-E4D3-59DC-AC38-8397AADCEDB1
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6>{ 0x965D538F,0x5958,0x5D98,{ 0x89,0x72,0xF6,0x22,0x02,0x1D,0xF4,0x02 } }; // 965D538F-5958-5D98-8972-F622021DF402
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7>{ 0xE1F44FE2,0xFC54,0x5383,{ 0xA3,0x83,0xC8,0x7E,0x1D,0xA9,0x6B,0x83 } }; // E1F44FE2-FC54-5383-A383-C87E1DA96B83
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>{ 0xDB67C807,0xD0DB,0x5980,{ 0xA3,0xA9,0x75,0xEF,0x8F,0x63,0xD6,0xF6 } }; // DB67C807-D0DB-5980-A3A9-75EF8F63D6F6
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9>{ 0xC8213EC7,0x7DC9,0x5468,{ 0xA8,0x8B,0x15,0xC6,0xB7,0x14,0x44,0x78 } }; // C8213EC7-7DC9-5468-A88B-15C6B7144478
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions>{ 0x25D6DC39,0x0062,0x5735,{ 0x8B,0x09,0xA6,0xF5,0x35,0xF1,0x9E,0x97 } }; // 25D6DC39-0062-5735-8B09-A6F535F19E97
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2>{ 0xE77350FB,0x77A1,0x56F7,{ 0xBE,0x95,0xEB,0x7F,0x8A,0x7A,0x30,0x72 } }; // E77350FB-77A1-56F7-BE95-EB7F8A7A3072
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual>{ 0x1F104443,0xEA93,0x5A37,{ 0xB7,0x91,0x34,0xE6,0xA3,0x11,0x72,0xED } }; // 1F104443-EA93-5A37-B791-34E6A31172ED
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics>{ 0x0E33F804,0xF20B,0x5635,{ 0x84,0x91,0x16,0x2A,0xAA,0x27,0x51,0x7B } }; // 0E33F804-F20B-5635-8491-162AAA27517B
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual>{ 0xF51CFABE,0x73AD,0x5635,{ 0xA9,0x35,0x63,0x86,0xAE,0xF9,0x23,0x8E } }; // F51CFABE-73AD-5635-A935-6386AEF9238E
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>{ 0x02FFCBF9,0x19E7,0x5BB8,{ 0x82,0x73,0x34,0x64,0x20,0xFB,0x15,0x03 } }; // 02FFCBF9-19E7-5BB8-8273-346420FB1503
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>{ 0x33DBC9C9,0xA103,0x56E3,{ 0xB7,0x22,0x36,0x38,0x14,0x20,0x03,0x20 } }; // 33DBC9C9-A103-56E3-B722-************
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>{ 0x6545DAC4,0x1666,0x50A5,{ 0xBB,0xE8,0xEC,0x04,0x84,0x2A,0x46,0x6F } }; // 6545DAC4-1666-50A5-BBE8-EC04842A466F
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs>{ 0x527B01B8,0xFC6D,0x5543,{ 0x8D,0xCE,0x96,0xCD,0xFD,0xB3,0x2C,0x81 } }; // 527B01B8-FC6D-5543-8DCE-96CDFDB32C81
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo>{ 0xF9B82E06,0x73F3,0x513B,{ 0xBC,0x2C,0x44,0x5D,0xDE,0xDB,0xA9,0x76 } }; // F9B82E06-73F3-513B-BC2C-445DDEDBA976
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator>{ 0xADF264EE,0xD980,0x5F48,{ 0xA6,0x0E,0x87,0x05,0xDE,0x04,0x66,0x08 } }; // ADF264EE-D980-5F48-A60E-8705DE046608
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders>{ 0xDC2226C7,0x3515,0x55BB,{ 0xBC,0xB2,0x57,0xB7,0x8F,0x86,0xB9,0x1D } }; // DC2226C7-3515-55BB-BCB2-57B78F86B91D
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders>{ 0xF3D383E9,0x747F,0x5574,{ 0x86,0x62,0x9A,0x6B,0x92,0x0C,0xEC,0xD4 } }; // F3D383E9-747F-5574-8662-9A6B920CECD4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs>{ 0x2E29103B,0xECDD,0x5C1D,{ 0xB2,0x88,0x3F,0x06,0x6D,0x60,0x89,0x19 } }; // 2E29103B-ECDD-5C1D-B288-3F066D608919
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs>{ 0x4865E238,0x036A,0x5664,{ 0x95,0xA3,0x44,0x7E,0xC4,0x4C,0xF4,0x98 } }; // 4865E238-036A-5664-95A3-447EC44CF498
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2>{ 0x6E4D3C33,0xA6E2,0x5896,{ 0x90,0xC5,0x68,0xB4,0xB5,0xE5,0x5B,0x40 } }; // 6E4D3C33-A6E2-5896-90C5-68B4B5E55B40
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs>{ 0x548D23D3,0xFEA3,0x5616,{ 0xBD,0x05,0xAE,0x08,0x06,0x6C,0x86,0xD3 } }; // 548D23D3-FEA3-5616-BD05-AE08066C86D3
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2>{ 0xD7A3824E,0x7654,0x5C4B,{ 0xB0,0x69,0xE6,0x50,0x16,0x34,0xD8,0x4C } }; // D7A3824E-7654-5C4B-B069-E6501634D84C
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs>{ 0xE6E013BA,0xAEC8,0x532E,{ 0x9A,0xC9,0x15,0x90,0xAF,0x7B,0x25,0xEC } }; // E6E013BA-AEC8-532E-9AC9-1590AF7B25EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2>{ 0xF4806259,0xE63A,0x5C0B,{ 0xA0,0x2C,0x5F,0x10,0xE1,0x10,0x94,0xF4 } }; // F4806259-E63A-5C0B-A02C-5F10E11094F4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs>{ 0x118BDD9B,0xCEF1,0x5910,{ 0x92,0x9E,0xC1,0xA3,0x21,0x32,0x82,0x39 } }; // 118BDD9B-CEF1-5910-929E-C1A321328239
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2>{ 0xA6652FBA,0xEBE5,0x5891,{ 0xAD,0xDC,0xCB,0x37,0xDA,0x8F,0x7E,0x66 } }; // A6652FBA-EBE5-5891-ADDC-CB37DA8F7E66
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo>{ 0xC3860E0D,0xC018,0x5A84,{ 0xBC,0x06,0x9F,0x8F,0x7B,0x27,0x5D,0xFF } }; // C3860E0D-C018-5A84-BC06-9F8F7B275DFF
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings>{ 0x9C75C8C0,0xEF3D,0x58A8,{ 0x9A,0x8C,0x18,0xEE,0xD9,0xFD,0x0F,0x16 } }; // 9C75C8C0-EF3D-58A8-9A8C-18EED9FD0F16
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs>{ 0x25A8F8C9,0xD944,0x539D,{ 0xAF,0xA3,0x24,0x17,0x2B,0x48,0xEF,0x47 } }; // 25A8F8C9-D944-539D-AFA3-24172B48EF47
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2>{ 0xC5D9C952,0xB456,0x5DC7,{ 0x9F,0x76,0xFD,0xE9,0x67,0x48,0x4A,0xF5 } }; // C5D9C952-B456-5DC7-9F76-FDE967484AF5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo>{ 0xB6EC37E1,0x23EB,0x5924,{ 0xB3,0x46,0xE8,0x37,0x89,0x0A,0xA9,0xD5 } }; // B6EC37E1-23EB-5924-B346-E837890AA9D5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile>{ 0xD4BDD25C,0xA2DB,0x5C03,{ 0x96,0x59,0xAB,0xDE,0xB9,0x79,0x36,0x21 } }; // D4BDD25C-A2DB-5C03-9659-ABDEB9793621
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2>{ 0x93D21E18,0x1B06,0x59D0,{ 0x96,0x87,0x10,0xF4,0x84,0x4B,0x01,0x6D } }; // 93D21E18-1B06-59D0-9687-10F4844B016D
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs>{ 0xA4315212,0xC7EB,0x568A,{ 0x86,0xE4,0xC6,0x1E,0x31,0xBA,0x6C,0xDA } }; // A4315212-C7EB-568A-86E4-C61E31BA6CDA
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs>{ 0x90FDC703,0x5A9E,0x56F6,{ 0xA4,0x22,0x7C,0x11,0x4C,0x73,0x64,0x20 } }; // 90FDC703-5A9E-56F6-A422-7C114C736420
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings>{ 0x003B325E,0x74CD,0x52DD,{ 0x80,0x24,0xEB,0xB8,0xBE,0x38,0xE4,0x8E } }; // 003B325E-74CD-52DD-8024-EBB8BE38E48E
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2>{ 0x377D3480,0xFDB2,0x56E7,{ 0xBA,0xDE,0x50,0x7D,0x35,0x28,0x87,0xE9 } }; // 377D3480-FDB2-56E7-BADE-507D352887E9
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3>{ 0x52200F01,0x5309,0x5B2E,{ 0xA0,0x3C,0x3D,0x26,0x77,0x59,0x19,0x40 } }; // 52200F01-5309-5B2E-A03C-3D2677591940
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4>{ 0xD6A955F0,0xDAEF,0x5A6A,{ 0xA6,0xF6,0xC7,0x2F,0x0E,0xDE,0x76,0x20 } }; // D6A955F0-DAEF-5A6A-A6F6-C72F0EDE7620
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5>{ 0xAFC42B23,0x4839,0x5D73,{ 0xAC,0xF7,0xE0,0x33,0x56,0x31,0xAB,0xF5 } }; // AFC42B23-4839-5D73-ACF7-E0335631ABF5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6>{ 0x3FE4AE85,0x0540,0x5BF1,{ 0xB4,0xD9,0x99,0xEC,0x57,0xAA,0x64,0xF5 } }; // 3FE4AE85-0540-5BF1-B4D9-99EC57AA64F5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7>{ 0x688027CD,0x9F84,0x59E8,{ 0x8D,0x5C,0x91,0x12,0x3D,0xF2,0x4B,0x92 } }; // 688027CD-9F84-59E8-8D5C-91123DF24B92
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual>{ 0x0A538C87,0xE000,0x511C,{ 0x87,0xCA,0xDE,0xD3,0x41,0x3D,0x03,0xDA } }; // 0A538C87-E000-511C-87CA-DED3413D03DA
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs>{ 0xCA437B2C,0x6A18,0x5552,{ 0xB7,0x49,0xB1,0x98,0xF8,0xCC,0x34,0xD9 } }; // CA437B2C-6A18-5552-B749-B198F8CC34D9
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs>{ 0xEB066159,0xB725,0x5D5B,{ 0xAD,0xC8,0xF5,0xD7,0xB9,0x29,0x03,0x04 } }; // EB066159-B725-5D5B-ADC8-F5D7B9290304
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest>{ 0x5C742259,0x67D2,0x5DF2,{ 0x83,0x82,0x0F,0x20,0x1B,0x4D,0x71,0x97 } }; // 5C742259-67D2-5DF2-8382-0F201B4D7197
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs>{ 0x577F1FC4,0xC943,0x54A9,{ 0x97,0x00,0xBD,0x46,0x9B,0x48,0xBD,0x41 } }; // 577F1FC4-C943-54A9-9700-BD469B48BD41
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse>{ 0x14621923,0xE485,0x5F44,{ 0x8F,0x5D,0xBD,0x42,0x43,0xBC,0x39,0x8F } }; // 14621923-E485-5F44-8F5D-BD4243BC398F
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs>{ 0x12424671,0x9711,0x54F4,{ 0xBC,0xDF,0x5F,0x30,0x7A,0xDD,0x6E,0xC2 } }; // 12424671-9711-54F4-BCDF-5F307ADD6EC2
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView>{ 0x33EE060B,0xB578,0x5698,{ 0xB5,0x41,0xFE,0xF8,0x7F,0xE7,0xFE,0x72 } }; // 33EE060B-B578-5698-B541-FEF87FE7FE72
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures>{ 0xEE8686D6,0x056F,0x5E06,{ 0x82,0x4F,0x4E,0x2A,0x24,0xC1,0xC1,0xD6 } }; // EE8686D6-056F-5E06-824F-4E2A24C1C1D6
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>{ 0xA7B20434,0x970F,0x54B1,{ 0xAA,0x63,0x3C,0x42,0x67,0x1F,0xA9,0xAB } }; // A7B20434-970F-54B1-AA63-3C42671FA9AB
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>{ 0xC00ACBB1,0xAE32,0x501F,{ 0xAD,0x19,0x9D,0x0A,0xC3,0x2D,0x61,0x42 } }; // C00ACBB1-AE32-501F-AD19-9D0AC32D6142
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>{ 0xDBBBE9A1,0x18D3,0x5F67,{ 0xB3,0x62,0x0F,0x4A,0xE9,0x37,0xD7,0x54 } }; // DBBBE9A1-18D3-5F67-B362-0F4AE937D754
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13>{ 0x314B5846,0xDBC7,0x5DE4,{ 0xA7,0x92,0x64,0x7E,0xA0,0xF3,0x29,0x6A } }; // 314B5846-DBC7-5DE4-A792-647EA0F3296A
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>{ 0xA7647B24,0x3B1E,0x50A9,{ 0xBE,0x24,0x6E,0x8A,0xC6,0x3F,0xE4,0x91 } }; // A7647B24-3B1E-50A9-BE24-6E8AC63FE491
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>{ 0x578CB133,0x2873,0x5408,{ 0xBD,0x9E,0x38,0x9B,0xBE,0x9F,0xA7,0xFA } }; // 578CB133-2873-5408-BD9E-389BBE9FA7FA
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3>{ 0xA8C76AE7,0x6170,0x5DFE,{ 0x8F,0x00,0x79,0xCD,0x76,0xA9,0xB4,0xD9 } }; // A8C76AE7-6170-5DFE-8F00-79CD76A9B4D9
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>{ 0x4AC595CE,0x1502,0x5775,{ 0xB2,0xC8,0x22,0xC1,0x1A,0x36,0x9C,0x25 } }; // 4AC595CE-1502-5775-B2C8-22C11A369C25
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>{ 0xDD6AF643,0x220C,0x5DC6,{ 0xB0,0xA8,0x22,0xC4,0x1E,0x47,0x25,0x95 } }; // DD6AF643-220C-5DC6-B0A8-22C41E472595
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6>{ 0x92B34B96,0x853D,0x5BB6,{ 0xAC,0x52,0x30,0x29,0x7C,0xE8,0x05,0xF1 } }; // 92B34B96-853D-5BB6-AC52-30297CE805F1
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7>{ 0xF9B7107A,0x2E09,0x5596,{ 0xA0,0x33,0x91,0x1B,0xA1,0x23,0x15,0xF7 } }; // F9B7107A-2E09-5596-A033-911BA12315F7
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>{ 0xAA2503C0,0x8D1C,0x5A3D,{ 0xB8,0x98,0xF5,0x5F,0x75,0x95,0x26,0x8A } }; // AA2503C0-8D1C-5A3D-B898-F55F7595268A
    template <> inline constexpr guid guid_v<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>{ 0x64B2EC16,0x0B29,0x5216,{ 0xBF,0x86,0xE5,0x75,0xC8,0x8F,0x70,0x31 } }; // 64B2EC16-0B29-5216-BF86-E575C88F7031
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationResponse>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuTarget>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieManager>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceiver>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameInfo>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpHeadersCollectionIterator>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpRequestHeaders>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2HttpResponseHeaders>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessInfo>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Settings>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseView>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView; };
    template <> struct default_interface<winrt::Microsoft::Web::WebView2::Core::CoreWebView2WindowFeatures>{ using type = winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures; };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ToCertificate(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ToCertificate(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ClearBrowsingDataAsync(uint32_t, int64_t, int64_t, void**) noexcept = 0;
            virtual int32_t __stdcall ClearBrowsingDataAsync2(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Settings(void**) noexcept = 0;
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall get_BrowserProcessId(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_CanGoBack(bool*) noexcept = 0;
            virtual int32_t __stdcall get_CanGoForward(bool*) noexcept = 0;
            virtual int32_t __stdcall get_DocumentTitle(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContainsFullScreenElement(bool*) noexcept = 0;
            virtual int32_t __stdcall add_NavigationStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NavigationStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ContentLoading(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContentLoading(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_SourceChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_SourceChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_HistoryChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_HistoryChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_NavigationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NavigationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_FrameNavigationStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_FrameNavigationStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_FrameNavigationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_FrameNavigationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ScriptDialogOpening(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ScriptDialogOpening(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PermissionRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PermissionRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ProcessFailed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ProcessFailed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_WebMessageReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_WebMessageReceived(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_NewWindowRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NewWindowRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DocumentTitleChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DocumentTitleChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ContainsFullScreenElementChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContainsFullScreenElementChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_WebResourceRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_WebResourceRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_WindowCloseRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_WindowCloseRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Navigate(void*) noexcept = 0;
            virtual int32_t __stdcall NavigateToString(void*) noexcept = 0;
            virtual int32_t __stdcall AddScriptToExecuteOnDocumentCreatedAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall RemoveScriptToExecuteOnDocumentCreated(void*) noexcept = 0;
            virtual int32_t __stdcall ExecuteScriptAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CapturePreviewAsync(int32_t, void*, void**) noexcept = 0;
            virtual int32_t __stdcall Reload() noexcept = 0;
            virtual int32_t __stdcall PostWebMessageAsJson(void*) noexcept = 0;
            virtual int32_t __stdcall PostWebMessageAsString(void*) noexcept = 0;
            virtual int32_t __stdcall CallDevToolsProtocolMethodAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall GoBack() noexcept = 0;
            virtual int32_t __stdcall GoForward() noexcept = 0;
            virtual int32_t __stdcall GetDevToolsProtocolEventReceiver(void*, void**) noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
            virtual int32_t __stdcall AddHostObjectToScript(void*, void*) noexcept = 0;
            virtual int32_t __stdcall RemoveHostObjectFromScript(void*) noexcept = 0;
            virtual int32_t __stdcall OpenDevToolsWindow() noexcept = 0;
            virtual int32_t __stdcall AddWebResourceRequestedFilter(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall RemoveWebResourceRequestedFilter(void*, int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_KeyEventKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_VirtualKey(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_KeyEventLParam(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_PhysicalKeyStatus(struct struct_Microsoft_Web_WebView2_Core_CoreWebView2PhysicalKeyStatus*) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_Challenge(void**) noexcept = 0;
            virtual int32_t __stdcall get_Response(void**) noexcept = 0;
            virtual int32_t __stdcall get_Cancel(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Cancel(bool) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UserName(void**) noexcept = 0;
            virtual int32_t __stdcall put_UserName(void*) noexcept = 0;
            virtual int32_t __stdcall get_Password(void**) noexcept = 0;
            virtual int32_t __stdcall put_Password(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_BrowserProcessExitKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_BrowserProcessId(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Subject(void**) noexcept = 0;
            virtual int32_t __stdcall get_Issuer(void**) noexcept = 0;
            virtual int32_t __stdcall get_ValidFrom(double*) noexcept = 0;
            virtual int32_t __stdcall get_ValidTo(double*) noexcept = 0;
            virtual int32_t __stdcall get_DerEncodedSerialNumber(void**) noexcept = 0;
            virtual int32_t __stdcall get_DisplayName(void**) noexcept = 0;
            virtual int32_t __stdcall get_PemEncodedIssuerCertificateChain(void**) noexcept = 0;
            virtual int32_t __stdcall ToPemEncoding(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Subject(void**) noexcept = 0;
            virtual int32_t __stdcall get_Issuer(void**) noexcept = 0;
            virtual int32_t __stdcall get_ValidFrom(double*) noexcept = 0;
            virtual int32_t __stdcall get_ValidTo(double*) noexcept = 0;
            virtual int32_t __stdcall get_DerEncodedSerialNumber(void**) noexcept = 0;
            virtual int32_t __stdcall get_DisplayName(void**) noexcept = 0;
            virtual int32_t __stdcall get_PemEncodedIssuerCertificateChain(void**) noexcept = 0;
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall ToPemEncoding(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Host(void**) noexcept = 0;
            virtual int32_t __stdcall get_Port(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsProxy(bool*) noexcept = 0;
            virtual int32_t __stdcall get_AllowedCertificateAuthorities(void**) noexcept = 0;
            virtual int32_t __stdcall get_MutuallyTrustedCertificates(void**) noexcept = 0;
            virtual int32_t __stdcall get_SelectedCertificate(void**) noexcept = 0;
            virtual int32_t __stdcall put_SelectedCertificate(void*) noexcept = 0;
            virtual int32_t __stdcall get_Cancel(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Cancel(bool) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_RootVisualTarget(void**) noexcept = 0;
            virtual int32_t __stdcall put_RootVisualTarget(void*) noexcept = 0;
            virtual int32_t __stdcall add_CursorChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CursorChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall SendMouseInput(int32_t, uint32_t, uint32_t, winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall SendPointerInput(int32_t, void*) noexcept = 0;
            virtual int32_t __stdcall get_Cursor(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsErrorPage(bool*) noexcept = 0;
            virtual int32_t __stdcall get_NavigationId(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Label(void**) noexcept = 0;
            virtual int32_t __stdcall get_CommandId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ShortcutKeyDescription(void**) noexcept = 0;
            virtual int32_t __stdcall get_Icon(void**) noexcept = 0;
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsChecked(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsChecked(bool) noexcept = 0;
            virtual int32_t __stdcall get_Children(void**) noexcept = 0;
            virtual int32_t __stdcall add_CustomItemSelected(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CustomItemSelected(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MenuItems(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContextMenuTarget(void**) noexcept = 0;
            virtual int32_t __stdcall get_Location(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall get_SelectedCommandId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SelectedCommandId(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsEditable(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsRequestedForMainFrame(bool*) noexcept = 0;
            virtual int32_t __stdcall get_PageUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_FrameUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_HasLinkUri(bool*) noexcept = 0;
            virtual int32_t __stdcall get_LinkUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_HasLinkText(bool*) noexcept = 0;
            virtual int32_t __stdcall get_LinkText(void**) noexcept = 0;
            virtual int32_t __stdcall get_HasSourceUri(bool*) noexcept = 0;
            virtual int32_t __stdcall get_SourceUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_HasSelection(bool*) noexcept = 0;
            virtual int32_t __stdcall get_SelectionText(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsVisible(bool) noexcept = 0;
            virtual int32_t __stdcall get_Bounds(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_Bounds(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_ZoomFactor(double*) noexcept = 0;
            virtual int32_t __stdcall put_ZoomFactor(double) noexcept = 0;
            virtual int32_t __stdcall get_ParentWindow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ParentWindow(void*) noexcept = 0;
            virtual int32_t __stdcall get_CoreWebView2(void**) noexcept = 0;
            virtual int32_t __stdcall add_ZoomFactorChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ZoomFactorChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_MoveFocusRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_MoveFocusRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_GotFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_GotFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_LostFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_LostFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AcceleratorKeyPressed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AcceleratorKeyPressed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall SetBoundsAndZoomFactor(winrt::Windows::Foundation::Rect, double) noexcept = 0;
            virtual int32_t __stdcall MoveFocus(int32_t) noexcept = 0;
            virtual int32_t __stdcall NotifyParentWindowPositionChanged() noexcept = 0;
            virtual int32_t __stdcall Close() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DefaultBackgroundColor(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_DefaultBackgroundColor(struct struct_Windows_UI_Color) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_RasterizationScale(double*) noexcept = 0;
            virtual int32_t __stdcall put_RasterizationScale(double) noexcept = 0;
            virtual int32_t __stdcall get_ShouldDetectMonitorScaleChanges(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShouldDetectMonitorScaleChanges(bool) noexcept = 0;
            virtual int32_t __stdcall get_BoundsMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_BoundsMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall add_RasterizationScaleChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_RasterizationScaleChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AllowExternalDrop(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowExternalDrop(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ProfileName(void**) noexcept = 0;
            virtual int32_t __stdcall put_ProfileName(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsInPrivateModeEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsInPrivateModeEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WindowHandle(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall get_CoreWindow(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateFromWindowHandle(uint64_t, void**) noexcept = 0;
            virtual int32_t __stdcall CreateFromCoreWindow(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Value(void**) noexcept = 0;
            virtual int32_t __stdcall put_Value(void*) noexcept = 0;
            virtual int32_t __stdcall get_Domain(void**) noexcept = 0;
            virtual int32_t __stdcall get_Path(void**) noexcept = 0;
            virtual int32_t __stdcall get_Expires(double*) noexcept = 0;
            virtual int32_t __stdcall put_Expires(double) noexcept = 0;
            virtual int32_t __stdcall get_IsHttpOnly(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsHttpOnly(bool) noexcept = 0;
            virtual int32_t __stdcall get_SameSite(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SameSite(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_IsSecure(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsSecure(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsSession(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateCookie(void*, void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CopyCookie(void*, void**) noexcept = 0;
            virtual int32_t __stdcall AddOrUpdateCookie(void*) noexcept = 0;
            virtual int32_t __stdcall DeleteCookie(void*) noexcept = 0;
            virtual int32_t __stdcall DeleteCookies(void*, void*) noexcept = 0;
            virtual int32_t __stdcall DeleteCookiesWithDomainAndPath(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall DeleteAllCookies() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetCookiesAsync(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_NavigationId(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ParameterObjectAsJson(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SessionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_DevToolsProtocolEventReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DevToolsProtocolEventReceived(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall WrapNamedObject(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall WrapObject(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall UnwrapObject(void*, void**) noexcept = 0;
            virtual int32_t __stdcall Clean() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContentDisposition(void**) noexcept = 0;
            virtual int32_t __stdcall get_MimeType(void**) noexcept = 0;
            virtual int32_t __stdcall get_TotalBytesToReceive(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_BytesReceived(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_EstimatedEndTime(void**) noexcept = 0;
            virtual int32_t __stdcall get_ResultFilePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_State(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_InterruptReason(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_CanResume(bool*) noexcept = 0;
            virtual int32_t __stdcall add_BytesReceivedChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BytesReceivedChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EstimatedEndTimeChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EstimatedEndTimeChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_StateChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_StateChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Cancel() noexcept = 0;
            virtual int32_t __stdcall Pause() noexcept = 0;
            virtual int32_t __stdcall Resume() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DownloadOperation(void**) noexcept = 0;
            virtual int32_t __stdcall get_Cancel(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Cancel(bool) noexcept = 0;
            virtual int32_t __stdcall get_ResultFilePath(void**) noexcept = 0;
            virtual int32_t __stdcall put_ResultFilePath(void*) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_BrowserVersionString(void**) noexcept = 0;
            virtual int32_t __stdcall add_NewBrowserVersionAvailable(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NewBrowserVersionAvailable(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall CreateCoreWebView2ControllerAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateWebResourceResponse(void*, int32_t, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateCoreWebView2ControllerOptions(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateWebResourceRequest(void*, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateCoreWebView2CompositionControllerAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateCoreWebView2PointerInfo(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_BrowserProcessExited(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BrowserProcessExited(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreatePrintSettings(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UserDataFolder(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_ProcessInfosChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ProcessInfosChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall GetProcessInfos(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateContextMenuItem(void*, void*, int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AdditionalBrowserArguments(void**) noexcept = 0;
            virtual int32_t __stdcall put_AdditionalBrowserArguments(void*) noexcept = 0;
            virtual int32_t __stdcall get_Language(void**) noexcept = 0;
            virtual int32_t __stdcall put_Language(void*) noexcept = 0;
            virtual int32_t __stdcall get_TargetCompatibleBrowserVersion(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetCompatibleBrowserVersion(void*) noexcept = 0;
            virtual int32_t __stdcall get_AllowSingleSignOnUsingOSPrimaryAccount(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowSingleSignOnUsingOSPrimaryAccount(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExclusiveUserDataFolderAccess(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ExclusiveUserDataFolderAccess(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateAsync(void**) noexcept = 0;
            virtual int32_t __stdcall CreateWithOptionsAsync(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetAvailableBrowserVersionString(void**) noexcept = 0;
            virtual int32_t __stdcall GetAvailableBrowserVersionString2(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CompareBrowserVersionString(void*, void*, int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateCoreWebView2ControllerAsync(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateCoreWebView2CompositionControllerAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall add_NameChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NameChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Destroyed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Destroyed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall RemoveHostObjectFromScript(void*) noexcept = 0;
            virtual int32_t __stdcall IsDestroyed(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_NavigationStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NavigationStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ContentLoading(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContentLoading(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_NavigationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NavigationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DOMContentLoaded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DOMContentLoaded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_WebMessageReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_WebMessageReceived(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall ExecuteScriptAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall PostWebMessageAsJson(void*) noexcept = 0;
            virtual int32_t __stdcall PostWebMessageAsString(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_PermissionRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PermissionRequested(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Frame(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetHeader(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetHeaders(void*, void**) noexcept = 0;
            virtual int32_t __stdcall Contains(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall SetHeader(void*, void*) noexcept = 0;
            virtual int32_t __stdcall RemoveHeader(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AppendHeader(void*, void*) noexcept = 0;
            virtual int32_t __stdcall Contains(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall GetHeader(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetHeaders(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Reason(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSuccess(bool*) noexcept = 0;
            virtual int32_t __stdcall get_WebErrorStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_NavigationId(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HttpStatusCode(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsUserInitiated(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsRedirected(bool*) noexcept = 0;
            virtual int32_t __stdcall get_RequestHeaders(void**) noexcept = 0;
            virtual int32_t __stdcall get_Cancel(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Cancel(bool) noexcept = 0;
            virtual int32_t __stdcall get_NavigationId(uint64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AdditionalAllowedFrameAncestors(void**) noexcept = 0;
            virtual int32_t __stdcall put_AdditionalAllowedFrameAncestors(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_NewWindow(void**) noexcept = 0;
            virtual int32_t __stdcall put_NewWindow(void*) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsUserInitiated(bool*) noexcept = 0;
            virtual int32_t __stdcall get_WindowFeatures(void**) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_PermissionKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsUserInitiated(bool*) noexcept = 0;
            virtual int32_t __stdcall get_State(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_State(int32_t) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_PointerKind(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PointerKind(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PointerId(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PointerId(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_FrameId(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_FrameId(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PointerFlags(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PointerFlags(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PointerDeviceRect(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_PointerDeviceRect(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_DisplayRect(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_DisplayRect(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_PixelLocation(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_PixelLocation(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_HimetricLocation(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_HimetricLocation(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_PixelLocationRaw(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_PixelLocationRaw(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_HimetricLocationRaw(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_HimetricLocationRaw(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_Time(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Time(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_HistoryCount(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HistoryCount(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_InputData(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_InputData(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_KeyStates(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_KeyStates(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PerformanceCount(uint64_t*) noexcept = 0;
            virtual int32_t __stdcall put_PerformanceCount(uint64_t) noexcept = 0;
            virtual int32_t __stdcall get_ButtonChangeKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ButtonChangeKind(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenFlags(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenFlags(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenMask(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenMask(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenPressure(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenPressure(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenRotation(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenRotation(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenTiltX(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenTiltX(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_PenTiltY(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PenTiltY(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_TouchFlags(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TouchFlags(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_TouchMask(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TouchMask(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_TouchContact(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_TouchContact(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_TouchContactRaw(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_TouchContactRaw(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_TouchOrientation(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TouchOrientation(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_TouchPressure(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TouchPressure(uint32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Orientation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Orientation(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ScaleFactor(double*) noexcept = 0;
            virtual int32_t __stdcall put_ScaleFactor(double) noexcept = 0;
            virtual int32_t __stdcall get_PageWidth(double*) noexcept = 0;
            virtual int32_t __stdcall put_PageWidth(double) noexcept = 0;
            virtual int32_t __stdcall get_PageHeight(double*) noexcept = 0;
            virtual int32_t __stdcall put_PageHeight(double) noexcept = 0;
            virtual int32_t __stdcall get_MarginTop(double*) noexcept = 0;
            virtual int32_t __stdcall put_MarginTop(double) noexcept = 0;
            virtual int32_t __stdcall get_MarginBottom(double*) noexcept = 0;
            virtual int32_t __stdcall put_MarginBottom(double) noexcept = 0;
            virtual int32_t __stdcall get_MarginLeft(double*) noexcept = 0;
            virtual int32_t __stdcall put_MarginLeft(double) noexcept = 0;
            virtual int32_t __stdcall get_MarginRight(double*) noexcept = 0;
            virtual int32_t __stdcall put_MarginRight(double) noexcept = 0;
            virtual int32_t __stdcall get_ShouldPrintBackgrounds(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShouldPrintBackgrounds(bool) noexcept = 0;
            virtual int32_t __stdcall get_ShouldPrintSelectionOnly(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShouldPrintSelectionOnly(bool) noexcept = 0;
            virtual int32_t __stdcall get_ShouldPrintHeaderAndFooter(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShouldPrintHeaderAndFooter(bool) noexcept = 0;
            virtual int32_t __stdcall get_HeaderTitle(void**) noexcept = 0;
            virtual int32_t __stdcall put_HeaderTitle(void*) noexcept = 0;
            virtual int32_t __stdcall get_FooterUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_FooterUri(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ProcessFailedKind(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Reason(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ExitCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ProcessDescription(void**) noexcept = 0;
            virtual int32_t __stdcall get_FrameInfosForFailedProcess(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ProcessId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ProfileName(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsInPrivateModeEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ProfilePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_DefaultDownloadFolderPath(void**) noexcept = 0;
            virtual int32_t __stdcall put_DefaultDownloadFolderPath(void*) noexcept = 0;
            virtual int32_t __stdcall get_PreferredColorScheme(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PreferredColorScheme(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ClearBrowsingDataAsync(uint32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
            virtual int32_t __stdcall get_DefaultText(void**) noexcept = 0;
            virtual int32_t __stdcall get_ResultText(void**) noexcept = 0;
            virtual int32_t __stdcall put_ResultText(void*) noexcept = 0;
            virtual int32_t __stdcall Accept() noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ErrorStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_RequestUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_ServerCertificate(void**) noexcept = 0;
            virtual int32_t __stdcall get_Action(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Action(int32_t) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsScriptEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsScriptEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsWebMessageEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsWebMessageEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_AreDefaultScriptDialogsEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AreDefaultScriptDialogsEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsStatusBarEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsStatusBarEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_AreDevToolsEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AreDevToolsEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_AreDefaultContextMenusEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AreDefaultContextMenusEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_AreHostObjectsAllowed(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AreHostObjectsAllowed(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsZoomControlEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsZoomControlEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsBuiltInErrorPageEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsBuiltInErrorPageEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UserAgent(void**) noexcept = 0;
            virtual int32_t __stdcall put_UserAgent(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AreBrowserAcceleratorKeysEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AreBrowserAcceleratorKeysEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsPasswordAutosaveEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsPasswordAutosaveEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsGeneralAutofillEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsGeneralAutofillEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsPinchZoomEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsPinchZoomEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSwipeNavigationEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsSwipeNavigationEnabled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HiddenPdfToolbarItems(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HiddenPdfToolbarItems(uint32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HostObjectDispatchAdapter(void**) noexcept = 0;
            virtual int32_t __stdcall put_HostObjectDispatchAdapter(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsNewDocument(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall get_WebMessageAsJson(void**) noexcept = 0;
            virtual int32_t __stdcall TryGetWebMessageAsString(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall put_Uri(void*) noexcept = 0;
            virtual int32_t __stdcall get_Method(void**) noexcept = 0;
            virtual int32_t __stdcall put_Method(void*) noexcept = 0;
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Request(void**) noexcept = 0;
            virtual int32_t __stdcall get_Response(void**) noexcept = 0;
            virtual int32_t __stdcall put_Response(void*) noexcept = 0;
            virtual int32_t __stdcall get_ResourceContext(int32_t*) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
            virtual int32_t __stdcall get_StatusCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_StatusCode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ReasonPhrase(void**) noexcept = 0;
            virtual int32_t __stdcall put_ReasonPhrase(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Request(void**) noexcept = 0;
            virtual int32_t __stdcall get_Response(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
            virtual int32_t __stdcall get_StatusCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ReasonPhrase(void**) noexcept = 0;
            virtual int32_t __stdcall GetContentAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HasPosition(bool*) noexcept = 0;
            virtual int32_t __stdcall get_HasSize(bool*) noexcept = 0;
            virtual int32_t __stdcall get_Left(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Top(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Height(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Width(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ShouldDisplayMenuBar(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ShouldDisplayStatus(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ShouldDisplayToolbar(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ShouldDisplayScrollBars(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_BasicAuthenticationRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BasicAuthenticationRequested(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_ContextMenuRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContextMenuRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall CallDevToolsProtocolMethodForSessionAsync(void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_StatusBarText(void**) noexcept = 0;
            virtual int32_t __stdcall add_StatusBarTextChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_StatusBarTextChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Profile(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_ServerCertificateErrorDetected(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ServerCertificateErrorDetected(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall ClearServerCertificateErrorActionsAsync(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_CookieManager(void**) noexcept = 0;
            virtual int32_t __stdcall get_Environment(void**) noexcept = 0;
            virtual int32_t __stdcall add_WebResourceResponseReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_WebResourceResponseReceived(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DOMContentLoaded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DOMContentLoaded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall NavigateWithWebResourceRequest(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSuspended(bool*) noexcept = 0;
            virtual int32_t __stdcall TrySuspendAsync(void**) noexcept = 0;
            virtual int32_t __stdcall Resume() noexcept = 0;
            virtual int32_t __stdcall SetVirtualHostNameToFolderMapping(void*, void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall ClearVirtualHostNameToFolderMapping(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_FrameCreated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_FrameCreated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DownloadStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DownloadStarting(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_ClientCertificateRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ClientCertificateRequested(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OpenTaskManagerWindow() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall PrintToPdfAsync(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsMuted(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsMuted(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsDocumentPlayingAudio(bool*) noexcept = 0;
            virtual int32_t __stdcall add_IsMutedChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_IsMutedChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_IsDocumentPlayingAudioChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_IsDocumentPlayingAudioChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsDefaultDownloadDialogOpen(bool*) noexcept = 0;
            virtual int32_t __stdcall get_DefaultDownloadDialogCornerAlignment(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_DefaultDownloadDialogCornerAlignment(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall add_IsDefaultDownloadDialogOpenChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_IsDefaultDownloadDialogOpenChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall OpenDefaultDownloadDialog() noexcept = 0;
            virtual int32_t __stdcall CloseDefaultDownloadDialog() noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_CoreWebView2Certificate_Manual
    {
        auto ToCertificate() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Certificate_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_CoreWebView2Certificate_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_CoreWebView2ClientCertificate_Manual
    {
        auto ToCertificate() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_CoreWebView2ClientCertificate_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_CoreWebView2Profile_Manual
    {
        auto ClearBrowsingDataAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const& dataKinds, winrt::Windows::Foundation::DateTime const& startTime, winrt::Windows::Foundation::DateTime const& endTime) const;
        auto ClearBrowsingDataAsync() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Profile_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_CoreWebView2Profile_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2
    {
        [[nodiscard]] auto Settings() const;
        [[nodiscard]] auto Source() const;
        [[nodiscard]] auto BrowserProcessId() const;
        [[nodiscard]] auto CanGoBack() const;
        [[nodiscard]] auto CanGoForward() const;
        [[nodiscard]] auto DocumentTitle() const;
        [[nodiscard]] auto ContainsFullScreenElement() const;
        auto NavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        using NavigationStarting_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_NavigationStarting>;
        [[nodiscard]] auto NavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        auto NavigationStarting(winrt::event_token const& token) const noexcept;
        auto ContentLoading(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const;
        using ContentLoading_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_ContentLoading>;
        [[nodiscard]] auto ContentLoading(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const;
        auto ContentLoading(winrt::event_token const& token) const noexcept;
        auto SourceChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> const& handler) const;
        using SourceChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_SourceChanged>;
        [[nodiscard]] auto SourceChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2SourceChangedEventArgs> const& handler) const;
        auto SourceChanged(winrt::event_token const& token) const noexcept;
        auto HistoryChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using HistoryChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_HistoryChanged>;
        [[nodiscard]] auto HistoryChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto HistoryChanged(winrt::event_token const& token) const noexcept;
        auto NavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        using NavigationCompleted_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_NavigationCompleted>;
        [[nodiscard]] auto NavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        auto NavigationCompleted(winrt::event_token const& token) const noexcept;
        auto FrameNavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        using FrameNavigationStarting_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_FrameNavigationStarting>;
        [[nodiscard]] auto FrameNavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        auto FrameNavigationStarting(winrt::event_token const& token) const noexcept;
        auto FrameNavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        using FrameNavigationCompleted_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_FrameNavigationCompleted>;
        [[nodiscard]] auto FrameNavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        auto FrameNavigationCompleted(winrt::event_token const& token) const noexcept;
        auto ScriptDialogOpening(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> const& handler) const;
        using ScriptDialogOpening_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_ScriptDialogOpening>;
        [[nodiscard]] auto ScriptDialogOpening(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ScriptDialogOpeningEventArgs> const& handler) const;
        auto ScriptDialogOpening(winrt::event_token const& token) const noexcept;
        auto PermissionRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const;
        using PermissionRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_PermissionRequested>;
        [[nodiscard]] auto PermissionRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const;
        auto PermissionRequested(winrt::event_token const& token) const noexcept;
        auto ProcessFailed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> const& handler) const;
        using ProcessFailed_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_ProcessFailed>;
        [[nodiscard]] auto ProcessFailed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ProcessFailedEventArgs> const& handler) const;
        auto ProcessFailed(winrt::event_token const& token) const noexcept;
        auto WebMessageReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const;
        using WebMessageReceived_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_WebMessageReceived>;
        [[nodiscard]] auto WebMessageReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const;
        auto WebMessageReceived(winrt::event_token const& token) const noexcept;
        auto NewWindowRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> const& handler) const;
        using NewWindowRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_NewWindowRequested>;
        [[nodiscard]] auto NewWindowRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NewWindowRequestedEventArgs> const& handler) const;
        auto NewWindowRequested(winrt::event_token const& token) const noexcept;
        auto DocumentTitleChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using DocumentTitleChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_DocumentTitleChanged>;
        [[nodiscard]] auto DocumentTitleChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto DocumentTitleChanged(winrt::event_token const& token) const noexcept;
        auto ContainsFullScreenElementChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ContainsFullScreenElementChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_ContainsFullScreenElementChanged>;
        [[nodiscard]] auto ContainsFullScreenElementChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto ContainsFullScreenElementChanged(winrt::event_token const& token) const noexcept;
        auto WebResourceRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> const& handler) const;
        using WebResourceRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_WebResourceRequested>;
        [[nodiscard]] auto WebResourceRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequestedEventArgs> const& handler) const;
        auto WebResourceRequested(winrt::event_token const& token) const noexcept;
        auto WindowCloseRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using WindowCloseRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>::remove_WindowCloseRequested>;
        [[nodiscard]] auto WindowCloseRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto WindowCloseRequested(winrt::event_token const& token) const noexcept;
        auto Navigate(param::hstring const& uri) const;
        auto NavigateToString(param::hstring const& htmlContent) const;
        auto AddScriptToExecuteOnDocumentCreatedAsync(param::hstring const& javaScript) const;
        auto RemoveScriptToExecuteOnDocumentCreated(param::hstring const& id) const;
        auto ExecuteScriptAsync(param::hstring const& javaScript) const;
        auto CapturePreviewAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2CapturePreviewImageFormat const& imageFormat, winrt::Windows::Storage::Streams::IRandomAccessStream const& imageStream) const;
        auto Reload() const;
        auto PostWebMessageAsJson(param::hstring const& webMessageAsJson) const;
        auto PostWebMessageAsString(param::hstring const& webMessageAsString) const;
        auto CallDevToolsProtocolMethodAsync(param::hstring const& methodName, param::hstring const& parametersAsJson) const;
        auto GoBack() const;
        auto GoForward() const;
        auto GetDevToolsProtocolEventReceiver(param::hstring const& eventName) const;
        auto Stop() const;
        auto AddHostObjectToScript(param::hstring const& name, winrt::Windows::Foundation::IInspectable const& rawObject) const;
        auto RemoveHostObjectFromScript(param::hstring const& name) const;
        auto OpenDevToolsWindow() const;
        auto AddWebResourceRequestedFilter(param::hstring const& uri, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const& ResourceContext) const;
        auto RemoveWebResourceRequestedFilter(param::hstring const& uri, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceContext const& ResourceContext) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs
    {
        [[nodiscard]] auto KeyEventKind() const;
        [[nodiscard]] auto VirtualKey() const;
        [[nodiscard]] auto KeyEventLParam() const;
        [[nodiscard]] auto PhysicalKeyStatus() const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2AcceleratorKeyPressedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2AcceleratorKeyPressedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto Challenge() const;
        [[nodiscard]] auto Response() const;
        [[nodiscard]] auto Cancel() const;
        auto Cancel(bool value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse
    {
        [[nodiscard]] auto UserName() const;
        auto UserName(param::hstring const& value) const;
        [[nodiscard]] auto Password() const;
        auto Password(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BasicAuthenticationResponse>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2BasicAuthenticationResponse<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2BrowserProcessExitedEventArgs
    {
        [[nodiscard]] auto BrowserProcessExitKind() const;
        [[nodiscard]] auto BrowserProcessId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2BrowserProcessExitedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2BrowserProcessExitedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate
    {
        [[nodiscard]] auto Subject() const;
        [[nodiscard]] auto Issuer() const;
        [[nodiscard]] auto ValidFrom() const;
        [[nodiscard]] auto ValidTo() const;
        [[nodiscard]] auto DerEncodedSerialNumber() const;
        [[nodiscard]] auto DisplayName() const;
        [[nodiscard]] auto PemEncodedIssuerCertificateChain() const;
        auto ToPemEncoding() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Certificate>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Certificate<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate
    {
        [[nodiscard]] auto Subject() const;
        [[nodiscard]] auto Issuer() const;
        [[nodiscard]] auto ValidFrom() const;
        [[nodiscard]] auto ValidTo() const;
        [[nodiscard]] auto DerEncodedSerialNumber() const;
        [[nodiscard]] auto DisplayName() const;
        [[nodiscard]] auto PemEncodedIssuerCertificateChain() const;
        [[nodiscard]] auto Kind() const;
        auto ToPemEncoding() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificate>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificate<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs
    {
        [[nodiscard]] auto Host() const;
        [[nodiscard]] auto Port() const;
        [[nodiscard]] auto IsProxy() const;
        [[nodiscard]] auto AllowedCertificateAuthorities() const;
        [[nodiscard]] auto MutuallyTrustedCertificates() const;
        [[nodiscard]] auto SelectedCertificate() const;
        auto SelectedCertificate(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificate const& value) const;
        [[nodiscard]] auto Cancel() const;
        auto Cancel(bool value) const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ClientCertificateRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ClientCertificateRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController
    {
        [[nodiscard]] auto RootVisualTarget() const;
        auto RootVisualTarget(winrt::Windows::Foundation::IInspectable const& value) const;
        auto CursorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using CursorChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>::remove_CursorChanged>;
        [[nodiscard]] auto CursorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2CompositionController, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto CursorChanged(winrt::event_token const& token) const noexcept;
        auto SendMouseInput(winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventKind const& eventKind, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MouseEventVirtualKeys const& virtualKeys, uint32_t mouseData, winrt::Windows::Foundation::Point const& point) const;
        auto SendPointerInput(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerEventKind const& eventKind, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PointerInfo const& pointerInfo) const;
        [[nodiscard]] auto Cursor() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController2
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionController2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionController2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionControllerStatics
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CompositionControllerStatics>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2CompositionControllerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContentLoadingEventArgs
    {
        [[nodiscard]] auto IsErrorPage() const;
        [[nodiscard]] auto NavigationId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContentLoadingEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContentLoadingEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Label() const;
        [[nodiscard]] auto CommandId() const;
        [[nodiscard]] auto ShortcutKeyDescription() const;
        [[nodiscard]] auto Icon() const;
        [[nodiscard]] auto Kind() const;
        [[nodiscard]] auto IsEnabled() const;
        auto IsEnabled(bool value) const;
        [[nodiscard]] auto IsChecked() const;
        auto IsChecked(bool value) const;
        [[nodiscard]] auto Children() const;
        auto CustomItemSelected(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using CustomItemSelected_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>::remove_CustomItemSelected>;
        [[nodiscard]] auto CustomItemSelected(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItem, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto CustomItemSelected(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuItem>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuItem<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs
    {
        [[nodiscard]] auto MenuItems() const;
        [[nodiscard]] auto ContextMenuTarget() const;
        [[nodiscard]] auto Location() const;
        [[nodiscard]] auto SelectedCommandId() const;
        auto SelectedCommandId(int32_t value) const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget
    {
        [[nodiscard]] auto Kind() const;
        [[nodiscard]] auto IsEditable() const;
        [[nodiscard]] auto IsRequestedForMainFrame() const;
        [[nodiscard]] auto PageUri() const;
        [[nodiscard]] auto FrameUri() const;
        [[nodiscard]] auto HasLinkUri() const;
        [[nodiscard]] auto LinkUri() const;
        [[nodiscard]] auto HasLinkText() const;
        [[nodiscard]] auto LinkText() const;
        [[nodiscard]] auto HasSourceUri() const;
        [[nodiscard]] auto SourceUri() const;
        [[nodiscard]] auto HasSelection() const;
        [[nodiscard]] auto SelectionText() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ContextMenuTarget>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ContextMenuTarget<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller
    {
        [[nodiscard]] auto IsVisible() const;
        auto IsVisible(bool value) const;
        [[nodiscard]] auto Bounds() const;
        auto Bounds(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto ZoomFactor() const;
        auto ZoomFactor(double value) const;
        [[nodiscard]] auto ParentWindow() const;
        auto ParentWindow(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& value) const;
        [[nodiscard]] auto CoreWebView2() const;
        auto ZoomFactorChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ZoomFactorChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>::remove_ZoomFactorChanged>;
        [[nodiscard]] auto ZoomFactorChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto ZoomFactorChanged(winrt::event_token const& token) const noexcept;
        auto MoveFocusRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> const& handler) const;
        using MoveFocusRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>::remove_MoveFocusRequested>;
        [[nodiscard]] auto MoveFocusRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusRequestedEventArgs> const& handler) const;
        auto MoveFocusRequested(winrt::event_token const& token) const noexcept;
        auto GotFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using GotFocus_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>::remove_GotFocus>;
        [[nodiscard]] auto GotFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto GotFocus(winrt::event_token const& token) const noexcept;
        auto LostFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using LostFocus_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>::remove_LostFocus>;
        [[nodiscard]] auto LostFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto LostFocus(winrt::event_token const& token) const noexcept;
        auto AcceleratorKeyPressed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> const& handler) const;
        using AcceleratorKeyPressed_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>::remove_AcceleratorKeyPressed>;
        [[nodiscard]] auto AcceleratorKeyPressed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Microsoft::Web::WebView2::Core::CoreWebView2AcceleratorKeyPressedEventArgs> const& handler) const;
        auto AcceleratorKeyPressed(winrt::event_token const& token) const noexcept;
        auto SetBoundsAndZoomFactor(winrt::Windows::Foundation::Rect const& Bounds, double ZoomFactor) const;
        auto MoveFocus(winrt::Microsoft::Web::WebView2::Core::CoreWebView2MoveFocusReason const& reason) const;
        auto NotifyParentWindowPositionChanged() const;
        auto Close() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller2
    {
        [[nodiscard]] auto DefaultBackgroundColor() const;
        auto DefaultBackgroundColor(winrt::Windows::UI::Color const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3
    {
        [[nodiscard]] auto RasterizationScale() const;
        auto RasterizationScale(double value) const;
        [[nodiscard]] auto ShouldDetectMonitorScaleChanges() const;
        auto ShouldDetectMonitorScaleChanges(bool value) const;
        [[nodiscard]] auto BoundsMode() const;
        auto BoundsMode(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BoundsMode const& value) const;
        auto RasterizationScaleChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using RasterizationScaleChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>::remove_RasterizationScaleChanged>;
        [[nodiscard]] auto RasterizationScaleChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Controller, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto RasterizationScaleChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller3>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller3<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller4
    {
        [[nodiscard]] auto AllowExternalDrop() const;
        auto AllowExternalDrop(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Controller4>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Controller4<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerFactory
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerFactory>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions
    {
        [[nodiscard]] auto ProfileName() const;
        auto ProfileName(param::hstring const& value) const;
        [[nodiscard]] auto IsInPrivateModeEnabled() const;
        auto IsInPrivateModeEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerOptions>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReference
    {
        [[nodiscard]] auto WindowHandle() const;
        [[nodiscard]] auto CoreWindow() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReference>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReference<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReferenceStatics
    {
        auto CreateFromWindowHandle(uint64_t windowHandle) const;
        auto CreateFromCoreWindow(winrt::Windows::UI::Core::CoreWindow const& coreWindow) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ControllerWindowReferenceStatics>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ControllerWindowReferenceStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Value() const;
        auto Value(param::hstring const& value) const;
        [[nodiscard]] auto Domain() const;
        [[nodiscard]] auto Path() const;
        [[nodiscard]] auto Expires() const;
        auto Expires(double value) const;
        [[nodiscard]] auto IsHttpOnly() const;
        auto IsHttpOnly(bool value) const;
        [[nodiscard]] auto SameSite() const;
        auto SameSite(winrt::Microsoft::Web::WebView2::Core::CoreWebView2CookieSameSiteKind const& value) const;
        [[nodiscard]] auto IsSecure() const;
        auto IsSecure(bool value) const;
        [[nodiscard]] auto IsSession() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Cookie>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Cookie<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager
    {
        auto CreateCookie(param::hstring const& name, param::hstring const& value, param::hstring const& Domain, param::hstring const& Path) const;
        auto CopyCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookieParam) const;
        auto AddOrUpdateCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookie) const;
        auto DeleteCookie(winrt::Microsoft::Web::WebView2::Core::CoreWebView2Cookie const& cookie) const;
        auto DeleteCookies(param::hstring const& name, param::hstring const& uri) const;
        auto DeleteCookiesWithDomainAndPath(param::hstring const& name, param::hstring const& Domain, param::hstring const& Path) const;
        auto DeleteAllCookies() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager_Manual
    {
        auto GetCookiesAsync(param::hstring const& uri) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2CookieManager_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2CookieManager_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DOMContentLoadedEventArgs
    {
        [[nodiscard]] auto NavigationId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DOMContentLoadedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DOMContentLoadedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs
    {
        [[nodiscard]] auto ParameterObjectAsJson() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs2
    {
        [[nodiscard]] auto SessionId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceivedEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceivedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceiver
    {
        auto DevToolsProtocolEventReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> const& handler) const;
        using DevToolsProtocolEventReceived_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>::remove_DevToolsProtocolEventReceived>;
        [[nodiscard]] auto DevToolsProtocolEventReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DevToolsProtocolEventReceivedEventArgs> const& handler) const;
        auto DevToolsProtocolEventReceived(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DevToolsProtocolEventReceiver>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DevToolsProtocolEventReceiver<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter
    {
        auto WrapNamedObject(param::hstring const& name, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& adapter) const;
        auto WrapObject(winrt::Windows::Foundation::IInspectable const& unwrapped, winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& adapter) const;
        auto UnwrapObject(winrt::Windows::Foundation::IInspectable const& wrapped) const;
        auto Clean() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DispatchAdapter<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto ContentDisposition() const;
        [[nodiscard]] auto MimeType() const;
        [[nodiscard]] auto TotalBytesToReceive() const;
        [[nodiscard]] auto BytesReceived() const;
        [[nodiscard]] auto EstimatedEndTime() const;
        [[nodiscard]] auto ResultFilePath() const;
        [[nodiscard]] auto State() const;
        [[nodiscard]] auto InterruptReason() const;
        [[nodiscard]] auto CanResume() const;
        auto BytesReceivedChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using BytesReceivedChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>::remove_BytesReceivedChanged>;
        [[nodiscard]] auto BytesReceivedChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto BytesReceivedChanged(winrt::event_token const& token) const noexcept;
        auto EstimatedEndTimeChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using EstimatedEndTimeChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>::remove_EstimatedEndTimeChanged>;
        [[nodiscard]] auto EstimatedEndTimeChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto EstimatedEndTimeChanged(winrt::event_token const& token) const noexcept;
        auto StateChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using StateChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>::remove_StateChanged>;
        [[nodiscard]] auto StateChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadOperation, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto StateChanged(winrt::event_token const& token) const noexcept;
        auto Cancel() const;
        auto Pause() const;
        auto Resume() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadOperation>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadOperation<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs
    {
        [[nodiscard]] auto DownloadOperation() const;
        [[nodiscard]] auto Cancel() const;
        auto Cancel(bool value) const;
        [[nodiscard]] auto ResultFilePath() const;
        auto ResultFilePath(param::hstring const& value) const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DownloadStartingEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2DownloadStartingEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment
    {
        [[nodiscard]] auto BrowserVersionString() const;
        auto NewBrowserVersionAvailable(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using NewBrowserVersionAvailable_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>::remove_NewBrowserVersionAvailable>;
        [[nodiscard]] auto NewBrowserVersionAvailable(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto NewBrowserVersionAvailable(winrt::event_token const& token) const noexcept;
        auto CreateCoreWebView2ControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow) const;
        auto CreateWebResourceResponse(winrt::Windows::Storage::Streams::IRandomAccessStream const& Content, int32_t StatusCode, param::hstring const& ReasonPhrase, param::hstring const& Headers) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment10
    {
        auto CreateCoreWebView2ControllerOptions() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment10>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment10<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment2
    {
        auto CreateWebResourceRequest(param::hstring const& uri, param::hstring const& Method, winrt::Windows::Storage::Streams::IRandomAccessStream const& postData, param::hstring const& Headers) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment3
    {
        auto CreateCoreWebView2CompositionControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow) const;
        auto CreateCoreWebView2PointerInfo() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment3>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment3<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment4
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment4>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment4<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment5
    {
        auto BrowserProcessExited(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> const& handler) const;
        using BrowserProcessExited_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>::remove_BrowserProcessExited>;
        [[nodiscard]] auto BrowserProcessExited(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowserProcessExitedEventArgs> const& handler) const;
        auto BrowserProcessExited(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment5>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment5<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment6
    {
        auto CreatePrintSettings() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment6>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment6<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment7
    {
        [[nodiscard]] auto UserDataFolder() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment7>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment7<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8
    {
        auto ProcessInfosChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ProcessInfosChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>::remove_ProcessInfosChanged>;
        [[nodiscard]] auto ProcessInfosChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Environment, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto ProcessInfosChanged(winrt::event_token const& token) const noexcept;
        auto GetProcessInfos() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment8>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment8<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment9
    {
        auto CreateContextMenuItem(param::hstring const& Label, winrt::Windows::Storage::Streams::IRandomAccessStream const& iconStream, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuItemKind const& Kind) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment9>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment9<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions
    {
        [[nodiscard]] auto AdditionalBrowserArguments() const;
        auto AdditionalBrowserArguments(param::hstring const& value) const;
        [[nodiscard]] auto Language() const;
        auto Language(param::hstring const& value) const;
        [[nodiscard]] auto TargetCompatibleBrowserVersion() const;
        auto TargetCompatibleBrowserVersion(param::hstring const& value) const;
        [[nodiscard]] auto AllowSingleSignOnUsingOSPrimaryAccount() const;
        auto AllowSingleSignOnUsingOSPrimaryAccount(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions2
    {
        [[nodiscard]] auto ExclusiveUserDataFolderAccess() const;
        auto ExclusiveUserDataFolderAccess(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions_Manual
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentOptions_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentOptions_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics
    {
        auto CreateAsync() const;
        auto CreateWithOptionsAsync(param::hstring const& browserExecutableFolder, param::hstring const& userDataFolder, winrt::Microsoft::Web::WebView2::Core::CoreWebView2EnvironmentOptions const& options) const;
        auto GetAvailableBrowserVersionString() const;
        auto GetAvailableBrowserVersionString(param::hstring const& browserExecutableFolder) const;
        auto CompareBrowserVersionString(param::hstring const& browserVersionString1, param::hstring const& browserVersionString2) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2EnvironmentStatics>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2EnvironmentStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment_Manual
    {
        auto CreateCoreWebView2ControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const& options) const;
        auto CreateCoreWebView2CompositionControllerAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerWindowReference const& ParentWindow, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ControllerOptions const& options) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Environment_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Environment_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame
    {
        [[nodiscard]] auto Name() const;
        auto NameChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using NameChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>::remove_NameChanged>;
        [[nodiscard]] auto NameChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto NameChanged(winrt::event_token const& token) const noexcept;
        auto Destroyed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Destroyed_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>::remove_Destroyed>;
        [[nodiscard]] auto Destroyed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto Destroyed(winrt::event_token const& token) const noexcept;
        auto RemoveHostObjectFromScript(param::hstring const& name) const;
        auto IsDestroyed() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2
    {
        auto NavigationStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        using NavigationStarting_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>::remove_NavigationStarting>;
        [[nodiscard]] auto NavigationStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationStartingEventArgs> const& handler) const;
        auto NavigationStarting(winrt::event_token const& token) const noexcept;
        auto ContentLoading(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const;
        using ContentLoading_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>::remove_ContentLoading>;
        [[nodiscard]] auto ContentLoading(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContentLoadingEventArgs> const& handler) const;
        auto ContentLoading(winrt::event_token const& token) const noexcept;
        auto NavigationCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        using NavigationCompleted_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>::remove_NavigationCompleted>;
        [[nodiscard]] auto NavigationCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2NavigationCompletedEventArgs> const& handler) const;
        auto NavigationCompleted(winrt::event_token const& token) const noexcept;
        auto DOMContentLoaded(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const;
        using DOMContentLoaded_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>::remove_DOMContentLoaded>;
        [[nodiscard]] auto DOMContentLoaded(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const;
        auto DOMContentLoaded(winrt::event_token const& token) const noexcept;
        auto WebMessageReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const;
        using WebMessageReceived_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>::remove_WebMessageReceived>;
        [[nodiscard]] auto WebMessageReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebMessageReceivedEventArgs> const& handler) const;
        auto WebMessageReceived(winrt::event_token const& token) const noexcept;
        auto ExecuteScriptAsync(param::hstring const& javaScript) const;
        auto PostWebMessageAsJson(param::hstring const& webMessageAsJson) const;
        auto PostWebMessageAsString(param::hstring const& webMessageAsString) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame3
    {
        auto PermissionRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const;
        using PermissionRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>::remove_PermissionRequested>;
        [[nodiscard]] auto PermissionRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2Frame, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionRequestedEventArgs> const& handler) const;
        auto PermissionRequested(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Frame3>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Frame3<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameCreatedEventArgs
    {
        [[nodiscard]] auto Frame() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameCreatedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameCreatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameInfo
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Source() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2FrameInfo>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2FrameInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpHeadersCollectionIterator
    {
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpHeadersCollectionIterator>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpHeadersCollectionIterator<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders
    {
        auto GetHeader(param::hstring const& name) const;
        auto GetHeaders(param::hstring const& name) const;
        auto Contains(param::hstring const& name) const;
        auto SetHeader(param::hstring const& name, param::hstring const& value) const;
        auto RemoveHeader(param::hstring const& name) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpRequestHeaders>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpRequestHeaders<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders
    {
        auto AppendHeader(param::hstring const& name, param::hstring const& value) const;
        auto Contains(param::hstring const& name) const;
        auto GetHeader(param::hstring const& name) const;
        auto GetHeaders(param::hstring const& name) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2HttpResponseHeaders>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2HttpResponseHeaders<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2MoveFocusRequestedEventArgs
    {
        [[nodiscard]] auto Reason() const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2MoveFocusRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2MoveFocusRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs
    {
        [[nodiscard]] auto IsSuccess() const;
        [[nodiscard]] auto WebErrorStatus() const;
        [[nodiscard]] auto NavigationId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs2
    {
        [[nodiscard]] auto HttpStatusCode() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationCompletedEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationCompletedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto IsUserInitiated() const;
        [[nodiscard]] auto IsRedirected() const;
        [[nodiscard]] auto RequestHeaders() const;
        [[nodiscard]] auto Cancel() const;
        auto Cancel(bool value) const;
        [[nodiscard]] auto NavigationId() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs2
    {
        [[nodiscard]] auto AdditionalAllowedFrameAncestors() const;
        auto AdditionalAllowedFrameAncestors(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NavigationStartingEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NavigationStartingEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto NewWindow() const;
        auto NewWindow(winrt::Microsoft::Web::WebView2::Core::CoreWebView2 const& value) const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        [[nodiscard]] auto IsUserInitiated() const;
        [[nodiscard]] auto WindowFeatures() const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs2
    {
        [[nodiscard]] auto Name() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2NewWindowRequestedEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2NewWindowRequestedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto PermissionKind() const;
        [[nodiscard]] auto IsUserInitiated() const;
        [[nodiscard]] auto State() const;
        auto State(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PermissionState const& value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs2
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PermissionRequestedEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2PermissionRequestedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo
    {
        [[nodiscard]] auto PointerKind() const;
        auto PointerKind(uint32_t value) const;
        [[nodiscard]] auto PointerId() const;
        auto PointerId(uint32_t value) const;
        [[nodiscard]] auto FrameId() const;
        auto FrameId(uint32_t value) const;
        [[nodiscard]] auto PointerFlags() const;
        auto PointerFlags(uint32_t value) const;
        [[nodiscard]] auto PointerDeviceRect() const;
        auto PointerDeviceRect(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto DisplayRect() const;
        auto DisplayRect(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto PixelLocation() const;
        auto PixelLocation(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto HimetricLocation() const;
        auto HimetricLocation(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto PixelLocationRaw() const;
        auto PixelLocationRaw(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto HimetricLocationRaw() const;
        auto HimetricLocationRaw(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto Time() const;
        auto Time(uint32_t value) const;
        [[nodiscard]] auto HistoryCount() const;
        auto HistoryCount(uint32_t value) const;
        [[nodiscard]] auto InputData() const;
        auto InputData(int32_t value) const;
        [[nodiscard]] auto KeyStates() const;
        auto KeyStates(uint32_t value) const;
        [[nodiscard]] auto PerformanceCount() const;
        auto PerformanceCount(uint64_t value) const;
        [[nodiscard]] auto ButtonChangeKind() const;
        auto ButtonChangeKind(int32_t value) const;
        [[nodiscard]] auto PenFlags() const;
        auto PenFlags(uint32_t value) const;
        [[nodiscard]] auto PenMask() const;
        auto PenMask(uint32_t value) const;
        [[nodiscard]] auto PenPressure() const;
        auto PenPressure(uint32_t value) const;
        [[nodiscard]] auto PenRotation() const;
        auto PenRotation(uint32_t value) const;
        [[nodiscard]] auto PenTiltX() const;
        auto PenTiltX(int32_t value) const;
        [[nodiscard]] auto PenTiltY() const;
        auto PenTiltY(int32_t value) const;
        [[nodiscard]] auto TouchFlags() const;
        auto TouchFlags(uint32_t value) const;
        [[nodiscard]] auto TouchMask() const;
        auto TouchMask(uint32_t value) const;
        [[nodiscard]] auto TouchContact() const;
        auto TouchContact(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto TouchContactRaw() const;
        auto TouchContactRaw(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto TouchOrientation() const;
        auto TouchOrientation(uint32_t value) const;
        [[nodiscard]] auto TouchPressure() const;
        auto TouchPressure(uint32_t value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PointerInfo>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2PointerInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings
    {
        [[nodiscard]] auto Orientation() const;
        auto Orientation(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintOrientation const& value) const;
        [[nodiscard]] auto ScaleFactor() const;
        auto ScaleFactor(double value) const;
        [[nodiscard]] auto PageWidth() const;
        auto PageWidth(double value) const;
        [[nodiscard]] auto PageHeight() const;
        auto PageHeight(double value) const;
        [[nodiscard]] auto MarginTop() const;
        auto MarginTop(double value) const;
        [[nodiscard]] auto MarginBottom() const;
        auto MarginBottom(double value) const;
        [[nodiscard]] auto MarginLeft() const;
        auto MarginLeft(double value) const;
        [[nodiscard]] auto MarginRight() const;
        auto MarginRight(double value) const;
        [[nodiscard]] auto ShouldPrintBackgrounds() const;
        auto ShouldPrintBackgrounds(bool value) const;
        [[nodiscard]] auto ShouldPrintSelectionOnly() const;
        auto ShouldPrintSelectionOnly(bool value) const;
        [[nodiscard]] auto ShouldPrintHeaderAndFooter() const;
        auto ShouldPrintHeaderAndFooter(bool value) const;
        [[nodiscard]] auto HeaderTitle() const;
        auto HeaderTitle(param::hstring const& value) const;
        [[nodiscard]] auto FooterUri() const;
        auto FooterUri(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2PrintSettings>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2PrintSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs
    {
        [[nodiscard]] auto ProcessFailedKind() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2
    {
        [[nodiscard]] auto Reason() const;
        [[nodiscard]] auto ExitCode() const;
        [[nodiscard]] auto ProcessDescription() const;
        [[nodiscard]] auto FrameInfosForFailedProcess() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessFailedEventArgs2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessFailedEventArgs2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessInfo
    {
        [[nodiscard]] auto ProcessId() const;
        [[nodiscard]] auto Kind() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ProcessInfo>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ProcessInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile
    {
        [[nodiscard]] auto ProfileName() const;
        [[nodiscard]] auto IsInPrivateModeEnabled() const;
        [[nodiscard]] auto ProfilePath() const;
        [[nodiscard]] auto DefaultDownloadFolderPath() const;
        auto DefaultDownloadFolderPath(param::hstring const& value) const;
        [[nodiscard]] auto PreferredColorScheme() const;
        auto PreferredColorScheme(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PreferredColorScheme const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile2
    {
        auto ClearBrowsingDataAsync(winrt::Microsoft::Web::WebView2::Core::CoreWebView2BrowsingDataKinds const& dataKinds) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Profile2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Profile2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto Kind() const;
        [[nodiscard]] auto Message() const;
        [[nodiscard]] auto DefaultText() const;
        [[nodiscard]] auto ResultText() const;
        auto ResultText(param::hstring const& value) const;
        auto Accept() const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ScriptDialogOpeningEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ScriptDialogOpeningEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs
    {
        [[nodiscard]] auto ErrorStatus() const;
        [[nodiscard]] auto RequestUri() const;
        [[nodiscard]] auto ServerCertificate() const;
        [[nodiscard]] auto Action() const;
        auto Action(winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorAction const& value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2ServerCertificateErrorDetectedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2ServerCertificateErrorDetectedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings
    {
        [[nodiscard]] auto IsScriptEnabled() const;
        auto IsScriptEnabled(bool value) const;
        [[nodiscard]] auto IsWebMessageEnabled() const;
        auto IsWebMessageEnabled(bool value) const;
        [[nodiscard]] auto AreDefaultScriptDialogsEnabled() const;
        auto AreDefaultScriptDialogsEnabled(bool value) const;
        [[nodiscard]] auto IsStatusBarEnabled() const;
        auto IsStatusBarEnabled(bool value) const;
        [[nodiscard]] auto AreDevToolsEnabled() const;
        auto AreDevToolsEnabled(bool value) const;
        [[nodiscard]] auto AreDefaultContextMenusEnabled() const;
        auto AreDefaultContextMenusEnabled(bool value) const;
        [[nodiscard]] auto AreHostObjectsAllowed() const;
        auto AreHostObjectsAllowed(bool value) const;
        [[nodiscard]] auto IsZoomControlEnabled() const;
        auto IsZoomControlEnabled(bool value) const;
        [[nodiscard]] auto IsBuiltInErrorPageEnabled() const;
        auto IsBuiltInErrorPageEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings2
    {
        [[nodiscard]] auto UserAgent() const;
        auto UserAgent(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings3
    {
        [[nodiscard]] auto AreBrowserAcceleratorKeysEnabled() const;
        auto AreBrowserAcceleratorKeysEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings3>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings3<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4
    {
        [[nodiscard]] auto IsPasswordAutosaveEnabled() const;
        auto IsPasswordAutosaveEnabled(bool value) const;
        [[nodiscard]] auto IsGeneralAutofillEnabled() const;
        auto IsGeneralAutofillEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings4>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings4<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings5
    {
        [[nodiscard]] auto IsPinchZoomEnabled() const;
        auto IsPinchZoomEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings5>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings5<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings6
    {
        [[nodiscard]] auto IsSwipeNavigationEnabled() const;
        auto IsSwipeNavigationEnabled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings6>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings6<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings7
    {
        [[nodiscard]] auto HiddenPdfToolbarItems() const;
        auto HiddenPdfToolbarItems(winrt::Microsoft::Web::WebView2::Core::CoreWebView2PdfToolbarItems const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings7>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings7<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings_Manual
    {
        [[nodiscard]] auto HostObjectDispatchAdapter() const;
        auto HostObjectDispatchAdapter(winrt::Microsoft::Web::WebView2::Core::ICoreWebView2DispatchAdapter const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2Settings_Manual>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2Settings_Manual<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2SourceChangedEventArgs
    {
        [[nodiscard]] auto IsNewDocument() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2SourceChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2SourceChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebMessageReceivedEventArgs
    {
        [[nodiscard]] auto Source() const;
        [[nodiscard]] auto WebMessageAsJson() const;
        auto TryGetWebMessageAsString() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebMessageReceivedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebMessageReceivedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest
    {
        [[nodiscard]] auto Uri() const;
        auto Uri(param::hstring const& value) const;
        [[nodiscard]] auto Method() const;
        auto Method(param::hstring const& value) const;
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Windows::Storage::Streams::IRandomAccessStream const& value) const;
        [[nodiscard]] auto Headers() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequest>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequest<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs
    {
        [[nodiscard]] auto Request() const;
        [[nodiscard]] auto Response() const;
        auto Response(winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponse const& value) const;
        [[nodiscard]] auto ResourceContext() const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse
    {
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Windows::Storage::Streams::IRandomAccessStream const& value) const;
        [[nodiscard]] auto Headers() const;
        [[nodiscard]] auto StatusCode() const;
        auto StatusCode(int32_t value) const;
        [[nodiscard]] auto ReasonPhrase() const;
        auto ReasonPhrase(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponse>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponse<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseReceivedEventArgs
    {
        [[nodiscard]] auto Request() const;
        [[nodiscard]] auto Response() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseReceivedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseReceivedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView
    {
        [[nodiscard]] auto Headers() const;
        [[nodiscard]] auto StatusCode() const;
        [[nodiscard]] auto ReasonPhrase() const;
        auto GetContentAsync() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WebResourceResponseView>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WebResourceResponseView<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures
    {
        [[nodiscard]] auto HasPosition() const;
        [[nodiscard]] auto HasSize() const;
        [[nodiscard]] auto Left() const;
        [[nodiscard]] auto Top() const;
        [[nodiscard]] auto Height() const;
        [[nodiscard]] auto Width() const;
        [[nodiscard]] auto ShouldDisplayMenuBar() const;
        [[nodiscard]] auto ShouldDisplayStatus() const;
        [[nodiscard]] auto ShouldDisplayToolbar() const;
        [[nodiscard]] auto ShouldDisplayScrollBars() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2WindowFeatures>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2WindowFeatures<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_10
    {
        auto BasicAuthenticationRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> const& handler) const;
        using BasicAuthenticationRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>::remove_BasicAuthenticationRequested>;
        [[nodiscard]] auto BasicAuthenticationRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2BasicAuthenticationRequestedEventArgs> const& handler) const;
        auto BasicAuthenticationRequested(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_10>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_10<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11
    {
        auto ContextMenuRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> const& handler) const;
        using ContextMenuRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>::remove_ContextMenuRequested>;
        [[nodiscard]] auto ContextMenuRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ContextMenuRequestedEventArgs> const& handler) const;
        auto ContextMenuRequested(winrt::event_token const& token) const noexcept;
        auto CallDevToolsProtocolMethodForSessionAsync(param::hstring const& sessionId, param::hstring const& methodName, param::hstring const& parametersAsJson) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_11>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_11<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12
    {
        [[nodiscard]] auto StatusBarText() const;
        auto StatusBarTextChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using StatusBarTextChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>::remove_StatusBarTextChanged>;
        [[nodiscard]] auto StatusBarTextChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto StatusBarTextChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_12>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_12<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_13
    {
        [[nodiscard]] auto Profile() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_13>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_13<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14
    {
        auto ServerCertificateErrorDetected(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> const& handler) const;
        using ServerCertificateErrorDetected_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>::remove_ServerCertificateErrorDetected>;
        [[nodiscard]] auto ServerCertificateErrorDetected(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ServerCertificateErrorDetectedEventArgs> const& handler) const;
        auto ServerCertificateErrorDetected(winrt::event_token const& token) const noexcept;
        auto ClearServerCertificateErrorActionsAsync() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_14>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_14<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2
    {
        [[nodiscard]] auto CookieManager() const;
        [[nodiscard]] auto Environment() const;
        auto WebResourceResponseReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> const& handler) const;
        using WebResourceResponseReceived_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>::remove_WebResourceResponseReceived>;
        [[nodiscard]] auto WebResourceResponseReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceResponseReceivedEventArgs> const& handler) const;
        auto WebResourceResponseReceived(winrt::event_token const& token) const noexcept;
        auto DOMContentLoaded(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const;
        using DOMContentLoaded_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>::remove_DOMContentLoaded>;
        [[nodiscard]] auto DOMContentLoaded(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DOMContentLoadedEventArgs> const& handler) const;
        auto DOMContentLoaded(winrt::event_token const& token) const noexcept;
        auto NavigateWithWebResourceRequest(winrt::Microsoft::Web::WebView2::Core::CoreWebView2WebResourceRequest const& Request) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_2>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3
    {
        [[nodiscard]] auto IsSuspended() const;
        auto TrySuspendAsync() const;
        auto Resume() const;
        auto SetVirtualHostNameToFolderMapping(param::hstring const& hostName, param::hstring const& folderPath, winrt::Microsoft::Web::WebView2::Core::CoreWebView2HostResourceAccessKind const& accessKind) const;
        auto ClearVirtualHostNameToFolderMapping(param::hstring const& hostName) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_3>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_3<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4
    {
        auto FrameCreated(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> const& handler) const;
        using FrameCreated_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>::remove_FrameCreated>;
        [[nodiscard]] auto FrameCreated(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2FrameCreatedEventArgs> const& handler) const;
        auto FrameCreated(winrt::event_token const& token) const noexcept;
        auto DownloadStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> const& handler) const;
        using DownloadStarting_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>::remove_DownloadStarting>;
        [[nodiscard]] auto DownloadStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2DownloadStartingEventArgs> const& handler) const;
        auto DownloadStarting(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_4>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_4<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_5
    {
        auto ClientCertificateRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> const& handler) const;
        using ClientCertificateRequested_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>::remove_ClientCertificateRequested>;
        [[nodiscard]] auto ClientCertificateRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Microsoft::Web::WebView2::Core::CoreWebView2ClientCertificateRequestedEventArgs> const& handler) const;
        auto ClientCertificateRequested(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_5>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_5<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_6
    {
        auto OpenTaskManagerWindow() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_6>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_6<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_7
    {
        auto PrintToPdfAsync(param::hstring const& ResultFilePath, winrt::Microsoft::Web::WebView2::Core::CoreWebView2PrintSettings const& printSettings) const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_7>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_7<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8
    {
        [[nodiscard]] auto IsMuted() const;
        auto IsMuted(bool value) const;
        [[nodiscard]] auto IsDocumentPlayingAudio() const;
        auto IsMutedChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using IsMutedChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>::remove_IsMutedChanged>;
        [[nodiscard]] auto IsMutedChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto IsMutedChanged(winrt::event_token const& token) const noexcept;
        auto IsDocumentPlayingAudioChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using IsDocumentPlayingAudioChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>::remove_IsDocumentPlayingAudioChanged>;
        [[nodiscard]] auto IsDocumentPlayingAudioChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto IsDocumentPlayingAudioChanged(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_8>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_8<D>;
    };
    template <typename D>
    struct consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9
    {
        [[nodiscard]] auto IsDefaultDownloadDialogOpen() const;
        [[nodiscard]] auto DefaultDownloadDialogCornerAlignment() const;
        auto DefaultDownloadDialogCornerAlignment(winrt::Microsoft::Web::WebView2::Core::CoreWebView2DefaultDownloadDialogCornerAlignment const& value) const;
        [[nodiscard]] auto DefaultDownloadDialogMargin() const;
        auto DefaultDownloadDialogMargin(winrt::Windows::Foundation::Point const& value) const;
        auto IsDefaultDownloadDialogOpenChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using IsDefaultDownloadDialogOpenChanged_revoker = impl::event_revoker<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9, &impl::abi_t<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>::remove_IsDefaultDownloadDialogOpenChanged>;
        [[nodiscard]] auto IsDefaultDownloadDialogOpenChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::Web::WebView2::Core::CoreWebView2, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto IsDefaultDownloadDialogOpenChanged(winrt::event_token const& token) const noexcept;
        auto OpenDefaultDownloadDialog() const;
        auto CloseDefaultDownloadDialog() const;
    };
    template <> struct consume<winrt::Microsoft::Web::WebView2::Core::ICoreWebView2_9>
    {
        template <typename D> using type = consume_Microsoft_Web_WebView2_Core_ICoreWebView2_9<D>;
    };
    struct struct_Microsoft_Web_WebView2_Core_CoreWebView2PhysicalKeyStatus
    {
        uint32_t RepeatCount;
        uint32_t ScanCode;
        int32_t IsExtendedKey;
        int32_t IsMenuKeyDown;
        int32_t WasKeyDown;
        int32_t IsKeyReleased;
    };
    template <> struct abi<Microsoft::Web::WebView2::Core::CoreWebView2PhysicalKeyStatus>
    {
        using type = struct_Microsoft_Web_WebView2_Core_CoreWebView2PhysicalKeyStatus;
    };
}
#endif
