// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_plexy_H
#define WINRT_plexy_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.2.h"
#include "winrt/impl/Windows.UI.Xaml.Markup.2.h"
#include "winrt/impl/plexy.2.h"
namespace winrt::impl
{
    template <typename D>
    struct produce<D, winrt::plexy::IMainPage> : produce_base<D, winrt::plexy::IMainPage>
    {
    };
}
WINRT_EXPORT namespace winrt::plexy
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::plexy::IMainPage> : winrt::impl::hash_base {};
    template<> struct hash<winrt::plexy::MainPage> : winrt::impl::hash_base {};
    template<> struct hash<winrt::plexy::XamlMetaDataProvider> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
