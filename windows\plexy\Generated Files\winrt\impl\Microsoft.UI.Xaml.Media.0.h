// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Media_0_H
#define WINRT_Microsoft_UI_Xaml_Media_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IReference;
    struct Point;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct Color;
}
WINRT_EXPORT namespace winrt::Windows::UI::Composition
{
    enum class CompositionColorSpace : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml
{
    enum class ApplicationTheme : int32_t;
    struct DependencyProperty;
    struct UIElement;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media
{
    enum class BrushMappingMode : int32_t;
    enum class GradientSpreadMethod : int32_t;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media
{
    enum class AcrylicBackgroundSource : int32_t
    {
        HostBackdrop = 0,
        Backdrop = 1,
    };
    enum class RevealBrushState : int32_t
    {
        Normal = 0,
        PointerOver = 1,
        Pressed = 2,
    };
    struct IAcrylicBrush;
    struct IAcrylicBrush2;
    struct IAcrylicBrushFactory;
    struct IAcrylicBrushStatics;
    struct IAcrylicBrushStatics2;
    struct IRadialGradientBrush;
    struct IRadialGradientBrushFactory;
    struct IRadialGradientBrushStatics;
    struct IRevealBackgroundBrush;
    struct IRevealBackgroundBrushFactory;
    struct IRevealBorderBrush;
    struct IRevealBorderBrushFactory;
    struct IRevealBrush;
    struct IRevealBrushProtectedFactory;
    struct IRevealBrushStatics;
    struct AcrylicBrush;
    struct RadialGradientBrush;
    struct RevealBackgroundBrush;
    struct RevealBorderBrush;
    struct RevealBrush;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBrush>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::AcrylicBrush>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::RevealBrush>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Media::RevealBrushState>{ using type = enum_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::AcrylicBrush> = L"Microsoft.UI.Xaml.Media.AcrylicBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush> = L"Microsoft.UI.Xaml.Media.RadialGradientBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush> = L"Microsoft.UI.Xaml.Media.RevealBackgroundBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush> = L"Microsoft.UI.Xaml.Media.RevealBorderBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::RevealBrush> = L"Microsoft.UI.Xaml.Media.RevealBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource> = L"Microsoft.UI.Xaml.Media.AcrylicBackgroundSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::RevealBrushState> = L"Microsoft.UI.Xaml.Media.RevealBrushState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush> = L"Microsoft.UI.Xaml.Media.IAcrylicBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2> = L"Microsoft.UI.Xaml.Media.IAcrylicBrush2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory> = L"Microsoft.UI.Xaml.Media.IAcrylicBrushFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics> = L"Microsoft.UI.Xaml.Media.IAcrylicBrushStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2> = L"Microsoft.UI.Xaml.Media.IAcrylicBrushStatics2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush> = L"Microsoft.UI.Xaml.Media.IRadialGradientBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory> = L"Microsoft.UI.Xaml.Media.IRadialGradientBrushFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics> = L"Microsoft.UI.Xaml.Media.IRadialGradientBrushStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush> = L"Microsoft.UI.Xaml.Media.IRevealBackgroundBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory> = L"Microsoft.UI.Xaml.Media.IRevealBackgroundBrushFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush> = L"Microsoft.UI.Xaml.Media.IRevealBorderBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory> = L"Microsoft.UI.Xaml.Media.IRevealBorderBrushFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrush> = L"Microsoft.UI.Xaml.Media.IRevealBrush";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory> = L"Microsoft.UI.Xaml.Media.IRevealBrushProtectedFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics> = L"Microsoft.UI.Xaml.Media.IRevealBrushStatics";
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush>{ 0x26861C81,0x72F4,0x5065,{ 0xB1,0x44,0xE9,0xEC,0x7F,0xCE,0x86,0xA9 } }; // 26861C81-72F4-5065-B144-E9EC7FCE86A9
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2>{ 0x23FAD570,0x43ED,0x5A73,{ 0x9D,0xE7,0xA3,0x03,0x55,0x3D,0x54,0x14 } }; // 23FAD570-43ED-5A73-9DE7-A303553D5414
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory>{ 0x80173353,0x611D,0x5A02,{ 0x88,0x64,0x1A,0xAA,0x27,0x9D,0xFF,0x1C } }; // 80173353-611D-5A02-8864-1AAA279DFF1C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics>{ 0x3162419C,0xCABC,0x5B4E,{ 0x9C,0x64,0x78,0x63,0x54,0x14,0x72,0x57 } }; // 3162419C-CABC-5B4E-9C64-************
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2>{ 0xF03EE756,0x8A99,0x5AED,{ 0xBD,0x17,0x4C,0x73,0x14,0xAC,0xC9,0xFF } }; // F03EE756-8A99-5AED-BD17-4C7314ACC9FF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush>{ 0x11C9DA5D,0xB928,0x53AB,{ 0xBA,0x6E,0x8F,0x4A,0xCC,0x46,0x9B,0x0F } }; // 11C9DA5D-B928-53AB-BA6E-8F4ACC469B0F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory>{ 0xD90BA26E,0x9E67,0x54BD,{ 0xA2,0xD9,0x61,0xC8,0xF9,0xF1,0xD4,0x33 } }; // D90BA26E-9E67-54BD-A2D9-61C8F9F1D433
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics>{ 0x4F0F8DDD,0xCA87,0x582F,{ 0x99,0x07,0x2C,0xC4,0xD5,0x7E,0x63,0x36 } }; // 4F0F8DDD-CA87-582F-9907-2CC4D57E6336
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush>{ 0x3741D912,0xFC83,0x5C92,{ 0xA2,0x2F,0xEF,0xC2,0x95,0x88,0xB3,0x73 } }; // 3741D912-FC83-5C92-A22F-EFC29588B373
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory>{ 0x7E6F2B0A,0xE70D,0x529F,{ 0x80,0x97,0x43,0xE0,0x23,0xF2,0x94,0x3B } }; // 7E6F2B0A-E70D-529F-8097-43E023F2943B
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush>{ 0xF85DBFCB,0x2EC6,0x5C73,{ 0xA8,0xBE,0x38,0x64,0xC9,0x80,0xB9,0x17 } }; // F85DBFCB-2EC6-5C73-A8BE-3864C980B917
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory>{ 0xC677BDC2,0xF045,0x532C,{ 0xBB,0x13,0x3C,0xEE,0xE1,0x91,0x43,0xB2 } }; // C677BDC2-F045-532C-BB13-3CEEE19143B2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrush>{ 0x9D1DDB86,0xADFA,0x5BAE,{ 0xBF,0x9A,0x3E,0xDD,0x03,0x1E,0x61,0xC2 } }; // 9D1DDB86-ADFA-5BAE-BF9A-3EDD031E61C2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory>{ 0x46A94F5B,0x8ABD,0x5571,{ 0xB0,0x49,0x7F,0x76,0xE3,0xF9,0x67,0xC9 } }; // 46A94F5B-8ABD-5571-B049-7F76E3F967C9
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics>{ 0x0AFEE634,0x34F3,0x50F5,{ 0xA5,0x09,0x21,0xCE,0xF2,0x66,0x76,0x7E } }; // 0AFEE634-34F3-50F5-A509-21CEF266767E
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Media::AcrylicBrush>{ using type = winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Media::RadialGradientBrush>{ using type = winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Media::RevealBackgroundBrush>{ using type = winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Media::RevealBorderBrush>{ using type = winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Media::RevealBrush>{ using type = winrt::Microsoft::UI::Xaml::Media::IRevealBrush; };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_BackgroundSource(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_BackgroundSource(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_TintColor(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_TintColor(struct struct_Windows_UI_Color) noexcept = 0;
            virtual int32_t __stdcall get_TintOpacity(double*) noexcept = 0;
            virtual int32_t __stdcall put_TintOpacity(double) noexcept = 0;
            virtual int32_t __stdcall get_TintTransitionDuration(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_TintTransitionDuration(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_AlwaysUseFallback(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AlwaysUseFallback(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_TintLuminosityOpacity(void**) noexcept = 0;
            virtual int32_t __stdcall put_TintLuminosityOpacity(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_BackgroundSourceProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TintColorProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TintOpacityProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TintTransitionDurationProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AlwaysUseFallbackProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_TintLuminosityOpacityProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Center(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_Center(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_RadiusX(double*) noexcept = 0;
            virtual int32_t __stdcall put_RadiusX(double) noexcept = 0;
            virtual int32_t __stdcall get_RadiusY(double*) noexcept = 0;
            virtual int32_t __stdcall put_RadiusY(double) noexcept = 0;
            virtual int32_t __stdcall get_GradientOrigin(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_GradientOrigin(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_MappingMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_MappingMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_InterpolationSpace(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_InterpolationSpace(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_SpreadMethod(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SpreadMethod(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_GradientStops(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_CenterProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RadiusXProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RadiusYProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_GradientOriginProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_InterpolationSpaceProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MappingModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_SpreadMethodProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBrush>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Color(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_Color(struct struct_Windows_UI_Color) noexcept = 0;
            virtual int32_t __stdcall get_TargetTheme(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TargetTheme(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_AlwaysUseFallback(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AlwaysUseFallback(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ColorProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TargetThemeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AlwaysUseFallbackProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_StateProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetState(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall GetState(void*, int32_t*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IAcrylicBrush
    {
        [[nodiscard]] auto BackgroundSource() const;
        auto BackgroundSource(winrt::Microsoft::UI::Xaml::Media::AcrylicBackgroundSource const& value) const;
        [[nodiscard]] auto TintColor() const;
        auto TintColor(winrt::Windows::UI::Color const& value) const;
        [[nodiscard]] auto TintOpacity() const;
        auto TintOpacity(double value) const;
        [[nodiscard]] auto TintTransitionDuration() const;
        auto TintTransitionDuration(winrt::Windows::Foundation::TimeSpan const& value) const;
        [[nodiscard]] auto AlwaysUseFallback() const;
        auto AlwaysUseFallback(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IAcrylicBrush<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IAcrylicBrush2
    {
        [[nodiscard]] auto TintLuminosityOpacity() const;
        auto TintLuminosityOpacity(winrt::Windows::Foundation::IReference<double> const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IAcrylicBrush2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IAcrylicBrushFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IAcrylicBrushFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics
    {
        [[nodiscard]] auto BackgroundSourceProperty() const;
        [[nodiscard]] auto TintColorProperty() const;
        [[nodiscard]] auto TintOpacityProperty() const;
        [[nodiscard]] auto TintTransitionDurationProperty() const;
        [[nodiscard]] auto AlwaysUseFallbackProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics2
    {
        [[nodiscard]] auto TintLuminosityOpacityProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IAcrylicBrushStatics2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IAcrylicBrushStatics2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush
    {
        [[nodiscard]] auto Center() const;
        auto Center(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto RadiusX() const;
        auto RadiusX(double value) const;
        [[nodiscard]] auto RadiusY() const;
        auto RadiusY(double value) const;
        [[nodiscard]] auto GradientOrigin() const;
        auto GradientOrigin(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto MappingMode() const;
        auto MappingMode(winrt::Windows::UI::Xaml::Media::BrushMappingMode const& value) const;
        [[nodiscard]] auto InterpolationSpace() const;
        auto InterpolationSpace(winrt::Windows::UI::Composition::CompositionColorSpace const& value) const;
        [[nodiscard]] auto SpreadMethod() const;
        auto SpreadMethod(winrt::Windows::UI::Xaml::Media::GradientSpreadMethod const& value) const;
        [[nodiscard]] auto GradientStops() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRadialGradientBrush<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics
    {
        [[nodiscard]] auto CenterProperty() const;
        [[nodiscard]] auto RadiusXProperty() const;
        [[nodiscard]] auto RadiusYProperty() const;
        [[nodiscard]] auto GradientOriginProperty() const;
        [[nodiscard]] auto InterpolationSpaceProperty() const;
        [[nodiscard]] auto MappingModeProperty() const;
        [[nodiscard]] auto SpreadMethodProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrushStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRadialGradientBrushStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBackgroundBrush
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBackgroundBrush<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBackgroundBrushFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrushFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBackgroundBrushFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBorderBrush
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBorderBrush<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBorderBrushFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrushFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBorderBrushFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBrush
    {
        [[nodiscard]] auto Color() const;
        auto Color(winrt::Windows::UI::Color const& value) const;
        [[nodiscard]] auto TargetTheme() const;
        auto TargetTheme(winrt::Windows::UI::Xaml::ApplicationTheme const& value) const;
        [[nodiscard]] auto AlwaysUseFallback() const;
        auto AlwaysUseFallback(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBrush>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBrush<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBrushProtectedFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBrushProtectedFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBrushProtectedFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics
    {
        [[nodiscard]] auto ColorProperty() const;
        [[nodiscard]] auto TargetThemeProperty() const;
        [[nodiscard]] auto AlwaysUseFallbackProperty() const;
        [[nodiscard]] auto StateProperty() const;
        auto SetState(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::UI::Xaml::Media::RevealBrushState const& value) const;
        auto GetState(winrt::Windows::UI::Xaml::UIElement const& element) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Media::IRevealBrushStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Media_IRevealBrushStatics<D>;
    };
}
#endif
