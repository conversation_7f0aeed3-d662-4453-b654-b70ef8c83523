// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_Primitives_1_H
#define WINRT_Microsoft_UI_Xaml_Controls_Primitives_1_H
#include "winrt/impl/Microsoft.UI.Xaml.Controls.Primitives.0.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    struct WINRT_IMPL_EMPTY_BASES IAutoSuggestBoxHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxHelper>
    {
        IAutoSuggestBoxHelper(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAutoSuggestBoxHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAutoSuggestBoxHelperStatics>
    {
        IAutoSuggestBoxHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IAutoSuggestBoxHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPickerSlider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSlider>
    {
        IColorPickerSlider(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSlider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPickerSliderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderFactory>
    {
        IColorPickerSliderFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPickerSliderStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderStatics>
    {
        IColorPickerSliderStatics(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSpectrum :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrum>
    {
        IColorSpectrum(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrum(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSpectrumFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumFactory>
    {
        IColorSpectrumFactory(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSpectrumStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumStatics>
    {
        IColorSpectrumStatics(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColumnMajorUniformToLargestGridLayout :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColumnMajorUniformToLargestGridLayout>
    {
        IColumnMajorUniformToLargestGridLayout(std::nullptr_t = nullptr) noexcept {}
        IColumnMajorUniformToLargestGridLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColumnMajorUniformToLargestGridLayoutFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColumnMajorUniformToLargestGridLayoutFactory>
    {
        IColumnMajorUniformToLargestGridLayoutFactory(std::nullptr_t = nullptr) noexcept {}
        IColumnMajorUniformToLargestGridLayoutFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColumnMajorUniformToLargestGridLayoutStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColumnMajorUniformToLargestGridLayoutStatics>
    {
        IColumnMajorUniformToLargestGridLayoutStatics(std::nullptr_t = nullptr) noexcept {}
        IColumnMajorUniformToLargestGridLayoutStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IComboBoxHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxHelper>
    {
        IComboBoxHelper(std::nullptr_t = nullptr) noexcept {}
        IComboBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IComboBoxHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IComboBoxHelperStatics>
    {
        IComboBoxHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IComboBoxHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommandBarFlyoutCommandBar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBar>
    {
        ICommandBarFlyoutCommandBar(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommandBarFlyoutCommandBarAutomationPropertiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBarAutomationPropertiesStatics>
    {
        ICommandBarFlyoutCommandBarAutomationPropertiesStatics(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBarAutomationPropertiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommandBarFlyoutCommandBarFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBarFactory>
    {
        ICommandBarFlyoutCommandBarFactory(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBarFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommandBarFlyoutCommandBarTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommandBarFlyoutCommandBarTemplateSettings>
    {
        ICommandBarFlyoutCommandBarTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        ICommandBarFlyoutCommandBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusFilterConverter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusFilterConverter>
    {
        ICornerRadiusFilterConverter(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusFilterConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusFilterConverterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusFilterConverterStatics>
    {
        ICornerRadiusFilterConverterStatics(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusFilterConverterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusToThicknessConverter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusToThicknessConverter>
    {
        ICornerRadiusToThicknessConverter(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusToThicknessConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICornerRadiusToThicknessConverterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICornerRadiusToThicknessConverterStatics>
    {
        ICornerRadiusToThicknessConverterStatics(std::nullptr_t = nullptr) noexcept {}
        ICornerRadiusToThicknessConverterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfoBarPanel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfoBarPanel>
    {
        IInfoBarPanel(std::nullptr_t = nullptr) noexcept {}
        IInfoBarPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfoBarPanelFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfoBarPanelFactory>
    {
        IInfoBarPanelFactory(std::nullptr_t = nullptr) noexcept {}
        IInfoBarPanelFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfoBarPanelStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfoBarPanelStatics>
    {
        IInfoBarPanelStatics(std::nullptr_t = nullptr) noexcept {}
        IInfoBarPanelStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMonochromaticOverlayPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMonochromaticOverlayPresenter>
    {
        IMonochromaticOverlayPresenter(std::nullptr_t = nullptr) noexcept {}
        IMonochromaticOverlayPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMonochromaticOverlayPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMonochromaticOverlayPresenterFactory>
    {
        IMonochromaticOverlayPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        IMonochromaticOverlayPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMonochromaticOverlayPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMonochromaticOverlayPresenterStatics>
    {
        IMonochromaticOverlayPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        IMonochromaticOverlayPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenter>
    {
        INavigationViewItemPresenter(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenter2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenter2>
    {
        INavigationViewItemPresenter2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenter2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterFactory>
    {
        INavigationViewItemPresenterFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterStatics>
    {
        INavigationViewItemPresenterStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterStatics2>
    {
        INavigationViewItemPresenterStatics2(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterTemplateSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterTemplateSettings>
    {
        INavigationViewItemPresenterTemplateSettings(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterTemplateSettingsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterTemplateSettingsFactory>
    {
        INavigationViewItemPresenterTemplateSettingsFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterTemplateSettingsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemPresenterTemplateSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemPresenterTemplateSettingsStatics>
    {
        INavigationViewItemPresenterTemplateSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemPresenterTemplateSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewListView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewListView>
    {
        ITabViewListView(std::nullptr_t = nullptr) noexcept {}
        ITabViewListView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewListViewFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewListViewFactory>
    {
        ITabViewListViewFactory(std::nullptr_t = nullptr) noexcept {}
        ITabViewListViewFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
