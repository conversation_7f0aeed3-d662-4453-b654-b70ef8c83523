{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\plexy\\windows\\plexy\\plexy.vcxproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\Common.vcxproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\Common.vcxproj", "projectName": "Common", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\Common.vcxproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["native"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"native": {"projectReferences": {}}}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"native": {"dependencies": {"Microsoft.Windows.CppWinRT": {"suppressParent": "All", "target": "Package", "version": "[2.0.230706.1, )"}, "boost": {"target": "Package", "version": "[1.83.0, )"}}, "imports": ["uap10.0.17763", "net461"], "assetTargetFallback": true, "warn": true}}, "runtimes": {"win10-arm": {"#import": []}, "win10-arm-aot": {"#import": []}, "win10-arm64-aot": {"#import": []}, "win10-x64": {"#import": []}, "win10-x64-aot": {"#import": []}, "win10-x86": {"#import": []}, "win10-x86-aot": {"#import": []}}}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\fmt\\fmt.vcxproj": {"restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\fmt\\fmt.vcxproj", "projectName": "fmt", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\fmt\\fmt.vcxproj", "UsingMicrosoftNETSdk": false, "frameworks": {"native": {"projectReferences": {}}}}, "frameworks": {"native": {}}}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj", "projectName": "Folly", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["native"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"native": {"projectReferences": {"C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\fmt\\fmt.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\fmt\\fmt.vcxproj"}}}}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"native": {"dependencies": {"boost": {"target": "Package", "version": "[1.83.0, )"}}, "imports": ["uap10.0.17763", "net461"], "assetTargetFallback": true, "warn": true}}, "runtimes": {"win10-arm": {"#import": []}, "win10-arm-aot": {"#import": []}, "win10-arm64-aot": {"#import": []}, "win10-x64": {"#import": []}, "win10-x64-aot": {"#import": []}, "win10-x86": {"#import": []}, "win10-x86-aot": {"#import": []}}}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\Microsoft.ReactNative.vcxproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\Microsoft.ReactNative.vcxproj", "projectName": "Microsoft.ReactNative", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\Microsoft.ReactNative.vcxproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["native"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"native": {"projectReferences": {"C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\Common.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Common\\Common.vcxproj"}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj"}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\ReactCommon.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\ReactCommon.vcxproj"}}}}, "restoreLockProperties": {"restorePackagesWithLockFile": "true", "nuGetLockFilePath": ".\\packages.lock.json", "restoreLockedMode": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"native": {"dependencies": {"Microsoft.JavaScript.Hermes": {"target": "Package", "version": "[0.0.0-2505.2001-0e4bc3b9, )"}, "Microsoft.UI.Xaml": {"target": "Package", "version": "[2.8.0, )"}, "Microsoft.Windows.CppWinRT": {"suppressParent": "All", "target": "Package", "version": "[2.0.230706.1, )"}, "boost": {"target": "Package", "version": "[1.83.0, )"}}, "imports": ["uap10.0.17763", "net461"], "assetTargetFallback": true, "warn": true}}, "runtimes": {"win10-arm": {"#import": []}, "win10-arm-aot": {"#import": []}, "win10-arm64-aot": {"#import": []}, "win10-x64": {"#import": []}, "win10-x64-aot": {"#import": []}, "win10-x86": {"#import": []}, "win10-x86-aot": {"#import": []}}}, "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\ReactCommon.vcxproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\ReactCommon.vcxproj", "projectName": "ReactCommon", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\ReactCommon.vcxproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\ReactCommon\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["native"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"native": {"projectReferences": {"C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Folly\\Folly.vcxproj"}}}}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"native": {"dependencies": {"Microsoft.Windows.CppWinRT": {"suppressParent": "All", "target": "Package", "version": "[2.0.230706.1, )"}, "boost": {"target": "Package", "version": "[1.83.0, )"}}, "imports": ["uap10.0.17763", "net461"], "assetTargetFallback": true, "warn": true}}, "runtimes": {"win10-arm": {"#import": []}, "win10-arm-aot": {"#import": []}, "win10-arm64-aot": {"#import": []}, "win10-x64": {"#import": []}, "win10-x64-aot": {"#import": []}, "win10-x86": {"#import": []}, "win10-x86-aot": {"#import": []}}}, "C:\\Users\\<USER>\\Desktop\\plexy\\windows\\plexy\\plexy.vcxproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\plexy\\windows\\plexy\\plexy.vcxproj", "projectName": "plexy", "projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\windows\\plexy\\plexy.vcxproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\plexy\\windows\\plexy\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\Desktop\\plexy\\windows\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["native"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/ms/react-native/_packaging/react-native-public/nuget/v3/index.json": {}}, "frameworks": {"native": {"projectReferences": {"C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\Microsoft.ReactNative.vcxproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\plexy\\node_modules\\react-native-windows\\Microsoft.ReactNative\\Microsoft.ReactNative.vcxproj"}}}}, "restoreLockProperties": {"restorePackagesWithLockFile": "true"}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"native": {"dependencies": {"Microsoft.JavaScript.Hermes": {"target": "Package", "version": "[0.0.0-2505.2001-0e4bc3b9, )"}, "Microsoft.UI.Xaml": {"target": "Package", "version": "[2.8.0, )"}, "Microsoft.Windows.CppWinRT": {"suppressParent": "All", "target": "Package", "version": "[2.0.230706.1, )"}}, "imports": ["uap10.0.17763", "net461"], "assetTargetFallback": true, "warn": true}}, "runtimes": {"win10-arm": {"#import": []}, "win10-arm-aot": {"#import": []}, "win10-arm64-aot": {"#import": []}, "win10-x64": {"#import": []}, "win10-x64-aot": {"#import": []}, "win10-x86": {"#import": []}, "win10-x86-aot": {"#import": []}}}}}