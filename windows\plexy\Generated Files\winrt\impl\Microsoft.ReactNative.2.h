// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_ReactNative_2_H
#define WINRT_Microsoft_ReactNative_2_H
#include "winrt/impl/Windows.Foundation.Collections.2.h"
#include "winrt/impl/Windows.Graphics.Effects.2.h"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.2.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.2.h"
#include "winrt/impl/Windows.UI.Xaml.Markup.2.h"
#include "winrt/impl/Microsoft.ReactNative.1.h"
WINRT_EXPORT namespace winrt::Microsoft::ReactNative
{
    struct AccessibilityActionEventHandler : winrt::Windows::Foundation::IUnknown
    {
        AccessibilityActionEventHandler(std::nullptr_t = nullptr) noexcept {}
        AccessibilityActionEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> AccessibilityActionEventHandler(L lambda);
        template <typename F> AccessibilityActionEventHandler(F* function);
        template <typename O, typename M> AccessibilityActionEventHandler(O* object, M method);
        template <typename O, typename M> AccessibilityActionEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename M> AccessibilityActionEventHandler(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::AccessibilityAction const& action) const;
    };
    struct AccessibilityInvokeEventHandler : winrt::Windows::Foundation::IUnknown
    {
        AccessibilityInvokeEventHandler(std::nullptr_t = nullptr) noexcept {}
        AccessibilityInvokeEventHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> AccessibilityInvokeEventHandler(L lambda);
        template <typename F> AccessibilityInvokeEventHandler(F* function);
        template <typename O, typename M> AccessibilityInvokeEventHandler(O* object, M method);
        template <typename O, typename M> AccessibilityInvokeEventHandler(com_ptr<O>&& object, M method);
        template <typename O, typename M> AccessibilityInvokeEventHandler(weak_ref<O>&& object, M method);
        auto operator()() const;
    };
    struct CallFunc : winrt::Windows::Foundation::IUnknown
    {
        CallFunc(std::nullptr_t = nullptr) noexcept {}
        CallFunc(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> CallFunc(L lambda);
        template <typename F> CallFunc(F* function);
        template <typename O, typename M> CallFunc(O* object, M method);
        template <typename O, typename M> CallFunc(com_ptr<O>&& object, M method);
        template <typename O, typename M> CallFunc(weak_ref<O>&& object, M method);
        auto operator()(winrt::Windows::Foundation::IInspectable const& runtime) const;
    };
    struct ConstantProviderDelegate : winrt::Windows::Foundation::IUnknown
    {
        ConstantProviderDelegate(std::nullptr_t = nullptr) noexcept {}
        ConstantProviderDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ConstantProviderDelegate(L lambda);
        template <typename F> ConstantProviderDelegate(F* function);
        template <typename O, typename M> ConstantProviderDelegate(O* object, M method);
        template <typename O, typename M> ConstantProviderDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> ConstantProviderDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& constantWriter) const;
    };
    struct EmitEventSetterDelegate : winrt::Windows::Foundation::IUnknown
    {
        EmitEventSetterDelegate(std::nullptr_t = nullptr) noexcept {}
        EmitEventSetterDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> EmitEventSetterDelegate(L lambda);
        template <typename F> EmitEventSetterDelegate(F* function);
        template <typename O, typename M> EmitEventSetterDelegate(O* object, M method);
        template <typename O, typename M> EmitEventSetterDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> EmitEventSetterDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::JSValueArgWriter const& argWriter) const;
    };
    struct EventEmitterInitializerDelegate : winrt::Windows::Foundation::IUnknown
    {
        EventEmitterInitializerDelegate(std::nullptr_t = nullptr) noexcept {}
        EventEmitterInitializerDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> EventEmitterInitializerDelegate(L lambda);
        template <typename F> EventEmitterInitializerDelegate(F* function);
        template <typename O, typename M> EventEmitterInitializerDelegate(O* object, M method);
        template <typename O, typename M> EventEmitterInitializerDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> EventEmitterInitializerDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::EmitEventSetterDelegate const& emitter) const;
    };
    struct InitializerDelegate : winrt::Windows::Foundation::IUnknown
    {
        InitializerDelegate(std::nullptr_t = nullptr) noexcept {}
        InitializerDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> InitializerDelegate(L lambda);
        template <typename F> InitializerDelegate(F* function);
        template <typename O, typename M> InitializerDelegate(O* object, M method);
        template <typename O, typename M> InitializerDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> InitializerDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IReactContext const& reactContext) const;
    };
    struct JSValueArgWriter : winrt::Windows::Foundation::IUnknown
    {
        JSValueArgWriter(std::nullptr_t = nullptr) noexcept {}
        JSValueArgWriter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> JSValueArgWriter(L lambda);
        template <typename F> JSValueArgWriter(F* function);
        template <typename O, typename M> JSValueArgWriter(O* object, M method);
        template <typename O, typename M> JSValueArgWriter(com_ptr<O>&& object, M method);
        template <typename O, typename M> JSValueArgWriter(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& writer) const;
    };
    struct JsiByteArrayUser : winrt::Windows::Foundation::IUnknown
    {
        JsiByteArrayUser(std::nullptr_t = nullptr) noexcept {}
        JsiByteArrayUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> JsiByteArrayUser(L lambda);
        template <typename F> JsiByteArrayUser(F* function);
        template <typename O, typename M> JsiByteArrayUser(O* object, M method);
        template <typename O, typename M> JsiByteArrayUser(com_ptr<O>&& object, M method);
        template <typename O, typename M> JsiByteArrayUser(weak_ref<O>&& object, M method);
        auto operator()(array_view<uint8_t const> bytes) const;
    };
    struct JsiHostFunction : winrt::Windows::Foundation::IUnknown
    {
        JsiHostFunction(std::nullptr_t = nullptr) noexcept {}
        JsiHostFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> JsiHostFunction(L lambda);
        template <typename F> JsiHostFunction(F* function);
        template <typename O, typename M> JsiHostFunction(O* object, M method);
        template <typename O, typename M> JsiHostFunction(com_ptr<O>&& object, M method);
        template <typename O, typename M> JsiHostFunction(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiValueRef const& thisArg, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const;
    };
    struct JsiInitializerDelegate : winrt::Windows::Foundation::IUnknown
    {
        JsiInitializerDelegate(std::nullptr_t = nullptr) noexcept {}
        JsiInitializerDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> JsiInitializerDelegate(L lambda);
        template <typename F> JsiInitializerDelegate(F* function);
        template <typename O, typename M> JsiInitializerDelegate(O* object, M method);
        template <typename O, typename M> JsiInitializerDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> JsiInitializerDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IReactContext const& reactContext, winrt::Windows::Foundation::IInspectable const& runtimeHandle) const;
    };
    struct LogHandler : winrt::Windows::Foundation::IUnknown
    {
        LogHandler(std::nullptr_t = nullptr) noexcept {}
        LogHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> LogHandler(L lambda);
        template <typename F> LogHandler(F* function);
        template <typename O, typename M> LogHandler(O* object, M method);
        template <typename O, typename M> LogHandler(com_ptr<O>&& object, M method);
        template <typename O, typename M> LogHandler(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::LogLevel const& level, param::hstring const& message) const;
    };
    struct MethodDelegate : winrt::Windows::Foundation::IUnknown
    {
        MethodDelegate(std::nullptr_t = nullptr) noexcept {}
        MethodDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> MethodDelegate(L lambda);
        template <typename F> MethodDelegate(F* function);
        template <typename O, typename M> MethodDelegate(O* object, M method);
        template <typename O, typename M> MethodDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> MethodDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IJSValueReader const& inputReader, winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter, winrt::Microsoft::ReactNative::MethodResultCallback const& resolve, winrt::Microsoft::ReactNative::MethodResultCallback const& reject) const;
    };
    struct MethodResultCallback : winrt::Windows::Foundation::IUnknown
    {
        MethodResultCallback(std::nullptr_t = nullptr) noexcept {}
        MethodResultCallback(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> MethodResultCallback(L lambda);
        template <typename F> MethodResultCallback(F* function);
        template <typename O, typename M> MethodResultCallback(O* object, M method);
        template <typename O, typename M> MethodResultCallback(com_ptr<O>&& object, M method);
        template <typename O, typename M> MethodResultCallback(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter) const;
    };
    struct ReactCreatePropertyValue : winrt::Windows::Foundation::IUnknown
    {
        ReactCreatePropertyValue(std::nullptr_t = nullptr) noexcept {}
        ReactCreatePropertyValue(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ReactCreatePropertyValue(L lambda);
        template <typename F> ReactCreatePropertyValue(F* function);
        template <typename O, typename M> ReactCreatePropertyValue(O* object, M method);
        template <typename O, typename M> ReactCreatePropertyValue(com_ptr<O>&& object, M method);
        template <typename O, typename M> ReactCreatePropertyValue(weak_ref<O>&& object, M method);
        auto operator()() const;
    };
    struct ReactDispatcherCallback : winrt::Windows::Foundation::IUnknown
    {
        ReactDispatcherCallback(std::nullptr_t = nullptr) noexcept {}
        ReactDispatcherCallback(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ReactDispatcherCallback(L lambda);
        template <typename F> ReactDispatcherCallback(F* function);
        template <typename O, typename M> ReactDispatcherCallback(O* object, M method);
        template <typename O, typename M> ReactDispatcherCallback(com_ptr<O>&& object, M method);
        template <typename O, typename M> ReactDispatcherCallback(weak_ref<O>&& object, M method);
        auto operator()() const;
    };
    struct ReactModuleProvider : winrt::Windows::Foundation::IUnknown
    {
        ReactModuleProvider(std::nullptr_t = nullptr) noexcept {}
        ReactModuleProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ReactModuleProvider(L lambda);
        template <typename F> ReactModuleProvider(F* function);
        template <typename O, typename M> ReactModuleProvider(O* object, M method);
        template <typename O, typename M> ReactModuleProvider(com_ptr<O>&& object, M method);
        template <typename O, typename M> ReactModuleProvider(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IReactModuleBuilder const& moduleBuilder) const;
    };
    struct ReactNotificationHandler : winrt::Windows::Foundation::IUnknown
    {
        ReactNotificationHandler(std::nullptr_t = nullptr) noexcept {}
        ReactNotificationHandler(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ReactNotificationHandler(L lambda);
        template <typename F> ReactNotificationHandler(F* function);
        template <typename O, typename M> ReactNotificationHandler(O* object, M method);
        template <typename O, typename M> ReactNotificationHandler(com_ptr<O>&& object, M method);
        template <typename O, typename M> ReactNotificationHandler(weak_ref<O>&& object, M method);
        auto operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Microsoft::ReactNative::IReactNotificationArgs const& args) const;
    };
    struct ReactViewManagerProvider : winrt::Windows::Foundation::IUnknown
    {
        ReactViewManagerProvider(std::nullptr_t = nullptr) noexcept {}
        ReactViewManagerProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> ReactViewManagerProvider(L lambda);
        template <typename F> ReactViewManagerProvider(F* function);
        template <typename O, typename M> ReactViewManagerProvider(O* object, M method);
        template <typename O, typename M> ReactViewManagerProvider(com_ptr<O>&& object, M method);
        template <typename O, typename M> ReactViewManagerProvider(weak_ref<O>&& object, M method);
        auto operator()() const;
    };
    struct SyncMethodDelegate : winrt::Windows::Foundation::IUnknown
    {
        SyncMethodDelegate(std::nullptr_t = nullptr) noexcept {}
        SyncMethodDelegate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> SyncMethodDelegate(L lambda);
        template <typename F> SyncMethodDelegate(F* function);
        template <typename O, typename M> SyncMethodDelegate(O* object, M method);
        template <typename O, typename M> SyncMethodDelegate(com_ptr<O>&& object, M method);
        template <typename O, typename M> SyncMethodDelegate(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IJSValueReader const& inputReader, winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter) const;
    };
    struct TimerFactory : winrt::Windows::Foundation::IUnknown
    {
        TimerFactory(std::nullptr_t = nullptr) noexcept {}
        TimerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> TimerFactory(L lambda);
        template <typename F> TimerFactory(F* function);
        template <typename O, typename M> TimerFactory(O* object, M method);
        template <typename O, typename M> TimerFactory(com_ptr<O>&& object, M method);
        template <typename O, typename M> TimerFactory(weak_ref<O>&& object, M method);
        auto operator()() const;
    };
    struct UIBatchCompleteCallback : winrt::Windows::Foundation::IUnknown
    {
        UIBatchCompleteCallback(std::nullptr_t = nullptr) noexcept {}
        UIBatchCompleteCallback(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IUnknown(ptr, take_ownership_from_abi) {}
        template <typename L> UIBatchCompleteCallback(L lambda);
        template <typename F> UIBatchCompleteCallback(F* function);
        template <typename O, typename M> UIBatchCompleteCallback(O* object, M method);
        template <typename O, typename M> UIBatchCompleteCallback(com_ptr<O>&& object, M method);
        template <typename O, typename M> UIBatchCompleteCallback(weak_ref<O>&& object, M method);
        auto operator()(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
    };
    struct AccessibilityAction
    {
        hstring Name;
        hstring Label;
    };
    inline bool operator==(AccessibilityAction const& left, AccessibilityAction const& right) noexcept
    {
        return left.Name == right.Name && left.Label == right.Label;
    }
    inline bool operator!=(AccessibilityAction const& left, AccessibilityAction const& right) noexcept
    {
        return !(left == right);
    }
    struct DesktopWindowMessage
    {
        uint32_t Msg;
        uint64_t WParam;
        int64_t LParam;
    };
    inline bool operator==(DesktopWindowMessage const& left, DesktopWindowMessage const& right) noexcept
    {
        return left.Msg == right.Msg && left.WParam == right.WParam && left.LParam == right.LParam;
    }
    inline bool operator!=(DesktopWindowMessage const& left, DesktopWindowMessage const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiBigIntRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiBigIntRef const& left, JsiBigIntRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiBigIntRef const& left, JsiBigIntRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiObjectRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiObjectRef const& left, JsiObjectRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiObjectRef const& left, JsiObjectRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiPropertyIdRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiPropertyIdRef const& left, JsiPropertyIdRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiPropertyIdRef const& left, JsiPropertyIdRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiScopeState
    {
        uint64_t Data;
    };
    inline bool operator==(JsiScopeState const& left, JsiScopeState const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiScopeState const& left, JsiScopeState const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiStringRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiStringRef const& left, JsiStringRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiStringRef const& left, JsiStringRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiSymbolRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiSymbolRef const& left, JsiSymbolRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiSymbolRef const& left, JsiSymbolRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiValueRef
    {
        winrt::Microsoft::ReactNative::JsiValueKind Kind;
        uint64_t Data;
    };
    inline bool operator==(JsiValueRef const& left, JsiValueRef const& right) noexcept
    {
        return left.Kind == right.Kind && left.Data == right.Data;
    }
    inline bool operator!=(JsiValueRef const& left, JsiValueRef const& right) noexcept
    {
        return !(left == right);
    }
    struct JsiWeakObjectRef
    {
        uint64_t Data;
    };
    inline bool operator==(JsiWeakObjectRef const& left, JsiWeakObjectRef const& right) noexcept
    {
        return left.Data == right.Data;
    }
    inline bool operator!=(JsiWeakObjectRef const& left, JsiWeakObjectRef const& right) noexcept
    {
        return !(left == right);
    }
    struct WINRT_IMPL_EMPTY_BASES BorderEffect : winrt::Microsoft::ReactNative::IBorderEffect,
        impl::require<BorderEffect, winrt::Windows::Graphics::Effects::IGraphicsEffectSource, winrt::Windows::Graphics::Effects::IGraphicsEffect>
    {
        BorderEffect(std::nullptr_t) noexcept {}
        BorderEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IBorderEffect(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CallInvoker : winrt::Microsoft::ReactNative::ICallInvoker
    {
        CallInvoker(std::nullptr_t) noexcept {}
        CallInvoker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::ICallInvoker(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ColorSourceEffect : winrt::Microsoft::ReactNative::IColorSourceEffect,
        impl::require<ColorSourceEffect, winrt::Windows::Graphics::Effects::IGraphicsEffectSource, winrt::Windows::Graphics::Effects::IGraphicsEffect>
    {
        ColorSourceEffect(std::nullptr_t) noexcept {}
        ColorSourceEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IColorSourceEffect(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CompositeStepEffect : winrt::Microsoft::ReactNative::ICompositeStepEffect,
        impl::require<CompositeStepEffect, winrt::Windows::Graphics::Effects::IGraphicsEffectSource, winrt::Windows::Graphics::Effects::IGraphicsEffect>
    {
        CompositeStepEffect(std::nullptr_t) noexcept {}
        CompositeStepEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::ICompositeStepEffect(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DevMenuControl : winrt::Microsoft::ReactNative::IDevMenuControl,
        impl::base<DevMenuControl, winrt::Windows::UI::Xaml::Controls::UserControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DevMenuControl, winrt::Windows::UI::Xaml::Controls::IUserControl, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DevMenuControl(std::nullptr_t) noexcept {}
        DevMenuControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IDevMenuControl(ptr, take_ownership_from_abi) {}
        DevMenuControl();
    };
    struct WINRT_IMPL_EMPTY_BASES DynamicAutomationPeer : winrt::Microsoft::ReactNative::IDynamicAutomationPeer,
        impl::base<DynamicAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DynamicAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionItemProvider, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DynamicAutomationPeer(std::nullptr_t) noexcept {}
        DynamicAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IDynamicAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit DynamicAutomationPeer(winrt::Windows::UI::Xaml::FrameworkElement const& owner);
        using impl::consume_t<DynamicAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<DynamicAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES DynamicAutomationProperties : winrt::Microsoft::ReactNative::IDynamicAutomationProperties
    {
        DynamicAutomationProperties(std::nullptr_t) noexcept {}
        DynamicAutomationProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IDynamicAutomationProperties(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto AccessibilityRoleProperty();
        static auto SetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityRoles const& value);
        static auto GetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AriaRoleProperty();
        static auto SetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AriaRole const& value);
        static auto GetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityStateSelectedProperty();
        static auto SetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element, bool value);
        static auto GetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityStateDisabledProperty();
        static auto SetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element, bool value);
        static auto GetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityStateCheckedProperty();
        static auto SetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue const& value);
        static auto GetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityStateBusyProperty();
        static auto SetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element, bool value);
        static auto GetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityStateExpandedProperty();
        static auto SetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element, bool value);
        static auto GetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityValueMinProperty();
        static auto SetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element, double value);
        static auto GetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityValueMaxProperty();
        static auto SetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element, double value);
        static auto GetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityValueNowProperty();
        static auto SetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element, double value);
        static auto GetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityValueTextProperty();
        static auto SetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element, param::hstring const& value);
        static auto GetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityInvokeEventHandlerProperty();
        static auto SetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler const& value);
        static auto GetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityActionsProperty();
        static auto SetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element, param::vector<winrt::Microsoft::ReactNative::AccessibilityAction> const& value);
        static auto GetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto AccessibilityActionEventHandlerProperty();
        static auto SetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityActionEventHandler const& value);
        static auto GetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element);
    };
    struct WINRT_IMPL_EMPTY_BASES DynamicValueProvider : winrt::Microsoft::ReactNative::IDynamicValueProvider,
        impl::require<DynamicValueProvider, winrt::Windows::UI::Xaml::Automation::Provider::IValueProvider>
    {
        DynamicValueProvider(std::nullptr_t) noexcept {}
        DynamicValueProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IDynamicValueProvider(ptr, take_ownership_from_abi) {}
        explicit DynamicValueProvider(winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer const& peer);
    };
    struct WINRT_IMPL_EMPTY_BASES GaussianBlurEffect : winrt::Microsoft::ReactNative::IGaussianBlurEffect,
        impl::require<GaussianBlurEffect, winrt::Windows::Graphics::Effects::IGraphicsEffectSource, winrt::Windows::Graphics::Effects::IGraphicsEffect>
    {
        GaussianBlurEffect(std::nullptr_t) noexcept {}
        GaussianBlurEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IGaussianBlurEffect(ptr, take_ownership_from_abi) {}
    };
    struct HttpSettings
    {
        HttpSettings() = delete;
        static auto SetDefaultUserAgent(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, param::hstring const& userAgent);
    };
    struct WINRT_IMPL_EMPTY_BASES InstanceCreatedEventArgs : winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs
    {
        InstanceCreatedEventArgs(std::nullptr_t) noexcept {}
        InstanceCreatedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES InstanceDestroyedEventArgs : winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs
    {
        InstanceDestroyedEventArgs(std::nullptr_t) noexcept {}
        InstanceDestroyedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES InstanceLoadedEventArgs : winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs
    {
        InstanceLoadedEventArgs(std::nullptr_t) noexcept {}
        InstanceLoadedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES JsiError : winrt::Microsoft::ReactNative::IJsiError
    {
        JsiError(std::nullptr_t) noexcept {}
        JsiError(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IJsiError(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES JsiPreparedJavaScript : winrt::Microsoft::ReactNative::IJsiPreparedJavaScript
    {
        JsiPreparedJavaScript(std::nullptr_t) noexcept {}
        JsiPreparedJavaScript(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IJsiPreparedJavaScript(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES JsiRuntime : winrt::Microsoft::ReactNative::IJsiRuntime
    {
        JsiRuntime(std::nullptr_t) noexcept {}
        JsiRuntime(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IJsiRuntime(ptr, take_ownership_from_abi) {}
        static auto MakeChakraRuntime();
    };
    struct WINRT_IMPL_EMPTY_BASES LayoutService : winrt::Microsoft::ReactNative::ILayoutService
    {
        LayoutService(std::nullptr_t) noexcept {}
        LayoutService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::ILayoutService(ptr, take_ownership_from_abi) {}
        static auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& context);
    };
    struct WINRT_IMPL_EMPTY_BASES QuirkSettings : winrt::Microsoft::ReactNative::IQuirkSettings
    {
        QuirkSettings(std::nullptr_t) noexcept {}
        QuirkSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IQuirkSettings(ptr, take_ownership_from_abi) {}
        static auto SetMatchAndroidAndIOSStretchBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
        static auto SetUseWebFlexBasisBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
        static auto SetAcceptSelfSigned(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
        static auto SetBackHandlerKind(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, winrt::Microsoft::ReactNative::BackNavigationHandlerKind const& kind);
        static auto SetMapWindowDeactivatedToAppStateInactive(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
        static auto SetSuppressWindowFocusOnViewFocus(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
        static auto SetUseRuntimeScheduler(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value);
    };
    struct WINRT_IMPL_EMPTY_BASES ReactApplication : winrt::Microsoft::ReactNative::IReactApplication,
        impl::base<ReactApplication, winrt::Windows::UI::Xaml::Application>,
        impl::require<ReactApplication, winrt::Windows::UI::Xaml::IApplication, winrt::Windows::UI::Xaml::IApplication2, winrt::Windows::UI::Xaml::IApplication3, winrt::Windows::UI::Xaml::IApplicationOverrides, winrt::Windows::UI::Xaml::IApplicationOverrides2>
    {
        ReactApplication(std::nullptr_t) noexcept {}
        ReactApplication(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactApplication(ptr, take_ownership_from_abi) {}
        ReactApplication();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactCoreInjection : winrt::Microsoft::ReactNative::IReactCoreInjection
    {
        ReactCoreInjection(std::nullptr_t) noexcept {}
        ReactCoreInjection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactCoreInjection(ptr, take_ownership_from_abi) {}
        static auto SetUIBatchCompleteCallback(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::UIBatchCompleteCallback const& xamlRoot);
        static auto MakeViewHost(winrt::Microsoft::ReactNative::ReactNativeHost const& host, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions);
        static auto PostToUIBatchingQueue(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback);
        static auto SetPlatformNameOverride(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, param::hstring const& platformName);
        static auto GetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties);
        static auto SetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowId);
        static auto SetTimerFactory(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::TimerFactory const& timerFactory);
    };
    struct ReactDispatcherHelper
    {
        ReactDispatcherHelper() = delete;
        static auto CreateSerialDispatcher();
        [[nodiscard]] static auto UIThreadDispatcher();
        [[nodiscard]] static auto UIDispatcherProperty();
        [[nodiscard]] static auto JSDispatcherProperty();
        [[nodiscard]] static auto JSDispatcherTaskStartingEventName();
        [[nodiscard]] static auto JSDispatcherIdleWaitStartingEventName();
        [[nodiscard]] static auto JSDispatcherIdleWaitCompletedEventName();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactInstanceSettings : winrt::Microsoft::ReactNative::IReactInstanceSettings
    {
        ReactInstanceSettings(std::nullptr_t) noexcept {}
        ReactInstanceSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactInstanceSettings(ptr, take_ownership_from_abi) {}
        ReactInstanceSettings();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactNativeHost : winrt::Microsoft::ReactNative::IReactNativeHost
    {
        ReactNativeHost(std::nullptr_t) noexcept {}
        ReactNativeHost(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactNativeHost(ptr, take_ownership_from_abi) {}
        ReactNativeHost();
        static auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& reactContext);
    };
    struct ReactNotificationServiceHelper
    {
        ReactNotificationServiceHelper() = delete;
        static auto CreateNotificationService();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactPointerEventArgs : winrt::Microsoft::ReactNative::IReactPointerEventArgs
    {
        ReactPointerEventArgs(std::nullptr_t) noexcept {}
        ReactPointerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactPointerEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct ReactPropertyBagHelper
    {
        ReactPropertyBagHelper() = delete;
        [[nodiscard]] static auto GlobalNamespace();
        static auto GetNamespace(param::hstring const& namespaceName);
        static auto GetName(winrt::Microsoft::ReactNative::IReactPropertyNamespace const& ns, param::hstring const& localName);
        static auto CreatePropertyBag();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactRootView : winrt::Microsoft::ReactNative::IReactRootView,
        impl::base<ReactRootView, winrt::Windows::UI::Xaml::Controls::Grid, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ReactRootView, winrt::Windows::UI::Xaml::Controls::IGrid, winrt::Windows::UI::Xaml::Controls::IGrid2, winrt::Windows::UI::Xaml::Controls::IGrid3, winrt::Windows::UI::Xaml::Controls::IGrid4, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ReactRootView(std::nullptr_t) noexcept {}
        ReactRootView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactRootView(ptr, take_ownership_from_abi) {}
        ReactRootView();
    };
    struct WINRT_IMPL_EMPTY_BASES ReactViewOptions : winrt::Microsoft::ReactNative::IReactViewOptions
    {
        ReactViewOptions(std::nullptr_t) noexcept {}
        ReactViewOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IReactViewOptions(ptr, take_ownership_from_abi) {}
        ReactViewOptions();
    };
    struct WINRT_IMPL_EMPTY_BASES RedBoxHelper : winrt::Microsoft::ReactNative::IRedBoxHelper
    {
        RedBoxHelper(std::nullptr_t) noexcept {}
        RedBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IRedBoxHelper(ptr, take_ownership_from_abi) {}
        static auto CreateDefaultHandler(winrt::Microsoft::ReactNative::ReactNativeHost const& host);
    };
    struct WINRT_IMPL_EMPTY_BASES Timer : winrt::Microsoft::ReactNative::ITimer2
    {
        Timer(std::nullptr_t) noexcept {}
        Timer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::ITimer2(ptr, take_ownership_from_abi) {}
        static auto Create(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties);
    };
    struct WINRT_IMPL_EMPTY_BASES ViewControl : winrt::Microsoft::ReactNative::IViewControl,
        impl::base<ViewControl, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ViewControl, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ViewControl(std::nullptr_t) noexcept {}
        ViewControl(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IViewControl(ptr, take_ownership_from_abi) {}
        ViewControl();
    };
    struct WINRT_IMPL_EMPTY_BASES ViewPanel : winrt::Microsoft::ReactNative::IViewPanel,
        impl::base<ViewPanel, winrt::Windows::UI::Xaml::Controls::Grid, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ViewPanel, winrt::Windows::UI::Xaml::Controls::IGrid, winrt::Windows::UI::Xaml::Controls::IGrid2, winrt::Windows::UI::Xaml::Controls::IGrid3, winrt::Windows::UI::Xaml::Controls::IGrid4, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ViewPanel(std::nullptr_t) noexcept {}
        ViewPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IViewPanel(ptr, take_ownership_from_abi) {}
        ViewPanel();
        [[nodiscard]] static auto ViewBackgroundProperty();
        [[nodiscard]] static auto BorderThicknessProperty();
        [[nodiscard]] static auto BorderBrushProperty();
        [[nodiscard]] static auto CornerRadiusProperty();
        [[nodiscard]] static auto TopProperty();
        static auto SetTop(winrt::Windows::UI::Xaml::UIElement const& element, double value);
        static auto GetTop(winrt::Windows::UI::Xaml::UIElement const& element);
        [[nodiscard]] static auto LeftProperty();
        static auto SetLeft(winrt::Windows::UI::Xaml::UIElement const& element, double value);
        static auto GetLeft(winrt::Windows::UI::Xaml::UIElement const& element);
    };
    struct WINRT_IMPL_EMPTY_BASES XamlHelper : winrt::Microsoft::ReactNative::IXamlHelper
    {
        XamlHelper(std::nullptr_t) noexcept {}
        XamlHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IXamlHelper(ptr, take_ownership_from_abi) {}
        static auto BrushFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider);
        static auto ColorFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider);
        [[nodiscard]] static auto ReactTagProperty();
        static auto GetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject);
        static auto SetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject, int64_t tag);
    };
    struct WINRT_IMPL_EMPTY_BASES XamlMetaDataProvider : winrt::Windows::UI::Xaml::Markup::IXamlMetadataProvider
    {
        XamlMetaDataProvider(std::nullptr_t) noexcept {}
        XamlMetaDataProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::UI::Xaml::Markup::IXamlMetadataProvider(ptr, take_ownership_from_abi) {}
        XamlMetaDataProvider();
    };
    struct WINRT_IMPL_EMPTY_BASES XamlUIService : winrt::Microsoft::ReactNative::IXamlUIService
    {
        XamlUIService(std::nullptr_t) noexcept {}
        XamlUIService(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::ReactNative::IXamlUIService(ptr, take_ownership_from_abi) {}
        static auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& context);
        static auto SetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::XamlRoot const& xamlRoot);
        static auto SetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::FrameworkElement const& accessibleRoot);
        static auto GetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties);
        static auto GetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties);
        static auto GetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties);
        static auto SetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowHandle);
    };
}
#endif
