// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_System_Implementation_FileExplorer_1_H
#define WINRT_Windows_System_Implementation_FileExplorer_1_H
#include "winrt/impl/Windows.System.Implementation.FileExplorer.0.h"
WINRT_EXPORT namespace winrt::Windows::System::Implementation::FileExplorer
{
    struct WINRT_IMPL_EMPTY_BASES ISysStorageProviderEventReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISysStorageProviderEventReceivedEventArgs>
    {
        ISysStorageProviderEventReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ISysStorageProviderEventReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISysStorageProviderEventReceivedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISysStorageProviderEventReceivedEventArgsFactory>
    {
        ISysStorageProviderEventReceivedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        ISysStorageProviderEventReceivedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISysStorageProviderEventSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISysStorageProviderEventSource>
    {
        ISysStorageProviderEventSource(std::nullptr_t = nullptr) noexcept {}
        ISysStorageProviderEventSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISysStorageProviderHandlerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISysStorageProviderHandlerFactory>
    {
        ISysStorageProviderHandlerFactory(std::nullptr_t = nullptr) noexcept {}
        ISysStorageProviderHandlerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISysStorageProviderHttpRequestProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISysStorageProviderHttpRequestProvider>
    {
        ISysStorageProviderHttpRequestProvider(std::nullptr_t = nullptr) noexcept {}
        ISysStorageProviderHttpRequestProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
