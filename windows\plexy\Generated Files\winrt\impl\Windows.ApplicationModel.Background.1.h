// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Background_1_H
#define WINRT_Windows_ApplicationModel_Background_1_H
#include "winrt/impl/Windows.ApplicationModel.Background.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Background
{
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IActivitySensorTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IActivitySensorTrigger(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IActivitySensorTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IActivitySensorTriggerFactory>
    {
        IActivitySensorTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IActivitySensorTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IApplicationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IApplicationTrigger(std::nullptr_t = nullptr) noexcept {}
        IApplicationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IApplicationTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IApplicationTriggerDetails>
    {
        IApplicationTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IApplicationTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAppointmentStoreNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAppointmentStoreNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IAppointmentStoreNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IAppointmentStoreNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IAppointmentStoreNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundCondition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundCondition>
    {
        IBackgroundCondition(std::nullptr_t = nullptr) noexcept {}
        IBackgroundCondition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundExecutionManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundExecutionManagerStatics>
    {
        IBackgroundExecutionManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundExecutionManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundExecutionManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundExecutionManagerStatics2>
    {
        IBackgroundExecutionManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundExecutionManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundExecutionManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundExecutionManagerStatics3>
    {
        IBackgroundExecutionManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IBackgroundExecutionManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTask :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTask>
    {
        IBackgroundTask(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTask(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskBuilder :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskBuilder>
    {
        IBackgroundTaskBuilder(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskBuilder(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskBuilder2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskBuilder2>
    {
        IBackgroundTaskBuilder2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskBuilder2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskBuilder3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskBuilder3>
    {
        IBackgroundTaskBuilder3(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskBuilder3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskBuilder4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskBuilder4>
    {
        IBackgroundTaskBuilder4(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskBuilder4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskBuilder5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskBuilder5>
    {
        IBackgroundTaskBuilder5(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskBuilder5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskCompletedEventArgs>
    {
        IBackgroundTaskCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskDeferral>
    {
        IBackgroundTaskDeferral(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskInstance :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskInstance>
    {
        IBackgroundTaskInstance(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskInstance(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskInstance2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskInstance2>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBackgroundTaskInstance2, winrt::Windows::ApplicationModel::Background::IBackgroundTaskInstance>
    {
        IBackgroundTaskInstance2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskInstance2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskInstance4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskInstance4>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBackgroundTaskInstance4, winrt::Windows::ApplicationModel::Background::IBackgroundTaskInstance>
    {
        IBackgroundTaskInstance4(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskInstance4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskProgressEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskProgressEventArgs>
    {
        IBackgroundTaskProgressEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskProgressEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistration :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistration>
    {
        IBackgroundTaskRegistration(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistration(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistration2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistration2>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBackgroundTaskRegistration2, winrt::Windows::ApplicationModel::Background::IBackgroundTaskRegistration>
    {
        IBackgroundTaskRegistration2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistration2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistration3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistration3>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBackgroundTaskRegistration3, winrt::Windows::ApplicationModel::Background::IBackgroundTaskRegistration>
    {
        IBackgroundTaskRegistration3(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistration3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistrationGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistrationGroup>
    {
        IBackgroundTaskRegistrationGroup(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistrationGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistrationGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistrationGroupFactory>
    {
        IBackgroundTaskRegistrationGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistrationGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistrationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistrationStatics>
    {
        IBackgroundTaskRegistrationStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistrationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTaskRegistrationStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTaskRegistrationStatics2>
    {
        IBackgroundTaskRegistrationStatics2(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTaskRegistrationStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundTrigger>
    {
        IBackgroundTrigger(std::nullptr_t = nullptr) noexcept {}
        IBackgroundTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundWorkCostStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundWorkCostStatics>
    {
        IBackgroundWorkCostStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundWorkCostStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAdvertisementPublisherTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBluetoothLEAdvertisementPublisherTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IBluetoothLEAdvertisementPublisherTrigger(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAdvertisementPublisherTrigger2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherTrigger2>
    {
        IBluetoothLEAdvertisementPublisherTrigger2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherTrigger2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAdvertisementWatcherTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcherTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IBluetoothLEAdvertisementWatcherTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IBluetoothLEAdvertisementWatcherTrigger(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcherTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBluetoothLEAdvertisementWatcherTrigger2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcherTrigger2>
    {
        IBluetoothLEAdvertisementWatcherTrigger2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcherTrigger2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICachedFileUpdaterTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICachedFileUpdaterTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ICachedFileUpdaterTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ICachedFileUpdaterTrigger(std::nullptr_t = nullptr) noexcept {}
        ICachedFileUpdaterTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICachedFileUpdaterTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICachedFileUpdaterTriggerDetails>
    {
        ICachedFileUpdaterTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        ICachedFileUpdaterTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IChatMessageNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChatMessageNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IChatMessageNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IChatMessageNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IChatMessageNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IChatMessageReceivedNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChatMessageReceivedNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IChatMessageReceivedNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IChatMessageReceivedNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IChatMessageReceivedNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICommunicationBlockingAppSetAsActiveTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICommunicationBlockingAppSetAsActiveTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ICommunicationBlockingAppSetAsActiveTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ICommunicationBlockingAppSetAsActiveTrigger(std::nullptr_t = nullptr) noexcept {}
        ICommunicationBlockingAppSetAsActiveTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContactStoreNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContactStoreNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IContactStoreNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IContactStoreNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IContactStoreNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentPrefetchTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPrefetchTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IContentPrefetchTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IContentPrefetchTrigger(std::nullptr_t = nullptr) noexcept {}
        IContentPrefetchTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContentPrefetchTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContentPrefetchTriggerFactory>
    {
        IContentPrefetchTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IContentPrefetchTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICustomSystemEventTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICustomSystemEventTrigger>
    {
        ICustomSystemEventTrigger(std::nullptr_t = nullptr) noexcept {}
        ICustomSystemEventTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICustomSystemEventTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICustomSystemEventTriggerFactory>
    {
        ICustomSystemEventTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ICustomSystemEventTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceConnectionChangeTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceConnectionChangeTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IDeviceConnectionChangeTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IDeviceConnectionChangeTrigger(std::nullptr_t = nullptr) noexcept {}
        IDeviceConnectionChangeTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceConnectionChangeTriggerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceConnectionChangeTriggerStatics>
    {
        IDeviceConnectionChangeTriggerStatics(std::nullptr_t = nullptr) noexcept {}
        IDeviceConnectionChangeTriggerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceManufacturerNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceManufacturerNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IDeviceManufacturerNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IDeviceManufacturerNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IDeviceManufacturerNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceManufacturerNotificationTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceManufacturerNotificationTriggerFactory>
    {
        IDeviceManufacturerNotificationTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IDeviceManufacturerNotificationTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceServicingTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceServicingTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IDeviceServicingTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IDeviceServicingTrigger(std::nullptr_t = nullptr) noexcept {}
        IDeviceServicingTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceUseTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceUseTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IDeviceUseTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IDeviceUseTrigger(std::nullptr_t = nullptr) noexcept {}
        IDeviceUseTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeviceWatcherTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeviceWatcherTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IDeviceWatcherTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IDeviceWatcherTrigger(std::nullptr_t = nullptr) noexcept {}
        IDeviceWatcherTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEmailStoreNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEmailStoreNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IEmailStoreNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IEmailStoreNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IEmailStoreNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattCharacteristicNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IGattCharacteristicNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IGattCharacteristicNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattCharacteristicNotificationTrigger2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTrigger2>
    {
        IGattCharacteristicNotificationTrigger2(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTrigger2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattCharacteristicNotificationTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTriggerFactory>
    {
        IGattCharacteristicNotificationTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattCharacteristicNotificationTriggerFactory2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTriggerFactory2>
    {
        IGattCharacteristicNotificationTriggerFactory2(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTriggerFactory2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattServiceProviderTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderTrigger>
    {
        IGattServiceProviderTrigger(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattServiceProviderTriggerResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderTriggerResult>
    {
        IGattServiceProviderTriggerResult(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderTriggerResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGattServiceProviderTriggerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderTriggerStatics>
    {
        IGattServiceProviderTriggerStatics(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderTriggerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IGeovisitTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGeovisitTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IGeovisitTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IGeovisitTrigger(std::nullptr_t = nullptr) noexcept {}
        IGeovisitTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ILocationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ILocationTrigger(std::nullptr_t = nullptr) noexcept {}
        ILocationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILocationTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocationTriggerFactory>
    {
        ILocationTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ILocationTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMaintenanceTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMaintenanceTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IMaintenanceTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IMaintenanceTrigger(std::nullptr_t = nullptr) noexcept {}
        IMaintenanceTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMaintenanceTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMaintenanceTriggerFactory>
    {
        IMaintenanceTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IMaintenanceTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaProcessingTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaProcessingTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IMediaProcessingTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IMediaProcessingTrigger(std::nullptr_t = nullptr) noexcept {}
        IMediaProcessingTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INetworkOperatorHotspotAuthenticationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INetworkOperatorHotspotAuthenticationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::INetworkOperatorHotspotAuthenticationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        INetworkOperatorHotspotAuthenticationTrigger(std::nullptr_t = nullptr) noexcept {}
        INetworkOperatorHotspotAuthenticationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INetworkOperatorNotificationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INetworkOperatorNotificationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::INetworkOperatorNotificationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        INetworkOperatorNotificationTrigger(std::nullptr_t = nullptr) noexcept {}
        INetworkOperatorNotificationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INetworkOperatorNotificationTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INetworkOperatorNotificationTriggerFactory>
    {
        INetworkOperatorNotificationTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        INetworkOperatorNotificationTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhoneTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhoneTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IPhoneTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IPhoneTrigger(std::nullptr_t = nullptr) noexcept {}
        IPhoneTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhoneTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhoneTriggerFactory>
    {
        IPhoneTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IPhoneTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationTriggerFactory>
    {
        IPushNotificationTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRcsEndUserMessageAvailableTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRcsEndUserMessageAvailableTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IRcsEndUserMessageAvailableTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IRcsEndUserMessageAvailableTrigger(std::nullptr_t = nullptr) noexcept {}
        IRcsEndUserMessageAvailableTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRfcommConnectionTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommConnectionTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IRfcommConnectionTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IRfcommConnectionTrigger(std::nullptr_t = nullptr) noexcept {}
        IRfcommConnectionTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISecondaryAuthenticationFactorAuthenticationTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISecondaryAuthenticationFactorAuthenticationTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ISecondaryAuthenticationFactorAuthenticationTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ISecondaryAuthenticationFactorAuthenticationTrigger(std::nullptr_t = nullptr) noexcept {}
        ISecondaryAuthenticationFactorAuthenticationTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorDataThresholdTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThresholdTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ISensorDataThresholdTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ISensorDataThresholdTrigger(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThresholdTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISensorDataThresholdTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISensorDataThresholdTriggerFactory>
    {
        ISensorDataThresholdTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ISensorDataThresholdTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISmartCardTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISmartCardTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ISmartCardTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ISmartCardTrigger(std::nullptr_t = nullptr) noexcept {}
        ISmartCardTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISmartCardTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISmartCardTriggerFactory>
    {
        ISmartCardTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ISmartCardTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISmsMessageReceivedTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISmsMessageReceivedTriggerFactory>
    {
        ISmsMessageReceivedTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ISmsMessageReceivedTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISocketActivityTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISocketActivityTrigger>
    {
        ISocketActivityTrigger(std::nullptr_t = nullptr) noexcept {}
        ISocketActivityTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStorageLibraryChangeTrackerTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryChangeTrackerTriggerFactory>
    {
        IStorageLibraryChangeTrackerTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryChangeTrackerTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStorageLibraryContentChangedTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryContentChangedTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::IStorageLibraryContentChangedTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        IStorageLibraryContentChangedTrigger(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryContentChangedTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStorageLibraryContentChangedTriggerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorageLibraryContentChangedTriggerStatics>
    {
        IStorageLibraryContentChangedTriggerStatics(std::nullptr_t = nullptr) noexcept {}
        IStorageLibraryContentChangedTriggerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemCondition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemCondition>,
        impl::require<winrt::Windows::ApplicationModel::Background::ISystemCondition, winrt::Windows::ApplicationModel::Background::IBackgroundCondition>
    {
        ISystemCondition(std::nullptr_t = nullptr) noexcept {}
        ISystemCondition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemConditionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemConditionFactory>
    {
        ISystemConditionFactory(std::nullptr_t = nullptr) noexcept {}
        ISystemConditionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ISystemTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ISystemTrigger(std::nullptr_t = nullptr) noexcept {}
        ISystemTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISystemTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISystemTriggerFactory>
    {
        ISystemTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ISystemTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimeTrigger :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeTrigger>,
        impl::require<winrt::Windows::ApplicationModel::Background::ITimeTrigger, winrt::Windows::ApplicationModel::Background::IBackgroundTrigger>
    {
        ITimeTrigger(std::nullptr_t = nullptr) noexcept {}
        ITimeTrigger(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITimeTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITimeTriggerFactory>
    {
        ITimeTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        ITimeTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IToastNotificationActionTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationActionTriggerFactory>
    {
        IToastNotificationActionTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationActionTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IToastNotificationHistoryChangedTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToastNotificationHistoryChangedTriggerFactory>
    {
        IToastNotificationHistoryChangedTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IToastNotificationHistoryChangedTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUserNotificationChangedTriggerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserNotificationChangedTriggerFactory>
    {
        IUserNotificationChangedTriggerFactory(std::nullptr_t = nullptr) noexcept {}
        IUserNotificationChangedTriggerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
