// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_UI_WindowManagement_Preview_1_H
#define WINRT_Windows_UI_WindowManagement_Preview_1_H
#include "winrt/impl/Windows.UI.WindowManagement.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::WindowManagement::Preview
{
    struct WINRT_IMPL_EMPTY_BASES IWindowManagementPreview :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowManagementPreview>
    {
        IWindowManagementPreview(std::nullptr_t = nullptr) noexcept {}
        IWindowManagementPreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWindowManagementPreviewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWindowManagementPreviewStatics>
    {
        IWindowManagementPreviewStatics(std::nullptr_t = nullptr) noexcept {}
        IWindowManagementPreviewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
