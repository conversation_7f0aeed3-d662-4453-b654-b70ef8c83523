// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Security_Authentication_OnlineId_1_H
#define WINRT_Windows_Security_Authentication_OnlineId_1_H
#include "winrt/impl/Windows.Security.Authentication.OnlineId.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Authentication::OnlineId
{
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdAuthenticator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdAuthenticator>
    {
        IOnlineIdAuthenticator(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdAuthenticator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdServiceTicket :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdServiceTicket>
    {
        IOnlineIdServiceTicket(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdServiceTicket(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdServiceTicketRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdServiceTicketRequest>
    {
        IOnlineIdServiceTicketRequest(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdServiceTicketRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdServiceTicketRequestFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdServiceTicketRequestFactory>
    {
        IOnlineIdServiceTicketRequestFactory(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdServiceTicketRequestFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdSystemAuthenticatorForUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdSystemAuthenticatorForUser>
    {
        IOnlineIdSystemAuthenticatorForUser(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdSystemAuthenticatorForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdSystemAuthenticatorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdSystemAuthenticatorStatics>
    {
        IOnlineIdSystemAuthenticatorStatics(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdSystemAuthenticatorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdSystemIdentity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdSystemIdentity>
    {
        IOnlineIdSystemIdentity(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdSystemIdentity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IOnlineIdSystemTicketResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IOnlineIdSystemTicketResult>
    {
        IOnlineIdSystemTicketResult(std::nullptr_t = nullptr) noexcept {}
        IOnlineIdSystemTicketResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUserIdentity :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserIdentity>
    {
        IUserIdentity(std::nullptr_t = nullptr) noexcept {}
        IUserIdentity(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
