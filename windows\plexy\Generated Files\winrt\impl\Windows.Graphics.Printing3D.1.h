// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Graphics_Printing3D_1_H
#define WINRT_Windows_Graphics_Printing3D_1_H
#include "winrt/impl/Windows.Graphics.Printing3D.0.h"
WINRT_EXPORT namespace winrt::Windows::Graphics::Printing3D
{
    struct WINRT_IMPL_EMPTY_BASES IPrint3DManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DManager>
    {
        IPrint3DManager(std::nullptr_t = nullptr) noexcept {}
        IPrint3DManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DManagerStatics>
    {
        IPrint3DManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPrint3DManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTask :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTask>
    {
        IPrint3DTask(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTask(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTaskCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTaskCompletedEventArgs>
    {
        IPrint3DTaskCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTaskCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTaskRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTaskRequest>
    {
        IPrint3DTaskRequest(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTaskRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTaskRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTaskRequestedEventArgs>
    {
        IPrint3DTaskRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTaskRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTaskSourceChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTaskSourceChangedEventArgs>
    {
        IPrint3DTaskSourceChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTaskSourceChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrint3DTaskSourceRequestedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrint3DTaskSourceRequestedArgs>
    {
        IPrint3DTaskSourceRequestedArgs(std::nullptr_t = nullptr) noexcept {}
        IPrint3DTaskSourceRequestedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3D3MFPackage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3D3MFPackage>
    {
        IPrinting3D3MFPackage(std::nullptr_t = nullptr) noexcept {}
        IPrinting3D3MFPackage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3D3MFPackage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3D3MFPackage2>
    {
        IPrinting3D3MFPackage2(std::nullptr_t = nullptr) noexcept {}
        IPrinting3D3MFPackage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3D3MFPackageStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3D3MFPackageStatics>
    {
        IPrinting3D3MFPackageStatics(std::nullptr_t = nullptr) noexcept {}
        IPrinting3D3MFPackageStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DBaseMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DBaseMaterial>
    {
        IPrinting3DBaseMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DBaseMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DBaseMaterialGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DBaseMaterialGroup>
    {
        IPrinting3DBaseMaterialGroup(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DBaseMaterialGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DBaseMaterialGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DBaseMaterialGroupFactory>
    {
        IPrinting3DBaseMaterialGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DBaseMaterialGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DBaseMaterialStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DBaseMaterialStatics>
    {
        IPrinting3DBaseMaterialStatics(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DBaseMaterialStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DColorMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DColorMaterial>
    {
        IPrinting3DColorMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DColorMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DColorMaterial2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DColorMaterial2>
    {
        IPrinting3DColorMaterial2(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DColorMaterial2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DColorMaterialGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DColorMaterialGroup>
    {
        IPrinting3DColorMaterialGroup(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DColorMaterialGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DColorMaterialGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DColorMaterialGroupFactory>
    {
        IPrinting3DColorMaterialGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DColorMaterialGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DComponent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DComponent>
    {
        IPrinting3DComponent(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DComponent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DComponentWithMatrix :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DComponentWithMatrix>
    {
        IPrinting3DComponentWithMatrix(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DComponentWithMatrix(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DCompositeMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DCompositeMaterial>
    {
        IPrinting3DCompositeMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DCompositeMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DCompositeMaterialGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DCompositeMaterialGroup>
    {
        IPrinting3DCompositeMaterialGroup(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DCompositeMaterialGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DCompositeMaterialGroup2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DCompositeMaterialGroup2>
    {
        IPrinting3DCompositeMaterialGroup2(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DCompositeMaterialGroup2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DCompositeMaterialGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DCompositeMaterialGroupFactory>
    {
        IPrinting3DCompositeMaterialGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DCompositeMaterialGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DFaceReductionOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DFaceReductionOptions>
    {
        IPrinting3DFaceReductionOptions(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DFaceReductionOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMaterial>
    {
        IPrinting3DMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMesh :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMesh>
    {
        IPrinting3DMesh(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMesh(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMeshVerificationResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMeshVerificationResult>
    {
        IPrinting3DMeshVerificationResult(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMeshVerificationResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DModel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DModel>
    {
        IPrinting3DModel(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DModel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DModel2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DModel2>
    {
        IPrinting3DModel2(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DModel2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DModelTexture :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DModelTexture>
    {
        IPrinting3DModelTexture(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DModelTexture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMultiplePropertyMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMultiplePropertyMaterial>
    {
        IPrinting3DMultiplePropertyMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMultiplePropertyMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMultiplePropertyMaterialGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMultiplePropertyMaterialGroup>
    {
        IPrinting3DMultiplePropertyMaterialGroup(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMultiplePropertyMaterialGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DMultiplePropertyMaterialGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DMultiplePropertyMaterialGroupFactory>
    {
        IPrinting3DMultiplePropertyMaterialGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DMultiplePropertyMaterialGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DTexture2CoordMaterial :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DTexture2CoordMaterial>
    {
        IPrinting3DTexture2CoordMaterial(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DTexture2CoordMaterial(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DTexture2CoordMaterialGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DTexture2CoordMaterialGroup>
    {
        IPrinting3DTexture2CoordMaterialGroup(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DTexture2CoordMaterialGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DTexture2CoordMaterialGroup2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DTexture2CoordMaterialGroup2>
    {
        IPrinting3DTexture2CoordMaterialGroup2(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DTexture2CoordMaterialGroup2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DTexture2CoordMaterialGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DTexture2CoordMaterialGroupFactory>
    {
        IPrinting3DTexture2CoordMaterialGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DTexture2CoordMaterialGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPrinting3DTextureResource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPrinting3DTextureResource>
    {
        IPrinting3DTextureResource(std::nullptr_t = nullptr) noexcept {}
        IPrinting3DTextureResource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
