﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>DESKTOP-JLCOIL2</Machine>
    <WindowsUser>Alper</WindowsUser>
    <TargetPlatformIdentifier>UAP</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>7b6a8a97-8ee2-4352-a946-b71d249d3f1e</PackageIdentityName>
    <PackageIdentityPublisher>CN=Alper</PackageIdentityPublisher>
    <IntermediateOutputPath>C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath>
    </PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>C:\Users\<USER>\Desktop\plexy\windows\x64\Debug\plexy\AppX</LayoutDir>
    <RegisteredManifestChecksum>2D8C0C4522F4866BA0C324DF2BCE8C8436BD4A196E3282E7D7216817630CAA56</RegisteredManifestChecksum>
    <RegisteredPackageMoniker>7b6a8a97-8ee2-4352-a946-b71d249d3f1e_1.0.0.0_x64__zd0bznd1erft2</RegisteredPackageMoniker>
    <RegisteredUserModeAppID>7b6a8a97-8ee2-4352-a946-b71d249d3f1e_zd0bznd1erft2!App</RegisteredUserModeAppID>
    <RegisteredPackageID>7b6a8a97-8ee2-4352-a946-b71d249d3f1e</RegisteredPackageID>
    <RegisteredPackagePublisher>CN=Alper</RegisteredPackagePublisher>
    <RegisteredVersion>1.0.0.0</RegisteredVersion>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="C:\Users\<USER>\Desktop\plexy\windows\x64\Debug\plexy\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T08:38:14.634</Modified>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\x64\Debug\plexy\plexy.exe">
      <PackagePath>plexy.exe</PackagePath>
      <Modified>2025-07-12T08:38:10.426</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1264.42\build\..\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
      <Modified>2022-06-28T22:02:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9\build\native\..\..\build\native\uwp\x64\hermes.dll">
      <PackagePath>hermes.dll</PackagePath>
      <Modified>2025-05-02T17:17:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\target\x64\Debug\Microsoft.ReactNative\Microsoft.ReactNative.dll">
      <PackagePath>Microsoft.ReactNative.dll</PackagePath>
      <Modified>2025-07-12T08:35:40.148</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\target\x64\Debug\Microsoft.ReactNative\Microsoft.ReactNative.winmd">
      <PackagePath>Microsoft.ReactNative.winmd</PackagePath>
      <Modified>2025-07-12T08:17:52.559</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1264.42\lib\Microsoft.Web.WebView2.Core.winmd">
      <PackagePath>Microsoft.Web.WebView2.Core.winmd</PackagePath>
      <Modified>2022-06-28T22:02:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\x64\Debug\plexy\plexy.winmd">
      <PackagePath>plexy.winmd</PackagePath>
      <Modified>2025-07-12T08:36:17.170</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\target\x64\Debug\Microsoft.ReactNative\Microsoft.ReactNative\DevMenuControl.xbf">
      <PackagePath>Microsoft.ReactNative\DevMenuControl.xbf</PackagePath>
      <Modified>2025-07-12T08:34:56.051</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
      <Modified>2025-07-12T07:54:29.003</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.004</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.004</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.004</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.004</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.005</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\plexy\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-07-12T07:54:29.005</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.UniversalCRT.Debug\10.0.22621.0\\redist\Debug\X64\ucrtbased.dll">
      <PackagePath>ucrtbased.dll</PackagePath>
      <Modified>2025-03-11T19:29:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\lib\uap10.0\Microsoft.UI.Xaml.winmd">
      <PackagePath>Microsoft.UI.Xaml.winmd</PackagePath>
      <Modified>2022-07-15T03:26:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\plexy\windows\x64\Debug\plexy\resources.pri">
      <PackagePath>resources.pri</PackagePath>
      <Modified>2025-07-12T09:05:17.863</Modified>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>Win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x64\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\x64\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\arm\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>arm</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\arm\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\arm64\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\buildTransitive\..\tools\AppX\arm64\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>Win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x86\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x64\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\x64\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\arm\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>arm</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\arm\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\arm64\Release\Microsoft.UI.Xaml.2.8.appx">
      <Name>Microsoft.UI.Xaml.2.8</Name>
      <Version>8.2207.14002.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.UI.Xaml.2.8, MinVersion = 8.2207.14002.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0\build\..\tools\AppX\arm64\Release\Microsoft.UI.Xaml.2.8.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>ARM</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\ARM\Microsoft.VCLibs.ARM.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>ARM64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\ARM64\Microsoft.VCLibs.ARM64.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\x64\Microsoft.VCLibs.x64.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\x86\Microsoft.VCLibs.x86.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>ARM</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\ARM\Microsoft.VCLibs.ARM.Debug.14.00.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>ARM64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\ARM64\Microsoft.VCLibs.ARM64.Debug.14.00.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\x64\Microsoft.VCLibs.x64.Debug.14.00.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\x86\Microsoft.VCLibs.x86.Debug.14.00.appx</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>