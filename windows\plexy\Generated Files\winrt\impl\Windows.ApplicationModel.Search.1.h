// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Search_1_H
#define WINRT_Windows_ApplicationModel_Search_1_H
#include "winrt/impl/Windows.ApplicationModel.Search.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Search
{
    struct WINRT_IMPL_EMPTY_BASES ILocalContentSuggestionSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILocalContentSuggestionSettings>
    {
        ILocalContentSuggestionSettings(std::nullptr_t = nullptr) noexcept {}
        ILocalContentSuggestionSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchPaneQueryLinguisticDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchPaneQueryLinguisticDetails>
    {
        ISearchPaneQueryLinguisticDetails(std::nullptr_t = nullptr) noexcept {}
        ISearchPaneQueryLinguisticDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchQueryLinguisticDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchQueryLinguisticDetails>
    {
        ISearchQueryLinguisticDetails(std::nullptr_t = nullptr) noexcept {}
        ISearchQueryLinguisticDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchQueryLinguisticDetailsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchQueryLinguisticDetailsFactory>
    {
        ISearchQueryLinguisticDetailsFactory(std::nullptr_t = nullptr) noexcept {}
        ISearchQueryLinguisticDetailsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchSuggestionCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchSuggestionCollection>
    {
        ISearchSuggestionCollection(std::nullptr_t = nullptr) noexcept {}
        ISearchSuggestionCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchSuggestionsRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchSuggestionsRequest>
    {
        ISearchSuggestionsRequest(std::nullptr_t = nullptr) noexcept {}
        ISearchSuggestionsRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISearchSuggestionsRequestDeferral :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISearchSuggestionsRequestDeferral>
    {
        ISearchSuggestionsRequestDeferral(std::nullptr_t = nullptr) noexcept {}
        ISearchSuggestionsRequestDeferral(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
