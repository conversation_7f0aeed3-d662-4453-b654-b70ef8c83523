// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Controls_Primitives_2_H
#define WINRT_Microsoft_UI_Xaml_Controls_Primitives_2_H
#include "winrt/impl/Microsoft.UI.Xaml.Controls.1.h"
#include "winrt/impl/Windows.UI.Composition.1.h"
#include "winrt/impl/Windows.UI.Xaml.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.1.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.Primitives.1.h"
#include "winrt/impl/Windows.UI.Xaml.Data.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.Primitives.1.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    struct WINRT_IMPL_EMPTY_BASES AutoSuggestBoxHelper : winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper
    {
        AutoSuggestBoxHelper(std::nullptr_t) noexcept {}
        AutoSuggestBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IAutoSuggestBoxHelper(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto KeepInteriorCornersSquareProperty();
        static auto SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox, bool value);
        static auto GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::AutoSuggestBox const& autoSuggestBox);
    };
    struct WINRT_IMPL_EMPTY_BASES ColorPickerSlider : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider,
        impl::base<ColorPickerSlider, winrt::Windows::UI::Xaml::Controls::Slider, winrt::Windows::UI::Xaml::Controls::Primitives::RangeBase, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorPickerSlider, winrt::Windows::UI::Xaml::Controls::ISlider, winrt::Windows::UI::Xaml::Controls::ISlider2, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBase, winrt::Windows::UI::Xaml::Controls::Primitives::IRangeBaseOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorPickerSlider(std::nullptr_t) noexcept {}
        ColorPickerSlider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorPickerSlider(ptr, take_ownership_from_abi) {}
        ColorPickerSlider();
        [[nodiscard]] static auto ColorChannelProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ColorSpectrum : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum,
        impl::base<ColorSpectrum, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorSpectrum, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorSpectrum(std::nullptr_t) noexcept {}
        ColorSpectrum(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColorSpectrum(ptr, take_ownership_from_abi) {}
        ColorSpectrum();
        [[nodiscard]] static auto ColorProperty();
        [[nodiscard]] static auto HsvColorProperty();
        [[nodiscard]] static auto MinHueProperty();
        [[nodiscard]] static auto MaxHueProperty();
        [[nodiscard]] static auto MinSaturationProperty();
        [[nodiscard]] static auto MaxSaturationProperty();
        [[nodiscard]] static auto MinValueProperty();
        [[nodiscard]] static auto MaxValueProperty();
        [[nodiscard]] static auto ShapeProperty();
        [[nodiscard]] static auto ComponentsProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ColumnMajorUniformToLargestGridLayout : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout,
        impl::base<ColumnMajorUniformToLargestGridLayout, winrt::Microsoft::UI::Xaml::Controls::NonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::Layout, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColumnMajorUniformToLargestGridLayout, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayout, winrt::Microsoft::UI::Xaml::Controls::INonVirtualizingLayoutOverrides, winrt::Microsoft::UI::Xaml::Controls::ILayout, winrt::Microsoft::UI::Xaml::Controls::ILayoutProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColumnMajorUniformToLargestGridLayout(std::nullptr_t) noexcept {}
        ColumnMajorUniformToLargestGridLayout(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IColumnMajorUniformToLargestGridLayout(ptr, take_ownership_from_abi) {}
        ColumnMajorUniformToLargestGridLayout();
        [[nodiscard]] static auto MaxColumnsProperty();
        [[nodiscard]] static auto ColumnSpacingProperty();
        [[nodiscard]] static auto RowSpacingProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES ComboBoxHelper : winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper
    {
        ComboBoxHelper(std::nullptr_t) noexcept {}
        ComboBoxHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IComboBoxHelper(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto KeepInteriorCornersSquareProperty();
        static auto SetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox, bool value);
        static auto GetKeepInteriorCornersSquare(winrt::Windows::UI::Xaml::Controls::ComboBox const& comboBox);
    };
    struct WINRT_IMPL_EMPTY_BASES CommandBarFlyoutCommandBar : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar,
        impl::base<CommandBarFlyoutCommandBar, winrt::Windows::UI::Xaml::Controls::CommandBar, winrt::Windows::UI::Xaml::Controls::AppBar, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CommandBarFlyoutCommandBar, winrt::Windows::UI::Xaml::Controls::ICommandBar, winrt::Windows::UI::Xaml::Controls::ICommandBar2, winrt::Windows::UI::Xaml::Controls::ICommandBar3, winrt::Windows::UI::Xaml::Controls::IAppBar, winrt::Windows::UI::Xaml::Controls::IAppBar2, winrt::Windows::UI::Xaml::Controls::IAppBar3, winrt::Windows::UI::Xaml::Controls::IAppBar4, winrt::Windows::UI::Xaml::Controls::IAppBarOverrides, winrt::Windows::UI::Xaml::Controls::IAppBarOverrides3, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CommandBarFlyoutCommandBar(std::nullptr_t) noexcept {}
        CommandBarFlyoutCommandBar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBar(ptr, take_ownership_from_abi) {}
        CommandBarFlyoutCommandBar();
    };
    struct CommandBarFlyoutCommandBarAutomationProperties
    {
        CommandBarFlyoutCommandBarAutomationProperties() = delete;
        [[nodiscard]] static auto ControlTypeProperty();
        static auto GetControlType(winrt::Windows::UI::Xaml::UIElement const& element);
        static auto SetControlType(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Windows::UI::Xaml::Automation::Peers::AutomationControlType const& value);
    };
    struct WINRT_IMPL_EMPTY_BASES CommandBarFlyoutCommandBarTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings,
        impl::base<CommandBarFlyoutCommandBarTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CommandBarFlyoutCommandBarTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CommandBarFlyoutCommandBarTemplateSettings(std::nullptr_t) noexcept {}
        CommandBarFlyoutCommandBarTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICommandBarFlyoutCommandBarTemplateSettings(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CornerRadiusFilterConverter : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter,
        impl::base<CornerRadiusFilterConverter, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CornerRadiusFilterConverter, winrt::Windows::UI::Xaml::Data::IValueConverter, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CornerRadiusFilterConverter(std::nullptr_t) noexcept {}
        CornerRadiusFilterConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusFilterConverter(ptr, take_ownership_from_abi) {}
        CornerRadiusFilterConverter();
        [[nodiscard]] static auto FilterProperty();
        [[nodiscard]] static auto ScaleProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES CornerRadiusToThicknessConverter : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter,
        impl::base<CornerRadiusToThicknessConverter, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<CornerRadiusToThicknessConverter, winrt::Windows::UI::Xaml::Data::IValueConverter, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        CornerRadiusToThicknessConverter(std::nullptr_t) noexcept {}
        CornerRadiusToThicknessConverter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::ICornerRadiusToThicknessConverter(ptr, take_ownership_from_abi) {}
        CornerRadiusToThicknessConverter();
        [[nodiscard]] static auto ConversionKindProperty();
        [[nodiscard]] static auto MultiplierProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBarPanel : winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel,
        impl::base<InfoBarPanel, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBarPanel, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBarPanel(std::nullptr_t) noexcept {}
        InfoBarPanel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IInfoBarPanel(ptr, take_ownership_from_abi) {}
        InfoBarPanel();
        [[nodiscard]] static auto HorizontalOrientationPaddingProperty();
        [[nodiscard]] static auto VerticalOrientationPaddingProperty();
        static auto SetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value);
        static auto GetHorizontalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object);
        [[nodiscard]] static auto HorizontalOrientationMarginProperty();
        static auto SetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object, winrt::Windows::UI::Xaml::Thickness const& value);
        static auto GetVerticalOrientationMargin(winrt::Windows::UI::Xaml::DependencyObject const& object);
        [[nodiscard]] static auto VerticalOrientationMarginProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES MonochromaticOverlayPresenter : winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter,
        impl::base<MonochromaticOverlayPresenter, winrt::Windows::UI::Xaml::Controls::Grid, winrt::Windows::UI::Xaml::Controls::Panel, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MonochromaticOverlayPresenter, winrt::Windows::UI::Xaml::Controls::IGrid, winrt::Windows::UI::Xaml::Controls::IGrid2, winrt::Windows::UI::Xaml::Controls::IGrid3, winrt::Windows::UI::Xaml::Controls::IGrid4, winrt::Windows::UI::Xaml::Controls::IPanel, winrt::Windows::UI::Xaml::Controls::IPanel2, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MonochromaticOverlayPresenter(std::nullptr_t) noexcept {}
        MonochromaticOverlayPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::IMonochromaticOverlayPresenter(ptr, take_ownership_from_abi) {}
        MonochromaticOverlayPresenter();
        [[nodiscard]] static auto SourceElementProperty();
        [[nodiscard]] static auto ReplacementColorProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemPresenter : winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter,
        impl::base<NavigationViewItemPresenter, winrt::Windows::UI::Xaml::Controls::ContentControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemPresenter, winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter2, winrt::Windows::UI::Xaml::Controls::IContentControl, winrt::Windows::UI::Xaml::Controls::IContentControl2, winrt::Windows::UI::Xaml::Controls::IContentControlOverrides, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemPresenter(std::nullptr_t) noexcept {}
        NavigationViewItemPresenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenter(ptr, take_ownership_from_abi) {}
        NavigationViewItemPresenter();
        [[nodiscard]] static auto IconProperty();
        [[nodiscard]] static auto TemplateSettingsProperty();
        [[nodiscard]] static auto InfoBadgeProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemPresenterTemplateSettings : winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings,
        impl::base<NavigationViewItemPresenterTemplateSettings, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemPresenterTemplateSettings, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemPresenterTemplateSettings(std::nullptr_t) noexcept {}
        NavigationViewItemPresenterTemplateSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::INavigationViewItemPresenterTemplateSettings(ptr, take_ownership_from_abi) {}
        NavigationViewItemPresenterTemplateSettings();
        [[nodiscard]] static auto IconWidthProperty();
        [[nodiscard]] static auto SmallerIconWidthProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewListView : winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView,
        impl::base<TabViewListView, winrt::Windows::UI::Xaml::Controls::ListView, winrt::Windows::UI::Xaml::Controls::ListViewBase, winrt::Windows::UI::Xaml::Controls::Primitives::Selector, winrt::Windows::UI::Xaml::Controls::ItemsControl, winrt::Windows::UI::Xaml::Controls::Control, winrt::Windows::UI::Xaml::FrameworkElement, winrt::Windows::UI::Xaml::UIElement, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabViewListView, winrt::Windows::UI::Xaml::Controls::IListView, winrt::Windows::UI::Xaml::Controls::IListViewBase, winrt::Windows::UI::Xaml::Controls::IListViewBase2, winrt::Windows::UI::Xaml::Controls::IListViewBase3, winrt::Windows::UI::Xaml::Controls::IListViewBase4, winrt::Windows::UI::Xaml::Controls::IListViewBase5, winrt::Windows::UI::Xaml::Controls::IListViewBase6, winrt::Windows::UI::Xaml::Controls::ISemanticZoomInformation, winrt::Windows::UI::Xaml::Controls::Primitives::ISelector, winrt::Windows::UI::Xaml::Controls::IItemsControl, winrt::Windows::UI::Xaml::Controls::IItemsControl2, winrt::Windows::UI::Xaml::Controls::IItemsControl3, winrt::Windows::UI::Xaml::Controls::IItemsControlOverrides, winrt::Windows::UI::Xaml::Controls::IItemContainerMapping, winrt::Windows::UI::Xaml::Controls::IControl, winrt::Windows::UI::Xaml::Controls::IControl2, winrt::Windows::UI::Xaml::Controls::IControl3, winrt::Windows::UI::Xaml::Controls::IControl4, winrt::Windows::UI::Xaml::Controls::IControl5, winrt::Windows::UI::Xaml::Controls::IControl7, winrt::Windows::UI::Xaml::Controls::IControlProtected, winrt::Windows::UI::Xaml::Controls::IControlOverrides, winrt::Windows::UI::Xaml::Controls::IControlOverrides6, winrt::Windows::UI::Xaml::IFrameworkElement, winrt::Windows::UI::Xaml::IFrameworkElement2, winrt::Windows::UI::Xaml::IFrameworkElement3, winrt::Windows::UI::Xaml::IFrameworkElement4, winrt::Windows::UI::Xaml::IFrameworkElement6, winrt::Windows::UI::Xaml::IFrameworkElement7, winrt::Windows::UI::Xaml::IFrameworkElementProtected7, winrt::Windows::UI::Xaml::IFrameworkElementOverrides, winrt::Windows::UI::Xaml::IFrameworkElementOverrides2, winrt::Windows::UI::Xaml::IUIElement, winrt::Windows::UI::Xaml::IUIElement2, winrt::Windows::UI::Xaml::IUIElement3, winrt::Windows::UI::Xaml::IUIElement4, winrt::Windows::UI::Xaml::IUIElement5, winrt::Windows::UI::Xaml::IUIElement7, winrt::Windows::UI::Xaml::IUIElement8, winrt::Windows::UI::Xaml::IUIElement9, winrt::Windows::UI::Xaml::IUIElement10, winrt::Windows::UI::Xaml::IUIElementOverrides, winrt::Windows::UI::Xaml::IUIElementOverrides7, winrt::Windows::UI::Xaml::IUIElementOverrides8, winrt::Windows::UI::Xaml::IUIElementOverrides9, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Composition::IVisualElement, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabViewListView(std::nullptr_t) noexcept {}
        TabViewListView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Controls::Primitives::ITabViewListView(ptr, take_ownership_from_abi) {}
        TabViewListView();
    };
}
#endif
