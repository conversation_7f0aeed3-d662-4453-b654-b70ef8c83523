import React from 'react';
import {
  StatusBar,
  StyleSheet,
  Text,
  useColorScheme,
  View,
} from 'react-native';

function App() {
  const isDarkMode = useColorScheme() === 'dark';

  const containerStyle = {
    backgroundColor: isDarkMode ? '#000' : '#fff',
  };

  const textStyle = {
    color: isDarkMode ? '#f0f0f0' : '#000',
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <Text style={[styles.text, textStyle]}>Merhaba Plexy!</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center', // dikey ortalama
    alignItems: 'center', // yatay ortalama
  },
  text: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});

export default App;
