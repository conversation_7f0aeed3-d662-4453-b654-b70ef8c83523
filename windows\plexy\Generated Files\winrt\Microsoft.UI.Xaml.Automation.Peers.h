// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Automation_Peers_H
#define WINRT_Microsoft_UI_Xaml_Automation_Peers_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.2.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.Primitives.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.2.h"
#include "winrt/impl/Microsoft.UI.Xaml.Automation.Peers.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IAnimatedVisualPlayerAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IBreadcrumbBarItemAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IColorPickerSliderAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IColorSpectrumAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IDropDownButtonAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::DropDownButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IExpanderAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::Expander const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IInfoBarAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::InfoBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::MenuBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarItemAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::NavigationView const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewItemAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_INumberBoxAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::NumberBox const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IPersonPictureAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::PersonPicture const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IPipsPagerAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::PipsPager const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IProgressBarAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ProgressBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IProgressRingAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ProgressRing const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IRadioButtonsAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::RadioButtons const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IRatingControlAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::RatingControl const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IRepeaterAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ISplitButtonAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::SplitButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TabView const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewItemAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TabViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITeachingTipAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TeachingTip const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_IToggleSplitButtonAutomationPeerFactory<D>::CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory)->CreateInstance(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemDataAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Windows::Foundation::IInspectable const& item, winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const& parent, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&item), *(void**)(&parent), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewListAutomationPeerFactory<D>::CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::TreeViewList const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory)->CreateInstanceWithOwner(*(void**)(&owner), *(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer{ value, take_ownership_from_abi };
    }
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::DropDownButton const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::Expander const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::InfoBar const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::MenuBar const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::NavigationView const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::NumberBox const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::PersonPicture const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::PipsPager const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ProgressBar const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ProgressRing const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::RadioButtons const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::RatingControl const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::SplitButton const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::TabView const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::TabViewItem const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::TeachingTip const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* item, void* parent, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&item), *reinterpret_cast<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const*>(&parent), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory> : produce_base<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstanceWithOwner(void* owner, void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer>(this->shim().CreateInstanceWithOwner(*reinterpret_cast<winrt::Microsoft::UI::Xaml::Controls::TreeViewList const*>(&owner), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Automation::Peers
{
    inline AnimatedVisualPlayerAutomationPeer::AnimatedVisualPlayerAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<AnimatedVisualPlayerAutomationPeer, IAnimatedVisualPlayerAutomationPeerFactory>([&](IAnimatedVisualPlayerAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline BreadcrumbBarItemAutomationPeer::BreadcrumbBarItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<BreadcrumbBarItemAutomationPeer, IBreadcrumbBarItemAutomationPeerFactory>([&](IBreadcrumbBarItemAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline ColorPickerSliderAutomationPeer::ColorPickerSliderAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ColorPickerSliderAutomationPeer, IColorPickerSliderAutomationPeerFactory>([&](IColorPickerSliderAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline ColorSpectrumAutomationPeer::ColorSpectrumAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ColorSpectrumAutomationPeer, IColorSpectrumAutomationPeerFactory>([&](IColorSpectrumAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline DropDownButtonAutomationPeer::DropDownButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::DropDownButton const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<DropDownButtonAutomationPeer, IDropDownButtonAutomationPeerFactory>([&](IDropDownButtonAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline ExpanderAutomationPeer::ExpanderAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Expander const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ExpanderAutomationPeer, IExpanderAutomationPeerFactory>([&](IExpanderAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline InfoBarAutomationPeer::InfoBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::InfoBar const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<InfoBarAutomationPeer, IInfoBarAutomationPeerFactory>([&](IInfoBarAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline MenuBarAutomationPeer::MenuBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::MenuBar const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<MenuBarAutomationPeer, IMenuBarAutomationPeerFactory>([&](IMenuBarAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline MenuBarItemAutomationPeer::MenuBarItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<MenuBarItemAutomationPeer, IMenuBarItemAutomationPeerFactory>([&](IMenuBarItemAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline NavigationViewAutomationPeer::NavigationViewAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NavigationView const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<NavigationViewAutomationPeer, INavigationViewAutomationPeerFactory>([&](INavigationViewAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline NavigationViewItemAutomationPeer::NavigationViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<NavigationViewItemAutomationPeer, INavigationViewItemAutomationPeerFactory>([&](INavigationViewItemAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline NumberBoxAutomationPeer::NumberBoxAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NumberBox const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<NumberBoxAutomationPeer, INumberBoxAutomationPeerFactory>([&](INumberBoxAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline PersonPictureAutomationPeer::PersonPictureAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::PersonPicture const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<PersonPictureAutomationPeer, IPersonPictureAutomationPeerFactory>([&](IPersonPictureAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline PipsPagerAutomationPeer::PipsPagerAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::PipsPager const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<PipsPagerAutomationPeer, IPipsPagerAutomationPeerFactory>([&](IPipsPagerAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline ProgressBarAutomationPeer::ProgressBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ProgressBar const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ProgressBarAutomationPeer, IProgressBarAutomationPeerFactory>([&](IProgressBarAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline ProgressRingAutomationPeer::ProgressRingAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ProgressRing const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ProgressRingAutomationPeer, IProgressRingAutomationPeerFactory>([&](IProgressRingAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline RadioButtonsAutomationPeer::RadioButtonsAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::RadioButtons const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RadioButtonsAutomationPeer, IRadioButtonsAutomationPeerFactory>([&](IRadioButtonsAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline RatingControlAutomationPeer::RatingControlAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::RatingControl const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RatingControlAutomationPeer, IRatingControlAutomationPeerFactory>([&](IRatingControlAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline RepeaterAutomationPeer::RepeaterAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<RepeaterAutomationPeer, IRepeaterAutomationPeerFactory>([&](IRepeaterAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline SplitButtonAutomationPeer::SplitButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::SplitButton const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<SplitButtonAutomationPeer, ISplitButtonAutomationPeerFactory>([&](ISplitButtonAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline TabViewAutomationPeer::TabViewAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TabView const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TabViewAutomationPeer, ITabViewAutomationPeerFactory>([&](ITabViewAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline TabViewItemAutomationPeer::TabViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TabViewItem const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TabViewItemAutomationPeer, ITabViewItemAutomationPeerFactory>([&](ITabViewItemAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline TeachingTipAutomationPeer::TeachingTipAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TeachingTip const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TeachingTipAutomationPeer, ITeachingTipAutomationPeerFactory>([&](ITeachingTipAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline ToggleSplitButtonAutomationPeer::ToggleSplitButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ToggleSplitButtonAutomationPeer, IToggleSplitButtonAutomationPeerFactory>([&](IToggleSplitButtonAutomationPeerFactory const& f) { return f.CreateInstance(owner, baseInterface, innerInterface); });
    }
    inline TreeViewItemAutomationPeer::TreeViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TreeViewItemAutomationPeer, ITreeViewItemAutomationPeerFactory>([&](ITreeViewItemAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    inline TreeViewItemDataAutomationPeer::TreeViewItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const& parent)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TreeViewItemDataAutomationPeer, ITreeViewItemDataAutomationPeerFactory>([&](ITreeViewItemDataAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(item, parent, baseInterface, innerInterface); });
    }
    inline TreeViewListAutomationPeer::TreeViewListAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TreeViewList const& owner)
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<TreeViewListAutomationPeer, ITreeViewListAutomationPeerFactory>([&](ITreeViewListAutomationPeerFactory const& f) { return f.CreateInstanceWithOwner(owner, baseInterface, innerInterface); });
    }
    template <typename D, typename... Interfaces>
    struct AnimatedVisualPlayerAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, AnimatedVisualPlayerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = AnimatedVisualPlayerAutomationPeer;
    protected:
        AnimatedVisualPlayerAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const& owner)
        {
            impl::call_factory<AnimatedVisualPlayerAutomationPeer, IAnimatedVisualPlayerAutomationPeerFactory>([&](IAnimatedVisualPlayerAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct BreadcrumbBarItemAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, BreadcrumbBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = BreadcrumbBarItemAutomationPeer;
    protected:
        BreadcrumbBarItemAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const& owner)
        {
            impl::call_factory<BreadcrumbBarItemAutomationPeer, IBreadcrumbBarItemAutomationPeerFactory>([&](IBreadcrumbBarItemAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ColorPickerSliderAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ColorPickerSliderAutomationPeer;
    protected:
        ColorPickerSliderAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner)
        {
            impl::call_factory<ColorPickerSliderAutomationPeer, IColorPickerSliderAutomationPeerFactory>([&](IColorPickerSliderAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
        using impl::consume_t<D, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<D, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    template <typename D, typename... Interfaces>
    struct ColorSpectrumAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ColorSpectrumAutomationPeer;
    protected:
        ColorSpectrumAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner)
        {
            impl::call_factory<ColorSpectrumAutomationPeer, IColorSpectrumAutomationPeerFactory>([&](IColorSpectrumAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct DropDownButtonAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, DropDownButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = DropDownButtonAutomationPeer;
    protected:
        DropDownButtonAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::DropDownButton const& owner)
        {
            impl::call_factory<DropDownButtonAutomationPeer, IDropDownButtonAutomationPeerFactory>([&](IDropDownButtonAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ExpanderAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ExpanderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ExpanderAutomationPeer;
    protected:
        ExpanderAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::Expander const& owner)
        {
            impl::call_factory<ExpanderAutomationPeer, IExpanderAutomationPeerFactory>([&](IExpanderAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct InfoBarAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, InfoBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = InfoBarAutomationPeer;
    protected:
        InfoBarAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::InfoBar const& owner)
        {
            impl::call_factory<InfoBarAutomationPeer, IInfoBarAutomationPeerFactory>([&](IInfoBarAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct MenuBarAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, MenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = MenuBarAutomationPeer;
    protected:
        MenuBarAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::MenuBar const& owner)
        {
            impl::call_factory<MenuBarAutomationPeer, IMenuBarAutomationPeerFactory>([&](IMenuBarAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct MenuBarItemAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, MenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = MenuBarItemAutomationPeer;
    protected:
        MenuBarItemAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const& owner)
        {
            impl::call_factory<MenuBarItemAutomationPeer, IMenuBarItemAutomationPeerFactory>([&](IMenuBarItemAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct NavigationViewAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, NavigationViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = NavigationViewAutomationPeer;
    protected:
        NavigationViewAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::NavigationView const& owner)
        {
            impl::call_factory<NavigationViewAutomationPeer, INavigationViewAutomationPeerFactory>([&](INavigationViewAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct NavigationViewItemAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, NavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = NavigationViewItemAutomationPeer;
    protected:
        NavigationViewItemAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const& owner)
        {
            impl::call_factory<NavigationViewItemAutomationPeer, INavigationViewItemAutomationPeerFactory>([&](INavigationViewItemAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct NumberBoxAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, NumberBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = NumberBoxAutomationPeer;
    protected:
        NumberBoxAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::NumberBox const& owner)
        {
            impl::call_factory<NumberBoxAutomationPeer, INumberBoxAutomationPeerFactory>([&](INumberBoxAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct PersonPictureAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, PersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = PersonPictureAutomationPeer;
    protected:
        PersonPictureAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::PersonPicture const& owner)
        {
            impl::call_factory<PersonPictureAutomationPeer, IPersonPictureAutomationPeerFactory>([&](IPersonPictureAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct PipsPagerAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, PipsPagerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = PipsPagerAutomationPeer;
    protected:
        PipsPagerAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::PipsPager const& owner)
        {
            impl::call_factory<PipsPagerAutomationPeer, IPipsPagerAutomationPeerFactory>([&](IPipsPagerAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ProgressBarAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ProgressBarAutomationPeer;
    protected:
        ProgressBarAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::ProgressBar const& owner)
        {
            impl::call_factory<ProgressBarAutomationPeer, IProgressBarAutomationPeerFactory>([&](IProgressBarAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
        using impl::consume_t<D, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<D, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    template <typename D, typename... Interfaces>
    struct ProgressRingAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ProgressRingAutomationPeer;
    protected:
        ProgressRingAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::ProgressRing const& owner)
        {
            impl::call_factory<ProgressRingAutomationPeer, IProgressRingAutomationPeerFactory>([&](IProgressRingAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
        using impl::consume_t<D, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<D, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    template <typename D, typename... Interfaces>
    struct RadioButtonsAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RadioButtonsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = RadioButtonsAutomationPeer;
    protected:
        RadioButtonsAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::RadioButtons const& owner)
        {
            impl::call_factory<RadioButtonsAutomationPeer, IRadioButtonsAutomationPeerFactory>([&](IRadioButtonsAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RatingControlAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = RatingControlAutomationPeer;
    protected:
        RatingControlAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::RatingControl const& owner)
        {
            impl::call_factory<RatingControlAutomationPeer, IRatingControlAutomationPeerFactory>([&](IRatingControlAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct RepeaterAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, RepeaterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = RepeaterAutomationPeer;
    protected:
        RepeaterAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const& owner)
        {
            impl::call_factory<RepeaterAutomationPeer, IRepeaterAutomationPeerFactory>([&](IRepeaterAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct SplitButtonAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, SplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = SplitButtonAutomationPeer;
    protected:
        SplitButtonAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::SplitButton const& owner)
        {
            impl::call_factory<SplitButtonAutomationPeer, ISplitButtonAutomationPeerFactory>([&](ISplitButtonAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TabViewAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TabViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TabViewAutomationPeer;
    protected:
        TabViewAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::TabView const& owner)
        {
            impl::call_factory<TabViewAutomationPeer, ITabViewAutomationPeerFactory>([&](ITabViewAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TabViewItemAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TabViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TabViewItemAutomationPeer;
    protected:
        TabViewItemAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::TabViewItem const& owner)
        {
            impl::call_factory<TabViewItemAutomationPeer, ITabViewItemAutomationPeerFactory>([&](ITabViewItemAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TeachingTipAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TeachingTipAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TeachingTipAutomationPeer;
    protected:
        TeachingTipAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::TeachingTip const& owner)
        {
            impl::call_factory<TeachingTipAutomationPeer, ITeachingTipAutomationPeerFactory>([&](ITeachingTipAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct ToggleSplitButtonAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, ToggleSplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = ToggleSplitButtonAutomationPeer;
    protected:
        ToggleSplitButtonAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const& owner)
        {
            impl::call_factory<ToggleSplitButtonAutomationPeer, IToggleSplitButtonAutomationPeerFactory>([&](IToggleSplitButtonAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TreeViewItemAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TreeViewItemAutomationPeer;
    protected:
        TreeViewItemAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const& owner)
        {
            impl::call_factory<TreeViewItemAutomationPeer, ITreeViewItemAutomationPeerFactory>([&](ITreeViewItemAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TreeViewItemDataAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TreeViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TreeViewItemDataAutomationPeer;
    protected:
        TreeViewItemDataAutomationPeerT(winrt::Windows::Foundation::IInspectable const& item, winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const& parent)
        {
            impl::call_factory<TreeViewItemDataAutomationPeer, ITreeViewItemDataAutomationPeerFactory>([&](ITreeViewItemDataAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(item, parent, *this, this->m_inner); });
        }
    };
    template <typename D, typename... Interfaces>
    struct TreeViewListAutomationPeerT :
        implements<D, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IDropTargetProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>,
        impl::base<D, TreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverridesT<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8T<D>, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9T<D>
    {
        using composable = TreeViewListAutomationPeer;
    protected:
        TreeViewListAutomationPeerT(winrt::Microsoft::UI::Xaml::Controls::TreeViewList const& owner)
        {
            impl::call_factory<TreeViewListAutomationPeer, ITreeViewListAutomationPeerFactory>([&](ITreeViewListAutomationPeerFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstanceWithOwner(owner, *this, this->m_inner); });
        }
    };
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
