// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Lights_Effects_1_H
#define WINRT_Windows_Devices_Lights_Effects_1_H
#include "winrt/impl/Windows.Devices.Lights.Effects.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Lights::Effects
{
    struct WINRT_IMPL_EMPTY_BASES ILampArrayBitmapEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapEffect>
    {
        ILampArrayBitmapEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayBitmapEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapEffectFactory>
    {
        ILampArrayBitmapEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayBitmapRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBitmapRequestedEventArgs>
    {
        ILampArrayBitmapRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBitmapRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayBlinkEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBlinkEffect>
    {
        ILampArrayBlinkEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBlinkEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayBlinkEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayBlinkEffectFactory>
    {
        ILampArrayBlinkEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayBlinkEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayColorRampEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayColorRampEffect>
    {
        ILampArrayColorRampEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayColorRampEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayColorRampEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayColorRampEffectFactory>
    {
        ILampArrayColorRampEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayColorRampEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayCustomEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayCustomEffect>
    {
        ILampArrayCustomEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayCustomEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayCustomEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayCustomEffectFactory>
    {
        ILampArrayCustomEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArrayCustomEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffect>
    {
        ILampArrayEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayEffectPlaylist :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffectPlaylist>
    {
        ILampArrayEffectPlaylist(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffectPlaylist(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayEffectPlaylistStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayEffectPlaylistStatics>
    {
        ILampArrayEffectPlaylistStatics(std::nullptr_t = nullptr) noexcept {}
        ILampArrayEffectPlaylistStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArraySolidEffect :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArraySolidEffect>
    {
        ILampArraySolidEffect(std::nullptr_t = nullptr) noexcept {}
        ILampArraySolidEffect(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArraySolidEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArraySolidEffectFactory>
    {
        ILampArraySolidEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ILampArraySolidEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILampArrayUpdateRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILampArrayUpdateRequestedEventArgs>
    {
        ILampArrayUpdateRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ILampArrayUpdateRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
