// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_ReactNative_H
#define WINRT_Microsoft_ReactNative_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.230706.1"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.230706.1"
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Windows.Foundation.Collections.2.h"
#include "winrt/impl/Windows.Graphics.Effects.2.h"
#include "winrt/impl/Windows.UI.2.h"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.2.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.2.h"
#include "winrt/impl/Windows.UI.Xaml.Controls.2.h"
#include "winrt/impl/Windows.UI.Xaml.Input.2.h"
#include "winrt/impl/Windows.UI.Xaml.Markup.2.h"
#include "winrt/impl/Windows.UI.Xaml.Media.2.h"
#include "winrt/impl/Microsoft.ReactNative.2.h"
namespace winrt::impl
{
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::ExtendX() const
    {
        winrt::Microsoft::ReactNative::CanvasEdgeBehavior value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->get_ExtendX(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::ExtendX(winrt::Microsoft::ReactNative::CanvasEdgeBehavior const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->put_ExtendX(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::ExtendY() const
    {
        winrt::Microsoft::ReactNative::CanvasEdgeBehavior value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->get_ExtendY(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::ExtendY(winrt::Microsoft::ReactNative::CanvasEdgeBehavior const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->put_ExtendY(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->get_Source(&value));
        return winrt::Windows::Graphics::Effects::IGraphicsEffectSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IBorderEffect<D>::Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IBorderEffect)->put_Source(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICallInvoker<D>::InvokeAsync(winrt::Microsoft::ReactNative::CallFunc const& func) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICallInvoker)->InvokeAsync(*(void**)(&func)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICallInvoker<D>::InvokeSync(winrt::Microsoft::ReactNative::CallFunc const& func) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICallInvoker)->InvokeSync(*(void**)(&func)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IColorSourceEffect<D>::Color() const
    {
        winrt::Windows::UI::Color value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IColorSourceEffect)->get_Color(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IColorSourceEffect<D>::Color(winrt::Windows::UI::Color const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IColorSourceEffect)->put_Color(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Mode() const
    {
        winrt::Microsoft::ReactNative::CanvasComposite value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->get_Mode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Mode(winrt::Microsoft::ReactNative::CanvasComposite const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->put_Mode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Destination() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->get_Destination(&value));
        return winrt::Windows::Graphics::Effects::IGraphicsEffectSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Destination(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->put_Destination(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->get_Source(&value));
        return winrt::Windows::Graphics::Effects::IGraphicsEffectSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_ICompositeStepEffect<D>::Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ICompositeStepEffect)->put_Source(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::Cancel() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_Cancel(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::ConfigBundler() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_ConfigBundler(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::Inspector() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_Inspector(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::FastRefresh() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_FastRefresh(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::SamplingProfiler() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_SamplingProfiler(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::BreakOnNextLine() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_BreakOnNextLine(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::DirectDebug() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_DirectDebug(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::Reload() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_Reload(&value));
        return winrt::Windows::UI::Xaml::Controls::Button{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::FastRefreshText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_FastRefreshText(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::DirectDebugText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_DirectDebugText(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::DirectDebugDesc() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_DirectDebugDesc(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::BreakOnNextLineText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_BreakOnNextLineText(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::SamplingProfilerText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_SamplingProfilerText(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::SamplingProfilerDescText() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_SamplingProfilerDescText(&value));
        return winrt::Windows::UI::Xaml::Controls::TextBlock{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDevMenuControl<D>::SamplingProfilerIcon() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDevMenuControl)->get_SamplingProfilerIcon(&value));
        return winrt::Windows::UI::Xaml::Controls::FontIcon{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPeerFactory<D>::CreateInstance(winrt::Windows::UI::Xaml::FrameworkElement const& owner) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory)->CreateInstance(*(void**)(&owner), &value));
        return winrt::Microsoft::ReactNative::DynamicAutomationPeer{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityRoleProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityRoleProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityRoles const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityRole(*(void**)(&element), static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        winrt::Microsoft::ReactNative::AccessibilityRoles result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityRole(*(void**)(&element), reinterpret_cast<int32_t*>(&result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AriaRoleProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AriaRoleProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AriaRole const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAriaRole(*(void**)(&element), static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        winrt::Microsoft::ReactNative::AriaRole result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAriaRole(*(void**)(&element), reinterpret_cast<int32_t*>(&result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityStateSelectedProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityStateSelectedProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityStateSelected(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityStateSelected(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityStateDisabledProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityStateDisabledProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityStateDisabled(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityStateDisabled(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityStateCheckedProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityStateCheckedProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityStateChecked(*(void**)(&element), static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityStateChecked(*(void**)(&element), reinterpret_cast<int32_t*>(&result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityStateBusyProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityStateBusyProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityStateBusy(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityStateBusy(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityStateExpandedProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityStateExpandedProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityStateExpanded(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityStateExpanded(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityValueMinProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityValueMinProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element, double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityValueMin(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityValueMin(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityValueMaxProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityValueMaxProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element, double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityValueMax(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityValueMax(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityValueNowProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityValueNowProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element, double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityValueNow(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityValueNow(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityValueTextProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityValueTextProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element, param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityValueText(*(void**)(&element), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityValueText(*(void**)(&element), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityInvokeEventHandlerProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityInvokeEventHandlerProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityInvokeEventHandler(*(void**)(&element), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityInvokeEventHandler(*(void**)(&element), &result));
        return winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityActionsProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityActionsProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element, param::vector<winrt::Microsoft::ReactNative::AccessibilityAction> const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityActions(*(void**)(&element), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityActions(*(void**)(&element), &result));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::AccessibilityAction>{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::AccessibilityActionEventHandlerProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->get_AccessibilityActionEventHandlerProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::SetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityActionEventHandler const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->SetAccessibilityActionEventHandler(*(void**)(&element), *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>::GetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics)->GetAccessibilityActionEventHandler(*(void**)(&element), &result));
        return winrt::Microsoft::ReactNative::AccessibilityActionEventHandler{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IDynamicValueProviderFactory<D>::CreateInstance(winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer const& peer) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IDynamicValueProviderFactory)->CreateInstance(*(void**)(&peer), &value));
        return winrt::Microsoft::ReactNative::DynamicValueProvider{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::BlurAmount() const
    {
        float value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->get_BlurAmount(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::BlurAmount(float value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->put_BlurAmount(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::Optimization() const
    {
        winrt::Microsoft::ReactNative::EffectOptimization value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->get_Optimization(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::Optimization(winrt::Microsoft::ReactNative::EffectOptimization const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->put_Optimization(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::BorderMode() const
    {
        winrt::Microsoft::ReactNative::EffectBorderMode value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->get_BorderMode(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::BorderMode(winrt::Microsoft::ReactNative::EffectBorderMode const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->put_BorderMode(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::Source() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->get_Source(&value));
        return winrt::Windows::Graphics::Effects::IGraphicsEffectSource{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IGaussianBlurEffect<D>::Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IGaussianBlurEffect)->put_Source(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IHttpSettingsStatics<D>::SetDefaultUserAgent(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, param::hstring const& userAgent) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IHttpSettingsStatics)->SetDefaultUserAgent(*(void**)(&settings), *(void**)(&userAgent)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceCreatedEventArgs<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs)->get_Context(&value));
        return winrt::Microsoft::ReactNative::IReactContext{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceCreatedEventArgs<D>::RuntimeHandle() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs)->get_RuntimeHandle(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceDestroyedEventArgs<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs)->get_Context(&value));
        return winrt::Microsoft::ReactNative::IReactContext{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceLoadedEventArgs<D>::Context() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs)->get_Context(&value));
        return winrt::Microsoft::ReactNative::IReactContext{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceLoadedEventArgs<D>::Failed() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs)->get_Failed(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IInstanceLoadedEventArgs<D>::RuntimeHandle() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs)->get_RuntimeHandle(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::ValueType() const
    {
        winrt::Microsoft::ReactNative::JSValueType value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->get_ValueType(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetNextObjectProperty(hstring& propertyName) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetNextObjectProperty(impl::bind_out(propertyName), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetNextArrayItem() const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetNextArrayItem(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetString() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetString(&result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetBoolean() const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetBoolean(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetInt64() const
    {
        int64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetInt64(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueReader<D>::GetDouble() const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueReader)->GetDouble(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteNull() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteNull());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteBoolean(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteBoolean(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteInt64(int64_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteInt64(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteDouble(double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteDouble(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteString(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteString(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteObjectBegin() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteObjectBegin());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WritePropertyName(param::hstring const& name) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WritePropertyName(*(void**)(&name)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteObjectEnd() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteObjectEnd());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteArrayBegin() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteArrayBegin());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJSValueWriter<D>::WriteArrayEnd() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJSValueWriter)->WriteArrayEnd());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiByteBuffer<D>::Size() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiByteBuffer)->get_Size(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiByteBuffer<D>::GetData(winrt::Microsoft::ReactNative::JsiByteArrayUser const& useBytes) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiByteBuffer)->GetData(*(void**)(&useBytes)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiError<D>::ErrorType() const
    {
        winrt::Microsoft::ReactNative::JsiErrorType value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiError)->get_ErrorType(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiError<D>::ErrorDetails() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiError)->get_ErrorDetails(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiError<D>::Message() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiError)->get_Message(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiError<D>::Stack() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiError)->get_Stack(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiError<D>::Value() const
    {
        winrt::Microsoft::ReactNative::JsiValueRef value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiError)->get_Value(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiHostObject<D>::GetProperty(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiHostObject)->GetProperty(*(void**)(&runtime), impl::bind_in(propertyId), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiHostObject<D>::SetProperty(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiValueRef const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiHostObject)->SetProperty(*(void**)(&runtime), impl::bind_in(propertyId), impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiHostObject<D>::GetPropertyIds(winrt::Microsoft::ReactNative::JsiRuntime const& runtime) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiHostObject)->GetPropertyIds(*(void**)(&runtime), &result));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::JsiPropertyIdRef>{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::EvaluateJavaScript(winrt::Microsoft::ReactNative::IJsiByteBuffer const& buffer, param::hstring const& sourceUrl) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->EvaluateJavaScript(*(void**)(&buffer), *(void**)(&sourceUrl), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PrepareJavaScript(winrt::Microsoft::ReactNative::IJsiByteBuffer const& buffer, param::hstring const& sourceUrl) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PrepareJavaScript(*(void**)(&buffer), *(void**)(&sourceUrl), &result));
        return winrt::Microsoft::ReactNative::JsiPreparedJavaScript{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::EvaluatePreparedJavaScript(winrt::Microsoft::ReactNative::JsiPreparedJavaScript const& js) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->EvaluatePreparedJavaScript(*(void**)(&js), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::DrainMicrotasks(int32_t maxMicrotasksHint) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->DrainMicrotasks(maxMicrotasksHint, &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::QueueMicrotask(winrt::Microsoft::ReactNative::JsiObjectRef const& callback) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->QueueMicrotask(impl::bind_in(callback)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::Global() const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->get_Global(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::Description() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->get_Description(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsInspectable() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->get_IsInspectable(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CloneSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const
    {
        winrt::Microsoft::ReactNative::JsiSymbolRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CloneSymbol(impl::bind_in(symbol), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CloneBigInt(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const
    {
        winrt::Microsoft::ReactNative::JsiBigIntRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CloneBigInt(impl::bind_in(bigInt), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CloneString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const
    {
        winrt::Microsoft::ReactNative::JsiStringRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CloneString(impl::bind_in(str), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CloneObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CloneObject(impl::bind_in(obj), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ClonePropertyId(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ClonePropertyId(impl::bind_in(propertyId), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreatePropertyId(param::hstring const& name) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreatePropertyId(*(void**)(&name), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreatePropertyIdFromAscii(array_view<uint8_t const> ascii) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreatePropertyIdFromAscii(ascii.size(), get_abi(ascii), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreatePropertyIdFromUtf8(array_view<uint8_t const> utf8) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreatePropertyIdFromUtf8(utf8.size(), get_abi(utf8), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreatePropertyIdFromString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreatePropertyIdFromString(impl::bind_in(str), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreatePropertyIdFromSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& sym) const
    {
        winrt::Microsoft::ReactNative::JsiPropertyIdRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreatePropertyIdFromSymbol(impl::bind_in(sym), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PropertyIdToString(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PropertyIdToString(impl::bind_in(propertyId), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PropertyIdToUtf8(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PropertyIdToUtf8(impl::bind_in(propertyId), *(void**)(&useUtf8String)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PropertyIdEquals(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& left, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& right) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PropertyIdEquals(impl::bind_in(left), impl::bind_in(right), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SymbolToString(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SymbolToString(impl::bind_in(symbol), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SymbolToUtf8(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SymbolToUtf8(impl::bind_in(symbol), *(void**)(&useUtf8String)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateString(param::hstring const& value) const
    {
        winrt::Microsoft::ReactNative::JsiStringRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateString(*(void**)(&value), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateStringFromAscii(array_view<uint8_t const> ascii) const
    {
        winrt::Microsoft::ReactNative::JsiStringRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateStringFromAscii(ascii.size(), get_abi(ascii), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateStringFromUtf8(array_view<uint8_t const> utf8) const
    {
        winrt::Microsoft::ReactNative::JsiStringRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateStringFromUtf8(utf8.size(), get_abi(utf8), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::StringToString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->StringToString(impl::bind_in(str), &result));
        return hstring{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::StringToUtf8(winrt::Microsoft::ReactNative::JsiStringRef const& str, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->StringToUtf8(impl::bind_in(str), *(void**)(&useUtf8String)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateValueFromJson(param::hstring const& json) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateValueFromJson(*(void**)(&json), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateValueFromJsonUtf8(array_view<uint8_t const> json) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateValueFromJsonUtf8(json.size(), get_abi(json), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateBigIntFromInt64(int64_t val) const
    {
        winrt::Microsoft::ReactNative::JsiBigIntRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateBigIntFromInt64(val, put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateBigIntFromUint64(uint64_t val) const
    {
        winrt::Microsoft::ReactNative::JsiBigIntRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateBigIntFromUint64(val, put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::BigintIsInt64(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->BigintIsInt64(impl::bind_in(bigInt), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::BigintIsUint64(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->BigintIsUint64(impl::bind_in(bigInt), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::Truncate(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const
    {
        uint64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->Truncate(impl::bind_in(bigInt), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::BigintToString(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt, int32_t val) const
    {
        winrt::Microsoft::ReactNative::JsiStringRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->BigintToString(impl::bind_in(bigInt), val, put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateArrayBuffer(winrt::Microsoft::ReactNative::JsiObjectRef const& buffer) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateArrayBuffer(impl::bind_in(buffer), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateObject() const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateObject(put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateObjectWithHostObject(winrt::Microsoft::ReactNative::IJsiHostObject const& hostObject) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateObjectWithHostObject(*(void**)(&hostObject), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetHostObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetHostObject(impl::bind_in(obj), &result));
        return winrt::Microsoft::ReactNative::IJsiHostObject{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetHostFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& func) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetHostFunction(impl::bind_in(func), &result));
        return winrt::Microsoft::ReactNative::JsiHostFunction{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetProperty(impl::bind_in(obj), impl::bind_in(propertyId), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::HasProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->HasProperty(impl::bind_in(obj), impl::bind_in(propertyId), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SetProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiValueRef const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SetProperty(impl::bind_in(obj), impl::bind_in(propertyId), impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetPropertyIdArray(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetPropertyIdArray(impl::bind_in(obj), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsArray(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->IsArray(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsArrayBuffer(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->IsArrayBuffer(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->IsFunction(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsHostObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->IsHostObject(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::IsHostFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->IsHostFunction(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateWeakObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        winrt::Microsoft::ReactNative::JsiWeakObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateWeakObject(impl::bind_in(obj), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::LockWeakObject(winrt::Microsoft::ReactNative::JsiWeakObjectRef const& weakObject) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->LockWeakObject(impl::bind_in(weakObject), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateArray(uint32_t size) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateArray(size, put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetArraySize(winrt::Microsoft::ReactNative::JsiObjectRef const& arr) const
    {
        uint32_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetArraySize(impl::bind_in(arr), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetArrayBufferSize(winrt::Microsoft::ReactNative::JsiObjectRef const& arrayBuffer) const
    {
        uint32_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetArrayBufferSize(impl::bind_in(arrayBuffer), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetArrayBufferData(winrt::Microsoft::ReactNative::JsiObjectRef const& arrayBuffer, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useArrayBytes) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetArrayBufferData(impl::bind_in(arrayBuffer), *(void**)(&useArrayBytes)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetValueAtIndex(winrt::Microsoft::ReactNative::JsiObjectRef const& arr, uint32_t index) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetValueAtIndex(impl::bind_in(arr), index, put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SetValueAtIndex(winrt::Microsoft::ReactNative::JsiObjectRef const& arr, uint32_t index, winrt::Microsoft::ReactNative::JsiValueRef const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SetValueAtIndex(impl::bind_in(arr), index, impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CreateFunctionFromHostFunction(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& funcName, uint32_t paramCount, winrt::Microsoft::ReactNative::JsiHostFunction const& hostFunc) const
    {
        winrt::Microsoft::ReactNative::JsiObjectRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CreateFunctionFromHostFunction(impl::bind_in(funcName), paramCount, *(void**)(&hostFunc), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::Call(winrt::Microsoft::ReactNative::JsiObjectRef const& func, winrt::Microsoft::ReactNative::JsiValueRef const& thisArg, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->Call(impl::bind_in(func), impl::bind_in(thisArg), args.size(), get_abi(args), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::CallAsConstructor(winrt::Microsoft::ReactNative::JsiObjectRef const& func, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->CallAsConstructor(impl::bind_in(func), args.size(), get_abi(args), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PushScope() const
    {
        winrt::Microsoft::ReactNative::JsiScopeState result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PushScope(put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::PopScope(winrt::Microsoft::ReactNative::JsiScopeState const& scopeState) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->PopScope(impl::bind_in(scopeState)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SymbolStrictEquals(winrt::Microsoft::ReactNative::JsiSymbolRef const& left, winrt::Microsoft::ReactNative::JsiSymbolRef const& right) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SymbolStrictEquals(impl::bind_in(left), impl::bind_in(right), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::BigIntStrictEquals(winrt::Microsoft::ReactNative::JsiBigIntRef const& left, winrt::Microsoft::ReactNative::JsiBigIntRef const& right) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->BigIntStrictEquals(impl::bind_in(left), impl::bind_in(right), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::StringStrictEquals(winrt::Microsoft::ReactNative::JsiStringRef const& left, winrt::Microsoft::ReactNative::JsiStringRef const& right) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->StringStrictEquals(impl::bind_in(left), impl::bind_in(right), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ObjectStrictEquals(winrt::Microsoft::ReactNative::JsiObjectRef const& left, winrt::Microsoft::ReactNative::JsiObjectRef const& right) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ObjectStrictEquals(impl::bind_in(left), impl::bind_in(right), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::InstanceOf(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiObjectRef const& constructor) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->InstanceOf(impl::bind_in(obj), impl::bind_in(constructor), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ReleaseSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ReleaseSymbol(impl::bind_in(symbol)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ReleaseBigInt(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ReleaseBigInt(impl::bind_in(bigInt)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ReleaseString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ReleaseString(impl::bind_in(str)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ReleaseObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ReleaseObject(impl::bind_in(obj)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::ReleasePropertyId(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->ReleasePropertyId(impl::bind_in(propertyId)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetAndClearError() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetAndClearError(&result));
        return winrt::Microsoft::ReactNative::JsiError{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SetError(winrt::Microsoft::ReactNative::JsiErrorType const& errorType, param::hstring const& errorDetails, winrt::Microsoft::ReactNative::JsiValueRef const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SetError(static_cast<int32_t>(errorType), *(void**)(&errorDetails), impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::HasNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        bool result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->HasNativeState(impl::bind_in(obj), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::GetNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->GetNativeState(impl::bind_in(obj), &result));
        return winrt::Microsoft::ReactNative::IReactNonAbiValue{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntime<D>::SetNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::IReactNonAbiValue const& state) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntime)->SetNativeState(impl::bind_in(obj), *(void**)(&state)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IJsiRuntimeStatics<D>::MakeChakraRuntime() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IJsiRuntimeStatics)->MakeChakraRuntime(&result));
        return winrt::Microsoft::ReactNative::JsiRuntime{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_ILayoutService<D>::ApplyLayoutForAllNodes() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ILayoutService)->ApplyLayoutForAllNodes());
    }
    template <typename D> auto consume_Microsoft_ReactNative_ILayoutService<D>::ApplyLayout(int64_t reactTag, float width, float height) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ILayoutService)->ApplyLayout(reactTag, width, height));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ILayoutService<D>::IsInBatch() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ILayoutService)->get_IsInBatch(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_ILayoutService<D>::MarkDirty(int64_t reactTag) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ILayoutService)->MarkDirty(reactTag));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ILayoutServiceStatics<D>::FromContext(winrt::Microsoft::ReactNative::IReactContext const& context) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ILayoutServiceStatics)->FromContext(*(void**)(&context), &result));
        return winrt::Microsoft::ReactNative::LayoutService{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetMatchAndroidAndIOSStretchBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetMatchAndroidAndIOSStretchBehavior(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetUseWebFlexBasisBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetUseWebFlexBasisBehavior(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetAcceptSelfSigned(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetAcceptSelfSigned(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetBackHandlerKind(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, winrt::Microsoft::ReactNative::BackNavigationHandlerKind const& kind) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetBackHandlerKind(*(void**)(&settings), static_cast<int32_t>(kind)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetMapWindowDeactivatedToAppStateInactive(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetMapWindowDeactivatedToAppStateInactive(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetSuppressWindowFocusOnViewFocus(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetSuppressWindowFocusOnViewFocus(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>::SetUseRuntimeScheduler(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IQuirkSettingsStatics)->SetUseRuntimeScheduler(*(void**)(&settings), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::InstanceSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_InstanceSettings(&value));
        return winrt::Microsoft::ReactNative::ReactInstanceSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::InstanceSettings(winrt::Microsoft::ReactNative::ReactInstanceSettings const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->put_InstanceSettings(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::PackageProviders() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_PackageProviders(&value));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::Host() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_Host(&value));
        return winrt::Microsoft::ReactNative::ReactNativeHost{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::UseDeveloperSupport() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_UseDeveloperSupport(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::UseDeveloperSupport(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->put_UseDeveloperSupport(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::JavaScriptBundleFile() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_JavaScriptBundleFile(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::JavaScriptBundleFile(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->put_JavaScriptBundleFile(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::BundleAppId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->get_BundleAppId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplication<D>::BundleAppId(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplication)->put_BundleAppId(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactApplicationFactory<D>::CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactApplicationFactory)->CreateInstance(*(void**)(&baseInterface), impl::bind_out(innerInterface), &value));
        return winrt::Microsoft::ReactNative::ReactApplication{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::SettingsSnapshot() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_SettingsSnapshot(&value));
        return winrt::Microsoft::ReactNative::IReactSettingsSnapshot{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::Properties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_Properties(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyBag{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::Notifications() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_Notifications(&value));
        return winrt::Microsoft::ReactNative::IReactNotificationService{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::UIDispatcher() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_UIDispatcher(&value));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::JSDispatcher() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_JSDispatcher(&value));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::JSRuntime() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_JSRuntime(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::CallInvoker() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_CallInvoker(&value));
        return winrt::Microsoft::ReactNative::CallInvoker{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::DispatchEvent(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& eventDataArgWriter) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->DispatchEvent(*(void**)(&view), *(void**)(&eventName), *(void**)(&eventDataArgWriter)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::CallJSFunction(param::hstring const& moduleName, param::hstring const& methodName, winrt::Microsoft::ReactNative::JSValueArgWriter const& paramsArgWriter) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->CallJSFunction(*(void**)(&moduleName), *(void**)(&methodName), *(void**)(&paramsArgWriter)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::EmitJSEvent(param::hstring const& eventEmitterName, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& paramsArgWriter) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->EmitJSEvent(*(void**)(&eventEmitterName), *(void**)(&eventName), *(void**)(&paramsArgWriter)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactContext<D>::LoadingState() const
    {
        winrt::Microsoft::ReactNative::LoadingState value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactContext)->get_LoadingState(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::SetUIBatchCompleteCallback(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::UIBatchCompleteCallback const& xamlRoot) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->SetUIBatchCompleteCallback(*(void**)(&properties), *(void**)(&xamlRoot)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::MakeViewHost(winrt::Microsoft::ReactNative::ReactNativeHost const& host, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->MakeViewHost(*(void**)(&host), *(void**)(&viewOptions), &result));
        return winrt::Microsoft::ReactNative::IReactViewHost{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::PostToUIBatchingQueue(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->PostToUIBatchingQueue(*(void**)(&context), *(void**)(&callback)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::SetPlatformNameOverride(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, param::hstring const& platformName) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->SetPlatformNameOverride(*(void**)(&properties), *(void**)(&platformName)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::GetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        uint64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->GetTopLevelWindowId(*(void**)(&properties), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::SetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowId) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->SetTopLevelWindowId(*(void**)(&properties), windowId));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>::SetTimerFactory(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::TimerFactory const& timerFactory) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactCoreInjectionStatics)->SetTimerFactory(*(void**)(&properties), *(void**)(&timerFactory)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcher<D>::HasThreadAccess() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcher)->get_HasThreadAccess(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcher<D>::Post(winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcher)->Post(*(void**)(&callback)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::CreateSerialDispatcher() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->CreateSerialDispatcher(&result));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::UIThreadDispatcher() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_UIThreadDispatcher(&value));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::UIDispatcherProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_UIDispatcherProperty(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::JSDispatcherProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_JSDispatcherProperty(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::JSDispatcherTaskStartingEventName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_JSDispatcherTaskStartingEventName(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::JSDispatcherIdleWaitStartingEventName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_JSDispatcherIdleWaitStartingEventName(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>::JSDispatcherIdleWaitCompletedEventName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics)->get_JSDispatcherIdleWaitCompletedEventName(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::Properties() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_Properties(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyBag{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::Notifications() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_Notifications(&value));
        return winrt::Microsoft::ReactNative::IReactNotificationService{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::PackageProviders() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_PackageProviders(&value));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseDeveloperSupport() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UseDeveloperSupport(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseDeveloperSupport(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UseDeveloperSupport(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::JavaScriptBundleFile() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_JavaScriptBundleFile(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::JavaScriptBundleFile(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_JavaScriptBundleFile(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::BundleAppId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_BundleAppId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::BundleAppId(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_BundleAppId(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RequestDevBundle() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_RequestDevBundle(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RequestDevBundle(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_RequestDevBundle(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseWebDebugger() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UseWebDebugger(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseWebDebugger(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UseWebDebugger(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseFastRefresh() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UseFastRefresh(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseFastRefresh(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UseFastRefresh(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseLiveReload() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UseLiveReload(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseLiveReload(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UseLiveReload(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseDirectDebugger() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UseDirectDebugger(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UseDirectDebugger(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UseDirectDebugger(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerBreakOnNextLine() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_DebuggerBreakOnNextLine(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerBreakOnNextLine(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_DebuggerBreakOnNextLine(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableJITCompilation() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_EnableJITCompilation(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableJITCompilation(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_EnableJITCompilation(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableByteCodeCaching() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_EnableByteCodeCaching(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableByteCodeCaching(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_EnableByteCodeCaching(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableDefaultCrashHandler() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_EnableDefaultCrashHandler(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableDefaultCrashHandler(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_EnableDefaultCrashHandler(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableDeveloperMenu() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_EnableDeveloperMenu(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::EnableDeveloperMenu(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_EnableDeveloperMenu(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::ByteCodeFileUri() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_ByteCodeFileUri(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::ByteCodeFileUri(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_ByteCodeFileUri(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebugBundlePath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_DebugBundlePath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebugBundlePath(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_DebugBundlePath(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::BundleRootPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_BundleRootPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::BundleRootPath(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_BundleRootPath(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerPort() const
    {
        uint16_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_DebuggerPort(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerPort(uint16_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_DebuggerPort(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerRuntimeName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_DebuggerRuntimeName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::DebuggerRuntimeName(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_DebuggerRuntimeName(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RedBoxHandler() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_RedBoxHandler(&value));
        return winrt::Microsoft::ReactNative::IRedBoxHandler{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RedBoxHandler(winrt::Microsoft::ReactNative::IRedBoxHandler const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_RedBoxHandler(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::NativeLogger() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_NativeLogger(&value));
        return winrt::Microsoft::ReactNative::LogHandler{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::NativeLogger(winrt::Microsoft::ReactNative::LogHandler const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_NativeLogger(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UIDispatcher() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_UIDispatcher(&value));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::UIDispatcher(winrt::Microsoft::ReactNative::IReactDispatcher const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_UIDispatcher(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::SourceBundleHost() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_SourceBundleHost(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::SourceBundleHost(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_SourceBundleHost(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::SourceBundlePort() const
    {
        uint16_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_SourceBundlePort(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::SourceBundlePort(uint16_t value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_SourceBundlePort(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RequestInlineSourceMap() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_RequestInlineSourceMap(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::RequestInlineSourceMap(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_RequestInlineSourceMap(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::JSIEngineOverride() const
    {
        winrt::Microsoft::ReactNative::JSIEngine value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->get_JSIEngineOverride(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::JSIEngineOverride(winrt::Microsoft::ReactNative::JSIEngine const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->put_JSIEngineOverride(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceCreated(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->add_InstanceCreated(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceCreated(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, InstanceCreated_revoker>(this, InstanceCreated(handler));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceCreated(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->remove_InstanceCreated(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceLoaded(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->add_InstanceLoaded(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceLoaded(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, InstanceLoaded_revoker>(this, InstanceLoaded(handler));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceLoaded(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->remove_InstanceLoaded(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceDestroyed(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->add_InstanceDestroyed(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceDestroyed(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> const& handler) const
    {
        return impl::make_event_revoker<D, InstanceDestroyed_revoker>(this, InstanceDestroyed(handler));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactInstanceSettings<D>::InstanceDestroyed(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactInstanceSettings)->remove_InstanceDestroyed(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddInitializer(winrt::Microsoft::ReactNative::InitializerDelegate const& initializer) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddInitializer(*(void**)(&initializer)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddJsiInitializer(winrt::Microsoft::ReactNative::JsiInitializerDelegate const& initializer) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddJsiInitializer(*(void**)(&initializer)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddConstantProvider(winrt::Microsoft::ReactNative::ConstantProviderDelegate const& constantProvider) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddConstantProvider(*(void**)(&constantProvider)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddMethod(param::hstring const& name, winrt::Microsoft::ReactNative::MethodReturnType const& returnType, winrt::Microsoft::ReactNative::MethodDelegate const& method) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddMethod(*(void**)(&name), static_cast<int32_t>(returnType), *(void**)(&method)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddSyncMethod(param::hstring const& name, winrt::Microsoft::ReactNative::SyncMethodDelegate const& method) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddSyncMethod(*(void**)(&name), *(void**)(&method)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactModuleBuilder<D>::AddEventEmitter(param::hstring const& name, winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate const& emitter) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactModuleBuilder)->AddEventEmitter(*(void**)(&name), *(void**)(&emitter)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::PackageProviders() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->get_PackageProviders(&value));
        return winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::InstanceSettings() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->get_InstanceSettings(&value));
        return winrt::Microsoft::ReactNative::ReactInstanceSettings{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::InstanceSettings(winrt::Microsoft::ReactNative::ReactInstanceSettings const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->put_InstanceSettings(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::LoadInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->LoadInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::ReloadInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->ReloadInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHost<D>::UnloadInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHost)->UnloadInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNativeHostStatics<D>::FromContext(winrt::Microsoft::ReactNative::IReactContext const& reactContext) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNativeHostStatics)->FromContext(*(void**)(&reactContext), &result));
        return winrt::Microsoft::ReactNative::ReactNativeHost{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNonAbiValue<D>::GetPtr() const
    {
        int64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNonAbiValue)->GetPtr(&result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationArgs<D>::Subscription() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationArgs)->get_Subscription(&value));
        return winrt::Microsoft::ReactNative::IReactNotificationSubscription{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationArgs<D>::Data() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationArgs)->get_Data(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationService<D>::Subscribe(winrt::Microsoft::ReactNative::IReactPropertyName const& notificationName, winrt::Microsoft::ReactNative::IReactDispatcher const& dispatcher, winrt::Microsoft::ReactNative::ReactNotificationHandler const& handler) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationService)->Subscribe(*(void**)(&notificationName), *(void**)(&dispatcher), *(void**)(&handler), &result));
        return winrt::Microsoft::ReactNative::IReactNotificationSubscription{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationService<D>::SendNotification(winrt::Microsoft::ReactNative::IReactPropertyName const& notificationName, winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::Foundation::IInspectable const& data) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationService)->SendNotification(*(void**)(&notificationName), *(void**)(&sender), *(void**)(&data)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationServiceHelperStatics<D>::CreateNotificationService() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics)->CreateNotificationService(&result));
        return winrt::Microsoft::ReactNative::IReactNotificationService{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationSubscription<D>::NotificationService() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationSubscription)->get_NotificationService(&value));
        return winrt::Microsoft::ReactNative::IReactNotificationService{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationSubscription<D>::NotificationName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationSubscription)->get_NotificationName(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationSubscription<D>::Dispatcher() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationSubscription)->get_Dispatcher(&value));
        return winrt::Microsoft::ReactNative::IReactDispatcher{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationSubscription<D>::IsSubscribed() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationSubscription)->get_IsSubscribed(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactNotificationSubscription<D>::Unsubscribe() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactNotificationSubscription)->Unsubscribe());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPackageBuilder<D>::AddModule(param::hstring const& moduleName, winrt::Microsoft::ReactNative::ReactModuleProvider const& moduleProvider) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPackageBuilder)->AddModule(*(void**)(&moduleName), *(void**)(&moduleProvider)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPackageBuilder<D>::AddTurboModule(param::hstring const& moduleName, winrt::Microsoft::ReactNative::ReactModuleProvider const& moduleProvider) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPackageBuilder)->AddTurboModule(*(void**)(&moduleName), *(void**)(&moduleProvider)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPackageBuilder<D>::AddViewManager(param::hstring const& viewManagerName, winrt::Microsoft::ReactNative::ReactViewManagerProvider const& viewManagerProvider) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPackageBuilder)->AddViewManager(*(void**)(&viewManagerName), *(void**)(&viewManagerProvider)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPackageProvider<D>::CreatePackage(winrt::Microsoft::ReactNative::IReactPackageBuilder const& packageBuilder) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPackageProvider)->CreatePackage(*(void**)(&packageBuilder)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::Args() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->get_Args(&value));
        return winrt::Windows::UI::Xaml::Input::PointerRoutedEventArgs{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::Kind() const
    {
        winrt::Microsoft::ReactNative::PointerEventKind value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->get_Kind(reinterpret_cast<int32_t*>(&value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::Kind(winrt::Microsoft::ReactNative::PointerEventKind const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->put_Kind(static_cast<int32_t>(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::Target() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->get_Target(&value));
        return winrt::Windows::Foundation::IInspectable{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::Target(winrt::Windows::Foundation::IInspectable const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->put_Target(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::AllowUncaptured() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->get_AllowUncaptured(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPointerEventArgs<D>::AllowUncaptured(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPointerEventArgs)->put_AllowUncaptured(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBag<D>::Get(winrt::Microsoft::ReactNative::IReactPropertyName const& name) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBag)->Get(*(void**)(&name), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBag<D>::GetOrCreate(winrt::Microsoft::ReactNative::IReactPropertyName const& name, winrt::Microsoft::ReactNative::ReactCreatePropertyValue const& createValue) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBag)->GetOrCreate(*(void**)(&name), *(void**)(&createValue), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBag<D>::Set(winrt::Microsoft::ReactNative::IReactPropertyName const& name, winrt::Windows::Foundation::IInspectable const& value) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBag)->Set(*(void**)(&name), *(void**)(&value), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBag<D>::CopyFrom(winrt::Microsoft::ReactNative::IReactPropertyBag const& other) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBag)->CopyFrom(*(void**)(&other)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics<D>::GlobalNamespace() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics)->get_GlobalNamespace(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyNamespace{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics<D>::GetNamespace(param::hstring const& namespaceName) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics)->GetNamespace(*(void**)(&namespaceName), &result));
        return winrt::Microsoft::ReactNative::IReactPropertyNamespace{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics<D>::GetName(winrt::Microsoft::ReactNative::IReactPropertyNamespace const& ns, param::hstring const& localName) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics)->GetName(*(void**)(&ns), *(void**)(&localName), &result));
        return winrt::Microsoft::ReactNative::IReactPropertyName{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics<D>::CreatePropertyBag() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics)->CreatePropertyBag(&result));
        return winrt::Microsoft::ReactNative::IReactPropertyBag{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyName<D>::LocalName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyName)->get_LocalName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyName<D>::Namespace() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyName)->get_Namespace(&value));
        return winrt::Microsoft::ReactNative::IReactPropertyNamespace{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactPropertyNamespace<D>::NamespaceName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactPropertyNamespace)->get_NamespaceName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::ReactNativeHost() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->get_ReactNativeHost(&value));
        return winrt::Microsoft::ReactNative::ReactNativeHost{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::ReactNativeHost(winrt::Microsoft::ReactNative::ReactNativeHost const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->put_ReactNativeHost(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::ComponentName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->get_ComponentName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::ComponentName(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->put_ComponentName(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::InitialProps() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->get_InitialProps(&value));
        return winrt::Microsoft::ReactNative::JSValueArgWriter{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::InitialProps(winrt::Microsoft::ReactNative::JSValueArgWriter const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->put_InitialProps(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::IsPerspectiveEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->get_IsPerspectiveEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::IsPerspectiveEnabled(bool value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->put_IsPerspectiveEnabled(value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactRootView<D>::ReloadView() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactRootView)->ReloadView());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::UseWebDebugger() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_UseWebDebugger(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::UseFastRefresh() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_UseFastRefresh(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::UseDirectDebugger() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_UseDirectDebugger(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::DebuggerBreakOnNextLine() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_DebuggerBreakOnNextLine(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::DebuggerPort() const
    {
        uint16_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_DebuggerPort(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::DebugBundlePath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_DebugBundlePath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::BundleRootPath() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_BundleRootPath(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::SourceBundleHost() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_SourceBundleHost(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::SourceBundlePort() const
    {
        uint16_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_SourceBundlePort(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::RequestInlineSourceMap() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_RequestInlineSourceMap(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::JavaScriptBundleFile() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_JavaScriptBundleFile(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::BundleAppId() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_BundleAppId(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>::RequestDevBundle() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactSettingsSnapshot)->get_RequestDevBundle(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::ReloadViewInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->ReloadViewInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::ReloadViewInstanceWithOptions(winrt::Microsoft::ReactNative::ReactViewOptions const& options) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->ReloadViewInstanceWithOptions(*(void**)(&options), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::UnloadViewInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->UnloadViewInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::AttachViewInstance(winrt::Microsoft::ReactNative::IReactViewInstance const& viewInstance) const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->AttachViewInstance(*(void**)(&viewInstance), &operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::DetachViewInstance() const
    {
        void* operation{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->DetachViewInstance(&operation));
        return winrt::Windows::Foundation::IAsyncAction{ operation, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewHost<D>::ReactNativeHost() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewHost)->get_ReactNativeHost(&value));
        return winrt::Microsoft::ReactNative::ReactNativeHost{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewInstance<D>::InitRootView(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewInstance)->InitRootView(*(void**)(&context), *(void**)(&viewOptions)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewInstance<D>::UpdateRootView() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewInstance)->UpdateRootView());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewInstance<D>::UninitRootView() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewInstance)->UninitRootView());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewOptions<D>::ComponentName() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewOptions)->get_ComponentName(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewOptions<D>::ComponentName(param::hstring const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewOptions)->put_ComponentName(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewOptions<D>::InitialProps() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewOptions)->get_InitialProps(&value));
        return winrt::Microsoft::ReactNative::JSValueArgWriter{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IReactViewOptions<D>::InitialProps(winrt::Microsoft::ReactNative::JSValueArgWriter const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IReactViewOptions)->put_InitialProps(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>::File() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo)->get_File(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>::Method() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo)->get_Method(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>::Line() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo)->get_Line(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>::Column() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo)->get_Column(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>::Collapse() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo)->get_Collapse(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::Message() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_Message(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::OriginalMessage() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_OriginalMessage(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::ComponentStack() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_ComponentStack(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::Id() const
    {
        uint32_t value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_Id(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::Callstack() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_Callstack(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>::ExtraData() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxErrorInfo)->get_ExtraData(&value));
        return winrt::Microsoft::ReactNative::IJSValueReader{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxHandler<D>::ShowNewError(winrt::Microsoft::ReactNative::IRedBoxErrorInfo const& info, winrt::Microsoft::ReactNative::RedBoxErrorType const& type) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxHandler)->ShowNewError(*(void**)(&info), static_cast<int32_t>(type)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxHandler<D>::IsDevSupportEnabled() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxHandler)->get_IsDevSupportEnabled(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxHandler<D>::UpdateError(winrt::Microsoft::ReactNative::IRedBoxErrorInfo const& info) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxHandler)->UpdateError(*(void**)(&info)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxHandler<D>::DismissRedBox() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxHandler)->DismissRedBox());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IRedBoxHelperStatics<D>::CreateDefaultHandler(winrt::Microsoft::ReactNative::ReactNativeHost const& host) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IRedBoxHelperStatics)->CreateDefaultHandler(*(void**)(&host), &result));
        return winrt::Microsoft::ReactNative::IRedBoxHandler{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Interval() const
    {
        winrt::Windows::Foundation::TimeSpan value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->get_Interval(put_abi(value)));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Interval(winrt::Windows::Foundation::TimeSpan const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->put_Interval(impl::bind_in(value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Start() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->Start());
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Stop() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->Stop());
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Tick(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        winrt::event_token token{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->add_Tick(*(void**)(&handler), put_abi(token)));
        return token;
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Tick(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const
    {
        return impl::make_event_revoker<D, Tick_revoker>(this, Tick(handler));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimer<D>::Tick(winrt::event_token const& token) const noexcept
    {
        WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimer)->remove_Tick(impl::bind_in(token));
    }
    template <typename D> auto consume_Microsoft_ReactNative_ITimerStatics<D>::Create(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::ITimerStatics)->Create(*(void**)(&properties), &result));
        return winrt::Microsoft::ReactNative::ITimer{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewControl<D>::GetPanel() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewControl)->GetPanel(&result));
        return winrt::Windows::UI::Xaml::Controls::Panel{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManager<D>::Name() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManager)->get_Name(&value));
        return hstring{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManager<D>::CreateView() const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManager)->CreateView(&result));
        return winrt::Windows::UI::Xaml::FrameworkElement{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerCreateWithProperties<D>::CreateViewWithProperties(winrt::Microsoft::ReactNative::IJSValueReader const& propertyMapReader) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties)->CreateViewWithProperties(*(void**)(&propertyMapReader), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerRequiresNativeLayout<D>::RequiresNativeLayout() const
    {
        bool value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout)->get_RequiresNativeLayout(&value));
        return value;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithChildren<D>::AddView(winrt::Windows::UI::Xaml::FrameworkElement const& parent, winrt::Windows::UI::Xaml::UIElement const& child, int64_t index) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithChildren)->AddView(*(void**)(&parent), *(void**)(&child), index));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithChildren<D>::RemoveAllChildren(winrt::Windows::UI::Xaml::FrameworkElement const& parent) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithChildren)->RemoveAllChildren(*(void**)(&parent)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithChildren<D>::RemoveChildAt(winrt::Windows::UI::Xaml::FrameworkElement const& parent, int64_t index) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithChildren)->RemoveChildAt(*(void**)(&parent), index));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithChildren<D>::ReplaceChild(winrt::Windows::UI::Xaml::FrameworkElement const& parent, winrt::Windows::UI::Xaml::UIElement const& oldChild, winrt::Windows::UI::Xaml::UIElement const& newChild) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithChildren)->ReplaceChild(*(void**)(&parent), *(void**)(&oldChild), *(void**)(&newChild)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithCommands<D>::Commands() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithCommands)->get_Commands(&value));
        return winrt::Windows::Foundation::Collections::IVectorView<hstring>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithCommands<D>::DispatchCommand(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& commandId, winrt::Microsoft::ReactNative::IJSValueReader const& commandArgsReader) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithCommands)->DispatchCommand(*(void**)(&view), *(void**)(&commandId), *(void**)(&commandArgsReader)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithDropViewInstance<D>::OnDropViewInstance(winrt::Windows::UI::Xaml::FrameworkElement const& view) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance)->OnDropViewInstance(*(void**)(&view)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithExportedEventTypeConstants<D>::ExportedCustomBubblingEventTypeConstants() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants)->get_ExportedCustomBubblingEventTypeConstants(&value));
        return winrt::Microsoft::ReactNative::ConstantProviderDelegate{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithExportedEventTypeConstants<D>::ExportedCustomDirectEventTypeConstants() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants)->get_ExportedCustomDirectEventTypeConstants(&value));
        return winrt::Microsoft::ReactNative::ConstantProviderDelegate{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithExportedViewConstants<D>::ExportedViewConstants() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants)->get_ExportedViewConstants(&value));
        return winrt::Microsoft::ReactNative::ConstantProviderDelegate{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithNativeProperties<D>::NativeProps() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties)->get_NativeProps(&value));
        return winrt::Windows::Foundation::Collections::IMapView<hstring, winrt::Microsoft::ReactNative::ViewManagerPropertyType>{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithNativeProperties<D>::UpdateProperties(winrt::Windows::UI::Xaml::FrameworkElement const& view, winrt::Microsoft::ReactNative::IJSValueReader const& propertyMapReader) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties)->UpdateProperties(*(void**)(&view), *(void**)(&propertyMapReader)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithOnLayout<D>::OnLayout(winrt::Windows::UI::Xaml::FrameworkElement const& view, float left, float top, float width, float height) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithOnLayout)->OnLayout(*(void**)(&view), left, top, width, height));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithPointerEvents<D>::OnPointerEvent(winrt::Windows::Foundation::IInspectable const& view, winrt::Microsoft::ReactNative::ReactPointerEventArgs const& args) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents)->OnPointerEvent(*(void**)(&view), *(void**)(&args)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithReactContext<D>::ReactContext() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithReactContext)->get_ReactContext(&value));
        return winrt::Microsoft::ReactNative::IReactContext{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewManagerWithReactContext<D>::ReactContext(winrt::Microsoft::ReactNative::IReactContext const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewManagerWithReactContext)->put_ReactContext(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanel<D>::InsertAt(uint32_t index, winrt::Windows::UI::Xaml::UIElement const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanel)->InsertAt(index, *(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanel<D>::RemoveAt(uint32_t index) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanel)->RemoveAt(index));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanel<D>::Clear() const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanel)->Clear());
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanel<D>::ViewBackground() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanel)->get_ViewBackground(&value));
        return winrt::Windows::UI::Xaml::Media::Brush{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanel<D>::ViewBackground(winrt::Windows::UI::Xaml::Media::Brush const& value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanel)->put_ViewBackground(*(void**)(&value)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::ViewBackgroundProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_ViewBackgroundProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::BorderThicknessProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_BorderThicknessProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::BorderBrushProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_BorderBrushProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::CornerRadiusProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_CornerRadiusProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::TopProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_TopProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::SetTop(winrt::Windows::UI::Xaml::UIElement const& element, double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->SetTop(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::GetTop(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->GetTop(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::LeftProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->get_LeftProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::SetLeft(winrt::Windows::UI::Xaml::UIElement const& element, double value) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->SetLeft(*(void**)(&element), value));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IViewPanelStatics<D>::GetLeft(winrt::Windows::UI::Xaml::UIElement const& element) const
    {
        double result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IViewPanelStatics)->GetLeft(*(void**)(&element), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlHelperStatics<D>::BrushFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlHelperStatics)->BrushFrom(*(void**)(&valueProvider), &result));
        return winrt::Windows::UI::Xaml::Media::Brush{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlHelperStatics<D>::ColorFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider) const
    {
        winrt::Windows::UI::Color result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlHelperStatics)->ColorFrom(*(void**)(&valueProvider), put_abi(result)));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlHelperStatics<D>::ReactTagProperty() const
    {
        void* value{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlHelperStatics)->get_ReactTagProperty(&value));
        return winrt::Windows::UI::Xaml::DependencyProperty{ value, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlHelperStatics<D>::GetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject) const
    {
        int64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlHelperStatics)->GetReactTag(*(void**)(&dependencyObject), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlHelperStatics<D>::SetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject, int64_t tag) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlHelperStatics)->SetReactTag(*(void**)(&dependencyObject), tag));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIService<D>::ElementFromReactTag(int64_t reactTag) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIService)->ElementFromReactTag(reactTag, &result));
        return winrt::Windows::UI::Xaml::DependencyObject{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIService<D>::DispatchEvent(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& eventDataArgWriter) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIService)->DispatchEvent(*(void**)(&view), *(void**)(&eventName), *(void**)(&eventDataArgWriter)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIService<D>::GetReactRootView(winrt::Windows::UI::Xaml::FrameworkElement const& view) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIService)->GetReactRootView(*(void**)(&view), &result));
        return winrt::Microsoft::ReactNative::ReactRootView{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::FromContext(winrt::Microsoft::ReactNative::IReactContext const& context) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->FromContext(*(void**)(&context), &result));
        return winrt::Microsoft::ReactNative::XamlUIService{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::SetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::XamlRoot const& xamlRoot) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->SetXamlRoot(*(void**)(&properties), *(void**)(&xamlRoot)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::SetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::FrameworkElement const& accessibleRoot) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->SetAccessibleRoot(*(void**)(&properties), *(void**)(&accessibleRoot)));
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::GetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->GetXamlRoot(*(void**)(&properties), &result));
        return winrt::Windows::UI::Xaml::XamlRoot{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::GetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        void* result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->GetAccessibleRoot(*(void**)(&properties), &result));
        return winrt::Windows::UI::Xaml::FrameworkElement{ result, take_ownership_from_abi };
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::GetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        uint64_t result{};
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->GetIslandWindowHandle(*(void**)(&properties), &result));
        return result;
    }
    template <typename D> auto consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>::SetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowHandle) const
    {
        check_hresult(WINRT_IMPL_SHIM(winrt::Microsoft::ReactNative::IXamlUIServiceStatics)->SetIslandWindowHandle(*(void**)(&properties), windowHandle));
    }
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler, H> final : implements_delegate<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(struct struct_Microsoft_ReactNative_AccessibilityAction action) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::AccessibilityAction const*>(&action));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler, H> final : implements_delegate<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke() noexcept final try
        {
            (*this)();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::CallFunc, H> final : implements_delegate<winrt::Microsoft::ReactNative::CallFunc, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::CallFunc, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* runtime) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&runtime));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ConstantProviderDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::ConstantProviderDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ConstantProviderDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* constantWriter) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueWriter const*>(&constantWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::EmitEventSetterDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::EmitEventSetterDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::EmitEventSetterDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* argWriter) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&argWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* emitter) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::EmitEventSetterDelegate const*>(&emitter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::InitializerDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::InitializerDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::InitializerDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* reactContext) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&reactContext));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::JSValueArgWriter, H> final : implements_delegate<winrt::Microsoft::ReactNative::JSValueArgWriter, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::JSValueArgWriter, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* writer) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueWriter const*>(&writer));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::JsiByteArrayUser, H> final : implements_delegate<winrt::Microsoft::ReactNative::JsiByteArrayUser, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::JsiByteArrayUser, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(uint32_t __bytesSize, uint8_t* bytes) noexcept final try
        {
            (*this)(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(bytes), reinterpret_cast<uint8_t const *>(bytes) + __bytesSize));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::JsiHostFunction, H> final : implements_delegate<winrt::Microsoft::ReactNative::JsiHostFunction, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::JsiHostFunction, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* runtime, struct struct_Microsoft_ReactNative_JsiValueRef thisArg, uint32_t __argsSize, struct struct_Microsoft_ReactNative_JsiValueRef* args, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>((*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiRuntime const*>(&runtime), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&thisArg), array_view<winrt::Microsoft::ReactNative::JsiValueRef const>(reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args), reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args) + __argsSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::JsiInitializerDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::JsiInitializerDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::JsiInitializerDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* reactContext, void* runtimeHandle) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&reactContext), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&runtimeHandle));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::LogHandler, H> final : implements_delegate<winrt::Microsoft::ReactNative::LogHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::LogHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(int32_t level, void* message) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::LogLevel const*>(&level), *reinterpret_cast<hstring const*>(&message));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::MethodDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::MethodDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::MethodDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* inputReader, void* outputWriter, void* resolve, void* reject) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueReader const*>(&inputReader), *reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueWriter const*>(&outputWriter), *reinterpret_cast<winrt::Microsoft::ReactNative::MethodResultCallback const*>(&resolve), *reinterpret_cast<winrt::Microsoft::ReactNative::MethodResultCallback const*>(&reject));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::MethodResultCallback, H> final : implements_delegate<winrt::Microsoft::ReactNative::MethodResultCallback, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::MethodResultCallback, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* outputWriter) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueWriter const*>(&outputWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ReactCreatePropertyValue, H> final : implements_delegate<winrt::Microsoft::ReactNative::ReactCreatePropertyValue, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ReactCreatePropertyValue, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void** result) noexcept final try
        {
            clear_abi(result);
            *result = detach_from<winrt::Windows::Foundation::IInspectable>((*this)());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ReactDispatcherCallback, H> final : implements_delegate<winrt::Microsoft::ReactNative::ReactDispatcherCallback, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ReactDispatcherCallback, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke() noexcept final try
        {
            (*this)();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ReactModuleProvider, H> final : implements_delegate<winrt::Microsoft::ReactNative::ReactModuleProvider, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ReactModuleProvider, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* moduleBuilder, void** result) noexcept final try
        {
            clear_abi(result);
            *result = detach_from<winrt::Windows::Foundation::IInspectable>((*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactModuleBuilder const*>(&moduleBuilder)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ReactNotificationHandler, H> final : implements_delegate<winrt::Microsoft::ReactNative::ReactNotificationHandler, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ReactNotificationHandler, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* sender, void* args) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Microsoft::ReactNative::IReactNotificationArgs const*>(&args));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::ReactViewManagerProvider, H> final : implements_delegate<winrt::Microsoft::ReactNative::ReactViewManagerProvider, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::ReactViewManagerProvider, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void** result) noexcept final try
        {
            clear_abi(result);
            *result = detach_from<winrt::Microsoft::ReactNative::IViewManager>((*this)());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::SyncMethodDelegate, H> final : implements_delegate<winrt::Microsoft::ReactNative::SyncMethodDelegate, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::SyncMethodDelegate, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* inputReader, void* outputWriter) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueReader const*>(&inputReader), *reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueWriter const*>(&outputWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::TimerFactory, H> final : implements_delegate<winrt::Microsoft::ReactNative::TimerFactory, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::TimerFactory, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void** result) noexcept final try
        {
            clear_abi(result);
            *result = detach_from<winrt::Microsoft::ReactNative::ITimer>((*this)());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename H> struct delegate<winrt::Microsoft::ReactNative::UIBatchCompleteCallback, H> final : implements_delegate<winrt::Microsoft::ReactNative::UIBatchCompleteCallback, H>
    {
        delegate(H&& handler) : implements_delegate<winrt::Microsoft::ReactNative::UIBatchCompleteCallback, H>(std::forward<H>(handler)) {}

        int32_t __stdcall Invoke(void* properties) noexcept final try
        {
            (*this)(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IBorderEffect> : produce_base<D, winrt::Microsoft::ReactNative::IBorderEffect>
    {
        int32_t __stdcall get_ExtendX(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::CanvasEdgeBehavior>(this->shim().ExtendX());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ExtendX(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ExtendX(*reinterpret_cast<winrt::Microsoft::ReactNative::CanvasEdgeBehavior const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExtendY(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::CanvasEdgeBehavior>(this->shim().ExtendY());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ExtendY(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ExtendY(*reinterpret_cast<winrt::Microsoft::ReactNative::CanvasEdgeBehavior const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Effects::IGraphicsEffectSource>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Source(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Source(*reinterpret_cast<winrt::Windows::Graphics::Effects::IGraphicsEffectSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ICallInvoker> : produce_base<D, winrt::Microsoft::ReactNative::ICallInvoker>
    {
        int32_t __stdcall InvokeAsync(void* func) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InvokeAsync(*reinterpret_cast<winrt::Microsoft::ReactNative::CallFunc const*>(&func));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall InvokeSync(void* func) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InvokeSync(*reinterpret_cast<winrt::Microsoft::ReactNative::CallFunc const*>(&func));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IColorSourceEffect> : produce_base<D, winrt::Microsoft::ReactNative::IColorSourceEffect>
    {
        int32_t __stdcall get_Color(struct struct_Windows_UI_Color* value) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Color>(this->shim().Color());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Color(struct struct_Windows_UI_Color value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Color(*reinterpret_cast<winrt::Windows::UI::Color const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ICompositeStepEffect> : produce_base<D, winrt::Microsoft::ReactNative::ICompositeStepEffect>
    {
        int32_t __stdcall get_Mode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::CanvasComposite>(this->shim().Mode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Mode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Mode(*reinterpret_cast<winrt::Microsoft::ReactNative::CanvasComposite const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Destination(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Effects::IGraphicsEffectSource>(this->shim().Destination());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Destination(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Destination(*reinterpret_cast<winrt::Windows::Graphics::Effects::IGraphicsEffectSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Effects::IGraphicsEffectSource>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Source(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Source(*reinterpret_cast<winrt::Windows::Graphics::Effects::IGraphicsEffectSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDevMenuControl> : produce_base<D, winrt::Microsoft::ReactNative::IDevMenuControl>
    {
        int32_t __stdcall get_Cancel(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().Cancel());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ConfigBundler(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().ConfigBundler());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Inspector(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().Inspector());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FastRefresh(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().FastRefresh());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SamplingProfiler(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().SamplingProfiler());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BreakOnNextLine(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().BreakOnNextLine());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DirectDebug(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().DirectDebug());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Reload(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::Button>(this->shim().Reload());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_FastRefreshText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().FastRefreshText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DirectDebugText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().DirectDebugText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DirectDebugDesc(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().DirectDebugDesc());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BreakOnNextLineText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().BreakOnNextLineText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SamplingProfilerText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().SamplingProfilerText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SamplingProfilerDescText(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::TextBlock>(this->shim().SamplingProfilerDescText());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SamplingProfilerIcon(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Controls::FontIcon>(this->shim().SamplingProfilerIcon());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicAutomationPeer> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicAutomationPeer>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory>
    {
        int32_t __stdcall CreateInstance(void* owner, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::DynamicAutomationPeer>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&owner)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicAutomationProperties> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicAutomationProperties>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics>
    {
        int32_t __stdcall get_AccessibilityRoleProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityRoleProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityRole(void* element, int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityRole(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::ReactNative::AccessibilityRoles const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityRole(void* element, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::AccessibilityRoles>(this->shim().GetAccessibilityRole(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AriaRoleProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AriaRoleProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAriaRole(void* element, int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAriaRole(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::ReactNative::AriaRole const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAriaRole(void* element, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::AriaRole>(this->shim().GetAriaRole(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityStateSelectedProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityStateSelectedProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityStateSelected(void* element, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityStateSelected(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityStateSelected(void* element, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetAccessibilityStateSelected(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityStateDisabledProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityStateDisabledProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityStateDisabled(void* element, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityStateDisabled(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityStateDisabled(void* element, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetAccessibilityStateDisabled(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityStateCheckedProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityStateCheckedProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityStateChecked(void* element, int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityStateChecked(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityStateChecked(void* element, int32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue>(this->shim().GetAccessibilityStateChecked(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityStateBusyProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityStateBusyProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityStateBusy(void* element, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityStateBusy(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityStateBusy(void* element, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetAccessibilityStateBusy(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityStateExpandedProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityStateExpandedProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityStateExpanded(void* element, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityStateExpanded(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityStateExpanded(void* element, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetAccessibilityStateExpanded(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityValueMinProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityValueMinProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityValueMin(void* element, double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityValueMin(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityValueMin(void* element, double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetAccessibilityValueMin(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityValueMaxProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityValueMaxProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityValueMax(void* element, double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityValueMax(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityValueMax(void* element, double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetAccessibilityValueMax(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityValueNowProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityValueNowProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityValueNow(void* element, double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityValueNow(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityValueNow(void* element, double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetAccessibilityValueNow(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityValueTextProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityValueTextProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityValueText(void* element, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityValueText(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityValueText(void* element, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetAccessibilityValueText(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityInvokeEventHandlerProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityInvokeEventHandlerProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityInvokeEventHandler(void* element, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityInvokeEventHandler(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityInvokeEventHandler(void* element, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler>(this->shim().GetAccessibilityInvokeEventHandler(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityActionsProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityActionsProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityActions(void* element, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityActions(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::AccessibilityAction> const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityActions(void* element, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::AccessibilityAction>>(this->shim().GetAccessibilityActions(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AccessibilityActionEventHandlerProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().AccessibilityActionEventHandlerProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibilityActionEventHandler(void* element, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibilityActionEventHandler(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), *reinterpret_cast<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibilityActionEventHandler(void* element, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler>(this->shim().GetAccessibilityActionEventHandler(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicValueProvider> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicValueProvider>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IDynamicValueProviderFactory> : produce_base<D, winrt::Microsoft::ReactNative::IDynamicValueProviderFactory>
    {
        int32_t __stdcall CreateInstance(void* peer, void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::DynamicValueProvider>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer const*>(&peer)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IGaussianBlurEffect> : produce_base<D, winrt::Microsoft::ReactNative::IGaussianBlurEffect>
    {
        int32_t __stdcall get_BlurAmount(float* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<float>(this->shim().BlurAmount());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BlurAmount(float value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BlurAmount(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Optimization(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::EffectOptimization>(this->shim().Optimization());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Optimization(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Optimization(*reinterpret_cast<winrt::Microsoft::ReactNative::EffectOptimization const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BorderMode(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::EffectBorderMode>(this->shim().BorderMode());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BorderMode(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BorderMode(*reinterpret_cast<winrt::Microsoft::ReactNative::EffectBorderMode const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Source(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Graphics::Effects::IGraphicsEffectSource>(this->shim().Source());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Source(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Source(*reinterpret_cast<winrt::Windows::Graphics::Effects::IGraphicsEffectSource const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IHttpSettingsStatics> : produce_base<D, winrt::Microsoft::ReactNative::IHttpSettingsStatics>
    {
        int32_t __stdcall SetDefaultUserAgent(void* settings, void* userAgent) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetDefaultUserAgent(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), *reinterpret_cast<hstring const*>(&userAgent));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs> : produce_base<D, winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs>
    {
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactContext>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RuntimeHandle(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().RuntimeHandle());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs> : produce_base<D, winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs>
    {
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactContext>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs> : produce_base<D, winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs>
    {
        int32_t __stdcall get_Context(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactContext>(this->shim().Context());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Failed(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Failed());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RuntimeHandle(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().RuntimeHandle());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJSValueReader> : produce_base<D, winrt::Microsoft::ReactNative::IJSValueReader>
    {
        int32_t __stdcall get_ValueType(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JSValueType>(this->shim().ValueType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetNextObjectProperty(void** propertyName, bool* result) noexcept final try
        {
            clear_abi(propertyName);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetNextObjectProperty(*reinterpret_cast<hstring*>(propertyName)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetNextArrayItem(bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetNextArrayItem());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetString(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().GetString());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetBoolean(bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().GetBoolean());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetInt64(int64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<int64_t>(this->shim().GetInt64());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetDouble(double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetDouble());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJSValueWriter> : produce_base<D, winrt::Microsoft::ReactNative::IJSValueWriter>
    {
        int32_t __stdcall WriteNull() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteNull();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteBoolean(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteBoolean(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteInt64(int64_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteInt64(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteDouble(double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteDouble(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteString(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteString(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteObjectBegin() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteObjectBegin();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WritePropertyName(void* name) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WritePropertyName(*reinterpret_cast<hstring const*>(&name));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteObjectEnd() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteObjectEnd();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteArrayBegin() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteArrayBegin();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall WriteArrayEnd() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().WriteArrayEnd();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiByteBuffer> : produce_base<D, winrt::Microsoft::ReactNative::IJsiByteBuffer>
    {
        int32_t __stdcall get_Size(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Size());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetData(void* useBytes) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GetData(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiByteArrayUser const*>(&useBytes));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiError> : produce_base<D, winrt::Microsoft::ReactNative::IJsiError>
    {
        int32_t __stdcall get_ErrorType(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JsiErrorType>(this->shim().ErrorType());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ErrorDetails(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ErrorDetails());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Message(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Message());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Stack(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Stack());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Value(struct struct_Microsoft_ReactNative_JsiValueRef* value) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().Value());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiHostObject> : produce_base<D, winrt::Microsoft::ReactNative::IJsiHostObject>
    {
        int32_t __stdcall GetProperty(void* runtime, struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().GetProperty(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiRuntime const*>(&runtime), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetProperty(void* runtime, struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, struct struct_Microsoft_ReactNative_JsiValueRef value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetProperty(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiRuntime const*>(&runtime), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPropertyIds(void* runtime, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::JsiPropertyIdRef>>(this->shim().GetPropertyIds(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiRuntime const*>(&runtime)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiPreparedJavaScript> : produce_base<D, winrt::Microsoft::ReactNative::IJsiPreparedJavaScript>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiRuntime> : produce_base<D, winrt::Microsoft::ReactNative::IJsiRuntime>
    {
        int32_t __stdcall EvaluateJavaScript(void* buffer, void* sourceUrl, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().EvaluateJavaScript(*reinterpret_cast<winrt::Microsoft::ReactNative::IJsiByteBuffer const*>(&buffer), *reinterpret_cast<hstring const*>(&sourceUrl)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PrepareJavaScript(void* buffer, void* sourceUrl, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPreparedJavaScript>(this->shim().PrepareJavaScript(*reinterpret_cast<winrt::Microsoft::ReactNative::IJsiByteBuffer const*>(&buffer), *reinterpret_cast<hstring const*>(&sourceUrl)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall EvaluatePreparedJavaScript(void* js, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().EvaluatePreparedJavaScript(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPreparedJavaScript const*>(&js)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DrainMicrotasks(int32_t maxMicrotasksHint, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().DrainMicrotasks(maxMicrotasksHint));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall QueueMicrotask(struct struct_Microsoft_ReactNative_JsiObjectRef callback) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().QueueMicrotask(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&callback));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Global(struct struct_Microsoft_ReactNative_JsiObjectRef* value) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().Global());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Description(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Description());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsInspectable(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsInspectable());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CloneSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef symbol, struct struct_Microsoft_ReactNative_JsiSymbolRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiSymbolRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiSymbolRef>(this->shim().CloneSymbol(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&symbol)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CloneBigInt(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt, struct struct_Microsoft_ReactNative_JsiBigIntRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiBigIntRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiBigIntRef>(this->shim().CloneBigInt(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CloneString(struct struct_Microsoft_ReactNative_JsiStringRef str, struct struct_Microsoft_ReactNative_JsiStringRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiStringRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiStringRef>(this->shim().CloneString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&str)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CloneObject(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CloneObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ClonePropertyId(struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().ClonePropertyId(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyId(void* name, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().CreatePropertyId(*reinterpret_cast<hstring const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyIdFromAscii(uint32_t __asciiSize, uint8_t* ascii, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().CreatePropertyIdFromAscii(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(ascii), reinterpret_cast<uint8_t const *>(ascii) + __asciiSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyIdFromUtf8(uint32_t __utf8Size, uint8_t* utf8, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().CreatePropertyIdFromUtf8(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(utf8), reinterpret_cast<uint8_t const *>(utf8) + __utf8Size)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyIdFromString(struct struct_Microsoft_ReactNative_JsiStringRef str, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().CreatePropertyIdFromString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&str)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyIdFromSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef sym, struct struct_Microsoft_ReactNative_JsiPropertyIdRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiPropertyIdRef>(this->shim().CreatePropertyIdFromSymbol(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&sym)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PropertyIdToString(struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().PropertyIdToString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PropertyIdToUtf8(struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, void* useUtf8String) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PropertyIdToUtf8(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiByteArrayUser const*>(&useUtf8String));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PropertyIdEquals(struct struct_Microsoft_ReactNative_JsiPropertyIdRef left, struct struct_Microsoft_ReactNative_JsiPropertyIdRef right, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().PropertyIdEquals(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&left), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&right)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SymbolToString(struct struct_Microsoft_ReactNative_JsiSymbolRef symbol, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().SymbolToString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&symbol)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SymbolToUtf8(struct struct_Microsoft_ReactNative_JsiSymbolRef symbol, void* useUtf8String) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SymbolToUtf8(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&symbol), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiByteArrayUser const*>(&useUtf8String));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateString(void* value, struct struct_Microsoft_ReactNative_JsiStringRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiStringRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiStringRef>(this->shim().CreateString(*reinterpret_cast<hstring const*>(&value)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateStringFromAscii(uint32_t __asciiSize, uint8_t* ascii, struct struct_Microsoft_ReactNative_JsiStringRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiStringRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiStringRef>(this->shim().CreateStringFromAscii(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(ascii), reinterpret_cast<uint8_t const *>(ascii) + __asciiSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateStringFromUtf8(uint32_t __utf8Size, uint8_t* utf8, struct struct_Microsoft_ReactNative_JsiStringRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiStringRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiStringRef>(this->shim().CreateStringFromUtf8(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(utf8), reinterpret_cast<uint8_t const *>(utf8) + __utf8Size)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StringToString(struct struct_Microsoft_ReactNative_JsiStringRef str, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<hstring>(this->shim().StringToString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&str)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StringToUtf8(struct struct_Microsoft_ReactNative_JsiStringRef str, void* useUtf8String) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().StringToUtf8(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&str), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiByteArrayUser const*>(&useUtf8String));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateValueFromJson(void* json, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().CreateValueFromJson(*reinterpret_cast<hstring const*>(&json)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateValueFromJsonUtf8(uint32_t __jsonSize, uint8_t* json, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().CreateValueFromJsonUtf8(array_view<uint8_t const>(reinterpret_cast<uint8_t const *>(json), reinterpret_cast<uint8_t const *>(json) + __jsonSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateBigIntFromInt64(int64_t val, struct struct_Microsoft_ReactNative_JsiBigIntRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiBigIntRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiBigIntRef>(this->shim().CreateBigIntFromInt64(val));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateBigIntFromUint64(uint64_t val, struct struct_Microsoft_ReactNative_JsiBigIntRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiBigIntRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiBigIntRef>(this->shim().CreateBigIntFromUint64(val));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall BigintIsInt64(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().BigintIsInt64(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall BigintIsUint64(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().BigintIsUint64(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Truncate(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt, uint64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<uint64_t>(this->shim().Truncate(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall BigintToString(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt, int32_t val, struct struct_Microsoft_ReactNative_JsiStringRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiStringRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiStringRef>(this->shim().BigintToString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt), val));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateArrayBuffer(struct struct_Microsoft_ReactNative_JsiObjectRef buffer, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CreateArrayBuffer(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&buffer)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateObject(struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CreateObject());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateObjectWithHostObject(void* hostObject, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CreateObjectWithHostObject(*reinterpret_cast<winrt::Microsoft::ReactNative::IJsiHostObject const*>(&hostObject)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHostObject(struct struct_Microsoft_ReactNative_JsiObjectRef obj, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IJsiHostObject>(this->shim().GetHostObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetHostFunction(struct struct_Microsoft_ReactNative_JsiObjectRef func, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiHostFunction>(this->shim().GetHostFunction(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&func)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetProperty(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().GetProperty(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall HasProperty(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().HasProperty(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetProperty(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId, struct struct_Microsoft_ReactNative_JsiValueRef value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetProperty(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetPropertyIdArray(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().GetPropertyIdArray(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsArray(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().IsArray(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsArrayBuffer(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().IsArrayBuffer(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsFunction(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().IsFunction(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsHostObject(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().IsHostObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall IsHostFunction(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().IsHostFunction(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateWeakObject(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiWeakObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiWeakObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiWeakObjectRef>(this->shim().CreateWeakObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall LockWeakObject(struct struct_Microsoft_ReactNative_JsiWeakObjectRef weakObject, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().LockWeakObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiWeakObjectRef const*>(&weakObject)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateArray(uint32_t size, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CreateArray(size));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetArraySize(struct struct_Microsoft_ReactNative_JsiObjectRef arr, uint32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<uint32_t>(this->shim().GetArraySize(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&arr)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetArrayBufferSize(struct struct_Microsoft_ReactNative_JsiObjectRef arrayBuffer, uint32_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<uint32_t>(this->shim().GetArrayBufferSize(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&arrayBuffer)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetArrayBufferData(struct struct_Microsoft_ReactNative_JsiObjectRef arrayBuffer, void* useArrayBytes) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().GetArrayBufferData(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&arrayBuffer), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiByteArrayUser const*>(&useArrayBytes));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetValueAtIndex(struct struct_Microsoft_ReactNative_JsiObjectRef arr, uint32_t index, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().GetValueAtIndex(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&arr), index));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetValueAtIndex(struct struct_Microsoft_ReactNative_JsiObjectRef arr, uint32_t index, struct struct_Microsoft_ReactNative_JsiValueRef value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetValueAtIndex(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&arr), index, *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateFunctionFromHostFunction(struct struct_Microsoft_ReactNative_JsiPropertyIdRef funcName, uint32_t paramCount, void* hostFunc, struct struct_Microsoft_ReactNative_JsiObjectRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiObjectRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiObjectRef>(this->shim().CreateFunctionFromHostFunction(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&funcName), paramCount, *reinterpret_cast<winrt::Microsoft::ReactNative::JsiHostFunction const*>(&hostFunc)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Call(struct struct_Microsoft_ReactNative_JsiObjectRef func, struct struct_Microsoft_ReactNative_JsiValueRef thisArg, uint32_t __argsSize, struct struct_Microsoft_ReactNative_JsiValueRef* args, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().Call(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&func), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&thisArg), array_view<winrt::Microsoft::ReactNative::JsiValueRef const>(reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args), reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args) + __argsSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CallAsConstructor(struct struct_Microsoft_ReactNative_JsiObjectRef func, uint32_t __argsSize, struct struct_Microsoft_ReactNative_JsiValueRef* args, struct struct_Microsoft_ReactNative_JsiValueRef* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiValueRef>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiValueRef>(this->shim().CallAsConstructor(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&func), array_view<winrt::Microsoft::ReactNative::JsiValueRef const>(reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args), reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const *>(args) + __argsSize)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PushScope(struct struct_Microsoft_ReactNative_JsiScopeState* result) noexcept final try
        {
            zero_abi<winrt::Microsoft::ReactNative::JsiScopeState>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiScopeState>(this->shim().PushScope());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PopScope(struct struct_Microsoft_ReactNative_JsiScopeState scopeState) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PopScope(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiScopeState const*>(&scopeState));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SymbolStrictEquals(struct struct_Microsoft_ReactNative_JsiSymbolRef left, struct struct_Microsoft_ReactNative_JsiSymbolRef right, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().SymbolStrictEquals(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&left), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&right)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall BigIntStrictEquals(struct struct_Microsoft_ReactNative_JsiBigIntRef left, struct struct_Microsoft_ReactNative_JsiBigIntRef right, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().BigIntStrictEquals(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&left), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&right)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall StringStrictEquals(struct struct_Microsoft_ReactNative_JsiStringRef left, struct struct_Microsoft_ReactNative_JsiStringRef right, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().StringStrictEquals(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&left), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&right)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ObjectStrictEquals(struct struct_Microsoft_ReactNative_JsiObjectRef left, struct struct_Microsoft_ReactNative_JsiObjectRef right, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().ObjectStrictEquals(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&left), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&right)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall InstanceOf(struct struct_Microsoft_ReactNative_JsiObjectRef obj, struct struct_Microsoft_ReactNative_JsiObjectRef constructor, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().InstanceOf(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&constructor)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReleaseSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef symbol) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReleaseSymbol(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiSymbolRef const*>(&symbol));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReleaseBigInt(struct struct_Microsoft_ReactNative_JsiBigIntRef bigInt) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReleaseBigInt(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiBigIntRef const*>(&bigInt));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReleaseString(struct struct_Microsoft_ReactNative_JsiStringRef str) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReleaseString(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiStringRef const*>(&str));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReleaseObject(struct struct_Microsoft_ReactNative_JsiObjectRef obj) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReleaseObject(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReleasePropertyId(struct struct_Microsoft_ReactNative_JsiPropertyIdRef propertyId) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReleasePropertyId(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiPropertyIdRef const*>(&propertyId));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAndClearError(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiError>(this->shim().GetAndClearError());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetError(int32_t errorType, void* errorDetails, struct struct_Microsoft_ReactNative_JsiValueRef value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetError(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiErrorType const*>(&errorType), *reinterpret_cast<hstring const*>(&errorDetails), *reinterpret_cast<winrt::Microsoft::ReactNative::JsiValueRef const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall HasNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef obj, bool* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<bool>(this->shim().HasNativeState(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef obj, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactNonAbiValue>(this->shim().GetNativeState(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef obj, void* state) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetNativeState(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiObjectRef const*>(&obj), *reinterpret_cast<winrt::Microsoft::ReactNative::IReactNonAbiValue const*>(&state));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IJsiRuntimeStatics> : produce_base<D, winrt::Microsoft::ReactNative::IJsiRuntimeStatics>
    {
        int32_t __stdcall MakeChakraRuntime(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::JsiRuntime>(this->shim().MakeChakraRuntime());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ILayoutService> : produce_base<D, winrt::Microsoft::ReactNative::ILayoutService>
    {
        int32_t __stdcall ApplyLayoutForAllNodes() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ApplyLayoutForAllNodes();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ApplyLayout(int64_t reactTag, float width, float height) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ApplyLayout(reactTag, width, height);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsInBatch(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsInBatch());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall MarkDirty(int64_t reactTag) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().MarkDirty(reactTag);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ILayoutServiceStatics> : produce_base<D, winrt::Microsoft::ReactNative::ILayoutServiceStatics>
    {
        int32_t __stdcall FromContext(void* context, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::LayoutService>(this->shim().FromContext(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&context)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IQuirkSettings> : produce_base<D, winrt::Microsoft::ReactNative::IQuirkSettings>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IQuirkSettingsStatics> : produce_base<D, winrt::Microsoft::ReactNative::IQuirkSettingsStatics>
    {
        int32_t __stdcall SetMatchAndroidAndIOSStretchBehavior(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetMatchAndroidAndIOSStretchBehavior(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetUseWebFlexBasisBehavior(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetUseWebFlexBasisBehavior(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAcceptSelfSigned(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAcceptSelfSigned(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetBackHandlerKind(void* settings, int32_t kind) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetBackHandlerKind(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), *reinterpret_cast<winrt::Microsoft::ReactNative::BackNavigationHandlerKind const*>(&kind));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetMapWindowDeactivatedToAppStateInactive(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetMapWindowDeactivatedToAppStateInactive(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetSuppressWindowFocusOnViewFocus(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetSuppressWindowFocusOnViewFocus(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetUseRuntimeScheduler(void* settings, bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetUseRuntimeScheduler(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&settings), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactApplication> : produce_base<D, winrt::Microsoft::ReactNative::IReactApplication>
    {
        int32_t __stdcall get_InstanceSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactInstanceSettings>(this->shim().InstanceSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InstanceSettings(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InstanceSettings(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PackageProviders(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>>(this->shim().PackageProviders());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Host(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactNativeHost>(this->shim().Host());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseDeveloperSupport(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseDeveloperSupport());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseDeveloperSupport(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseDeveloperSupport(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JavaScriptBundleFile(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().JavaScriptBundleFile());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_JavaScriptBundleFile(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().JavaScriptBundleFile(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BundleAppId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BundleAppId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BundleAppId(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BundleAppId(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactApplicationFactory> : produce_base<D, winrt::Microsoft::ReactNative::IReactApplicationFactory>
    {
        int32_t __stdcall CreateInstance(void* baseInterface, void** innerInterface, void** value) noexcept final try
        {
            if (innerInterface) *innerInterface = nullptr;
            winrt::Windows::Foundation::IInspectable winrt_impl_innerInterface;
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactApplication>(this->shim().CreateInstance(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&baseInterface), winrt_impl_innerInterface));
                if (innerInterface) *innerInterface = detach_abi(winrt_impl_innerInterface);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactContext> : produce_base<D, winrt::Microsoft::ReactNative::IReactContext>
    {
        int32_t __stdcall get_SettingsSnapshot(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactSettingsSnapshot>(this->shim().SettingsSnapshot());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Properties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyBag>(this->shim().Properties());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Notifications(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactNotificationService>(this->shim().Notifications());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UIDispatcher(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().UIDispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSDispatcher(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().JSDispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSRuntime(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().JSRuntime());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CallInvoker(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::CallInvoker>(this->shim().CallInvoker());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DispatchEvent(void* view, void* eventName, void* eventDataArgWriter) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DispatchEvent(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view), *reinterpret_cast<hstring const*>(&eventName), *reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&eventDataArgWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CallJSFunction(void* moduleName, void* methodName, void* paramsArgWriter) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CallJSFunction(*reinterpret_cast<hstring const*>(&moduleName), *reinterpret_cast<hstring const*>(&methodName), *reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&paramsArgWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall EmitJSEvent(void* eventEmitterName, void* eventName, void* paramsArgWriter) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EmitJSEvent(*reinterpret_cast<hstring const*>(&eventEmitterName), *reinterpret_cast<hstring const*>(&eventName), *reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&paramsArgWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_LoadingState(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::LoadingState>(this->shim().LoadingState());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactCoreInjection> : produce_base<D, winrt::Microsoft::ReactNative::IReactCoreInjection>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactCoreInjectionStatics> : produce_base<D, winrt::Microsoft::ReactNative::IReactCoreInjectionStatics>
    {
        int32_t __stdcall SetUIBatchCompleteCallback(void* properties, void* xamlRoot) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetUIBatchCompleteCallback(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), *reinterpret_cast<winrt::Microsoft::ReactNative::UIBatchCompleteCallback const*>(&xamlRoot));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall MakeViewHost(void* host, void* viewOptions, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactViewHost>(this->shim().MakeViewHost(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactNativeHost const*>(&host), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactViewOptions const*>(&viewOptions)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall PostToUIBatchingQueue(void* context, void* callback) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().PostToUIBatchingQueue(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&context), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactDispatcherCallback const*>(&callback));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetPlatformNameOverride(void* properties, void* platformName) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetPlatformNameOverride(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), *reinterpret_cast<hstring const*>(&platformName));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetTopLevelWindowId(void* properties, uint64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<uint64_t>(this->shim().GetTopLevelWindowId(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetTopLevelWindowId(void* properties, uint64_t windowId) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetTopLevelWindowId(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), windowId);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetTimerFactory(void* properties, void* timerFactory) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetTimerFactory(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), *reinterpret_cast<winrt::Microsoft::ReactNative::TimerFactory const*>(&timerFactory));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactDispatcher> : produce_base<D, winrt::Microsoft::ReactNative::IReactDispatcher>
    {
        int32_t __stdcall get_HasThreadAccess(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().HasThreadAccess());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Post(void* callback) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Post(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactDispatcherCallback const*>(&callback));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics> : produce_base<D, winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics>
    {
        int32_t __stdcall CreateSerialDispatcher(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().CreateSerialDispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UIThreadDispatcher(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().UIThreadDispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UIDispatcherProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().UIDispatcherProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSDispatcherProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().JSDispatcherProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSDispatcherTaskStartingEventName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().JSDispatcherTaskStartingEventName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSDispatcherIdleWaitStartingEventName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().JSDispatcherIdleWaitStartingEventName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSDispatcherIdleWaitCompletedEventName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().JSDispatcherIdleWaitCompletedEventName());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactInstanceSettings> : produce_base<D, winrt::Microsoft::ReactNative::IReactInstanceSettings>
    {
        int32_t __stdcall get_Properties(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyBag>(this->shim().Properties());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Notifications(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactNotificationService>(this->shim().Notifications());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_PackageProviders(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>>(this->shim().PackageProviders());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseDeveloperSupport(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseDeveloperSupport());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseDeveloperSupport(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseDeveloperSupport(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JavaScriptBundleFile(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().JavaScriptBundleFile());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_JavaScriptBundleFile(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().JavaScriptBundleFile(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BundleAppId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BundleAppId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BundleAppId(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BundleAppId(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestDevBundle(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().RequestDevBundle());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RequestDevBundle(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RequestDevBundle(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseWebDebugger(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseWebDebugger());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseWebDebugger(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseWebDebugger(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseFastRefresh(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseFastRefresh());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseFastRefresh(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseFastRefresh(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseLiveReload(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseLiveReload());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseLiveReload(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseLiveReload(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseDirectDebugger(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseDirectDebugger());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UseDirectDebugger(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UseDirectDebugger(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebuggerBreakOnNextLine(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().DebuggerBreakOnNextLine());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DebuggerBreakOnNextLine(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DebuggerBreakOnNextLine(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EnableJITCompilation(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().EnableJITCompilation());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_EnableJITCompilation(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EnableJITCompilation(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EnableByteCodeCaching(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().EnableByteCodeCaching());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_EnableByteCodeCaching(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EnableByteCodeCaching(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EnableDefaultCrashHandler(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().EnableDefaultCrashHandler());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_EnableDefaultCrashHandler(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EnableDefaultCrashHandler(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_EnableDeveloperMenu(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().EnableDeveloperMenu());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_EnableDeveloperMenu(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().EnableDeveloperMenu(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ByteCodeFileUri(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ByteCodeFileUri());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ByteCodeFileUri(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ByteCodeFileUri(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebugBundlePath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DebugBundlePath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DebugBundlePath(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DebugBundlePath(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BundleRootPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BundleRootPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_BundleRootPath(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().BundleRootPath(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebuggerPort(uint16_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint16_t>(this->shim().DebuggerPort());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DebuggerPort(uint16_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DebuggerPort(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebuggerRuntimeName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DebuggerRuntimeName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_DebuggerRuntimeName(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DebuggerRuntimeName(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RedBoxHandler(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IRedBoxHandler>(this->shim().RedBoxHandler());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RedBoxHandler(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RedBoxHandler(*reinterpret_cast<winrt::Microsoft::ReactNative::IRedBoxHandler const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NativeLogger(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::LogHandler>(this->shim().NativeLogger());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_NativeLogger(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().NativeLogger(*reinterpret_cast<winrt::Microsoft::ReactNative::LogHandler const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UIDispatcher(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().UIDispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_UIDispatcher(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UIDispatcher(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactDispatcher const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceBundleHost(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SourceBundleHost());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SourceBundleHost(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SourceBundleHost(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceBundlePort(uint16_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint16_t>(this->shim().SourceBundlePort());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_SourceBundlePort(uint16_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SourceBundlePort(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestInlineSourceMap(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().RequestInlineSourceMap());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_RequestInlineSourceMap(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RequestInlineSourceMap(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JSIEngineOverride(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JSIEngine>(this->shim().JSIEngineOverride());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_JSIEngineOverride(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().JSIEngineOverride(*reinterpret_cast<winrt::Microsoft::ReactNative::JSIEngine const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_InstanceCreated(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().InstanceCreated(*reinterpret_cast<winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_InstanceCreated(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InstanceCreated(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_InstanceLoaded(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().InstanceLoaded(*reinterpret_cast<winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_InstanceLoaded(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InstanceLoaded(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
        int32_t __stdcall add_InstanceDestroyed(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().InstanceDestroyed(*reinterpret_cast<winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_InstanceDestroyed(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InstanceDestroyed(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactModuleBuilder> : produce_base<D, winrt::Microsoft::ReactNative::IReactModuleBuilder>
    {
        int32_t __stdcall AddInitializer(void* initializer) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddInitializer(*reinterpret_cast<winrt::Microsoft::ReactNative::InitializerDelegate const*>(&initializer));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddJsiInitializer(void* initializer) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddJsiInitializer(*reinterpret_cast<winrt::Microsoft::ReactNative::JsiInitializerDelegate const*>(&initializer));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddConstantProvider(void* constantProvider) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddConstantProvider(*reinterpret_cast<winrt::Microsoft::ReactNative::ConstantProviderDelegate const*>(&constantProvider));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddMethod(void* name, int32_t returnType, void* method) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddMethod(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<winrt::Microsoft::ReactNative::MethodReturnType const*>(&returnType), *reinterpret_cast<winrt::Microsoft::ReactNative::MethodDelegate const*>(&method));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddSyncMethod(void* name, void* method) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddSyncMethod(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<winrt::Microsoft::ReactNative::SyncMethodDelegate const*>(&method));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddEventEmitter(void* name, void* emitter) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddEventEmitter(*reinterpret_cast<hstring const*>(&name), *reinterpret_cast<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate const*>(&emitter));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNativeHost> : produce_base<D, winrt::Microsoft::ReactNative::IReactNativeHost>
    {
        int32_t __stdcall get_PackageProviders(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::ReactNative::IReactPackageProvider>>(this->shim().PackageProviders());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InstanceSettings(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactInstanceSettings>(this->shim().InstanceSettings());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InstanceSettings(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InstanceSettings(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactInstanceSettings const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall LoadInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().LoadInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReloadInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ReloadInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UnloadInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().UnloadInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNativeHostStatics> : produce_base<D, winrt::Microsoft::ReactNative::IReactNativeHostStatics>
    {
        int32_t __stdcall FromContext(void* reactContext, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::ReactNativeHost>(this->shim().FromContext(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&reactContext)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNonAbiValue> : produce_base<D, winrt::Microsoft::ReactNative::IReactNonAbiValue>
    {
        int32_t __stdcall GetPtr(int64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<int64_t>(this->shim().GetPtr());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNotificationArgs> : produce_base<D, winrt::Microsoft::ReactNative::IReactNotificationArgs>
    {
        int32_t __stdcall get_Subscription(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactNotificationSubscription>(this->shim().Subscription());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Data(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Data());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNotificationService> : produce_base<D, winrt::Microsoft::ReactNative::IReactNotificationService>
    {
        int32_t __stdcall Subscribe(void* notificationName, void* dispatcher, void* handler, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactNotificationSubscription>(this->shim().Subscribe(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyName const*>(&notificationName), *reinterpret_cast<winrt::Microsoft::ReactNative::IReactDispatcher const*>(&dispatcher), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactNotificationHandler const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SendNotification(void* notificationName, void* sender, void* data) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SendNotification(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyName const*>(&notificationName), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&sender), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&data));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics> : produce_base<D, winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics>
    {
        int32_t __stdcall CreateNotificationService(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactNotificationService>(this->shim().CreateNotificationService());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactNotificationSubscription> : produce_base<D, winrt::Microsoft::ReactNative::IReactNotificationSubscription>
    {
        int32_t __stdcall get_NotificationService(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactNotificationService>(this->shim().NotificationService());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_NotificationName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().NotificationName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Dispatcher(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactDispatcher>(this->shim().Dispatcher());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsSubscribed(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsSubscribed());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Unsubscribe() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Unsubscribe();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPackageBuilder> : produce_base<D, winrt::Microsoft::ReactNative::IReactPackageBuilder>
    {
        int32_t __stdcall AddModule(void* moduleName, void* moduleProvider) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddModule(*reinterpret_cast<hstring const*>(&moduleName), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactModuleProvider const*>(&moduleProvider));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddTurboModule(void* moduleName, void* moduleProvider) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddTurboModule(*reinterpret_cast<hstring const*>(&moduleName), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactModuleProvider const*>(&moduleProvider));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AddViewManager(void* viewManagerName, void* viewManagerProvider) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddViewManager(*reinterpret_cast<hstring const*>(&viewManagerName), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactViewManagerProvider const*>(&viewManagerProvider));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPackageProvider> : produce_base<D, winrt::Microsoft::ReactNative::IReactPackageProvider>
    {
        int32_t __stdcall CreatePackage(void* packageBuilder) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CreatePackage(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPackageBuilder const*>(&packageBuilder));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPointerEventArgs> : produce_base<D, winrt::Microsoft::ReactNative::IReactPointerEventArgs>
    {
        int32_t __stdcall get_Args(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Input::PointerRoutedEventArgs>(this->shim().Args());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Kind(int32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::PointerEventKind>(this->shim().Kind());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Kind(int32_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Kind(*reinterpret_cast<winrt::Microsoft::ReactNative::PointerEventKind const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Target(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Target());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Target(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Target(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_AllowUncaptured(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().AllowUncaptured());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_AllowUncaptured(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AllowUncaptured(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPropertyBag> : produce_base<D, winrt::Microsoft::ReactNative::IReactPropertyBag>
    {
        int32_t __stdcall Get(void* name, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Get(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyName const*>(&name)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetOrCreate(void* name, void* createValue, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().GetOrCreate(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyName const*>(&name), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactCreatePropertyValue const*>(&createValue)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Set(void* name, void* value, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().Set(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyName const*>(&name), *reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&value)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CopyFrom(void* other) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().CopyFrom(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&other));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics> : produce_base<D, winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics>
    {
        int32_t __stdcall get_GlobalNamespace(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyNamespace>(this->shim().GlobalNamespace());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetNamespace(void* namespaceName, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactPropertyNamespace>(this->shim().GetNamespace(*reinterpret_cast<hstring const*>(&namespaceName)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetName(void* ns, void* localName, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactPropertyName>(this->shim().GetName(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyNamespace const*>(&ns), *reinterpret_cast<hstring const*>(&localName)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreatePropertyBag(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IReactPropertyBag>(this->shim().CreatePropertyBag());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPropertyName> : produce_base<D, winrt::Microsoft::ReactNative::IReactPropertyName>
    {
        int32_t __stdcall get_LocalName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().LocalName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Namespace(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactPropertyNamespace>(this->shim().Namespace());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactPropertyNamespace> : produce_base<D, winrt::Microsoft::ReactNative::IReactPropertyNamespace>
    {
        int32_t __stdcall get_NamespaceName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().NamespaceName());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactRootView> : produce_base<D, winrt::Microsoft::ReactNative::IReactRootView>
    {
        int32_t __stdcall get_ReactNativeHost(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactNativeHost>(this->shim().ReactNativeHost());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ReactNativeHost(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReactNativeHost(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactNativeHost const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ComponentName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ComponentName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ComponentName(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ComponentName(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InitialProps(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JSValueArgWriter>(this->shim().InitialProps());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InitialProps(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InitialProps(*reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsPerspectiveEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsPerspectiveEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_IsPerspectiveEnabled(bool value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().IsPerspectiveEnabled(value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReloadView() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReloadView();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactSettingsSnapshot> : produce_base<D, winrt::Microsoft::ReactNative::IReactSettingsSnapshot>
    {
        int32_t __stdcall get_UseWebDebugger(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseWebDebugger());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseFastRefresh(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseFastRefresh());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_UseDirectDebugger(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().UseDirectDebugger());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebuggerBreakOnNextLine(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().DebuggerBreakOnNextLine());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebuggerPort(uint16_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint16_t>(this->shim().DebuggerPort());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_DebugBundlePath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().DebugBundlePath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BundleRootPath(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BundleRootPath());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceBundleHost(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().SourceBundleHost());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_SourceBundlePort(uint16_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint16_t>(this->shim().SourceBundlePort());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestInlineSourceMap(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().RequestInlineSourceMap());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_JavaScriptBundleFile(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().JavaScriptBundleFile());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BundleAppId(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().BundleAppId());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_RequestDevBundle(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().RequestDevBundle());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactViewHost> : produce_base<D, winrt::Microsoft::ReactNative::IReactViewHost>
    {
        int32_t __stdcall ReloadViewInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ReloadViewInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReloadViewInstanceWithOptions(void* options, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().ReloadViewInstanceWithOptions(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactViewOptions const*>(&options)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UnloadViewInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().UnloadViewInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall AttachViewInstance(void* viewInstance, void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().AttachViewInstance(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactViewInstance const*>(&viewInstance)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DetachViewInstance(void** operation) noexcept final try
        {
            clear_abi(operation);
            typename D::abi_guard guard(this->shim());
            *operation = detach_from<winrt::Windows::Foundation::IAsyncAction>(this->shim().DetachViewInstance());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReactNativeHost(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ReactNativeHost>(this->shim().ReactNativeHost());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactViewInstance> : produce_base<D, winrt::Microsoft::ReactNative::IReactViewInstance>
    {
        int32_t __stdcall InitRootView(void* context, void* viewOptions) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InitRootView(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&context), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactViewOptions const*>(&viewOptions));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UpdateRootView() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UpdateRootView();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UninitRootView() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UninitRootView();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IReactViewOptions> : produce_base<D, winrt::Microsoft::ReactNative::IReactViewOptions>
    {
        int32_t __stdcall get_ComponentName(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ComponentName());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ComponentName(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ComponentName(*reinterpret_cast<hstring const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_InitialProps(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::JSValueArgWriter>(this->shim().InitialProps());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_InitialProps(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InitialProps(*reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo> : produce_base<D, winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>
    {
        int32_t __stdcall get_File(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().File());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Method(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Method());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Line(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Line());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Column(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Column());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Collapse(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().Collapse());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IRedBoxErrorInfo> : produce_base<D, winrt::Microsoft::ReactNative::IRedBoxErrorInfo>
    {
        int32_t __stdcall get_Message(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Message());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_OriginalMessage(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().OriginalMessage());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ComponentStack(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().ComponentStack());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Id(uint32_t* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<uint32_t>(this->shim().Id());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_Callstack(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>>(this->shim().Callstack());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExtraData(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IJSValueReader>(this->shim().ExtraData());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IRedBoxHandler> : produce_base<D, winrt::Microsoft::ReactNative::IRedBoxHandler>
    {
        int32_t __stdcall ShowNewError(void* info, int32_t type) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ShowNewError(*reinterpret_cast<winrt::Microsoft::ReactNative::IRedBoxErrorInfo const*>(&info), *reinterpret_cast<winrt::Microsoft::ReactNative::RedBoxErrorType const*>(&type));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_IsDevSupportEnabled(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().IsDevSupportEnabled());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UpdateError(void* info) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UpdateError(*reinterpret_cast<winrt::Microsoft::ReactNative::IRedBoxErrorInfo const*>(&info));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DismissRedBox() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DismissRedBox();
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IRedBoxHelper> : produce_base<D, winrt::Microsoft::ReactNative::IRedBoxHelper>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IRedBoxHelperStatics> : produce_base<D, winrt::Microsoft::ReactNative::IRedBoxHelperStatics>
    {
        int32_t __stdcall CreateDefaultHandler(void* host, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::IRedBoxHandler>(this->shim().CreateDefaultHandler(*reinterpret_cast<winrt::Microsoft::ReactNative::ReactNativeHost const*>(&host)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ITimer> : produce_base<D, winrt::Microsoft::ReactNative::ITimer>
    {
        int32_t __stdcall get_Interval(int64_t* value) noexcept final try
        {
            zero_abi<winrt::Windows::Foundation::TimeSpan>(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::TimeSpan>(this->shim().Interval());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_Interval(int64_t value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Interval(*reinterpret_cast<winrt::Windows::Foundation::TimeSpan const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Start() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Start();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Stop() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Stop();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall add_Tick(void* handler, winrt::event_token* token) noexcept final try
        {
            zero_abi<winrt::event_token>(token);
            typename D::abi_guard guard(this->shim());
            *token = detach_from<winrt::event_token>(this->shim().Tick(*reinterpret_cast<winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const*>(&handler)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall remove_Tick(winrt::event_token token) noexcept final
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Tick(*reinterpret_cast<winrt::event_token const*>(&token));
            return 0;
        }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ITimer2> : produce_base<D, winrt::Microsoft::ReactNative::ITimer2>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::ITimerStatics> : produce_base<D, winrt::Microsoft::ReactNative::ITimerStatics>
    {
        int32_t __stdcall Create(void* properties, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::ITimer>(this->shim().Create(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewControl> : produce_base<D, winrt::Microsoft::ReactNative::IViewControl>
    {
        int32_t __stdcall GetPanel(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::Controls::Panel>(this->shim().GetPanel());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManager> : produce_base<D, winrt::Microsoft::ReactNative::IViewManager>
    {
        int32_t __stdcall get_Name(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<hstring>(this->shim().Name());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall CreateView(void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::FrameworkElement>(this->shim().CreateView());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties>
    {
        int32_t __stdcall CreateViewWithProperties(void* propertyMapReader, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::Foundation::IInspectable>(this->shim().CreateViewWithProperties(*reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueReader const*>(&propertyMapReader)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout>
    {
        int32_t __stdcall get_RequiresNativeLayout(bool* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *value = detach_from<bool>(this->shim().RequiresNativeLayout());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithChildren> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithChildren>
    {
        int32_t __stdcall AddView(void* parent, void* child, int64_t index) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().AddView(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&parent), *reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&child), index);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveAllChildren(void* parent) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveAllChildren(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&parent));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveChildAt(void* parent, int64_t index) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveChildAt(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&parent), index);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ReplaceChild(void* parent, void* oldChild, void* newChild) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReplaceChild(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&parent), *reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&oldChild), *reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&newChild));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithCommands> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithCommands>
    {
        int32_t __stdcall get_Commands(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IVectorView<hstring>>(this->shim().Commands());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DispatchCommand(void* view, void* commandId, void* commandArgsReader) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DispatchCommand(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view), *reinterpret_cast<hstring const*>(&commandId), *reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueReader const*>(&commandArgsReader));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance>
    {
        int32_t __stdcall OnDropViewInstance(void* view) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OnDropViewInstance(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants>
    {
        int32_t __stdcall get_ExportedCustomBubblingEventTypeConstants(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ConstantProviderDelegate>(this->shim().ExportedCustomBubblingEventTypeConstants());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ExportedCustomDirectEventTypeConstants(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ConstantProviderDelegate>(this->shim().ExportedCustomDirectEventTypeConstants());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants>
    {
        int32_t __stdcall get_ExportedViewConstants(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::ConstantProviderDelegate>(this->shim().ExportedViewConstants());
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties>
    {
        int32_t __stdcall get_NativeProps(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::Foundation::Collections::IMapView<hstring, winrt::Microsoft::ReactNative::ViewManagerPropertyType>>(this->shim().NativeProps());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall UpdateProperties(void* view, void* propertyMapReader) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().UpdateProperties(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view), *reinterpret_cast<winrt::Microsoft::ReactNative::IJSValueReader const*>(&propertyMapReader));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithOnLayout> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithOnLayout>
    {
        int32_t __stdcall OnLayout(void* view, float left, float top, float width, float height) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OnLayout(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view), left, top, width, height);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents>
    {
        int32_t __stdcall OnPointerEvent(void* view, void* args) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().OnPointerEvent(*reinterpret_cast<winrt::Windows::Foundation::IInspectable const*>(&view), *reinterpret_cast<winrt::Microsoft::ReactNative::ReactPointerEventArgs const*>(&args));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewManagerWithReactContext> : produce_base<D, winrt::Microsoft::ReactNative::IViewManagerWithReactContext>
    {
        int32_t __stdcall get_ReactContext(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Microsoft::ReactNative::IReactContext>(this->shim().ReactContext());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ReactContext(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ReactContext(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewPanel> : produce_base<D, winrt::Microsoft::ReactNative::IViewPanel>
    {
        int32_t __stdcall InsertAt(uint32_t index, void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().InsertAt(index, *reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall RemoveAt(uint32_t index) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().RemoveAt(index);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall Clear() noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().Clear();
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ViewBackground(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::Media::Brush>(this->shim().ViewBackground());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall put_ViewBackground(void* value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().ViewBackground(*reinterpret_cast<winrt::Windows::UI::Xaml::Media::Brush const*>(&value));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IViewPanelStatics> : produce_base<D, winrt::Microsoft::ReactNative::IViewPanelStatics>
    {
        int32_t __stdcall get_ViewBackgroundProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ViewBackgroundProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BorderThicknessProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().BorderThicknessProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_BorderBrushProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().BorderBrushProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_CornerRadiusProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().CornerRadiusProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_TopProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().TopProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetTop(void* element, double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetTop(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetTop(void* element, double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetTop(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_LeftProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().LeftProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetLeft(void* element, double value) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetLeft(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element), value);
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetLeft(void* element, double* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<double>(this->shim().GetLeft(*reinterpret_cast<winrt::Windows::UI::Xaml::UIElement const*>(&element)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IXamlHelper> : produce_base<D, winrt::Microsoft::ReactNative::IXamlHelper>
    {
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IXamlHelperStatics> : produce_base<D, winrt::Microsoft::ReactNative::IXamlHelperStatics>
    {
        int32_t __stdcall BrushFrom(void* valueProvider, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::Media::Brush>(this->shim().BrushFrom(*reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&valueProvider)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall ColorFrom(void* valueProvider, struct struct_Windows_UI_Color* result) noexcept final try
        {
            zero_abi<winrt::Windows::UI::Color>(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Color>(this->shim().ColorFrom(*reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&valueProvider)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall get_ReactTagProperty(void** value) noexcept final try
        {
            clear_abi(value);
            typename D::abi_guard guard(this->shim());
            *value = detach_from<winrt::Windows::UI::Xaml::DependencyProperty>(this->shim().ReactTagProperty());
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetReactTag(void* dependencyObject, int64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<int64_t>(this->shim().GetReactTag(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&dependencyObject)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetReactTag(void* dependencyObject, int64_t tag) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetReactTag(*reinterpret_cast<winrt::Windows::UI::Xaml::DependencyObject const*>(&dependencyObject), tag);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IXamlUIService> : produce_base<D, winrt::Microsoft::ReactNative::IXamlUIService>
    {
        int32_t __stdcall ElementFromReactTag(int64_t reactTag, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::DependencyObject>(this->shim().ElementFromReactTag(reactTag));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall DispatchEvent(void* view, void* eventName, void* eventDataArgWriter) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().DispatchEvent(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view), *reinterpret_cast<hstring const*>(&eventName), *reinterpret_cast<winrt::Microsoft::ReactNative::JSValueArgWriter const*>(&eventDataArgWriter));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetReactRootView(void* view, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::ReactRootView>(this->shim().GetReactRootView(*reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&view)));
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
#ifndef WINRT_LEAN_AND_MEAN
    template <typename D>
    struct produce<D, winrt::Microsoft::ReactNative::IXamlUIServiceStatics> : produce_base<D, winrt::Microsoft::ReactNative::IXamlUIServiceStatics>
    {
        int32_t __stdcall FromContext(void* context, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Microsoft::ReactNative::XamlUIService>(this->shim().FromContext(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactContext const*>(&context)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetXamlRoot(void* properties, void* xamlRoot) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetXamlRoot(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), *reinterpret_cast<winrt::Windows::UI::Xaml::XamlRoot const*>(&xamlRoot));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetAccessibleRoot(void* properties, void* accessibleRoot) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetAccessibleRoot(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), *reinterpret_cast<winrt::Windows::UI::Xaml::FrameworkElement const*>(&accessibleRoot));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetXamlRoot(void* properties, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::XamlRoot>(this->shim().GetXamlRoot(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetAccessibleRoot(void* properties, void** result) noexcept final try
        {
            clear_abi(result);
            typename D::abi_guard guard(this->shim());
            *result = detach_from<winrt::Windows::UI::Xaml::FrameworkElement>(this->shim().GetAccessibleRoot(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall GetIslandWindowHandle(void* properties, uint64_t* result) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            *result = detach_from<uint64_t>(this->shim().GetIslandWindowHandle(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties)));
            return 0;
        }
        catch (...) { return to_hresult(); }
        int32_t __stdcall SetIslandWindowHandle(void* properties, uint64_t windowHandle) noexcept final try
        {
            typename D::abi_guard guard(this->shim());
            this->shim().SetIslandWindowHandle(*reinterpret_cast<winrt::Microsoft::ReactNative::IReactPropertyBag const*>(&properties), windowHandle);
            return 0;
        }
        catch (...) { return to_hresult(); }
    };
#endif
}
WINRT_EXPORT namespace winrt::Microsoft::ReactNative
{
    inline DevMenuControl::DevMenuControl() :
        DevMenuControl(impl::call_factory_cast<DevMenuControl(*)(winrt::Windows::Foundation::IActivationFactory const&), DevMenuControl>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<DevMenuControl>(); }))
    {
    }
    inline DynamicAutomationPeer::DynamicAutomationPeer(winrt::Windows::UI::Xaml::FrameworkElement const& owner) :
        DynamicAutomationPeer(impl::call_factory<DynamicAutomationPeer, IDynamicAutomationPeerFactory>([&](IDynamicAutomationPeerFactory const& f) { return f.CreateInstance(owner); }))
    {
    }
    inline auto DynamicAutomationProperties::AccessibilityRoleProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityRoleProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityRoles const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityRole(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityRole(element); });
    }
    inline auto DynamicAutomationProperties::AriaRoleProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AriaRoleProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AriaRole const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAriaRole(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAriaRole(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityStateSelectedProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityStateSelectedProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element, bool value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityStateSelected(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityStateSelected(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityStateDisabledProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityStateDisabledProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element, bool value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityStateDisabled(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityStateDisabled(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityStateCheckedProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityStateCheckedProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityStateChecked(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityStateChecked(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityStateBusyProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityStateBusyProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element, bool value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityStateBusy(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityStateBusy(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityStateExpandedProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityStateExpandedProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element, bool value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityStateExpanded(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityStateExpanded(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityValueMinProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityValueMinProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element, double value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityValueMin(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityValueMin(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityValueMaxProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityValueMaxProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element, double value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityValueMax(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityValueMax(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityValueNowProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityValueNowProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element, double value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityValueNow(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityValueNow(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityValueTextProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityValueTextProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element, param::hstring const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityValueText(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityValueText(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityInvokeEventHandlerProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityInvokeEventHandlerProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityInvokeEventHandler(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityInvokeEventHandler(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityActionsProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityActionsProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element, param::vector<winrt::Microsoft::ReactNative::AccessibilityAction> const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityActions(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityActions(element); });
    }
    inline auto DynamicAutomationProperties::AccessibilityActionEventHandlerProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IDynamicAutomationPropertiesStatics const&), DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([](IDynamicAutomationPropertiesStatics const& f) { return f.AccessibilityActionEventHandlerProperty(); });
    }
    inline auto DynamicAutomationProperties::SetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityActionEventHandler const& value)
    {
        impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.SetAccessibilityActionEventHandler(element, value); });
    }
    inline auto DynamicAutomationProperties::GetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<DynamicAutomationProperties, IDynamicAutomationPropertiesStatics>([&](IDynamicAutomationPropertiesStatics const& f) { return f.GetAccessibilityActionEventHandler(element); });
    }
    inline DynamicValueProvider::DynamicValueProvider(winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer const& peer) :
        DynamicValueProvider(impl::call_factory<DynamicValueProvider, IDynamicValueProviderFactory>([&](IDynamicValueProviderFactory const& f) { return f.CreateInstance(peer); }))
    {
    }
    inline auto HttpSettings::SetDefaultUserAgent(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, param::hstring const& userAgent)
    {
        impl::call_factory<HttpSettings, IHttpSettingsStatics>([&](IHttpSettingsStatics const& f) { return f.SetDefaultUserAgent(settings, userAgent); });
    }
    inline auto JsiRuntime::MakeChakraRuntime()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::JsiRuntime(*)(IJsiRuntimeStatics const&), JsiRuntime, IJsiRuntimeStatics>([](IJsiRuntimeStatics const& f) { return f.MakeChakraRuntime(); });
    }
    inline auto LayoutService::FromContext(winrt::Microsoft::ReactNative::IReactContext const& context)
    {
        return impl::call_factory<LayoutService, ILayoutServiceStatics>([&](ILayoutServiceStatics const& f) { return f.FromContext(context); });
    }
    inline auto QuirkSettings::SetMatchAndroidAndIOSStretchBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetMatchAndroidAndIOSStretchBehavior(settings, value); });
    }
    inline auto QuirkSettings::SetUseWebFlexBasisBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetUseWebFlexBasisBehavior(settings, value); });
    }
    inline auto QuirkSettings::SetAcceptSelfSigned(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetAcceptSelfSigned(settings, value); });
    }
    inline auto QuirkSettings::SetBackHandlerKind(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, winrt::Microsoft::ReactNative::BackNavigationHandlerKind const& kind)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetBackHandlerKind(settings, kind); });
    }
    inline auto QuirkSettings::SetMapWindowDeactivatedToAppStateInactive(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetMapWindowDeactivatedToAppStateInactive(settings, value); });
    }
    inline auto QuirkSettings::SetSuppressWindowFocusOnViewFocus(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetSuppressWindowFocusOnViewFocus(settings, value); });
    }
    inline auto QuirkSettings::SetUseRuntimeScheduler(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value)
    {
        impl::call_factory<QuirkSettings, IQuirkSettingsStatics>([&](IQuirkSettingsStatics const& f) { return f.SetUseRuntimeScheduler(settings, value); });
    }
    inline ReactApplication::ReactApplication()
    {
        winrt::Windows::Foundation::IInspectable baseInterface, innerInterface;
        *this = impl::call_factory<ReactApplication, IReactApplicationFactory>([&](IReactApplicationFactory const& f) { return f.CreateInstance(baseInterface, innerInterface); });
    }
    inline auto ReactCoreInjection::SetUIBatchCompleteCallback(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::UIBatchCompleteCallback const& xamlRoot)
    {
        impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.SetUIBatchCompleteCallback(properties, xamlRoot); });
    }
    inline auto ReactCoreInjection::MakeViewHost(winrt::Microsoft::ReactNative::ReactNativeHost const& host, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions)
    {
        return impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.MakeViewHost(host, viewOptions); });
    }
    inline auto ReactCoreInjection::PostToUIBatchingQueue(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback)
    {
        impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.PostToUIBatchingQueue(context, callback); });
    }
    inline auto ReactCoreInjection::SetPlatformNameOverride(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, param::hstring const& platformName)
    {
        impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.SetPlatformNameOverride(properties, platformName); });
    }
    inline auto ReactCoreInjection::GetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties)
    {
        return impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.GetTopLevelWindowId(properties); });
    }
    inline auto ReactCoreInjection::SetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowId)
    {
        impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.SetTopLevelWindowId(properties, windowId); });
    }
    inline auto ReactCoreInjection::SetTimerFactory(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::TimerFactory const& timerFactory)
    {
        impl::call_factory<ReactCoreInjection, IReactCoreInjectionStatics>([&](IReactCoreInjectionStatics const& f) { return f.SetTimerFactory(properties, timerFactory); });
    }
    inline auto ReactDispatcherHelper::CreateSerialDispatcher()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactDispatcher(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.CreateSerialDispatcher(); });
    }
    inline auto ReactDispatcherHelper::UIThreadDispatcher()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactDispatcher(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.UIThreadDispatcher(); });
    }
    inline auto ReactDispatcherHelper::UIDispatcherProperty()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyName(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.UIDispatcherProperty(); });
    }
    inline auto ReactDispatcherHelper::JSDispatcherProperty()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyName(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.JSDispatcherProperty(); });
    }
    inline auto ReactDispatcherHelper::JSDispatcherTaskStartingEventName()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyName(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.JSDispatcherTaskStartingEventName(); });
    }
    inline auto ReactDispatcherHelper::JSDispatcherIdleWaitStartingEventName()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyName(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.JSDispatcherIdleWaitStartingEventName(); });
    }
    inline auto ReactDispatcherHelper::JSDispatcherIdleWaitCompletedEventName()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyName(*)(IReactDispatcherHelperStatics const&), ReactDispatcherHelper, IReactDispatcherHelperStatics>([](IReactDispatcherHelperStatics const& f) { return f.JSDispatcherIdleWaitCompletedEventName(); });
    }
    inline ReactInstanceSettings::ReactInstanceSettings() :
        ReactInstanceSettings(impl::call_factory_cast<ReactInstanceSettings(*)(winrt::Windows::Foundation::IActivationFactory const&), ReactInstanceSettings>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ReactInstanceSettings>(); }))
    {
    }
    inline ReactNativeHost::ReactNativeHost() :
        ReactNativeHost(impl::call_factory_cast<ReactNativeHost(*)(winrt::Windows::Foundation::IActivationFactory const&), ReactNativeHost>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ReactNativeHost>(); }))
    {
    }
    inline auto ReactNativeHost::FromContext(winrt::Microsoft::ReactNative::IReactContext const& reactContext)
    {
        return impl::call_factory<ReactNativeHost, IReactNativeHostStatics>([&](IReactNativeHostStatics const& f) { return f.FromContext(reactContext); });
    }
    inline auto ReactNotificationServiceHelper::CreateNotificationService()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactNotificationService(*)(IReactNotificationServiceHelperStatics const&), ReactNotificationServiceHelper, IReactNotificationServiceHelperStatics>([](IReactNotificationServiceHelperStatics const& f) { return f.CreateNotificationService(); });
    }
    inline auto ReactPropertyBagHelper::GlobalNamespace()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyNamespace(*)(IReactPropertyBagHelperStatics const&), ReactPropertyBagHelper, IReactPropertyBagHelperStatics>([](IReactPropertyBagHelperStatics const& f) { return f.GlobalNamespace(); });
    }
    inline auto ReactPropertyBagHelper::GetNamespace(param::hstring const& namespaceName)
    {
        return impl::call_factory<ReactPropertyBagHelper, IReactPropertyBagHelperStatics>([&](IReactPropertyBagHelperStatics const& f) { return f.GetNamespace(namespaceName); });
    }
    inline auto ReactPropertyBagHelper::GetName(winrt::Microsoft::ReactNative::IReactPropertyNamespace const& ns, param::hstring const& localName)
    {
        return impl::call_factory<ReactPropertyBagHelper, IReactPropertyBagHelperStatics>([&](IReactPropertyBagHelperStatics const& f) { return f.GetName(ns, localName); });
    }
    inline auto ReactPropertyBagHelper::CreatePropertyBag()
    {
        return impl::call_factory_cast<winrt::Microsoft::ReactNative::IReactPropertyBag(*)(IReactPropertyBagHelperStatics const&), ReactPropertyBagHelper, IReactPropertyBagHelperStatics>([](IReactPropertyBagHelperStatics const& f) { return f.CreatePropertyBag(); });
    }
    inline ReactRootView::ReactRootView() :
        ReactRootView(impl::call_factory_cast<ReactRootView(*)(winrt::Windows::Foundation::IActivationFactory const&), ReactRootView>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ReactRootView>(); }))
    {
    }
    inline ReactViewOptions::ReactViewOptions() :
        ReactViewOptions(impl::call_factory_cast<ReactViewOptions(*)(winrt::Windows::Foundation::IActivationFactory const&), ReactViewOptions>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ReactViewOptions>(); }))
    {
    }
    inline auto RedBoxHelper::CreateDefaultHandler(winrt::Microsoft::ReactNative::ReactNativeHost const& host)
    {
        return impl::call_factory<RedBoxHelper, IRedBoxHelperStatics>([&](IRedBoxHelperStatics const& f) { return f.CreateDefaultHandler(host); });
    }
    inline auto Timer::Create(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties)
    {
        return impl::call_factory<Timer, ITimerStatics>([&](ITimerStatics const& f) { return f.Create(properties); });
    }
    inline ViewControl::ViewControl() :
        ViewControl(impl::call_factory_cast<ViewControl(*)(winrt::Windows::Foundation::IActivationFactory const&), ViewControl>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ViewControl>(); }))
    {
    }
    inline ViewPanel::ViewPanel() :
        ViewPanel(impl::call_factory_cast<ViewPanel(*)(winrt::Windows::Foundation::IActivationFactory const&), ViewPanel>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<ViewPanel>(); }))
    {
    }
    inline auto ViewPanel::ViewBackgroundProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.ViewBackgroundProperty(); });
    }
    inline auto ViewPanel::BorderThicknessProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.BorderThicknessProperty(); });
    }
    inline auto ViewPanel::BorderBrushProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.BorderBrushProperty(); });
    }
    inline auto ViewPanel::CornerRadiusProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.CornerRadiusProperty(); });
    }
    inline auto ViewPanel::TopProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.TopProperty(); });
    }
    inline auto ViewPanel::SetTop(winrt::Windows::UI::Xaml::UIElement const& element, double value)
    {
        impl::call_factory<ViewPanel, IViewPanelStatics>([&](IViewPanelStatics const& f) { return f.SetTop(element, value); });
    }
    inline auto ViewPanel::GetTop(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<ViewPanel, IViewPanelStatics>([&](IViewPanelStatics const& f) { return f.GetTop(element); });
    }
    inline auto ViewPanel::LeftProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IViewPanelStatics const&), ViewPanel, IViewPanelStatics>([](IViewPanelStatics const& f) { return f.LeftProperty(); });
    }
    inline auto ViewPanel::SetLeft(winrt::Windows::UI::Xaml::UIElement const& element, double value)
    {
        impl::call_factory<ViewPanel, IViewPanelStatics>([&](IViewPanelStatics const& f) { return f.SetLeft(element, value); });
    }
    inline auto ViewPanel::GetLeft(winrt::Windows::UI::Xaml::UIElement const& element)
    {
        return impl::call_factory<ViewPanel, IViewPanelStatics>([&](IViewPanelStatics const& f) { return f.GetLeft(element); });
    }
    inline auto XamlHelper::BrushFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider)
    {
        return impl::call_factory<XamlHelper, IXamlHelperStatics>([&](IXamlHelperStatics const& f) { return f.BrushFrom(valueProvider); });
    }
    inline auto XamlHelper::ColorFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider)
    {
        return impl::call_factory<XamlHelper, IXamlHelperStatics>([&](IXamlHelperStatics const& f) { return f.ColorFrom(valueProvider); });
    }
    inline auto XamlHelper::ReactTagProperty()
    {
        return impl::call_factory_cast<winrt::Windows::UI::Xaml::DependencyProperty(*)(IXamlHelperStatics const&), XamlHelper, IXamlHelperStatics>([](IXamlHelperStatics const& f) { return f.ReactTagProperty(); });
    }
    inline auto XamlHelper::GetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject)
    {
        return impl::call_factory<XamlHelper, IXamlHelperStatics>([&](IXamlHelperStatics const& f) { return f.GetReactTag(dependencyObject); });
    }
    inline auto XamlHelper::SetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject, int64_t tag)
    {
        impl::call_factory<XamlHelper, IXamlHelperStatics>([&](IXamlHelperStatics const& f) { return f.SetReactTag(dependencyObject, tag); });
    }
    inline XamlMetaDataProvider::XamlMetaDataProvider() :
        XamlMetaDataProvider(impl::call_factory_cast<XamlMetaDataProvider(*)(winrt::Windows::Foundation::IActivationFactory const&), XamlMetaDataProvider>([](winrt::Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<XamlMetaDataProvider>(); }))
    {
    }
    inline auto XamlUIService::FromContext(winrt::Microsoft::ReactNative::IReactContext const& context)
    {
        return impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.FromContext(context); });
    }
    inline auto XamlUIService::SetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::XamlRoot const& xamlRoot)
    {
        impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.SetXamlRoot(properties, xamlRoot); });
    }
    inline auto XamlUIService::SetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::FrameworkElement const& accessibleRoot)
    {
        impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.SetAccessibleRoot(properties, accessibleRoot); });
    }
    inline auto XamlUIService::GetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties)
    {
        return impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.GetXamlRoot(properties); });
    }
    inline auto XamlUIService::GetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties)
    {
        return impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.GetAccessibleRoot(properties); });
    }
    inline auto XamlUIService::GetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties)
    {
        return impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.GetIslandWindowHandle(properties); });
    }
    inline auto XamlUIService::SetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowHandle)
    {
        impl::call_factory<XamlUIService, IXamlUIServiceStatics>([&](IXamlUIServiceStatics const& f) { return f.SetIslandWindowHandle(properties, windowHandle); });
    }
    template <typename L> AccessibilityActionEventHandler::AccessibilityActionEventHandler(L handler) :
        AccessibilityActionEventHandler(impl::make_delegate<AccessibilityActionEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> AccessibilityActionEventHandler::AccessibilityActionEventHandler(F* handler) :
        AccessibilityActionEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> AccessibilityActionEventHandler::AccessibilityActionEventHandler(O* object, M method) :
        AccessibilityActionEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> AccessibilityActionEventHandler::AccessibilityActionEventHandler(com_ptr<O>&& object, M method) :
        AccessibilityActionEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> AccessibilityActionEventHandler::AccessibilityActionEventHandler(weak_ref<O>&& object, M method) :
        AccessibilityActionEventHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto AccessibilityActionEventHandler::operator()(winrt::Microsoft::ReactNative::AccessibilityAction const& action) const
    {
        check_hresult((*(impl::abi_t<AccessibilityActionEventHandler>**)this)->Invoke(impl::bind_in(action)));
    }
    template <typename L> AccessibilityInvokeEventHandler::AccessibilityInvokeEventHandler(L handler) :
        AccessibilityInvokeEventHandler(impl::make_delegate<AccessibilityInvokeEventHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> AccessibilityInvokeEventHandler::AccessibilityInvokeEventHandler(F* handler) :
        AccessibilityInvokeEventHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> AccessibilityInvokeEventHandler::AccessibilityInvokeEventHandler(O* object, M method) :
        AccessibilityInvokeEventHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> AccessibilityInvokeEventHandler::AccessibilityInvokeEventHandler(com_ptr<O>&& object, M method) :
        AccessibilityInvokeEventHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> AccessibilityInvokeEventHandler::AccessibilityInvokeEventHandler(weak_ref<O>&& object, M method) :
        AccessibilityInvokeEventHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto AccessibilityInvokeEventHandler::operator()() const
    {
        check_hresult((*(impl::abi_t<AccessibilityInvokeEventHandler>**)this)->Invoke());
    }
    template <typename L> CallFunc::CallFunc(L handler) :
        CallFunc(impl::make_delegate<CallFunc>(std::forward<L>(handler)))
    {
    }
    template <typename F> CallFunc::CallFunc(F* handler) :
        CallFunc([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> CallFunc::CallFunc(O* object, M method) :
        CallFunc([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> CallFunc::CallFunc(com_ptr<O>&& object, M method) :
        CallFunc([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> CallFunc::CallFunc(weak_ref<O>&& object, M method) :
        CallFunc([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto CallFunc::operator()(winrt::Windows::Foundation::IInspectable const& runtime) const
    {
        check_hresult((*(impl::abi_t<CallFunc>**)this)->Invoke(*(void**)(&runtime)));
    }
    template <typename L> ConstantProviderDelegate::ConstantProviderDelegate(L handler) :
        ConstantProviderDelegate(impl::make_delegate<ConstantProviderDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> ConstantProviderDelegate::ConstantProviderDelegate(F* handler) :
        ConstantProviderDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ConstantProviderDelegate::ConstantProviderDelegate(O* object, M method) :
        ConstantProviderDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ConstantProviderDelegate::ConstantProviderDelegate(com_ptr<O>&& object, M method) :
        ConstantProviderDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ConstantProviderDelegate::ConstantProviderDelegate(weak_ref<O>&& object, M method) :
        ConstantProviderDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ConstantProviderDelegate::operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& constantWriter) const
    {
        check_hresult((*(impl::abi_t<ConstantProviderDelegate>**)this)->Invoke(*(void**)(&constantWriter)));
    }
    template <typename L> EmitEventSetterDelegate::EmitEventSetterDelegate(L handler) :
        EmitEventSetterDelegate(impl::make_delegate<EmitEventSetterDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> EmitEventSetterDelegate::EmitEventSetterDelegate(F* handler) :
        EmitEventSetterDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> EmitEventSetterDelegate::EmitEventSetterDelegate(O* object, M method) :
        EmitEventSetterDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> EmitEventSetterDelegate::EmitEventSetterDelegate(com_ptr<O>&& object, M method) :
        EmitEventSetterDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> EmitEventSetterDelegate::EmitEventSetterDelegate(weak_ref<O>&& object, M method) :
        EmitEventSetterDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto EmitEventSetterDelegate::operator()(winrt::Microsoft::ReactNative::JSValueArgWriter const& argWriter) const
    {
        check_hresult((*(impl::abi_t<EmitEventSetterDelegate>**)this)->Invoke(*(void**)(&argWriter)));
    }
    template <typename L> EventEmitterInitializerDelegate::EventEmitterInitializerDelegate(L handler) :
        EventEmitterInitializerDelegate(impl::make_delegate<EventEmitterInitializerDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> EventEmitterInitializerDelegate::EventEmitterInitializerDelegate(F* handler) :
        EventEmitterInitializerDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> EventEmitterInitializerDelegate::EventEmitterInitializerDelegate(O* object, M method) :
        EventEmitterInitializerDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> EventEmitterInitializerDelegate::EventEmitterInitializerDelegate(com_ptr<O>&& object, M method) :
        EventEmitterInitializerDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> EventEmitterInitializerDelegate::EventEmitterInitializerDelegate(weak_ref<O>&& object, M method) :
        EventEmitterInitializerDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto EventEmitterInitializerDelegate::operator()(winrt::Microsoft::ReactNative::EmitEventSetterDelegate const& emitter) const
    {
        check_hresult((*(impl::abi_t<EventEmitterInitializerDelegate>**)this)->Invoke(*(void**)(&emitter)));
    }
    template <typename L> InitializerDelegate::InitializerDelegate(L handler) :
        InitializerDelegate(impl::make_delegate<InitializerDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> InitializerDelegate::InitializerDelegate(F* handler) :
        InitializerDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> InitializerDelegate::InitializerDelegate(O* object, M method) :
        InitializerDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> InitializerDelegate::InitializerDelegate(com_ptr<O>&& object, M method) :
        InitializerDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> InitializerDelegate::InitializerDelegate(weak_ref<O>&& object, M method) :
        InitializerDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto InitializerDelegate::operator()(winrt::Microsoft::ReactNative::IReactContext const& reactContext) const
    {
        check_hresult((*(impl::abi_t<InitializerDelegate>**)this)->Invoke(*(void**)(&reactContext)));
    }
    template <typename L> JSValueArgWriter::JSValueArgWriter(L handler) :
        JSValueArgWriter(impl::make_delegate<JSValueArgWriter>(std::forward<L>(handler)))
    {
    }
    template <typename F> JSValueArgWriter::JSValueArgWriter(F* handler) :
        JSValueArgWriter([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> JSValueArgWriter::JSValueArgWriter(O* object, M method) :
        JSValueArgWriter([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> JSValueArgWriter::JSValueArgWriter(com_ptr<O>&& object, M method) :
        JSValueArgWriter([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> JSValueArgWriter::JSValueArgWriter(weak_ref<O>&& object, M method) :
        JSValueArgWriter([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto JSValueArgWriter::operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& writer) const
    {
        check_hresult((*(impl::abi_t<JSValueArgWriter>**)this)->Invoke(*(void**)(&writer)));
    }
    template <typename L> JsiByteArrayUser::JsiByteArrayUser(L handler) :
        JsiByteArrayUser(impl::make_delegate<JsiByteArrayUser>(std::forward<L>(handler)))
    {
    }
    template <typename F> JsiByteArrayUser::JsiByteArrayUser(F* handler) :
        JsiByteArrayUser([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> JsiByteArrayUser::JsiByteArrayUser(O* object, M method) :
        JsiByteArrayUser([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiByteArrayUser::JsiByteArrayUser(com_ptr<O>&& object, M method) :
        JsiByteArrayUser([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiByteArrayUser::JsiByteArrayUser(weak_ref<O>&& object, M method) :
        JsiByteArrayUser([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto JsiByteArrayUser::operator()(array_view<uint8_t const> bytes) const
    {
        check_hresult((*(impl::abi_t<JsiByteArrayUser>**)this)->Invoke(bytes.size(), get_abi(bytes)));
    }
    template <typename L> JsiHostFunction::JsiHostFunction(L handler) :
        JsiHostFunction(impl::make_delegate<JsiHostFunction>(std::forward<L>(handler)))
    {
    }
    template <typename F> JsiHostFunction::JsiHostFunction(F* handler) :
        JsiHostFunction([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> JsiHostFunction::JsiHostFunction(O* object, M method) :
        JsiHostFunction([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiHostFunction::JsiHostFunction(com_ptr<O>&& object, M method) :
        JsiHostFunction([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiHostFunction::JsiHostFunction(weak_ref<O>&& object, M method) :
        JsiHostFunction([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto JsiHostFunction::operator()(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiValueRef const& thisArg, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const
    {
        winrt::Microsoft::ReactNative::JsiValueRef result{};
        check_hresult((*(impl::abi_t<JsiHostFunction>**)this)->Invoke(*(void**)(&runtime), impl::bind_in(thisArg), args.size(), get_abi(args), put_abi(result)));
        return result;
    }
    template <typename L> JsiInitializerDelegate::JsiInitializerDelegate(L handler) :
        JsiInitializerDelegate(impl::make_delegate<JsiInitializerDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> JsiInitializerDelegate::JsiInitializerDelegate(F* handler) :
        JsiInitializerDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> JsiInitializerDelegate::JsiInitializerDelegate(O* object, M method) :
        JsiInitializerDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiInitializerDelegate::JsiInitializerDelegate(com_ptr<O>&& object, M method) :
        JsiInitializerDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> JsiInitializerDelegate::JsiInitializerDelegate(weak_ref<O>&& object, M method) :
        JsiInitializerDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto JsiInitializerDelegate::operator()(winrt::Microsoft::ReactNative::IReactContext const& reactContext, winrt::Windows::Foundation::IInspectable const& runtimeHandle) const
    {
        check_hresult((*(impl::abi_t<JsiInitializerDelegate>**)this)->Invoke(*(void**)(&reactContext), *(void**)(&runtimeHandle)));
    }
    template <typename L> LogHandler::LogHandler(L handler) :
        LogHandler(impl::make_delegate<LogHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> LogHandler::LogHandler(F* handler) :
        LogHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> LogHandler::LogHandler(O* object, M method) :
        LogHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> LogHandler::LogHandler(com_ptr<O>&& object, M method) :
        LogHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> LogHandler::LogHandler(weak_ref<O>&& object, M method) :
        LogHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto LogHandler::operator()(winrt::Microsoft::ReactNative::LogLevel const& level, param::hstring const& message) const
    {
        check_hresult((*(impl::abi_t<LogHandler>**)this)->Invoke(static_cast<int32_t>(level), *(void**)(&message)));
    }
    template <typename L> MethodDelegate::MethodDelegate(L handler) :
        MethodDelegate(impl::make_delegate<MethodDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> MethodDelegate::MethodDelegate(F* handler) :
        MethodDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> MethodDelegate::MethodDelegate(O* object, M method) :
        MethodDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> MethodDelegate::MethodDelegate(com_ptr<O>&& object, M method) :
        MethodDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> MethodDelegate::MethodDelegate(weak_ref<O>&& object, M method) :
        MethodDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto MethodDelegate::operator()(winrt::Microsoft::ReactNative::IJSValueReader const& inputReader, winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter, winrt::Microsoft::ReactNative::MethodResultCallback const& resolve, winrt::Microsoft::ReactNative::MethodResultCallback const& reject) const
    {
        check_hresult((*(impl::abi_t<MethodDelegate>**)this)->Invoke(*(void**)(&inputReader), *(void**)(&outputWriter), *(void**)(&resolve), *(void**)(&reject)));
    }
    template <typename L> MethodResultCallback::MethodResultCallback(L handler) :
        MethodResultCallback(impl::make_delegate<MethodResultCallback>(std::forward<L>(handler)))
    {
    }
    template <typename F> MethodResultCallback::MethodResultCallback(F* handler) :
        MethodResultCallback([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> MethodResultCallback::MethodResultCallback(O* object, M method) :
        MethodResultCallback([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> MethodResultCallback::MethodResultCallback(com_ptr<O>&& object, M method) :
        MethodResultCallback([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> MethodResultCallback::MethodResultCallback(weak_ref<O>&& object, M method) :
        MethodResultCallback([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto MethodResultCallback::operator()(winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter) const
    {
        check_hresult((*(impl::abi_t<MethodResultCallback>**)this)->Invoke(*(void**)(&outputWriter)));
    }
    template <typename L> ReactCreatePropertyValue::ReactCreatePropertyValue(L handler) :
        ReactCreatePropertyValue(impl::make_delegate<ReactCreatePropertyValue>(std::forward<L>(handler)))
    {
    }
    template <typename F> ReactCreatePropertyValue::ReactCreatePropertyValue(F* handler) :
        ReactCreatePropertyValue([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ReactCreatePropertyValue::ReactCreatePropertyValue(O* object, M method) :
        ReactCreatePropertyValue([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactCreatePropertyValue::ReactCreatePropertyValue(com_ptr<O>&& object, M method) :
        ReactCreatePropertyValue([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactCreatePropertyValue::ReactCreatePropertyValue(weak_ref<O>&& object, M method) :
        ReactCreatePropertyValue([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ReactCreatePropertyValue::operator()() const
    {
        void* result{};
        check_hresult((*(impl::abi_t<ReactCreatePropertyValue>**)this)->Invoke(&result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename L> ReactDispatcherCallback::ReactDispatcherCallback(L handler) :
        ReactDispatcherCallback(impl::make_delegate<ReactDispatcherCallback>(std::forward<L>(handler)))
    {
    }
    template <typename F> ReactDispatcherCallback::ReactDispatcherCallback(F* handler) :
        ReactDispatcherCallback([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ReactDispatcherCallback::ReactDispatcherCallback(O* object, M method) :
        ReactDispatcherCallback([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactDispatcherCallback::ReactDispatcherCallback(com_ptr<O>&& object, M method) :
        ReactDispatcherCallback([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactDispatcherCallback::ReactDispatcherCallback(weak_ref<O>&& object, M method) :
        ReactDispatcherCallback([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ReactDispatcherCallback::operator()() const
    {
        check_hresult((*(impl::abi_t<ReactDispatcherCallback>**)this)->Invoke());
    }
    template <typename L> ReactModuleProvider::ReactModuleProvider(L handler) :
        ReactModuleProvider(impl::make_delegate<ReactModuleProvider>(std::forward<L>(handler)))
    {
    }
    template <typename F> ReactModuleProvider::ReactModuleProvider(F* handler) :
        ReactModuleProvider([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ReactModuleProvider::ReactModuleProvider(O* object, M method) :
        ReactModuleProvider([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactModuleProvider::ReactModuleProvider(com_ptr<O>&& object, M method) :
        ReactModuleProvider([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactModuleProvider::ReactModuleProvider(weak_ref<O>&& object, M method) :
        ReactModuleProvider([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ReactModuleProvider::operator()(winrt::Microsoft::ReactNative::IReactModuleBuilder const& moduleBuilder) const
    {
        void* result{};
        check_hresult((*(impl::abi_t<ReactModuleProvider>**)this)->Invoke(*(void**)(&moduleBuilder), &result));
        return winrt::Windows::Foundation::IInspectable{ result, take_ownership_from_abi };
    }
    template <typename L> ReactNotificationHandler::ReactNotificationHandler(L handler) :
        ReactNotificationHandler(impl::make_delegate<ReactNotificationHandler>(std::forward<L>(handler)))
    {
    }
    template <typename F> ReactNotificationHandler::ReactNotificationHandler(F* handler) :
        ReactNotificationHandler([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ReactNotificationHandler::ReactNotificationHandler(O* object, M method) :
        ReactNotificationHandler([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactNotificationHandler::ReactNotificationHandler(com_ptr<O>&& object, M method) :
        ReactNotificationHandler([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactNotificationHandler::ReactNotificationHandler(weak_ref<O>&& object, M method) :
        ReactNotificationHandler([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ReactNotificationHandler::operator()(winrt::Windows::Foundation::IInspectable const& sender, winrt::Microsoft::ReactNative::IReactNotificationArgs const& args) const
    {
        check_hresult((*(impl::abi_t<ReactNotificationHandler>**)this)->Invoke(*(void**)(&sender), *(void**)(&args)));
    }
    template <typename L> ReactViewManagerProvider::ReactViewManagerProvider(L handler) :
        ReactViewManagerProvider(impl::make_delegate<ReactViewManagerProvider>(std::forward<L>(handler)))
    {
    }
    template <typename F> ReactViewManagerProvider::ReactViewManagerProvider(F* handler) :
        ReactViewManagerProvider([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> ReactViewManagerProvider::ReactViewManagerProvider(O* object, M method) :
        ReactViewManagerProvider([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactViewManagerProvider::ReactViewManagerProvider(com_ptr<O>&& object, M method) :
        ReactViewManagerProvider([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> ReactViewManagerProvider::ReactViewManagerProvider(weak_ref<O>&& object, M method) :
        ReactViewManagerProvider([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto ReactViewManagerProvider::operator()() const
    {
        void* result{};
        check_hresult((*(impl::abi_t<ReactViewManagerProvider>**)this)->Invoke(&result));
        return winrt::Microsoft::ReactNative::IViewManager{ result, take_ownership_from_abi };
    }
    template <typename L> SyncMethodDelegate::SyncMethodDelegate(L handler) :
        SyncMethodDelegate(impl::make_delegate<SyncMethodDelegate>(std::forward<L>(handler)))
    {
    }
    template <typename F> SyncMethodDelegate::SyncMethodDelegate(F* handler) :
        SyncMethodDelegate([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> SyncMethodDelegate::SyncMethodDelegate(O* object, M method) :
        SyncMethodDelegate([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> SyncMethodDelegate::SyncMethodDelegate(com_ptr<O>&& object, M method) :
        SyncMethodDelegate([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> SyncMethodDelegate::SyncMethodDelegate(weak_ref<O>&& object, M method) :
        SyncMethodDelegate([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto SyncMethodDelegate::operator()(winrt::Microsoft::ReactNative::IJSValueReader const& inputReader, winrt::Microsoft::ReactNative::IJSValueWriter const& outputWriter) const
    {
        check_hresult((*(impl::abi_t<SyncMethodDelegate>**)this)->Invoke(*(void**)(&inputReader), *(void**)(&outputWriter)));
    }
    template <typename L> TimerFactory::TimerFactory(L handler) :
        TimerFactory(impl::make_delegate<TimerFactory>(std::forward<L>(handler)))
    {
    }
    template <typename F> TimerFactory::TimerFactory(F* handler) :
        TimerFactory([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> TimerFactory::TimerFactory(O* object, M method) :
        TimerFactory([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> TimerFactory::TimerFactory(com_ptr<O>&& object, M method) :
        TimerFactory([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> TimerFactory::TimerFactory(weak_ref<O>&& object, M method) :
        TimerFactory([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto TimerFactory::operator()() const
    {
        void* result{};
        check_hresult((*(impl::abi_t<TimerFactory>**)this)->Invoke(&result));
        return winrt::Microsoft::ReactNative::ITimer{ result, take_ownership_from_abi };
    }
    template <typename L> UIBatchCompleteCallback::UIBatchCompleteCallback(L handler) :
        UIBatchCompleteCallback(impl::make_delegate<UIBatchCompleteCallback>(std::forward<L>(handler)))
    {
    }
    template <typename F> UIBatchCompleteCallback::UIBatchCompleteCallback(F* handler) :
        UIBatchCompleteCallback([=](auto&&... args) { return handler(args...); })
    {
    }
    template <typename O, typename M> UIBatchCompleteCallback::UIBatchCompleteCallback(O* object, M method) :
        UIBatchCompleteCallback([=](auto&&... args) { return ((*object).*(method))(args...); })
    {
    }
    template <typename O, typename M> UIBatchCompleteCallback::UIBatchCompleteCallback(com_ptr<O>&& object, M method) :
        UIBatchCompleteCallback([o = std::move(object), method](auto&&... args) { return ((*o).*(method))(args...); })
    {
    }
    template <typename O, typename M> UIBatchCompleteCallback::UIBatchCompleteCallback(weak_ref<O>&& object, M method) :
        UIBatchCompleteCallback([o = std::move(object), method](auto&&... args) { if (auto s = o.get()) { ((*s).*(method))(args...); } })
    {
    }
    inline auto UIBatchCompleteCallback::operator()(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const
    {
        check_hresult((*(impl::abi_t<UIBatchCompleteCallback>**)this)->Invoke(*(void**)(&properties)));
    }
    template <typename D, typename... Interfaces>
    struct ReactApplicationT :
        implements<D, winrt::Windows::UI::Xaml::IApplicationOverrides, winrt::Windows::UI::Xaml::IApplicationOverrides2, composing, Interfaces...>,
        impl::require<D, winrt::Microsoft::ReactNative::IReactApplication, winrt::Windows::UI::Xaml::IApplication, winrt::Windows::UI::Xaml::IApplication2, winrt::Windows::UI::Xaml::IApplication3>,
        impl::base<D, ReactApplication, winrt::Windows::UI::Xaml::Application>,
        winrt::Windows::UI::Xaml::IApplicationOverridesT<D>, winrt::Windows::UI::Xaml::IApplicationOverrides2T<D>
    {
        using composable = ReactApplication;
    protected:
        ReactApplicationT()
        {
            impl::call_factory<ReactApplication, IReactApplicationFactory>([&](IReactApplicationFactory const& f) { [[maybe_unused]] auto winrt_impl_discarded = f.CreateInstance(*this, this->m_inner); });
        }
    };
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::Microsoft::ReactNative::IBorderEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ICallInvoker> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IColorSourceEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ICompositeStepEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDevMenuControl> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicAutomationProperties> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicValueProvider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IGaussianBlurEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IHttpSettingsStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJSValueReader> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJSValueWriter> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiByteBuffer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiError> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiHostObject> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiRuntime> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IJsiRuntimeStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ILayoutService> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ILayoutServiceStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IQuirkSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IQuirkSettingsStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactApplication> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactApplicationFactory> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactCoreInjection> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactDispatcher> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactInstanceSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactModuleBuilder> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNativeHost> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNativeHostStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNonAbiValue> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNotificationArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNotificationService> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactNotificationSubscription> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPackageBuilder> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPackageProvider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPointerEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPropertyBag> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPropertyName> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactPropertyNamespace> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactRootView> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactSettingsSnapshot> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactViewHost> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactViewInstance> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IReactViewOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IRedBoxErrorInfo> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IRedBoxHandler> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IRedBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IRedBoxHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ITimer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ITimer2> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ITimerStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewControl> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManager> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithChildren> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithCommands> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewManagerWithReactContext> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewPanel> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IViewPanelStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IXamlHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IXamlHelperStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IXamlUIService> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::IXamlUIServiceStatics> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::BorderEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::CallInvoker> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ColorSourceEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::CompositeStepEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::DevMenuControl> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::DynamicAutomationPeer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::DynamicAutomationProperties> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::DynamicValueProvider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::GaussianBlurEffect> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::HttpSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::JsiError> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::JsiPreparedJavaScript> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::JsiRuntime> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::LayoutService> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::QuirkSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactApplication> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactCoreInjection> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactDispatcherHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactInstanceSettings> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactNativeHost> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactNotificationServiceHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactPointerEventArgs> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactPropertyBagHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactRootView> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ReactViewOptions> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::RedBoxHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::Timer> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ViewControl> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::ViewPanel> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::XamlHelper> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::XamlMetaDataProvider> : winrt::impl::hash_base {};
    template<> struct hash<winrt::Microsoft::ReactNative::XamlUIService> : winrt::impl::hash_base {};
#endif
#ifdef __cpp_lib_format
#endif
}
#endif
