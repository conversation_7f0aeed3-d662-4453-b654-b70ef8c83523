// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_SocialInfo_2_H
#define WINRT_Windows_ApplicationModel_SocialInfo_2_H
#include "winrt/impl/Windows.ApplicationModel.SocialInfo.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::SocialInfo
{
    struct WINRT_IMPL_EMPTY_BASES SocialFeedChildItem : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedChildItem
    {
        SocialFeedChildItem(std::nullptr_t) noexcept {}
        SocialFeedChildItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedChildItem(ptr, take_ownership_from_abi) {}
        SocialFeedChildItem();
    };
    struct WINRT_IMPL_EMPTY_BASES SocialFeedContent : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedContent
    {
        SocialFeedContent(std::nullptr_t) noexcept {}
        SocialFeedContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedContent(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES SocialFeedItem : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedItem
    {
        SocialFeedItem(std::nullptr_t) noexcept {}
        SocialFeedItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedItem(ptr, take_ownership_from_abi) {}
        SocialFeedItem();
    };
    struct WINRT_IMPL_EMPTY_BASES SocialFeedSharedItem : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedSharedItem
    {
        SocialFeedSharedItem(std::nullptr_t) noexcept {}
        SocialFeedSharedItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialFeedSharedItem(ptr, take_ownership_from_abi) {}
        SocialFeedSharedItem();
    };
    struct WINRT_IMPL_EMPTY_BASES SocialItemThumbnail : winrt::Windows::ApplicationModel::SocialInfo::ISocialItemThumbnail
    {
        SocialItemThumbnail(std::nullptr_t) noexcept {}
        SocialItemThumbnail(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialItemThumbnail(ptr, take_ownership_from_abi) {}
        SocialItemThumbnail();
    };
    struct WINRT_IMPL_EMPTY_BASES SocialUserInfo : winrt::Windows::ApplicationModel::SocialInfo::ISocialUserInfo
    {
        SocialUserInfo(std::nullptr_t) noexcept {}
        SocialUserInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::ApplicationModel::SocialInfo::ISocialUserInfo(ptr, take_ownership_from_abi) {}
    };
}
#endif
