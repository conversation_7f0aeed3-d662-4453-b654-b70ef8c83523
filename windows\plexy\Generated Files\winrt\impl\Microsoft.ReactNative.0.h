// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_ReactNative_0_H
#define WINRT_Microsoft_ReactNative_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES EventHandler;
    struct EventRegistrationToken;
    struct IAsyncAction;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IVector;
}
WINRT_EXPORT namespace winrt::Windows::Graphics::Effects
{
    struct IGraphicsEffectSource;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct Color;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml
{
    struct DependencyObject;
    struct DependencyProperty;
    struct FrameworkElement;
    struct UIElement;
    struct XamlRoot;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Automation::Peers
{
    struct FrameworkElementAutomationPeer;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Controls
{
    struct Button;
    struct FontIcon;
    struct Panel;
    struct TextBlock;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Input
{
    struct PointerRoutedEventArgs;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Markup
{
    struct IXamlMetadataProvider;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Media
{
    struct Brush;
}
WINRT_EXPORT namespace winrt::Microsoft::ReactNative
{
    enum class AccessibilityRoles : int32_t
    {
        None = 0,
        Button = 1,
        Link = 2,
        Search = 3,
        Image = 4,
        KeyboardKey = 5,
        Text = 6,
        Adjustable = 7,
        ImageButton = 8,
        Header = 9,
        Summary = 10,
        Alert = 11,
        CheckBox = 12,
        ComboBox = 13,
        Menu = 14,
        MenuBar = 15,
        MenuItem = 16,
        ProgressBar = 17,
        Radio = 18,
        RadioGroup = 19,
        ScrollBar = 20,
        SpinButton = 21,
        Switch = 22,
        Tab = 23,
        TabList = 24,
        Timer = 25,
        ToggleButton = 26,
        ToolBar = 27,
        List = 28,
        ListItem = 29,
        Unknown = 30,
        CountRoles = 31,
    };
    enum class AccessibilityStateCheckedValue : int32_t
    {
        Unchecked = 0,
        Checked = 1,
        Mixed = 2,
    };
    enum class AccessibilityStates : int32_t
    {
        Selected = 0,
        Disabled = 1,
        Checked = 2,
        Busy = 3,
        Expanded = 4,
        CountStates = 5,
    };
    enum class AccessibilityValue : int32_t
    {
        Min = 0,
        Max = 1,
        Now = 2,
        Text = 3,
    };
    enum class AriaRole : int32_t
    {
        Unknown = 0,
        Alert = 1,
        AlertDialog = 2,
        Application = 3,
        Article = 4,
        Banner = 5,
        Button = 6,
        Cell = 7,
        CheckBox = 8,
        ColumnHeader = 9,
        ComboBox = 10,
        Complementary = 11,
        ContentInfo = 12,
        Definition = 13,
        Dialog = 14,
        Directory = 15,
        Document = 16,
        Feed = 17,
        Figure = 18,
        Form = 19,
        Grid = 20,
        Group = 21,
        Heading = 22,
        Img = 23,
        Link = 24,
        List = 25,
        ListItem = 26,
        Log = 27,
        Main = 28,
        Marquee = 29,
        Math = 30,
        Menu = 31,
        MenuBar = 32,
        MenuItem = 33,
        Meter = 34,
        Navigation = 35,
        None = 36,
        Note = 37,
        Option = 38,
        Presentation = 39,
        ProgressBar = 40,
        Radio = 41,
        RadioGroup = 42,
        Region = 43,
        Row = 44,
        RowGroup = 45,
        RowHeader = 46,
        ScrollBar = 47,
        SearchBox = 48,
        Separator = 49,
        Slider = 50,
        SpinButton = 51,
        Status = 52,
        Summary = 53,
        Switch = 54,
        Tab = 55,
        Table = 56,
        TabList = 57,
        TabPanel = 58,
        Term = 59,
        Timer = 60,
        ToolBar = 61,
        ToolTip = 62,
        Tree = 63,
        TreeGrid = 64,
        TreeItem = 65,
        CountRoles = 66,
    };
    enum class BackNavigationHandlerKind : int32_t
    {
        JavaScript = 0,
        Native = 1,
    };
    enum class CanvasComposite : int32_t
    {
        SourceOver = 0,
        DestinationOver = 1,
        SourceIn = 2,
        DestinationIn = 3,
        SourceOut = 4,
        DestinationOut = 5,
        SourceAtop = 6,
        DestinationAtop = 7,
        Xor = 8,
        Add = 9,
        Copy = 10,
        BoundedCopy = 11,
        MaskInvert = 12,
    };
    enum class CanvasEdgeBehavior : int32_t
    {
        Clamp = 0,
        Wrap = 1,
        Mirror = 2,
    };
    enum class EffectBorderMode : int32_t
    {
        Soft = 0,
        Hard = 1,
    };
    enum class EffectOptimization : int32_t
    {
        Speed = 0,
        Balanced = 1,
        Quality = 2,
    };
    enum class JSIEngine : int32_t
    {
        Chakra = 0,
        Hermes = 1,
        V8 = 2,
    };
    enum class JSValueType : int32_t
    {
        Null = 0,
        Object = 1,
        Array = 2,
        String = 3,
        Boolean = 4,
        Int64 = 5,
        Double = 6,
    };
    enum class JsiErrorType : int32_t
    {
        JSError = 0,
        NativeException = 1,
    };
    enum class JsiValueKind : int32_t
    {
        Undefined = 0,
        Null = 1,
        Boolean = 2,
        Number = 3,
        Symbol = 4,
        BigInt = 5,
        String = 6,
        Object = 7,
    };
    enum class LoadingState : int32_t
    {
        Loading = 0,
        Loaded = 1,
        HasError = 2,
        Unloaded = 3,
    };
    enum class LogLevel : int32_t
    {
        Trace = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Fatal = 4,
    };
    enum class MethodReturnType : int32_t
    {
        Void = 0,
        Callback = 1,
        TwoCallbacks = 2,
        Promise = 3,
    };
    enum class PointerEventKind : int32_t
    {
        None = 0,
        Start = 1,
        End = 2,
        Move = 3,
        Cancel = 4,
        CaptureLost = 5,
    };
    enum class RedBoxErrorType : int32_t
    {
        JavaScriptFatal = 0,
        JavaScriptSoft = 1,
        Native = 2,
    };
    enum class ViewManagerPropertyType : int32_t
    {
        Boolean = 0,
        Number = 1,
        String = 2,
        Array = 3,
        Map = 4,
        Color = 5,
        Function = 6,
        ImageSource = 7,
    };
    struct IBorderEffect;
    struct ICallInvoker;
    struct IColorSourceEffect;
    struct ICompositeStepEffect;
    struct IDevMenuControl;
    struct IDynamicAutomationPeer;
    struct IDynamicAutomationPeerFactory;
    struct IDynamicAutomationProperties;
    struct IDynamicAutomationPropertiesStatics;
    struct IDynamicValueProvider;
    struct IDynamicValueProviderFactory;
    struct IGaussianBlurEffect;
    struct IHttpSettingsStatics;
    struct IInstanceCreatedEventArgs;
    struct IInstanceDestroyedEventArgs;
    struct IInstanceLoadedEventArgs;
    struct IJSValueReader;
    struct IJSValueWriter;
    struct IJsiByteBuffer;
    struct IJsiError;
    struct IJsiHostObject;
    struct IJsiPreparedJavaScript;
    struct IJsiRuntime;
    struct IJsiRuntimeStatics;
    struct ILayoutService;
    struct ILayoutServiceStatics;
    struct IQuirkSettings;
    struct IQuirkSettingsStatics;
    struct IReactApplication;
    struct IReactApplicationFactory;
    struct IReactContext;
    struct IReactCoreInjection;
    struct IReactCoreInjectionStatics;
    struct IReactDispatcher;
    struct IReactDispatcherHelperStatics;
    struct IReactInstanceSettings;
    struct IReactModuleBuilder;
    struct IReactNativeHost;
    struct IReactNativeHostStatics;
    struct IReactNonAbiValue;
    struct IReactNotificationArgs;
    struct IReactNotificationService;
    struct IReactNotificationServiceHelperStatics;
    struct IReactNotificationSubscription;
    struct IReactPackageBuilder;
    struct IReactPackageProvider;
    struct IReactPointerEventArgs;
    struct IReactPropertyBag;
    struct IReactPropertyBagHelperStatics;
    struct IReactPropertyName;
    struct IReactPropertyNamespace;
    struct IReactRootView;
    struct IReactSettingsSnapshot;
    struct IReactViewHost;
    struct IReactViewInstance;
    struct IReactViewOptions;
    struct IRedBoxErrorFrameInfo;
    struct IRedBoxErrorInfo;
    struct IRedBoxHandler;
    struct IRedBoxHelper;
    struct IRedBoxHelperStatics;
    struct ITimer;
    struct ITimer2;
    struct ITimerStatics;
    struct IViewControl;
    struct IViewManager;
    struct IViewManagerCreateWithProperties;
    struct IViewManagerRequiresNativeLayout;
    struct IViewManagerWithChildren;
    struct IViewManagerWithCommands;
    struct IViewManagerWithDropViewInstance;
    struct IViewManagerWithExportedEventTypeConstants;
    struct IViewManagerWithExportedViewConstants;
    struct IViewManagerWithNativeProperties;
    struct IViewManagerWithOnLayout;
    struct IViewManagerWithPointerEvents;
    struct IViewManagerWithReactContext;
    struct IViewPanel;
    struct IViewPanelStatics;
    struct IXamlHelper;
    struct IXamlHelperStatics;
    struct IXamlUIService;
    struct IXamlUIServiceStatics;
    struct BorderEffect;
    struct CallInvoker;
    struct ColorSourceEffect;
    struct CompositeStepEffect;
    struct DevMenuControl;
    struct DynamicAutomationPeer;
    struct DynamicAutomationProperties;
    struct DynamicValueProvider;
    struct GaussianBlurEffect;
    struct HttpSettings;
    struct InstanceCreatedEventArgs;
    struct InstanceDestroyedEventArgs;
    struct InstanceLoadedEventArgs;
    struct JsiError;
    struct JsiPreparedJavaScript;
    struct JsiRuntime;
    struct LayoutService;
    struct QuirkSettings;
    struct ReactApplication;
    struct ReactCoreInjection;
    struct ReactDispatcherHelper;
    struct ReactInstanceSettings;
    struct ReactNativeHost;
    struct ReactNotificationServiceHelper;
    struct ReactPointerEventArgs;
    struct ReactPropertyBagHelper;
    struct ReactRootView;
    struct ReactViewOptions;
    struct RedBoxHelper;
    struct Timer;
    struct ViewControl;
    struct ViewPanel;
    struct XamlHelper;
    struct XamlMetaDataProvider;
    struct XamlUIService;
    struct AccessibilityAction;
    struct DesktopWindowMessage;
    struct JsiBigIntRef;
    struct JsiObjectRef;
    struct JsiPropertyIdRef;
    struct JsiScopeState;
    struct JsiStringRef;
    struct JsiSymbolRef;
    struct JsiValueRef;
    struct JsiWeakObjectRef;
    struct AccessibilityActionEventHandler;
    struct AccessibilityInvokeEventHandler;
    struct CallFunc;
    struct ConstantProviderDelegate;
    struct EmitEventSetterDelegate;
    struct EventEmitterInitializerDelegate;
    struct InitializerDelegate;
    struct JSValueArgWriter;
    struct JsiByteArrayUser;
    struct JsiHostFunction;
    struct JsiInitializerDelegate;
    struct LogHandler;
    struct MethodDelegate;
    struct MethodResultCallback;
    struct ReactCreatePropertyValue;
    struct ReactDispatcherCallback;
    struct ReactModuleProvider;
    struct ReactNotificationHandler;
    struct ReactViewManagerProvider;
    struct SyncMethodDelegate;
    struct TimerFactory;
    struct UIBatchCompleteCallback;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::ReactNative::IBorderEffect>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ICallInvoker>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IColorSourceEffect>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ICompositeStepEffect>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDevMenuControl>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicAutomationProperties>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicValueProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IGaussianBlurEffect>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IHttpSettingsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJSValueReader>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJSValueWriter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiByteBuffer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiError>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiHostObject>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiRuntime>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IJsiRuntimeStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ILayoutService>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ILayoutServiceStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IQuirkSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IQuirkSettingsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactApplication>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactApplicationFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactContext>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactCoreInjection>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactDispatcher>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactInstanceSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactModuleBuilder>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNativeHost>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNativeHostStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNonAbiValue>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNotificationArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNotificationService>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactNotificationSubscription>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPackageBuilder>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPackageProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPointerEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPropertyBag>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPropertyName>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactPropertyNamespace>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactRootView>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactSettingsSnapshot>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactViewHost>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactViewInstance>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IReactViewOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IRedBoxErrorInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IRedBoxHandler>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IRedBoxHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IRedBoxHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ITimer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ITimer2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ITimerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewControl>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithChildren>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithCommands>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewManagerWithReactContext>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewPanel>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IViewPanelStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IXamlHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IXamlHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IXamlUIService>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::IXamlUIServiceStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::ReactNative::BorderEffect>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::CallInvoker>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ColorSourceEffect>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::CompositeStepEffect>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::DevMenuControl>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::DynamicAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::DynamicAutomationProperties>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::DynamicValueProvider>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::GaussianBlurEffect>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::HttpSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiError>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiPreparedJavaScript>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiRuntime>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::LayoutService>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::QuirkSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactApplication>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactCoreInjection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactDispatcherHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactInstanceSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactNativeHost>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactNotificationServiceHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactPointerEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactPropertyBagHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactRootView>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactViewOptions>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::RedBoxHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::Timer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ViewControl>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ViewPanel>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::XamlHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::XamlMetaDataProvider>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::XamlUIService>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityRoles>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityStates>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityValue>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AriaRole>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::BackNavigationHandlerKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::CanvasComposite>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::CanvasEdgeBehavior>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::EffectBorderMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::EffectOptimization>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JSIEngine>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JSValueType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiErrorType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiValueKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::LoadingState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::LogLevel>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::MethodReturnType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::PointerEventKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::RedBoxErrorType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ViewManagerPropertyType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityAction>{ using type = struct_category<hstring, hstring>; };
    template <> struct category<winrt::Microsoft::ReactNative::DesktopWindowMessage>{ using type = struct_category<uint32_t, uint64_t, int64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiBigIntRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiObjectRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiPropertyIdRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiScopeState>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiStringRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiSymbolRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiValueRef>{ using type = struct_category<winrt::Microsoft::ReactNative::JsiValueKind, uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiWeakObjectRef>{ using type = struct_category<uint64_t>; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::CallFunc>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ConstantProviderDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::EmitEventSetterDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::InitializerDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JSValueArgWriter>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiByteArrayUser>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiHostFunction>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::JsiInitializerDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::LogHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::MethodDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::MethodResultCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactCreatePropertyValue>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactDispatcherCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactModuleProvider>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactNotificationHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::ReactViewManagerProvider>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::SyncMethodDelegate>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::TimerFactory>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::ReactNative::UIBatchCompleteCallback>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::BorderEffect> = L"Microsoft.ReactNative.BorderEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::CallInvoker> = L"Microsoft.ReactNative.CallInvoker";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ColorSourceEffect> = L"Microsoft.ReactNative.ColorSourceEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::CompositeStepEffect> = L"Microsoft.ReactNative.CompositeStepEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::DevMenuControl> = L"Microsoft.ReactNative.DevMenuControl";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::DynamicAutomationPeer> = L"Microsoft.ReactNative.DynamicAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::DynamicAutomationProperties> = L"Microsoft.ReactNative.DynamicAutomationProperties";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::DynamicValueProvider> = L"Microsoft.ReactNative.DynamicValueProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::GaussianBlurEffect> = L"Microsoft.ReactNative.GaussianBlurEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::HttpSettings> = L"Microsoft.ReactNative.HttpSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> = L"Microsoft.ReactNative.InstanceCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> = L"Microsoft.ReactNative.InstanceDestroyedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> = L"Microsoft.ReactNative.InstanceLoadedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiError> = L"Microsoft.ReactNative.JsiError";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiPreparedJavaScript> = L"Microsoft.ReactNative.JsiPreparedJavaScript";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiRuntime> = L"Microsoft.ReactNative.JsiRuntime";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::LayoutService> = L"Microsoft.ReactNative.LayoutService";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::QuirkSettings> = L"Microsoft.ReactNative.QuirkSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactApplication> = L"Microsoft.ReactNative.ReactApplication";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactCoreInjection> = L"Microsoft.ReactNative.ReactCoreInjection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactDispatcherHelper> = L"Microsoft.ReactNative.ReactDispatcherHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactInstanceSettings> = L"Microsoft.ReactNative.ReactInstanceSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactNativeHost> = L"Microsoft.ReactNative.ReactNativeHost";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactNotificationServiceHelper> = L"Microsoft.ReactNative.ReactNotificationServiceHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactPointerEventArgs> = L"Microsoft.ReactNative.ReactPointerEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactPropertyBagHelper> = L"Microsoft.ReactNative.ReactPropertyBagHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactRootView> = L"Microsoft.ReactNative.ReactRootView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactViewOptions> = L"Microsoft.ReactNative.ReactViewOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::RedBoxHelper> = L"Microsoft.ReactNative.RedBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::Timer> = L"Microsoft.ReactNative.Timer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ViewControl> = L"Microsoft.ReactNative.ViewControl";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ViewPanel> = L"Microsoft.ReactNative.ViewPanel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::XamlHelper> = L"Microsoft.ReactNative.XamlHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::XamlMetaDataProvider> = L"Microsoft.ReactNative.XamlMetaDataProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::XamlUIService> = L"Microsoft.ReactNative.XamlUIService";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityRoles> = L"Microsoft.ReactNative.AccessibilityRoles";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue> = L"Microsoft.ReactNative.AccessibilityStateCheckedValue";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityStates> = L"Microsoft.ReactNative.AccessibilityStates";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityValue> = L"Microsoft.ReactNative.AccessibilityValue";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AriaRole> = L"Microsoft.ReactNative.AriaRole";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::BackNavigationHandlerKind> = L"Microsoft.ReactNative.BackNavigationHandlerKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::CanvasComposite> = L"Microsoft.ReactNative.CanvasComposite";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::CanvasEdgeBehavior> = L"Microsoft.ReactNative.CanvasEdgeBehavior";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::EffectBorderMode> = L"Microsoft.ReactNative.EffectBorderMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::EffectOptimization> = L"Microsoft.ReactNative.EffectOptimization";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JSIEngine> = L"Microsoft.ReactNative.JSIEngine";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JSValueType> = L"Microsoft.ReactNative.JSValueType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiErrorType> = L"Microsoft.ReactNative.JsiErrorType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiValueKind> = L"Microsoft.ReactNative.JsiValueKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::LoadingState> = L"Microsoft.ReactNative.LoadingState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::LogLevel> = L"Microsoft.ReactNative.LogLevel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::MethodReturnType> = L"Microsoft.ReactNative.MethodReturnType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::PointerEventKind> = L"Microsoft.ReactNative.PointerEventKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::RedBoxErrorType> = L"Microsoft.ReactNative.RedBoxErrorType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ViewManagerPropertyType> = L"Microsoft.ReactNative.ViewManagerPropertyType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityAction> = L"Microsoft.ReactNative.AccessibilityAction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::DesktopWindowMessage> = L"Microsoft.ReactNative.DesktopWindowMessage";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiBigIntRef> = L"Microsoft.ReactNative.JsiBigIntRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiObjectRef> = L"Microsoft.ReactNative.JsiObjectRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiPropertyIdRef> = L"Microsoft.ReactNative.JsiPropertyIdRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiScopeState> = L"Microsoft.ReactNative.JsiScopeState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiStringRef> = L"Microsoft.ReactNative.JsiStringRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiSymbolRef> = L"Microsoft.ReactNative.JsiSymbolRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiValueRef> = L"Microsoft.ReactNative.JsiValueRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiWeakObjectRef> = L"Microsoft.ReactNative.JsiWeakObjectRef";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IBorderEffect> = L"Microsoft.ReactNative.IBorderEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ICallInvoker> = L"Microsoft.ReactNative.ICallInvoker";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IColorSourceEffect> = L"Microsoft.ReactNative.IColorSourceEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ICompositeStepEffect> = L"Microsoft.ReactNative.ICompositeStepEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDevMenuControl> = L"Microsoft.ReactNative.IDevMenuControl";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicAutomationPeer> = L"Microsoft.ReactNative.IDynamicAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory> = L"Microsoft.ReactNative.IDynamicAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicAutomationProperties> = L"Microsoft.ReactNative.IDynamicAutomationProperties";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics> = L"Microsoft.ReactNative.IDynamicAutomationPropertiesStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicValueProvider> = L"Microsoft.ReactNative.IDynamicValueProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory> = L"Microsoft.ReactNative.IDynamicValueProviderFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IGaussianBlurEffect> = L"Microsoft.ReactNative.IGaussianBlurEffect";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IHttpSettingsStatics> = L"Microsoft.ReactNative.IHttpSettingsStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs> = L"Microsoft.ReactNative.IInstanceCreatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs> = L"Microsoft.ReactNative.IInstanceDestroyedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs> = L"Microsoft.ReactNative.IInstanceLoadedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJSValueReader> = L"Microsoft.ReactNative.IJSValueReader";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJSValueWriter> = L"Microsoft.ReactNative.IJSValueWriter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiByteBuffer> = L"Microsoft.ReactNative.IJsiByteBuffer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiError> = L"Microsoft.ReactNative.IJsiError";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiHostObject> = L"Microsoft.ReactNative.IJsiHostObject";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript> = L"Microsoft.ReactNative.IJsiPreparedJavaScript";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiRuntime> = L"Microsoft.ReactNative.IJsiRuntime";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IJsiRuntimeStatics> = L"Microsoft.ReactNative.IJsiRuntimeStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ILayoutService> = L"Microsoft.ReactNative.ILayoutService";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ILayoutServiceStatics> = L"Microsoft.ReactNative.ILayoutServiceStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IQuirkSettings> = L"Microsoft.ReactNative.IQuirkSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IQuirkSettingsStatics> = L"Microsoft.ReactNative.IQuirkSettingsStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactApplication> = L"Microsoft.ReactNative.IReactApplication";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactApplicationFactory> = L"Microsoft.ReactNative.IReactApplicationFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactContext> = L"Microsoft.ReactNative.IReactContext";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactCoreInjection> = L"Microsoft.ReactNative.IReactCoreInjection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics> = L"Microsoft.ReactNative.IReactCoreInjectionStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactDispatcher> = L"Microsoft.ReactNative.IReactDispatcher";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics> = L"Microsoft.ReactNative.IReactDispatcherHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactInstanceSettings> = L"Microsoft.ReactNative.IReactInstanceSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactModuleBuilder> = L"Microsoft.ReactNative.IReactModuleBuilder";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNativeHost> = L"Microsoft.ReactNative.IReactNativeHost";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNativeHostStatics> = L"Microsoft.ReactNative.IReactNativeHostStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNonAbiValue> = L"Microsoft.ReactNative.IReactNonAbiValue";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNotificationArgs> = L"Microsoft.ReactNative.IReactNotificationArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNotificationService> = L"Microsoft.ReactNative.IReactNotificationService";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics> = L"Microsoft.ReactNative.IReactNotificationServiceHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactNotificationSubscription> = L"Microsoft.ReactNative.IReactNotificationSubscription";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPackageBuilder> = L"Microsoft.ReactNative.IReactPackageBuilder";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPackageProvider> = L"Microsoft.ReactNative.IReactPackageProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPointerEventArgs> = L"Microsoft.ReactNative.IReactPointerEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPropertyBag> = L"Microsoft.ReactNative.IReactPropertyBag";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics> = L"Microsoft.ReactNative.IReactPropertyBagHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPropertyName> = L"Microsoft.ReactNative.IReactPropertyName";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactPropertyNamespace> = L"Microsoft.ReactNative.IReactPropertyNamespace";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactRootView> = L"Microsoft.ReactNative.IReactRootView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactSettingsSnapshot> = L"Microsoft.ReactNative.IReactSettingsSnapshot";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactViewHost> = L"Microsoft.ReactNative.IReactViewHost";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactViewInstance> = L"Microsoft.ReactNative.IReactViewInstance";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IReactViewOptions> = L"Microsoft.ReactNative.IReactViewOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo> = L"Microsoft.ReactNative.IRedBoxErrorFrameInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IRedBoxErrorInfo> = L"Microsoft.ReactNative.IRedBoxErrorInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IRedBoxHandler> = L"Microsoft.ReactNative.IRedBoxHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IRedBoxHelper> = L"Microsoft.ReactNative.IRedBoxHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IRedBoxHelperStatics> = L"Microsoft.ReactNative.IRedBoxHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ITimer> = L"Microsoft.ReactNative.ITimer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ITimer2> = L"Microsoft.ReactNative.ITimer2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ITimerStatics> = L"Microsoft.ReactNative.ITimerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewControl> = L"Microsoft.ReactNative.IViewControl";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManager> = L"Microsoft.ReactNative.IViewManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties> = L"Microsoft.ReactNative.IViewManagerCreateWithProperties";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout> = L"Microsoft.ReactNative.IViewManagerRequiresNativeLayout";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithChildren> = L"Microsoft.ReactNative.IViewManagerWithChildren";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithCommands> = L"Microsoft.ReactNative.IViewManagerWithCommands";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance> = L"Microsoft.ReactNative.IViewManagerWithDropViewInstance";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants> = L"Microsoft.ReactNative.IViewManagerWithExportedEventTypeConstants";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants> = L"Microsoft.ReactNative.IViewManagerWithExportedViewConstants";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties> = L"Microsoft.ReactNative.IViewManagerWithNativeProperties";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout> = L"Microsoft.ReactNative.IViewManagerWithOnLayout";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents> = L"Microsoft.ReactNative.IViewManagerWithPointerEvents";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewManagerWithReactContext> = L"Microsoft.ReactNative.IViewManagerWithReactContext";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewPanel> = L"Microsoft.ReactNative.IViewPanel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IViewPanelStatics> = L"Microsoft.ReactNative.IViewPanelStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IXamlHelper> = L"Microsoft.ReactNative.IXamlHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IXamlHelperStatics> = L"Microsoft.ReactNative.IXamlHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IXamlUIService> = L"Microsoft.ReactNative.IXamlUIService";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::IXamlUIServiceStatics> = L"Microsoft.ReactNative.IXamlUIServiceStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler> = L"Microsoft.ReactNative.AccessibilityActionEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler> = L"Microsoft.ReactNative.AccessibilityInvokeEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::CallFunc> = L"Microsoft.ReactNative.CallFunc";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ConstantProviderDelegate> = L"Microsoft.ReactNative.ConstantProviderDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::EmitEventSetterDelegate> = L"Microsoft.ReactNative.EmitEventSetterDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate> = L"Microsoft.ReactNative.EventEmitterInitializerDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::InitializerDelegate> = L"Microsoft.ReactNative.InitializerDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JSValueArgWriter> = L"Microsoft.ReactNative.JSValueArgWriter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiByteArrayUser> = L"Microsoft.ReactNative.JsiByteArrayUser";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiHostFunction> = L"Microsoft.ReactNative.JsiHostFunction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::JsiInitializerDelegate> = L"Microsoft.ReactNative.JsiInitializerDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::LogHandler> = L"Microsoft.ReactNative.LogHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::MethodDelegate> = L"Microsoft.ReactNative.MethodDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::MethodResultCallback> = L"Microsoft.ReactNative.MethodResultCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactCreatePropertyValue> = L"Microsoft.ReactNative.ReactCreatePropertyValue";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactDispatcherCallback> = L"Microsoft.ReactNative.ReactDispatcherCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactModuleProvider> = L"Microsoft.ReactNative.ReactModuleProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactNotificationHandler> = L"Microsoft.ReactNative.ReactNotificationHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::ReactViewManagerProvider> = L"Microsoft.ReactNative.ReactViewManagerProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::SyncMethodDelegate> = L"Microsoft.ReactNative.SyncMethodDelegate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::TimerFactory> = L"Microsoft.ReactNative.TimerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::ReactNative::UIBatchCompleteCallback> = L"Microsoft.ReactNative.UIBatchCompleteCallback";
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IBorderEffect>{ 0x4459F71A,0x552B,0x54F4,{ 0x8E,0x41,0x9C,0x65,0xD1,0x2F,0xDA,0x6A } }; // 4459F71A-552B-54F4-8E41-9C65D12FDA6A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ICallInvoker>{ 0xAD70D999,0x280A,0x5410,{ 0xAB,0x06,0x75,0x58,0xA5,0x83,0x0B,0x74 } }; // AD70D999-280A-5410-AB06-7558A5830B74
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IColorSourceEffect>{ 0x4B585CAE,0x41E4,0x5D57,{ 0xA3,0x1D,0x37,0x3C,0xE7,0x3C,0x96,0x44 } }; // 4B585CAE-41E4-5D57-A31D-373CE73C9644
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ICompositeStepEffect>{ 0x12FE6384,0xC894,0x5733,{ 0x94,0xC3,0x1B,0x0C,0x0C,0x5E,0x4E,0x09 } }; // 12FE6384-C894-5733-94C3-1B0C0C5E4E09
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDevMenuControl>{ 0xCDBAAF99,0xEFFC,0x54EB,{ 0x89,0xB5,0x76,0xF9,0x1D,0xA4,0xDA,0xA3 } }; // CDBAAF99-EFFC-54EB-89B5-76F91DA4DAA3
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicAutomationPeer>{ 0x6FA62291,0x8F43,0x556A,{ 0x94,0xFC,0xB2,0xCE,0xA7,0xBD,0x15,0xAE } }; // 6FA62291-8F43-556A-94FC-B2CEA7BD15AE
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory>{ 0x8C0D6722,0xD3FB,0x55F8,{ 0x8A,0xCF,0xE7,0x3A,0x39,0x1F,0xE9,0xD7 } }; // 8C0D6722-D3FB-55F8-8ACF-E73A391FE9D7
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicAutomationProperties>{ 0x6A621BDC,0x4C5C,0x5E1B,{ 0xAE,0x24,0xC3,0x9A,0x1D,0xFC,0x94,0x77 } }; // 6A621BDC-4C5C-5E1B-AE24-C39A1DFC9477
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics>{ 0x42BA45F6,0xAF9E,0x5DFC,{ 0x83,0xEE,0xF0,0xB9,0x6E,0xFA,0x64,0xAF } }; // 42BA45F6-AF9E-5DFC-83EE-F0B96EFA64AF
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicValueProvider>{ 0xAE016C4F,0x776F,0x5B9D,{ 0xA7,0xCC,0x9F,0x9B,0x86,0xC2,0xF2,0x70 } }; // AE016C4F-776F-5B9D-A7CC-9F9B86C2F270
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory>{ 0xF76F79F5,0x6F84,0x5C39,{ 0x9A,0x6A,0x8F,0xD1,0x11,0xAE,0x41,0x8D } }; // F76F79F5-6F84-5C39-9A6A-8FD111AE418D
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IGaussianBlurEffect>{ 0x7083CE5D,0x4867,0x570E,{ 0xAE,0xE1,0x82,0xA3,0xD4,0x8A,0x34,0x1E } }; // 7083CE5D-4867-570E-AEE1-82A3D48A341E
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IHttpSettingsStatics>{ 0x4FB5723E,0x7811,0x5B39,{ 0x83,0x7C,0x24,0xDA,0x19,0x00,0x9A,0x52 } }; // 4FB5723E-7811-5B39-837C-24DA19009A52
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs>{ 0x5481C46F,0x8587,0x5892,{ 0xAB,0x6E,0x4F,0x32,0x45,0x0E,0xD7,0x46 } }; // 5481C46F-8587-5892-AB6E-4F32450ED746
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs>{ 0x46D5C0CE,0x8A17,0x5262,{ 0xB5,0xC6,0xD1,0xD4,0x8E,0x86,0xE2,0x20 } }; // 46D5C0CE-8A17-5262-B5C6-D1D48E86E220
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs>{ 0x4A4B6416,0x14A9,0x5F44,{ 0x8E,0xB6,0x04,0xEC,0x5E,0x92,0x1C,0x7C } }; // 4A4B6416-14A9-5F44-8EB6-04EC5E921C7C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJSValueReader>{ 0x17CC9F84,0xA1CD,0x522C,{ 0x8A,0xBA,0xA7,0x39,0x66,0xFA,0xF5,0x06 } }; // 17CC9F84-A1CD-522C-8ABA-A73966FAF506
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJSValueWriter>{ 0xE69E1756,0x4E3B,0x52E9,{ 0xB1,0x7D,0xA9,0xD8,0x6F,0x19,0xE8,0x8B } }; // E69E1756-4E3B-52E9-B17D-A9D86F19E88B
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiByteBuffer>{ 0x81DDCCE6,0x3F5A,0x5649,{ 0xB8,0x09,0xF7,0x7D,0xD6,0x8B,0x1A,0x98 } }; // 81DDCCE6-3F5A-5649-B809-F77DD68B1A98
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiError>{ 0x8F8DE99D,0x11EF,0x516A,{ 0x88,0xBC,0xCE,0x3C,0x6F,0xEA,0x8F,0xF6 } }; // 8F8DE99D-11EF-516A-88BC-CE3C6FEA8FF6
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiHostObject>{ 0xEBA8626A,0xC8C8,0x5A1B,{ 0xB4,0xC4,0x9A,0xE2,0x95,0xCA,0x47,0xA5 } }; // EBA8626A-C8C8-5A1B-B4C4-9AE295CA47A5
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript>{ 0x82F36018,0x4081,0x5EF6,{ 0x80,0x66,0x4D,0xE2,0xCF,0x8B,0x0A,0xDD } }; // 82F36018-4081-5EF6-8066-4DE2CF8B0ADD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiRuntime>{ 0xA134CDBD,0x9DE5,0x5833,{ 0xBD,0x6F,0x1F,0x7A,0x01,0xB7,0x65,0x8E } }; // A134CDBD-9DE5-5833-BD6F-1F7A01B7658E
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IJsiRuntimeStatics>{ 0x8EBA6451,0x036B,0x5989,{ 0xA1,0x16,0xF8,0x55,0x5C,0xD6,0xD1,0xEC } }; // 8EBA6451-036B-5989-A116-F8555CD6D1EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ILayoutService>{ 0xCF497DE6,0x18BC,0x52FF,{ 0xB2,0x6B,0x91,0x94,0x00,0x9A,0xBD,0xE5 } }; // CF497DE6-18BC-52FF-B26B-9194009ABDE5
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ILayoutServiceStatics>{ 0x1D350BEC,0x619A,0x54C3,{ 0x9A,0x41,0x92,0xA0,0xCD,0xE8,0x7C,0x12 } }; // 1D350BEC-619A-54C3-9A41-92A0CDE87C12
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IQuirkSettings>{ 0xAEC073E1,0x5344,0x52D5,{ 0xBF,0x61,0xBC,0xE0,0xCC,0xA4,0xFB,0x2C } }; // AEC073E1-5344-52D5-BF61-BCE0CCA4FB2C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IQuirkSettingsStatics>{ 0xC6BD5D10,0xA494,0x5591,{ 0x96,0x8A,0x5C,0x5C,0x4A,0xD7,0x23,0x1D } }; // C6BD5D10-A494-5591-968A-5C5C4AD7231D
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactApplication>{ 0xB705A3A1,0x8173,0x5E26,{ 0xAF,0xC7,0x8F,0x1B,0xD0,0x77,0xD0,0x3A } }; // B705A3A1-8173-5E26-AFC7-8F1BD077D03A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactApplicationFactory>{ 0xCE6695E5,0x0997,0x5525,{ 0x9A,0x54,0x71,0x32,0xDF,0x91,0xD1,0xDF } }; // CE6695E5-0997-5525-9A54-7132DF91D1DF
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactContext>{ 0xEB3A2E36,0xA5AB,0x5EA6,{ 0xAA,0x9D,0xD3,0xB3,0xC6,0xE5,0x0D,0xFE } }; // EB3A2E36-A5AB-5EA6-AA9D-D3B3C6E50DFE
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactCoreInjection>{ 0xC32E7A95,0x5C19,0x5E7F,{ 0xAF,0x3B,0xB3,0x47,0x35,0x73,0xBF,0xA0 } }; // C32E7A95-5C19-5E7F-AF3B-B3473573BFA0
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics>{ 0x6C5ED35B,0x9C0B,0x5B76,{ 0x83,0x61,0x20,0x3B,0x7F,0x9A,0xA6,0xD4 } }; // 6C5ED35B-9C0B-5B76-8361-203B7F9AA6D4
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactDispatcher>{ 0xCAEAD8BB,0x06EC,0x554E,{ 0xB7,0xE9,0xF2,0x6C,0xF9,0x9E,0xD5,0x6C } }; // CAEAD8BB-06EC-554E-B7E9-F26CF99ED56C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics>{ 0xA3AC614F,0xD2AC,0x5312,{ 0xA5,0x6B,0xD1,0x43,0xB0,0xD1,0x5A,0xFA } }; // A3AC614F-D2AC-5312-A56B-D143B0D15AFA
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactInstanceSettings>{ 0xB541083B,0xFF14,0x55BF,{ 0xBD,0x14,0xC5,0xB1,0x0B,0x6C,0x2D,0x28 } }; // B541083B-FF14-55BF-BD14-C5B10B6C2D28
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactModuleBuilder>{ 0xFF746FA8,0x8490,0x56E6,{ 0x81,0xC4,0x75,0x00,0xC4,0x49,0x42,0x32 } }; // FF746FA8-8490-56E6-81C4-7500C4494232
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNativeHost>{ 0x7A0EE97C,0x7D37,0x53E8,{ 0x8C,0x86,0x2A,0xD1,0xEF,0x74,0xC8,0xA7 } }; // 7A0EE97C-7D37-53E8-8C86-2AD1EF74C8A7
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNativeHostStatics>{ 0xA242D198,0x5581,0x5EE0,{ 0xAA,0xEC,0x7E,0xD6,0x87,0x30,0xE3,0x69 } }; // A242D198-5581-5EE0-AAEC-7ED68730E369
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNonAbiValue>{ 0xDA557536,0xE937,0x5646,{ 0x92,0x45,0x2E,0xA5,0x41,0xC6,0x75,0x92 } }; // DA557536-E937-5646-9245-2EA541C67592
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNotificationArgs>{ 0x597D858B,0x03B7,0x5869,{ 0x81,0x00,0x6A,0xE7,0x88,0x4A,0x6B,0x04 } }; // 597D858B-03B7-5869-8100-6AE7884A6B04
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNotificationService>{ 0x64EB10F5,0x6E45,0x5C8C,{ 0x9C,0x01,0x64,0x5F,0xC8,0x75,0x6D,0x10 } }; // 64EB10F5-6E45-5C8C-9C01-645FC8756D10
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics>{ 0x9AD54AE0,0x43D5,0x5F2E,{ 0x89,0xD9,0xA3,0x5E,0x89,0x31,0x12,0xF8 } }; // 9AD54AE0-43D5-5F2E-89D9-A35E893112F8
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactNotificationSubscription>{ 0xA93F91DA,0x78A0,0x50F7,{ 0xBF,0x6C,0xDF,0xFD,0x8F,0x7E,0x51,0x1C } }; // A93F91DA-78A0-50F7-BF6C-DFFD8F7E511C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPackageBuilder>{ 0x8A6D4E70,0xDB9B,0x55A1,{ 0x87,0x1C,0x4B,0x1B,0x23,0xAC,0x42,0xF3 } }; // 8A6D4E70-DB9B-55A1-871C-4B1B23AC42F3
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPackageProvider>{ 0xE3293529,0xA202,0x5121,{ 0xA5,0x90,0x96,0x86,0x28,0xCD,0x4A,0xDE } }; // E3293529-A202-5121-A590-968628CD4ADE
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPointerEventArgs>{ 0x9732B3FD,0x2683,0x5E42,{ 0x87,0x32,0x7C,0x0D,0x56,0xDF,0x0A,0x0E } }; // 9732B3FD-2683-5E42-8732-7C0D56DF0A0E
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPropertyBag>{ 0x9DA6D36B,0x3CE9,0x5AAE,{ 0xAD,0xB3,0xE2,0x1E,0xC5,0xE5,0xF9,0xDB } }; // 9DA6D36B-3CE9-5AAE-ADB3-E21EC5E5F9DB
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics>{ 0xA7959BF9,0x1E62,0x5352,{ 0x8F,0xF0,0x91,0xF2,0x88,0xE9,0x24,0xDC } }; // A7959BF9-1E62-5352-8FF0-91F288E924DC
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPropertyName>{ 0x26A812C5,0x62F8,0x58B8,{ 0x9E,0x5B,0x9C,0x27,0x8B,0xDB,0x91,0x0A } }; // 26A812C5-62F8-58B8-9E5B-9C278BDB910A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactPropertyNamespace>{ 0x148C810B,0x6522,0x54D7,{ 0x8E,0x92,0x3C,0x31,0x83,0xDD,0x51,0x0A } }; // 148C810B-6522-54D7-8E92-3C3183DD510A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactRootView>{ 0x5FC7C9FB,0x736B,0x5023,{ 0x90,0x7A,0x17,0xB7,0x5C,0x80,0x54,0x69 } }; // 5FC7C9FB-736B-5023-907A-17B75C805469
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactSettingsSnapshot>{ 0xB40A2109,0xE63D,0x53FC,{ 0x85,0x6D,0xAB,0x93,0x45,0x0F,0xBA,0xA4 } }; // B40A2109-E63D-53FC-856D-AB93450FBAA4
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactViewHost>{ 0x0C36C916,0x6991,0x5183,{ 0x81,0xFC,0x8E,0xDD,0x6F,0xC5,0xF8,0x2D } }; // 0C36C916-6991-5183-81FC-8EDD6FC5F82D
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactViewInstance>{ 0x1839C832,0x7498,0x56B6,{ 0x8E,0x7D,0x42,0xBF,0x99,0xD3,0x9C,0x72 } }; // 1839C832-7498-56B6-8E7D-42BF99D39C72
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IReactViewOptions>{ 0x601B4B43,0x44D0,0x5443,{ 0xB1,0x66,0x29,0x31,0x27,0x57,0x0F,0xA6 } }; // 601B4B43-44D0-5443-B166-293127570FA6
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>{ 0xD03E670D,0x3CD1,0x5BB2,{ 0x9A,0x51,0xAC,0xDF,0x87,0xBD,0x64,0x74 } }; // D03E670D-3CD1-5BB2-9A51-ACDF87BD6474
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IRedBoxErrorInfo>{ 0x2FF770A9,0x4AD8,0x5B6A,{ 0xA9,0x91,0x34,0x5F,0xCF,0x7A,0x62,0xC9 } }; // 2FF770A9-4AD8-5B6A-A991-345FCF7A62C9
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IRedBoxHandler>{ 0x9138304B,0xCAA3,0x56D7,{ 0xA6,0x1F,0x22,0x83,0x5E,0xB0,0x3E,0xB1 } }; // 9138304B-CAA3-56D7-A61F-22835EB03EB1
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IRedBoxHelper>{ 0x55A8EA6D,0x7603,0x5261,{ 0xB7,0x2F,0xD2,0xCF,0x0C,0x6D,0x81,0xC5 } }; // 55A8EA6D-7603-5261-B72F-D2CF0C6D81C5
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IRedBoxHelperStatics>{ 0xC0248A81,0x75C3,0x5EA8,{ 0xB1,0x95,0xC9,0xB3,0xAF,0x90,0xC1,0xD2 } }; // C0248A81-75C3-5EA8-B195-C9B3AF90C1D2
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ITimer>{ 0xBE12418A,0x2AB6,0x54C5,{ 0x80,0x99,0xD6,0xE0,0x8E,0xD3,0x29,0xE0 } }; // BE12418A-2AB6-54C5-8099-D6E08ED329E0
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ITimer2>{ 0x19C9F7D5,0xEE62,0x560F,{ 0xAD,0x23,0xB5,0xD7,0x63,0x30,0x73,0x9A } }; // 19C9F7D5-EE62-560F-AD23-B5D76330739A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ITimerStatics>{ 0xC054256F,0xFCE9,0x5902,{ 0x9C,0xE5,0xCA,0x8F,0xD0,0x5B,0x2E,0xCD } }; // C054256F-FCE9-5902-9CE5-CA8FD05B2ECD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewControl>{ 0x57C956C4,0x9910,0x568C,{ 0x9D,0x27,0x00,0xDC,0x18,0xF8,0xF2,0x1D } }; // 57C956C4-9910-568C-9D27-00DC18F8F21D
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManager>{ 0x94107C01,0x36BE,0x574D,{ 0x84,0x6E,0xAA,0x36,0xFB,0x4E,0xDE,0x55 } }; // 94107C01-36BE-574D-846E-AA36FB4EDE55
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties>{ 0xC494AF47,0x0649,0x56BE,{ 0xB0,0x4C,0x39,0xE1,0xA6,0xC4,0x30,0x2A } }; // C494AF47-0649-56BE-B04C-39E1A6C4302A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout>{ 0xC00AB246,0x0D2B,0x54C7,{ 0xA1,0x22,0x91,0x29,0x0C,0x1B,0xC6,0xE3 } }; // C00AB246-0D2B-54C7-A122-91290C1BC6E3
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithChildren>{ 0x18175CCA,0x7926,0x54D2,{ 0x95,0x2F,0xB5,0xC7,0x59,0x53,0x3A,0xCD } }; // 18175CCA-7926-54D2-952F-B5C759533ACD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithCommands>{ 0xD49AFDAC,0x25E3,0x57A0,{ 0xB4,0xE6,0x38,0xE3,0x27,0xF7,0xE6,0xB3 } }; // D49AFDAC-25E3-57A0-B4E6-38E327F7E6B3
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance>{ 0xF53EE335,0x30DB,0x5F75,{ 0x9E,0xA7,0xDA,0x71,0xA3,0xE1,0x98,0x16 } }; // F53EE335-30DB-5F75-9EA7-DA71A3E19816
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants>{ 0xA6B67F93,0x0C3C,0x5803,{ 0x82,0xFC,0x3B,0xD6,0x5B,0xCE,0x24,0xE5 } }; // A6B67F93-0C3C-5803-82FC-3BD65BCE24E5
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants>{ 0xA7714B9A,0x44F9,0x501C,{ 0x84,0x4A,0x14,0x3C,0xEA,0x48,0x58,0x84 } }; // A7714B9A-44F9-501C-844A-143CEA485884
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties>{ 0x92229110,0xEBE4,0x5054,{ 0x9A,0x20,0x3A,0x62,0xA1,0xFE,0x2B,0xE8 } }; // 92229110-EBE4-5054-9A20-3A62A1FE2BE8
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout>{ 0x634BADD4,0x063C,0x58B0,{ 0xAF,0xD0,0x69,0x34,0x8D,0x66,0x28,0x7C } }; // 634BADD4-063C-58B0-AFD0-69348D66287C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents>{ 0x86069D63,0x486A,0x50CE,{ 0x84,0x3D,0x46,0xF1,0x57,0xE9,0xD3,0xAD } }; // 86069D63-486A-50CE-843D-46F157E9D3AD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewManagerWithReactContext>{ 0x9D6BB7C0,0xA21D,0x5FC3,{ 0x82,0xFB,0xF9,0x32,0x55,0x33,0x28,0x3E } }; // 9D6BB7C0-A21D-5FC3-82FB-F9325533283E
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewPanel>{ 0x82355467,0x2137,0x5734,{ 0x9A,0x2B,0x64,0x74,0xDB,0xB5,0x20,0x04 } }; // *************-5734-9A2B-6474DBB52004
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IViewPanelStatics>{ 0xCDBDA9E6,0x2A72,0x5325,{ 0x89,0xB6,0xD2,0xEC,0x54,0xF2,0x53,0xE1 } }; // CDBDA9E6-2A72-5325-89B6-D2EC54F253E1
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IXamlHelper>{ 0xE8AE5780,0xFD38,0x50F5,{ 0xA2,0xEC,0xB5,0xAE,0x79,0xD0,0x93,0x2A } }; // E8AE5780-FD38-50F5-A2EC-B5AE79D0932A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IXamlHelperStatics>{ 0xF8F102F3,0x309B,0x54A3,{ 0x9C,0xBD,0x24,0xBA,0x10,0x20,0x2F,0xA7 } }; // F8F102F3-309B-54A3-9CBD-24BA10202FA7
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IXamlUIService>{ 0x7082548C,0x6B34,0x5EAA,{ 0x92,0xE1,0x52,0x2C,0x94,0x2D,0x3D,0x9B } }; // 7082548C-6B34-5EAA-92E1-522C942D3D9B
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::IXamlUIServiceStatics>{ 0xE8B38720,0xAB9A,0x5646,{ 0x91,0x65,0x0A,0x47,0x77,0x8F,0xBE,0x1A } }; // E8B38720-AB9A-5646-9165-0A47778FBE1A
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler>{ 0x3D1A1850,0xF702,0x5D73,{ 0xB6,0x8C,0xA6,0xA1,0x5A,0xBE,0x12,0xD6 } }; // 3D1A1850-F702-5D73-B68C-A6A15ABE12D6
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler>{ 0xAD5A3845,0xFF83,0x527F,{ 0xAF,0xD2,0x8C,0x4E,0x98,0xE0,0x51,0xDD } }; // AD5A3845-FF83-527F-AFD2-8C4E98E051DD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::CallFunc>{ 0xF54A0F9A,0x3F7F,0x5C2A,{ 0x9A,0xC1,0x8E,0x0F,0x96,0x5E,0x7A,0x0B } }; // F54A0F9A-3F7F-5C2A-9AC1-8E0F965E7A0B
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ConstantProviderDelegate>{ 0xAE5924E5,0x6BA4,0x5696,{ 0x9D,0x39,0x5D,0x96,0x59,0x89,0x3F,0x52 } }; // AE5924E5-6BA4-5696-9D39-5D9659893F52
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::EmitEventSetterDelegate>{ 0xCF219C48,0x2781,0x504F,{ 0x8C,0xD8,0x5A,0xF8,0xED,0xC1,0x75,0x5D } }; // CF219C48-2781-504F-8CD8-5AF8EDC1755D
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate>{ 0x0F4ABA97,0xFC83,0x58BC,{ 0xBA,0xAD,0x97,0x77,0xE9,0x80,0xBC,0x46 } }; // 0F4ABA97-FC83-58BC-BAAD-9777E980BC46
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::InitializerDelegate>{ 0xA483A943,0xE5EC,0x5BF1,{ 0xA9,0xF7,0xFD,0xBE,0x35,0x71,0x98,0xAA } }; // A483A943-E5EC-5BF1-A9F7-FDBE357198AA
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::JSValueArgWriter>{ 0x35984877,0x3082,0x53FC,{ 0xAA,0xDC,0xA9,0xA5,0xE5,0x51,0x8C,0x16 } }; // *************-53FC-AADC-A9A5E5518C16
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::JsiByteArrayUser>{ 0xC7439C36,0x8A49,0x506C,{ 0x9C,0x28,0x2C,0xF6,0x0A,0x2B,0xB1,0xAA } }; // C7439C36-8A49-506C-9C28-2CF60A2BB1AA
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::JsiHostFunction>{ 0xA836BDE8,0x67F3,0x5BBD,{ 0xB9,0x9C,0x5F,0x9F,0x81,0x86,0x97,0x41 } }; // A836BDE8-67F3-5BBD-B99C-5F9F81869741
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::JsiInitializerDelegate>{ 0x8FDB63D4,0xE065,0x56B3,{ 0xA9,0x75,0x24,0x1F,0xAA,0x53,0x98,0x89 } }; // 8FDB63D4-E065-56B3-A975-241FAA539889
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::LogHandler>{ 0xBCB16D01,0x44DE,0x5779,{ 0x8F,0xB8,0x95,0x25,0x8C,0x1B,0xA5,0x15 } }; // BCB16D01-44DE-5779-8FB8-95258C1BA515
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::MethodDelegate>{ 0x0FB54B32,0xECEA,0x57B4,{ 0x84,0x45,0x8B,0x89,0x3A,0x2C,0x48,0x02 } }; // 0FB54B32-ECEA-57B4-8445-8B893A2C4802
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::MethodResultCallback>{ 0x48287C77,0xD21B,0x5A22,{ 0x89,0x7C,0x07,0xA1,0x86,0x44,0x9D,0x69 } }; // 48287C77-D21B-5A22-897C-07A186449D69
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ReactCreatePropertyValue>{ 0x9EED785C,0x4934,0x5621,{ 0x97,0xDC,0xE8,0x60,0xE9,0xA2,0xCF,0xFB } }; // 9EED785C-4934-5621-97DC-E860E9A2CFFB
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ReactDispatcherCallback>{ 0xB7377B32,0x6A11,0x5AB2,{ 0xBD,0x06,0x75,0xDF,0x47,0x3A,0x0D,0x79 } }; // B7377B32-6A11-5AB2-BD06-75DF473A0D79
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ReactModuleProvider>{ 0x********,0x6670,0x5E55,{ 0xA6,0xCC,0xC3,0x17,0xC4,0x5F,0x2C,0x91 } }; // ********-6670-5E55-A6CC-C317C45F2C91
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ReactNotificationHandler>{ 0x11C37238,0xC2CE,0x53CB,{ 0xA8,0x0C,0x03,0xA1,0x3B,0x74,0xB4,0xA5 } }; // 11C37238-C2CE-53CB-A80C-03A13B74B4A5
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::ReactViewManagerProvider>{ 0x4FC86394,0x4BD2,0x5888,{ 0xB8,0x61,0x9C,0xA7,0x11,0x72,0xA6,0xED } }; // 4FC86394-4BD2-5888-B861-9CA71172A6ED
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::SyncMethodDelegate>{ 0xC185C142,0x1D5E,0x52F0,{ 0xB9,0x77,0xC8,0x5E,0x0E,0x06,0xAB,0x6C } }; // C185C142-1D5E-52F0-B977-C85E0E06AB6C
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::TimerFactory>{ 0x0A7B370C,0xA6DA,0x5E16,{ 0x9B,0xC5,0x5C,0x48,0x9F,0x92,0x8B,0xDD } }; // 0A7B370C-A6DA-5E16-9BC5-5C489F928BDD
    template <> inline constexpr guid guid_v<winrt::Microsoft::ReactNative::UIBatchCompleteCallback>{ 0xFFA756D1,0x8152,0x5C3D,{ 0x8D,0xF0,0xC8,0xFB,0x89,0xE0,0x8D,0x4F } }; // FFA756D1-8152-5C3D-8DF0-C8FB89E08D4F
    template <> struct default_interface<winrt::Microsoft::ReactNative::BorderEffect>{ using type = winrt::Microsoft::ReactNative::IBorderEffect; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::CallInvoker>{ using type = winrt::Microsoft::ReactNative::ICallInvoker; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ColorSourceEffect>{ using type = winrt::Microsoft::ReactNative::IColorSourceEffect; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::CompositeStepEffect>{ using type = winrt::Microsoft::ReactNative::ICompositeStepEffect; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::DevMenuControl>{ using type = winrt::Microsoft::ReactNative::IDevMenuControl; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::DynamicAutomationPeer>{ using type = winrt::Microsoft::ReactNative::IDynamicAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::DynamicAutomationProperties>{ using type = winrt::Microsoft::ReactNative::IDynamicAutomationProperties; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::DynamicValueProvider>{ using type = winrt::Microsoft::ReactNative::IDynamicValueProvider; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::GaussianBlurEffect>{ using type = winrt::Microsoft::ReactNative::IGaussianBlurEffect; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs>{ using type = winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs>{ using type = winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs>{ using type = winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::JsiError>{ using type = winrt::Microsoft::ReactNative::IJsiError; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::JsiPreparedJavaScript>{ using type = winrt::Microsoft::ReactNative::IJsiPreparedJavaScript; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::JsiRuntime>{ using type = winrt::Microsoft::ReactNative::IJsiRuntime; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::LayoutService>{ using type = winrt::Microsoft::ReactNative::ILayoutService; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::QuirkSettings>{ using type = winrt::Microsoft::ReactNative::IQuirkSettings; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactApplication>{ using type = winrt::Microsoft::ReactNative::IReactApplication; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactCoreInjection>{ using type = winrt::Microsoft::ReactNative::IReactCoreInjection; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactInstanceSettings>{ using type = winrt::Microsoft::ReactNative::IReactInstanceSettings; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactNativeHost>{ using type = winrt::Microsoft::ReactNative::IReactNativeHost; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactPointerEventArgs>{ using type = winrt::Microsoft::ReactNative::IReactPointerEventArgs; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactRootView>{ using type = winrt::Microsoft::ReactNative::IReactRootView; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ReactViewOptions>{ using type = winrt::Microsoft::ReactNative::IReactViewOptions; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::RedBoxHelper>{ using type = winrt::Microsoft::ReactNative::IRedBoxHelper; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::Timer>{ using type = winrt::Microsoft::ReactNative::ITimer2; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ViewControl>{ using type = winrt::Microsoft::ReactNative::IViewControl; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::ViewPanel>{ using type = winrt::Microsoft::ReactNative::IViewPanel; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::XamlHelper>{ using type = winrt::Microsoft::ReactNative::IXamlHelper; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::XamlMetaDataProvider>{ using type = winrt::Windows::UI::Xaml::Markup::IXamlMetadataProvider; };
    template <> struct default_interface<winrt::Microsoft::ReactNative::XamlUIService>{ using type = winrt::Microsoft::ReactNative::IXamlUIService; };
    template <> struct abi<winrt::Microsoft::ReactNative::IBorderEffect>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExtendX(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ExtendX(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ExtendY(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ExtendY(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall put_Source(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ICallInvoker>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall InvokeAsync(void*) noexcept = 0;
            virtual int32_t __stdcall InvokeSync(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IColorSourceEffect>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Color(struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall put_Color(struct struct_Windows_UI_Color) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ICompositeStepEffect>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Mode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Mode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Destination(void**) noexcept = 0;
            virtual int32_t __stdcall put_Destination(void*) noexcept = 0;
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall put_Source(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDevMenuControl>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Cancel(void**) noexcept = 0;
            virtual int32_t __stdcall get_ConfigBundler(void**) noexcept = 0;
            virtual int32_t __stdcall get_Inspector(void**) noexcept = 0;
            virtual int32_t __stdcall get_FastRefresh(void**) noexcept = 0;
            virtual int32_t __stdcall get_SamplingProfiler(void**) noexcept = 0;
            virtual int32_t __stdcall get_BreakOnNextLine(void**) noexcept = 0;
            virtual int32_t __stdcall get_DirectDebug(void**) noexcept = 0;
            virtual int32_t __stdcall get_Reload(void**) noexcept = 0;
            virtual int32_t __stdcall get_FastRefreshText(void**) noexcept = 0;
            virtual int32_t __stdcall get_DirectDebugText(void**) noexcept = 0;
            virtual int32_t __stdcall get_DirectDebugDesc(void**) noexcept = 0;
            virtual int32_t __stdcall get_BreakOnNextLineText(void**) noexcept = 0;
            virtual int32_t __stdcall get_SamplingProfilerText(void**) noexcept = 0;
            virtual int32_t __stdcall get_SamplingProfilerDescText(void**) noexcept = 0;
            virtual int32_t __stdcall get_SamplingProfilerIcon(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicAutomationProperties>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AccessibilityRoleProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityRole(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityRole(void*, int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_AriaRoleProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAriaRole(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall GetAriaRole(void*, int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityStateSelectedProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityStateSelected(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityStateSelected(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityStateDisabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityStateDisabled(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityStateDisabled(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityStateCheckedProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityStateChecked(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityStateChecked(void*, int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityStateBusyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityStateBusy(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityStateBusy(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityStateExpandedProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityStateExpanded(void*, bool) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityStateExpanded(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityValueMinProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityValueMin(void*, double) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityValueMin(void*, double*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityValueMaxProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityValueMax(void*, double) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityValueMax(void*, double*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityValueNowProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityValueNow(void*, double) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityValueNow(void*, double*) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityValueTextProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityValueText(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityValueText(void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityInvokeEventHandlerProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityInvokeEventHandler(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityInvokeEventHandler(void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityActionsProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityActions(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityActions(void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_AccessibilityActionEventHandlerProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetAccessibilityActionEventHandler(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetAccessibilityActionEventHandler(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicValueProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IGaussianBlurEffect>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_BlurAmount(float*) noexcept = 0;
            virtual int32_t __stdcall put_BlurAmount(float) noexcept = 0;
            virtual int32_t __stdcall get_Optimization(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Optimization(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_BorderMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_BorderMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall put_Source(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IHttpSettingsStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetDefaultUserAgent(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
            virtual int32_t __stdcall get_RuntimeHandle(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Context(void**) noexcept = 0;
            virtual int32_t __stdcall get_Failed(bool*) noexcept = 0;
            virtual int32_t __stdcall get_RuntimeHandle(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJSValueReader>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ValueType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall GetNextObjectProperty(void**, bool*) noexcept = 0;
            virtual int32_t __stdcall GetNextArrayItem(bool*) noexcept = 0;
            virtual int32_t __stdcall GetString(void**) noexcept = 0;
            virtual int32_t __stdcall GetBoolean(bool*) noexcept = 0;
            virtual int32_t __stdcall GetInt64(int64_t*) noexcept = 0;
            virtual int32_t __stdcall GetDouble(double*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJSValueWriter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall WriteNull() noexcept = 0;
            virtual int32_t __stdcall WriteBoolean(bool) noexcept = 0;
            virtual int32_t __stdcall WriteInt64(int64_t) noexcept = 0;
            virtual int32_t __stdcall WriteDouble(double) noexcept = 0;
            virtual int32_t __stdcall WriteString(void*) noexcept = 0;
            virtual int32_t __stdcall WriteObjectBegin() noexcept = 0;
            virtual int32_t __stdcall WritePropertyName(void*) noexcept = 0;
            virtual int32_t __stdcall WriteObjectEnd() noexcept = 0;
            virtual int32_t __stdcall WriteArrayBegin() noexcept = 0;
            virtual int32_t __stdcall WriteArrayEnd() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiByteBuffer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Size(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall GetData(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiError>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ErrorType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ErrorDetails(void**) noexcept = 0;
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
            virtual int32_t __stdcall get_Stack(void**) noexcept = 0;
            virtual int32_t __stdcall get_Value(struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiHostObject>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetProperty(void*, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall SetProperty(void*, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiValueRef) noexcept = 0;
            virtual int32_t __stdcall GetPropertyIds(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiRuntime>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall EvaluateJavaScript(void*, void*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall PrepareJavaScript(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall EvaluatePreparedJavaScript(void*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall DrainMicrotasks(int32_t, bool*) noexcept = 0;
            virtual int32_t __stdcall QueueMicrotask(struct struct_Microsoft_ReactNative_JsiObjectRef) noexcept = 0;
            virtual int32_t __stdcall get_Global(struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall get_Description(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsInspectable(bool*) noexcept = 0;
            virtual int32_t __stdcall CloneSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef, struct struct_Microsoft_ReactNative_JsiSymbolRef*) noexcept = 0;
            virtual int32_t __stdcall CloneBigInt(struct struct_Microsoft_ReactNative_JsiBigIntRef, struct struct_Microsoft_ReactNative_JsiBigIntRef*) noexcept = 0;
            virtual int32_t __stdcall CloneString(struct struct_Microsoft_ReactNative_JsiStringRef, struct struct_Microsoft_ReactNative_JsiStringRef*) noexcept = 0;
            virtual int32_t __stdcall CloneObject(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall ClonePropertyId(struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyId(void*, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyIdFromAscii(uint32_t, uint8_t*, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyIdFromUtf8(uint32_t, uint8_t*, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyIdFromString(struct struct_Microsoft_ReactNative_JsiStringRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyIdFromSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef*) noexcept = 0;
            virtual int32_t __stdcall PropertyIdToString(struct struct_Microsoft_ReactNative_JsiPropertyIdRef, void**) noexcept = 0;
            virtual int32_t __stdcall PropertyIdToUtf8(struct struct_Microsoft_ReactNative_JsiPropertyIdRef, void*) noexcept = 0;
            virtual int32_t __stdcall PropertyIdEquals(struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, bool*) noexcept = 0;
            virtual int32_t __stdcall SymbolToString(struct struct_Microsoft_ReactNative_JsiSymbolRef, void**) noexcept = 0;
            virtual int32_t __stdcall SymbolToUtf8(struct struct_Microsoft_ReactNative_JsiSymbolRef, void*) noexcept = 0;
            virtual int32_t __stdcall CreateString(void*, struct struct_Microsoft_ReactNative_JsiStringRef*) noexcept = 0;
            virtual int32_t __stdcall CreateStringFromAscii(uint32_t, uint8_t*, struct struct_Microsoft_ReactNative_JsiStringRef*) noexcept = 0;
            virtual int32_t __stdcall CreateStringFromUtf8(uint32_t, uint8_t*, struct struct_Microsoft_ReactNative_JsiStringRef*) noexcept = 0;
            virtual int32_t __stdcall StringToString(struct struct_Microsoft_ReactNative_JsiStringRef, void**) noexcept = 0;
            virtual int32_t __stdcall StringToUtf8(struct struct_Microsoft_ReactNative_JsiStringRef, void*) noexcept = 0;
            virtual int32_t __stdcall CreateValueFromJson(void*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall CreateValueFromJsonUtf8(uint32_t, uint8_t*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall CreateBigIntFromInt64(int64_t, struct struct_Microsoft_ReactNative_JsiBigIntRef*) noexcept = 0;
            virtual int32_t __stdcall CreateBigIntFromUint64(uint64_t, struct struct_Microsoft_ReactNative_JsiBigIntRef*) noexcept = 0;
            virtual int32_t __stdcall BigintIsInt64(struct struct_Microsoft_ReactNative_JsiBigIntRef, bool*) noexcept = 0;
            virtual int32_t __stdcall BigintIsUint64(struct struct_Microsoft_ReactNative_JsiBigIntRef, bool*) noexcept = 0;
            virtual int32_t __stdcall Truncate(struct struct_Microsoft_ReactNative_JsiBigIntRef, uint64_t*) noexcept = 0;
            virtual int32_t __stdcall BigintToString(struct struct_Microsoft_ReactNative_JsiBigIntRef, int32_t, struct struct_Microsoft_ReactNative_JsiStringRef*) noexcept = 0;
            virtual int32_t __stdcall CreateArrayBuffer(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall CreateObject(struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall CreateObjectWithHostObject(void*, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall GetHostObject(struct struct_Microsoft_ReactNative_JsiObjectRef, void**) noexcept = 0;
            virtual int32_t __stdcall GetHostFunction(struct struct_Microsoft_ReactNative_JsiObjectRef, void**) noexcept = 0;
            virtual int32_t __stdcall GetProperty(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall HasProperty(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, bool*) noexcept = 0;
            virtual int32_t __stdcall SetProperty(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiPropertyIdRef, struct struct_Microsoft_ReactNative_JsiValueRef) noexcept = 0;
            virtual int32_t __stdcall GetPropertyIdArray(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall IsArray(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall IsArrayBuffer(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall IsFunction(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall IsHostObject(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall IsHostFunction(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall CreateWeakObject(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiWeakObjectRef*) noexcept = 0;
            virtual int32_t __stdcall LockWeakObject(struct struct_Microsoft_ReactNative_JsiWeakObjectRef, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall CreateArray(uint32_t, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall GetArraySize(struct struct_Microsoft_ReactNative_JsiObjectRef, uint32_t*) noexcept = 0;
            virtual int32_t __stdcall GetArrayBufferSize(struct struct_Microsoft_ReactNative_JsiObjectRef, uint32_t*) noexcept = 0;
            virtual int32_t __stdcall GetArrayBufferData(struct struct_Microsoft_ReactNative_JsiObjectRef, void*) noexcept = 0;
            virtual int32_t __stdcall GetValueAtIndex(struct struct_Microsoft_ReactNative_JsiObjectRef, uint32_t, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall SetValueAtIndex(struct struct_Microsoft_ReactNative_JsiObjectRef, uint32_t, struct struct_Microsoft_ReactNative_JsiValueRef) noexcept = 0;
            virtual int32_t __stdcall CreateFunctionFromHostFunction(struct struct_Microsoft_ReactNative_JsiPropertyIdRef, uint32_t, void*, struct struct_Microsoft_ReactNative_JsiObjectRef*) noexcept = 0;
            virtual int32_t __stdcall Call(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiValueRef, uint32_t, struct struct_Microsoft_ReactNative_JsiValueRef*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall CallAsConstructor(struct struct_Microsoft_ReactNative_JsiObjectRef, uint32_t, struct struct_Microsoft_ReactNative_JsiValueRef*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
            virtual int32_t __stdcall PushScope(struct struct_Microsoft_ReactNative_JsiScopeState*) noexcept = 0;
            virtual int32_t __stdcall PopScope(struct struct_Microsoft_ReactNative_JsiScopeState) noexcept = 0;
            virtual int32_t __stdcall SymbolStrictEquals(struct struct_Microsoft_ReactNative_JsiSymbolRef, struct struct_Microsoft_ReactNative_JsiSymbolRef, bool*) noexcept = 0;
            virtual int32_t __stdcall BigIntStrictEquals(struct struct_Microsoft_ReactNative_JsiBigIntRef, struct struct_Microsoft_ReactNative_JsiBigIntRef, bool*) noexcept = 0;
            virtual int32_t __stdcall StringStrictEquals(struct struct_Microsoft_ReactNative_JsiStringRef, struct struct_Microsoft_ReactNative_JsiStringRef, bool*) noexcept = 0;
            virtual int32_t __stdcall ObjectStrictEquals(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall InstanceOf(struct struct_Microsoft_ReactNative_JsiObjectRef, struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall ReleaseSymbol(struct struct_Microsoft_ReactNative_JsiSymbolRef) noexcept = 0;
            virtual int32_t __stdcall ReleaseBigInt(struct struct_Microsoft_ReactNative_JsiBigIntRef) noexcept = 0;
            virtual int32_t __stdcall ReleaseString(struct struct_Microsoft_ReactNative_JsiStringRef) noexcept = 0;
            virtual int32_t __stdcall ReleaseObject(struct struct_Microsoft_ReactNative_JsiObjectRef) noexcept = 0;
            virtual int32_t __stdcall ReleasePropertyId(struct struct_Microsoft_ReactNative_JsiPropertyIdRef) noexcept = 0;
            virtual int32_t __stdcall GetAndClearError(void**) noexcept = 0;
            virtual int32_t __stdcall SetError(int32_t, void*, struct struct_Microsoft_ReactNative_JsiValueRef) noexcept = 0;
            virtual int32_t __stdcall HasNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef, bool*) noexcept = 0;
            virtual int32_t __stdcall GetNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef, void**) noexcept = 0;
            virtual int32_t __stdcall SetNativeState(struct struct_Microsoft_ReactNative_JsiObjectRef, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IJsiRuntimeStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall MakeChakraRuntime(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ILayoutService>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ApplyLayoutForAllNodes() noexcept = 0;
            virtual int32_t __stdcall ApplyLayout(int64_t, float, float) noexcept = 0;
            virtual int32_t __stdcall get_IsInBatch(bool*) noexcept = 0;
            virtual int32_t __stdcall MarkDirty(int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ILayoutServiceStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromContext(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IQuirkSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IQuirkSettingsStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetMatchAndroidAndIOSStretchBehavior(void*, bool) noexcept = 0;
            virtual int32_t __stdcall SetUseWebFlexBasisBehavior(void*, bool) noexcept = 0;
            virtual int32_t __stdcall SetAcceptSelfSigned(void*, bool) noexcept = 0;
            virtual int32_t __stdcall SetBackHandlerKind(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall SetMapWindowDeactivatedToAppStateInactive(void*, bool) noexcept = 0;
            virtual int32_t __stdcall SetSuppressWindowFocusOnViewFocus(void*, bool) noexcept = 0;
            virtual int32_t __stdcall SetUseRuntimeScheduler(void*, bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactApplication>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_InstanceSettings(void**) noexcept = 0;
            virtual int32_t __stdcall put_InstanceSettings(void*) noexcept = 0;
            virtual int32_t __stdcall get_PackageProviders(void**) noexcept = 0;
            virtual int32_t __stdcall get_Host(void**) noexcept = 0;
            virtual int32_t __stdcall get_UseDeveloperSupport(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseDeveloperSupport(bool) noexcept = 0;
            virtual int32_t __stdcall get_JavaScriptBundleFile(void**) noexcept = 0;
            virtual int32_t __stdcall put_JavaScriptBundleFile(void*) noexcept = 0;
            virtual int32_t __stdcall get_BundleAppId(void**) noexcept = 0;
            virtual int32_t __stdcall put_BundleAppId(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactApplicationFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SettingsSnapshot(void**) noexcept = 0;
            virtual int32_t __stdcall get_Properties(void**) noexcept = 0;
            virtual int32_t __stdcall get_Notifications(void**) noexcept = 0;
            virtual int32_t __stdcall get_UIDispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSDispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSRuntime(void**) noexcept = 0;
            virtual int32_t __stdcall get_CallInvoker(void**) noexcept = 0;
            virtual int32_t __stdcall DispatchEvent(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall CallJSFunction(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall EmitJSEvent(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall get_LoadingState(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactCoreInjection>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetUIBatchCompleteCallback(void*, void*) noexcept = 0;
            virtual int32_t __stdcall MakeViewHost(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall PostToUIBatchingQueue(void*, void*) noexcept = 0;
            virtual int32_t __stdcall SetPlatformNameOverride(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetTopLevelWindowId(void*, uint64_t*) noexcept = 0;
            virtual int32_t __stdcall SetTopLevelWindowId(void*, uint64_t) noexcept = 0;
            virtual int32_t __stdcall SetTimerFactory(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactDispatcher>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_HasThreadAccess(bool*) noexcept = 0;
            virtual int32_t __stdcall Post(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateSerialDispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_UIThreadDispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_UIDispatcherProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSDispatcherProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSDispatcherTaskStartingEventName(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSDispatcherIdleWaitStartingEventName(void**) noexcept = 0;
            virtual int32_t __stdcall get_JSDispatcherIdleWaitCompletedEventName(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactInstanceSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Properties(void**) noexcept = 0;
            virtual int32_t __stdcall get_Notifications(void**) noexcept = 0;
            virtual int32_t __stdcall get_PackageProviders(void**) noexcept = 0;
            virtual int32_t __stdcall get_UseDeveloperSupport(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseDeveloperSupport(bool) noexcept = 0;
            virtual int32_t __stdcall get_JavaScriptBundleFile(void**) noexcept = 0;
            virtual int32_t __stdcall put_JavaScriptBundleFile(void*) noexcept = 0;
            virtual int32_t __stdcall get_BundleAppId(void**) noexcept = 0;
            virtual int32_t __stdcall put_BundleAppId(void*) noexcept = 0;
            virtual int32_t __stdcall get_RequestDevBundle(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RequestDevBundle(bool) noexcept = 0;
            virtual int32_t __stdcall get_UseWebDebugger(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseWebDebugger(bool) noexcept = 0;
            virtual int32_t __stdcall get_UseFastRefresh(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseFastRefresh(bool) noexcept = 0;
            virtual int32_t __stdcall get_UseLiveReload(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseLiveReload(bool) noexcept = 0;
            virtual int32_t __stdcall get_UseDirectDebugger(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseDirectDebugger(bool) noexcept = 0;
            virtual int32_t __stdcall get_DebuggerBreakOnNextLine(bool*) noexcept = 0;
            virtual int32_t __stdcall put_DebuggerBreakOnNextLine(bool) noexcept = 0;
            virtual int32_t __stdcall get_EnableJITCompilation(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableJITCompilation(bool) noexcept = 0;
            virtual int32_t __stdcall get_EnableByteCodeCaching(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableByteCodeCaching(bool) noexcept = 0;
            virtual int32_t __stdcall get_EnableDefaultCrashHandler(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableDefaultCrashHandler(bool) noexcept = 0;
            virtual int32_t __stdcall get_EnableDeveloperMenu(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableDeveloperMenu(bool) noexcept = 0;
            virtual int32_t __stdcall get_ByteCodeFileUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_ByteCodeFileUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_DebugBundlePath(void**) noexcept = 0;
            virtual int32_t __stdcall put_DebugBundlePath(void*) noexcept = 0;
            virtual int32_t __stdcall get_BundleRootPath(void**) noexcept = 0;
            virtual int32_t __stdcall put_BundleRootPath(void*) noexcept = 0;
            virtual int32_t __stdcall get_DebuggerPort(uint16_t*) noexcept = 0;
            virtual int32_t __stdcall put_DebuggerPort(uint16_t) noexcept = 0;
            virtual int32_t __stdcall get_DebuggerRuntimeName(void**) noexcept = 0;
            virtual int32_t __stdcall put_DebuggerRuntimeName(void*) noexcept = 0;
            virtual int32_t __stdcall get_RedBoxHandler(void**) noexcept = 0;
            virtual int32_t __stdcall put_RedBoxHandler(void*) noexcept = 0;
            virtual int32_t __stdcall get_NativeLogger(void**) noexcept = 0;
            virtual int32_t __stdcall put_NativeLogger(void*) noexcept = 0;
            virtual int32_t __stdcall get_UIDispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall put_UIDispatcher(void*) noexcept = 0;
            virtual int32_t __stdcall get_SourceBundleHost(void**) noexcept = 0;
            virtual int32_t __stdcall put_SourceBundleHost(void*) noexcept = 0;
            virtual int32_t __stdcall get_SourceBundlePort(uint16_t*) noexcept = 0;
            virtual int32_t __stdcall put_SourceBundlePort(uint16_t) noexcept = 0;
            virtual int32_t __stdcall get_RequestInlineSourceMap(bool*) noexcept = 0;
            virtual int32_t __stdcall put_RequestInlineSourceMap(bool) noexcept = 0;
            virtual int32_t __stdcall get_JSIEngineOverride(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_JSIEngineOverride(int32_t) noexcept = 0;
            virtual int32_t __stdcall add_InstanceCreated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_InstanceCreated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_InstanceLoaded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_InstanceLoaded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_InstanceDestroyed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_InstanceDestroyed(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactModuleBuilder>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddInitializer(void*) noexcept = 0;
            virtual int32_t __stdcall AddJsiInitializer(void*) noexcept = 0;
            virtual int32_t __stdcall AddConstantProvider(void*) noexcept = 0;
            virtual int32_t __stdcall AddMethod(void*, int32_t, void*) noexcept = 0;
            virtual int32_t __stdcall AddSyncMethod(void*, void*) noexcept = 0;
            virtual int32_t __stdcall AddEventEmitter(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNativeHost>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_PackageProviders(void**) noexcept = 0;
            virtual int32_t __stdcall get_InstanceSettings(void**) noexcept = 0;
            virtual int32_t __stdcall put_InstanceSettings(void*) noexcept = 0;
            virtual int32_t __stdcall LoadInstance(void**) noexcept = 0;
            virtual int32_t __stdcall ReloadInstance(void**) noexcept = 0;
            virtual int32_t __stdcall UnloadInstance(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNativeHostStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromContext(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNonAbiValue>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetPtr(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNotificationArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Subscription(void**) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNotificationService>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall Subscribe(void*, void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall SendNotification(void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateNotificationService(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactNotificationSubscription>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_NotificationService(void**) noexcept = 0;
            virtual int32_t __stdcall get_NotificationName(void**) noexcept = 0;
            virtual int32_t __stdcall get_Dispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsSubscribed(bool*) noexcept = 0;
            virtual int32_t __stdcall Unsubscribe() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPackageBuilder>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddModule(void*, void*) noexcept = 0;
            virtual int32_t __stdcall AddTurboModule(void*, void*) noexcept = 0;
            virtual int32_t __stdcall AddViewManager(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPackageProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreatePackage(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPointerEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Args(void**) noexcept = 0;
            virtual int32_t __stdcall get_Kind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Kind(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Target(void**) noexcept = 0;
            virtual int32_t __stdcall put_Target(void*) noexcept = 0;
            virtual int32_t __stdcall get_AllowUncaptured(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowUncaptured(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPropertyBag>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall Get(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetOrCreate(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall Set(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CopyFrom(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_GlobalNamespace(void**) noexcept = 0;
            virtual int32_t __stdcall GetNamespace(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetName(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreatePropertyBag(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPropertyName>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_LocalName(void**) noexcept = 0;
            virtual int32_t __stdcall get_Namespace(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactPropertyNamespace>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_NamespaceName(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactRootView>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ReactNativeHost(void**) noexcept = 0;
            virtual int32_t __stdcall put_ReactNativeHost(void*) noexcept = 0;
            virtual int32_t __stdcall get_ComponentName(void**) noexcept = 0;
            virtual int32_t __stdcall put_ComponentName(void*) noexcept = 0;
            virtual int32_t __stdcall get_InitialProps(void**) noexcept = 0;
            virtual int32_t __stdcall put_InitialProps(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsPerspectiveEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsPerspectiveEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall ReloadView() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactSettingsSnapshot>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UseWebDebugger(bool*) noexcept = 0;
            virtual int32_t __stdcall get_UseFastRefresh(bool*) noexcept = 0;
            virtual int32_t __stdcall get_UseDirectDebugger(bool*) noexcept = 0;
            virtual int32_t __stdcall get_DebuggerBreakOnNextLine(bool*) noexcept = 0;
            virtual int32_t __stdcall get_DebuggerPort(uint16_t*) noexcept = 0;
            virtual int32_t __stdcall get_DebugBundlePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_BundleRootPath(void**) noexcept = 0;
            virtual int32_t __stdcall get_SourceBundleHost(void**) noexcept = 0;
            virtual int32_t __stdcall get_SourceBundlePort(uint16_t*) noexcept = 0;
            virtual int32_t __stdcall get_RequestInlineSourceMap(bool*) noexcept = 0;
            virtual int32_t __stdcall get_JavaScriptBundleFile(void**) noexcept = 0;
            virtual int32_t __stdcall get_BundleAppId(void**) noexcept = 0;
            virtual int32_t __stdcall get_RequestDevBundle(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactViewHost>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ReloadViewInstance(void**) noexcept = 0;
            virtual int32_t __stdcall ReloadViewInstanceWithOptions(void*, void**) noexcept = 0;
            virtual int32_t __stdcall UnloadViewInstance(void**) noexcept = 0;
            virtual int32_t __stdcall AttachViewInstance(void*, void**) noexcept = 0;
            virtual int32_t __stdcall DetachViewInstance(void**) noexcept = 0;
            virtual int32_t __stdcall get_ReactNativeHost(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactViewInstance>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall InitRootView(void*, void*) noexcept = 0;
            virtual int32_t __stdcall UpdateRootView() noexcept = 0;
            virtual int32_t __stdcall UninitRootView() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IReactViewOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ComponentName(void**) noexcept = 0;
            virtual int32_t __stdcall put_ComponentName(void*) noexcept = 0;
            virtual int32_t __stdcall get_InitialProps(void**) noexcept = 0;
            virtual int32_t __stdcall put_InitialProps(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_File(void**) noexcept = 0;
            virtual int32_t __stdcall get_Method(void**) noexcept = 0;
            virtual int32_t __stdcall get_Line(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Column(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Collapse(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IRedBoxErrorInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
            virtual int32_t __stdcall get_OriginalMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_ComponentStack(void**) noexcept = 0;
            virtual int32_t __stdcall get_Id(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Callstack(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExtraData(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IRedBoxHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ShowNewError(void*, int32_t) noexcept = 0;
            virtual int32_t __stdcall get_IsDevSupportEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall UpdateError(void*) noexcept = 0;
            virtual int32_t __stdcall DismissRedBox() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IRedBoxHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IRedBoxHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateDefaultHandler(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ITimer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Interval(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Interval(int64_t) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
            virtual int32_t __stdcall add_Tick(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Tick(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ITimer2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ITimerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall Create(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewControl>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetPanel(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall CreateView(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateViewWithProperties(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_RequiresNativeLayout(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithChildren>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall AddView(void*, void*, int64_t) noexcept = 0;
            virtual int32_t __stdcall RemoveAllChildren(void*) noexcept = 0;
            virtual int32_t __stdcall RemoveChildAt(void*, int64_t) noexcept = 0;
            virtual int32_t __stdcall ReplaceChild(void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithCommands>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Commands(void**) noexcept = 0;
            virtual int32_t __stdcall DispatchCommand(void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnDropViewInstance(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExportedCustomBubblingEventTypeConstants(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExportedCustomDirectEventTypeConstants(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExportedViewConstants(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_NativeProps(void**) noexcept = 0;
            virtual int32_t __stdcall UpdateProperties(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnLayout(void*, float, float, float, float) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnPointerEvent(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewManagerWithReactContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ReactContext(void**) noexcept = 0;
            virtual int32_t __stdcall put_ReactContext(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewPanel>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall InsertAt(uint32_t, void*) noexcept = 0;
            virtual int32_t __stdcall RemoveAt(uint32_t) noexcept = 0;
            virtual int32_t __stdcall Clear() noexcept = 0;
            virtual int32_t __stdcall get_ViewBackground(void**) noexcept = 0;
            virtual int32_t __stdcall put_ViewBackground(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IViewPanelStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ViewBackgroundProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_BorderThicknessProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_BorderBrushProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_CornerRadiusProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TopProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetTop(void*, double) noexcept = 0;
            virtual int32_t __stdcall GetTop(void*, double*) noexcept = 0;
            virtual int32_t __stdcall get_LeftProperty(void**) noexcept = 0;
            virtual int32_t __stdcall SetLeft(void*, double) noexcept = 0;
            virtual int32_t __stdcall GetLeft(void*, double*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IXamlHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IXamlHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall BrushFrom(void*, void**) noexcept = 0;
            virtual int32_t __stdcall ColorFrom(void*, struct struct_Windows_UI_Color*) noexcept = 0;
            virtual int32_t __stdcall get_ReactTagProperty(void**) noexcept = 0;
            virtual int32_t __stdcall GetReactTag(void*, int64_t*) noexcept = 0;
            virtual int32_t __stdcall SetReactTag(void*, int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IXamlUIService>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ElementFromReactTag(int64_t, void**) noexcept = 0;
            virtual int32_t __stdcall DispatchEvent(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetReactRootView(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::IXamlUIServiceStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromContext(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetXamlRoot(void*, void*) noexcept = 0;
            virtual int32_t __stdcall SetAccessibleRoot(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetXamlRoot(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetAccessibleRoot(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetIslandWindowHandle(void*, uint64_t*) noexcept = 0;
            virtual int32_t __stdcall SetIslandWindowHandle(void*, uint64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::AccessibilityActionEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(struct struct_Microsoft_ReactNative_AccessibilityAction) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::CallFunc>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ConstantProviderDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::EmitEventSetterDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::InitializerDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::JSValueArgWriter>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::JsiByteArrayUser>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(uint32_t, uint8_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::JsiHostFunction>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, struct struct_Microsoft_ReactNative_JsiValueRef, uint32_t, struct struct_Microsoft_ReactNative_JsiValueRef*, struct struct_Microsoft_ReactNative_JsiValueRef*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::JsiInitializerDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::LogHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(int32_t, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::MethodDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::MethodResultCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ReactCreatePropertyValue>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ReactDispatcherCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ReactModuleProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ReactNotificationHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::ReactViewManagerProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::SyncMethodDelegate>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::TimerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::ReactNative::UIBatchCompleteCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IBorderEffect
    {
        [[nodiscard]] auto ExtendX() const;
        auto ExtendX(winrt::Microsoft::ReactNative::CanvasEdgeBehavior const& value) const;
        [[nodiscard]] auto ExtendY() const;
        auto ExtendY(winrt::Microsoft::ReactNative::CanvasEdgeBehavior const& value) const;
        [[nodiscard]] auto Source() const;
        auto Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IBorderEffect>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IBorderEffect<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ICallInvoker
    {
        auto InvokeAsync(winrt::Microsoft::ReactNative::CallFunc const& func) const;
        auto InvokeSync(winrt::Microsoft::ReactNative::CallFunc const& func) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ICallInvoker>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ICallInvoker<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IColorSourceEffect
    {
        [[nodiscard]] auto Color() const;
        auto Color(winrt::Windows::UI::Color const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IColorSourceEffect>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IColorSourceEffect<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ICompositeStepEffect
    {
        [[nodiscard]] auto Mode() const;
        auto Mode(winrt::Microsoft::ReactNative::CanvasComposite const& value) const;
        [[nodiscard]] auto Destination() const;
        auto Destination(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const;
        [[nodiscard]] auto Source() const;
        auto Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ICompositeStepEffect>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ICompositeStepEffect<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDevMenuControl
    {
        [[nodiscard]] auto Cancel() const;
        [[nodiscard]] auto ConfigBundler() const;
        [[nodiscard]] auto Inspector() const;
        [[nodiscard]] auto FastRefresh() const;
        [[nodiscard]] auto SamplingProfiler() const;
        [[nodiscard]] auto BreakOnNextLine() const;
        [[nodiscard]] auto DirectDebug() const;
        [[nodiscard]] auto Reload() const;
        [[nodiscard]] auto FastRefreshText() const;
        [[nodiscard]] auto DirectDebugText() const;
        [[nodiscard]] auto DirectDebugDesc() const;
        [[nodiscard]] auto BreakOnNextLineText() const;
        [[nodiscard]] auto SamplingProfilerText() const;
        [[nodiscard]] auto SamplingProfilerDescText() const;
        [[nodiscard]] auto SamplingProfilerIcon() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDevMenuControl>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDevMenuControl<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicAutomationPeerFactory
    {
        auto CreateInstance(winrt::Windows::UI::Xaml::FrameworkElement const& owner) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicAutomationProperties
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicAutomationProperties>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicAutomationProperties<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics
    {
        [[nodiscard]] auto AccessibilityRoleProperty() const;
        auto SetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityRoles const& value) const;
        auto GetAccessibilityRole(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AriaRoleProperty() const;
        auto SetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AriaRole const& value) const;
        auto GetAriaRole(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityStateSelectedProperty() const;
        auto SetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const;
        auto GetAccessibilityStateSelected(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityStateDisabledProperty() const;
        auto SetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const;
        auto GetAccessibilityStateDisabled(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityStateCheckedProperty() const;
        auto SetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityStateCheckedValue const& value) const;
        auto GetAccessibilityStateChecked(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityStateBusyProperty() const;
        auto SetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const;
        auto GetAccessibilityStateBusy(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityStateExpandedProperty() const;
        auto SetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element, bool value) const;
        auto GetAccessibilityStateExpanded(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityValueMinProperty() const;
        auto SetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element, double value) const;
        auto GetAccessibilityValueMin(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityValueMaxProperty() const;
        auto SetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element, double value) const;
        auto GetAccessibilityValueMax(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityValueNowProperty() const;
        auto SetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element, double value) const;
        auto GetAccessibilityValueNow(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityValueTextProperty() const;
        auto SetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element, param::hstring const& value) const;
        auto GetAccessibilityValueText(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityInvokeEventHandlerProperty() const;
        auto SetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityInvokeEventHandler const& value) const;
        auto GetAccessibilityInvokeEventHandler(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityActionsProperty() const;
        auto SetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element, param::vector<winrt::Microsoft::ReactNative::AccessibilityAction> const& value) const;
        auto GetAccessibilityActions(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto AccessibilityActionEventHandlerProperty() const;
        auto SetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::ReactNative::AccessibilityActionEventHandler const& value) const;
        auto GetAccessibilityActionEventHandler(winrt::Windows::UI::Xaml::UIElement const& element) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicAutomationPropertiesStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicAutomationPropertiesStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicValueProvider
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicValueProvider>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicValueProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IDynamicValueProviderFactory
    {
        auto CreateInstance(winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer const& peer) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IDynamicValueProviderFactory>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IDynamicValueProviderFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IGaussianBlurEffect
    {
        [[nodiscard]] auto BlurAmount() const;
        auto BlurAmount(float value) const;
        [[nodiscard]] auto Optimization() const;
        auto Optimization(winrt::Microsoft::ReactNative::EffectOptimization const& value) const;
        [[nodiscard]] auto BorderMode() const;
        auto BorderMode(winrt::Microsoft::ReactNative::EffectBorderMode const& value) const;
        [[nodiscard]] auto Source() const;
        auto Source(winrt::Windows::Graphics::Effects::IGraphicsEffectSource const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IGaussianBlurEffect>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IGaussianBlurEffect<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IHttpSettingsStatics
    {
        auto SetDefaultUserAgent(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, param::hstring const& userAgent) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IHttpSettingsStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IHttpSettingsStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IInstanceCreatedEventArgs
    {
        [[nodiscard]] auto Context() const;
        [[nodiscard]] auto RuntimeHandle() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IInstanceCreatedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IInstanceCreatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IInstanceDestroyedEventArgs
    {
        [[nodiscard]] auto Context() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IInstanceDestroyedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IInstanceDestroyedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IInstanceLoadedEventArgs
    {
        [[nodiscard]] auto Context() const;
        [[nodiscard]] auto Failed() const;
        [[nodiscard]] auto RuntimeHandle() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IInstanceLoadedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IInstanceLoadedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJSValueReader
    {
        [[nodiscard]] auto ValueType() const;
        auto GetNextObjectProperty(hstring& propertyName) const;
        auto GetNextArrayItem() const;
        auto GetString() const;
        auto GetBoolean() const;
        auto GetInt64() const;
        auto GetDouble() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJSValueReader>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJSValueReader<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJSValueWriter
    {
        auto WriteNull() const;
        auto WriteBoolean(bool value) const;
        auto WriteInt64(int64_t value) const;
        auto WriteDouble(double value) const;
        auto WriteString(param::hstring const& value) const;
        auto WriteObjectBegin() const;
        auto WritePropertyName(param::hstring const& name) const;
        auto WriteObjectEnd() const;
        auto WriteArrayBegin() const;
        auto WriteArrayEnd() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJSValueWriter>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJSValueWriter<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiByteBuffer
    {
        [[nodiscard]] auto Size() const;
        auto GetData(winrt::Microsoft::ReactNative::JsiByteArrayUser const& useBytes) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiByteBuffer>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiByteBuffer<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiError
    {
        [[nodiscard]] auto ErrorType() const;
        [[nodiscard]] auto ErrorDetails() const;
        [[nodiscard]] auto Message() const;
        [[nodiscard]] auto Stack() const;
        [[nodiscard]] auto Value() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiError>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiError<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiHostObject
    {
        auto GetProperty(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto SetProperty(winrt::Microsoft::ReactNative::JsiRuntime const& runtime, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiValueRef const& value) const;
        auto GetPropertyIds(winrt::Microsoft::ReactNative::JsiRuntime const& runtime) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiHostObject>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiHostObject<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiPreparedJavaScript
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiPreparedJavaScript>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiPreparedJavaScript<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiRuntime
    {
        auto EvaluateJavaScript(winrt::Microsoft::ReactNative::IJsiByteBuffer const& buffer, param::hstring const& sourceUrl) const;
        auto PrepareJavaScript(winrt::Microsoft::ReactNative::IJsiByteBuffer const& buffer, param::hstring const& sourceUrl) const;
        auto EvaluatePreparedJavaScript(winrt::Microsoft::ReactNative::JsiPreparedJavaScript const& js) const;
        auto DrainMicrotasks(int32_t maxMicrotasksHint) const;
        auto QueueMicrotask(winrt::Microsoft::ReactNative::JsiObjectRef const& callback) const;
        [[nodiscard]] auto Global() const;
        [[nodiscard]] auto Description() const;
        [[nodiscard]] auto IsInspectable() const;
        auto CloneSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const;
        auto CloneBigInt(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const;
        auto CloneString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const;
        auto CloneObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto ClonePropertyId(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto CreatePropertyId(param::hstring const& name) const;
        auto CreatePropertyIdFromAscii(array_view<uint8_t const> ascii) const;
        auto CreatePropertyIdFromUtf8(array_view<uint8_t const> utf8) const;
        auto CreatePropertyIdFromString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const;
        auto CreatePropertyIdFromSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& sym) const;
        auto PropertyIdToString(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto PropertyIdToUtf8(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const;
        auto PropertyIdEquals(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& left, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& right) const;
        auto SymbolToString(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const;
        auto SymbolToUtf8(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const;
        auto CreateString(param::hstring const& value) const;
        auto CreateStringFromAscii(array_view<uint8_t const> ascii) const;
        auto CreateStringFromUtf8(array_view<uint8_t const> utf8) const;
        auto StringToString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const;
        auto StringToUtf8(winrt::Microsoft::ReactNative::JsiStringRef const& str, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useUtf8String) const;
        auto CreateValueFromJson(param::hstring const& json) const;
        auto CreateValueFromJsonUtf8(array_view<uint8_t const> json) const;
        auto CreateBigIntFromInt64(int64_t val) const;
        auto CreateBigIntFromUint64(uint64_t val) const;
        auto BigintIsInt64(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const;
        auto BigintIsUint64(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const;
        auto Truncate(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const;
        auto BigintToString(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt, int32_t val) const;
        auto CreateArrayBuffer(winrt::Microsoft::ReactNative::JsiObjectRef const& buffer) const;
        auto CreateObject() const;
        auto CreateObjectWithHostObject(winrt::Microsoft::ReactNative::IJsiHostObject const& hostObject) const;
        auto GetHostObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto GetHostFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& func) const;
        auto GetProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto HasProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto SetProperty(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId, winrt::Microsoft::ReactNative::JsiValueRef const& value) const;
        auto GetPropertyIdArray(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto IsArray(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto IsArrayBuffer(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto IsFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto IsHostObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto IsHostFunction(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto CreateWeakObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto LockWeakObject(winrt::Microsoft::ReactNative::JsiWeakObjectRef const& weakObject) const;
        auto CreateArray(uint32_t size) const;
        auto GetArraySize(winrt::Microsoft::ReactNative::JsiObjectRef const& arr) const;
        auto GetArrayBufferSize(winrt::Microsoft::ReactNative::JsiObjectRef const& arrayBuffer) const;
        auto GetArrayBufferData(winrt::Microsoft::ReactNative::JsiObjectRef const& arrayBuffer, winrt::Microsoft::ReactNative::JsiByteArrayUser const& useArrayBytes) const;
        auto GetValueAtIndex(winrt::Microsoft::ReactNative::JsiObjectRef const& arr, uint32_t index) const;
        auto SetValueAtIndex(winrt::Microsoft::ReactNative::JsiObjectRef const& arr, uint32_t index, winrt::Microsoft::ReactNative::JsiValueRef const& value) const;
        auto CreateFunctionFromHostFunction(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& funcName, uint32_t paramCount, winrt::Microsoft::ReactNative::JsiHostFunction const& hostFunc) const;
        auto Call(winrt::Microsoft::ReactNative::JsiObjectRef const& func, winrt::Microsoft::ReactNative::JsiValueRef const& thisArg, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const;
        auto CallAsConstructor(winrt::Microsoft::ReactNative::JsiObjectRef const& func, array_view<winrt::Microsoft::ReactNative::JsiValueRef const> args) const;
        auto PushScope() const;
        auto PopScope(winrt::Microsoft::ReactNative::JsiScopeState const& scopeState) const;
        auto SymbolStrictEquals(winrt::Microsoft::ReactNative::JsiSymbolRef const& left, winrt::Microsoft::ReactNative::JsiSymbolRef const& right) const;
        auto BigIntStrictEquals(winrt::Microsoft::ReactNative::JsiBigIntRef const& left, winrt::Microsoft::ReactNative::JsiBigIntRef const& right) const;
        auto StringStrictEquals(winrt::Microsoft::ReactNative::JsiStringRef const& left, winrt::Microsoft::ReactNative::JsiStringRef const& right) const;
        auto ObjectStrictEquals(winrt::Microsoft::ReactNative::JsiObjectRef const& left, winrt::Microsoft::ReactNative::JsiObjectRef const& right) const;
        auto InstanceOf(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::JsiObjectRef const& constructor) const;
        auto ReleaseSymbol(winrt::Microsoft::ReactNative::JsiSymbolRef const& symbol) const;
        auto ReleaseBigInt(winrt::Microsoft::ReactNative::JsiBigIntRef const& bigInt) const;
        auto ReleaseString(winrt::Microsoft::ReactNative::JsiStringRef const& str) const;
        auto ReleaseObject(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto ReleasePropertyId(winrt::Microsoft::ReactNative::JsiPropertyIdRef const& propertyId) const;
        auto GetAndClearError() const;
        auto SetError(winrt::Microsoft::ReactNative::JsiErrorType const& errorType, param::hstring const& errorDetails, winrt::Microsoft::ReactNative::JsiValueRef const& value) const;
        auto HasNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto GetNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj) const;
        auto SetNativeState(winrt::Microsoft::ReactNative::JsiObjectRef const& obj, winrt::Microsoft::ReactNative::IReactNonAbiValue const& state) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiRuntime>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiRuntime<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IJsiRuntimeStatics
    {
        auto MakeChakraRuntime() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IJsiRuntimeStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IJsiRuntimeStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ILayoutService
    {
        auto ApplyLayoutForAllNodes() const;
        auto ApplyLayout(int64_t reactTag, float width, float height) const;
        [[nodiscard]] auto IsInBatch() const;
        auto MarkDirty(int64_t reactTag) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ILayoutService>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ILayoutService<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ILayoutServiceStatics
    {
        auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& context) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ILayoutServiceStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ILayoutServiceStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IQuirkSettings
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IQuirkSettings>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IQuirkSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IQuirkSettingsStatics
    {
        auto SetMatchAndroidAndIOSStretchBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
        auto SetUseWebFlexBasisBehavior(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
        auto SetAcceptSelfSigned(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
        auto SetBackHandlerKind(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, winrt::Microsoft::ReactNative::BackNavigationHandlerKind const& kind) const;
        auto SetMapWindowDeactivatedToAppStateInactive(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
        auto SetSuppressWindowFocusOnViewFocus(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
        auto SetUseRuntimeScheduler(winrt::Microsoft::ReactNative::ReactInstanceSettings const& settings, bool value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IQuirkSettingsStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IQuirkSettingsStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactApplication
    {
        [[nodiscard]] auto InstanceSettings() const;
        auto InstanceSettings(winrt::Microsoft::ReactNative::ReactInstanceSettings const& value) const;
        [[nodiscard]] auto PackageProviders() const;
        [[nodiscard]] auto Host() const;
        [[nodiscard]] auto UseDeveloperSupport() const;
        auto UseDeveloperSupport(bool value) const;
        [[nodiscard]] auto JavaScriptBundleFile() const;
        auto JavaScriptBundleFile(param::hstring const& value) const;
        [[nodiscard]] auto BundleAppId() const;
        auto BundleAppId(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactApplication>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactApplication<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactApplicationFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactApplicationFactory>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactApplicationFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactContext
    {
        [[nodiscard]] auto SettingsSnapshot() const;
        [[nodiscard]] auto Properties() const;
        [[nodiscard]] auto Notifications() const;
        [[nodiscard]] auto UIDispatcher() const;
        [[nodiscard]] auto JSDispatcher() const;
        [[nodiscard]] auto JSRuntime() const;
        [[nodiscard]] auto CallInvoker() const;
        auto DispatchEvent(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& eventDataArgWriter) const;
        auto CallJSFunction(param::hstring const& moduleName, param::hstring const& methodName, winrt::Microsoft::ReactNative::JSValueArgWriter const& paramsArgWriter) const;
        auto EmitJSEvent(param::hstring const& eventEmitterName, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& paramsArgWriter) const;
        [[nodiscard]] auto LoadingState() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactContext>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactContext<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactCoreInjection
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactCoreInjection>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactCoreInjection<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactCoreInjectionStatics
    {
        auto SetUIBatchCompleteCallback(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::UIBatchCompleteCallback const& xamlRoot) const;
        auto MakeViewHost(winrt::Microsoft::ReactNative::ReactNativeHost const& host, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions) const;
        auto PostToUIBatchingQueue(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback) const;
        auto SetPlatformNameOverride(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, param::hstring const& platformName) const;
        auto GetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
        auto SetTopLevelWindowId(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowId) const;
        auto SetTimerFactory(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Microsoft::ReactNative::TimerFactory const& timerFactory) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactCoreInjectionStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactCoreInjectionStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactDispatcher
    {
        [[nodiscard]] auto HasThreadAccess() const;
        auto Post(winrt::Microsoft::ReactNative::ReactDispatcherCallback const& callback) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactDispatcher>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactDispatcher<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactDispatcherHelperStatics
    {
        auto CreateSerialDispatcher() const;
        [[nodiscard]] auto UIThreadDispatcher() const;
        [[nodiscard]] auto UIDispatcherProperty() const;
        [[nodiscard]] auto JSDispatcherProperty() const;
        [[nodiscard]] auto JSDispatcherTaskStartingEventName() const;
        [[nodiscard]] auto JSDispatcherIdleWaitStartingEventName() const;
        [[nodiscard]] auto JSDispatcherIdleWaitCompletedEventName() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactDispatcherHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactDispatcherHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactInstanceSettings
    {
        [[nodiscard]] auto Properties() const;
        [[nodiscard]] auto Notifications() const;
        [[nodiscard]] auto PackageProviders() const;
        [[nodiscard]] auto UseDeveloperSupport() const;
        auto UseDeveloperSupport(bool value) const;
        [[nodiscard]] auto JavaScriptBundleFile() const;
        auto JavaScriptBundleFile(param::hstring const& value) const;
        [[nodiscard]] auto BundleAppId() const;
        auto BundleAppId(param::hstring const& value) const;
        [[nodiscard]] auto RequestDevBundle() const;
        auto RequestDevBundle(bool value) const;
        [[nodiscard]] auto UseWebDebugger() const;
        auto UseWebDebugger(bool value) const;
        [[nodiscard]] auto UseFastRefresh() const;
        auto UseFastRefresh(bool value) const;
        [[nodiscard]] auto UseLiveReload() const;
        auto UseLiveReload(bool value) const;
        [[nodiscard]] auto UseDirectDebugger() const;
        auto UseDirectDebugger(bool value) const;
        [[nodiscard]] auto DebuggerBreakOnNextLine() const;
        auto DebuggerBreakOnNextLine(bool value) const;
        [[nodiscard]] auto EnableJITCompilation() const;
        auto EnableJITCompilation(bool value) const;
        [[nodiscard]] auto EnableByteCodeCaching() const;
        auto EnableByteCodeCaching(bool value) const;
        [[nodiscard]] auto EnableDefaultCrashHandler() const;
        auto EnableDefaultCrashHandler(bool value) const;
        [[nodiscard]] auto EnableDeveloperMenu() const;
        auto EnableDeveloperMenu(bool value) const;
        [[nodiscard]] auto ByteCodeFileUri() const;
        auto ByteCodeFileUri(param::hstring const& value) const;
        [[nodiscard]] auto DebugBundlePath() const;
        auto DebugBundlePath(param::hstring const& value) const;
        [[nodiscard]] auto BundleRootPath() const;
        auto BundleRootPath(param::hstring const& value) const;
        [[nodiscard]] auto DebuggerPort() const;
        auto DebuggerPort(uint16_t value) const;
        [[nodiscard]] auto DebuggerRuntimeName() const;
        auto DebuggerRuntimeName(param::hstring const& value) const;
        [[nodiscard]] auto RedBoxHandler() const;
        auto RedBoxHandler(winrt::Microsoft::ReactNative::IRedBoxHandler const& value) const;
        [[nodiscard]] auto NativeLogger() const;
        auto NativeLogger(winrt::Microsoft::ReactNative::LogHandler const& value) const;
        [[nodiscard]] auto UIDispatcher() const;
        auto UIDispatcher(winrt::Microsoft::ReactNative::IReactDispatcher const& value) const;
        [[nodiscard]] auto SourceBundleHost() const;
        auto SourceBundleHost(param::hstring const& value) const;
        [[nodiscard]] auto SourceBundlePort() const;
        auto SourceBundlePort(uint16_t value) const;
        [[nodiscard]] auto RequestInlineSourceMap() const;
        auto RequestInlineSourceMap(bool value) const;
        [[nodiscard]] auto JSIEngineOverride() const;
        auto JSIEngineOverride(winrt::Microsoft::ReactNative::JSIEngine const& value) const;
        auto InstanceCreated(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> const& handler) const;
        using InstanceCreated_revoker = impl::event_revoker<winrt::Microsoft::ReactNative::IReactInstanceSettings, &impl::abi_t<winrt::Microsoft::ReactNative::IReactInstanceSettings>::remove_InstanceCreated>;
        [[nodiscard]] auto InstanceCreated(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceCreatedEventArgs> const& handler) const;
        auto InstanceCreated(winrt::event_token const& token) const noexcept;
        auto InstanceLoaded(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> const& handler) const;
        using InstanceLoaded_revoker = impl::event_revoker<winrt::Microsoft::ReactNative::IReactInstanceSettings, &impl::abi_t<winrt::Microsoft::ReactNative::IReactInstanceSettings>::remove_InstanceLoaded>;
        [[nodiscard]] auto InstanceLoaded(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceLoadedEventArgs> const& handler) const;
        auto InstanceLoaded(winrt::event_token const& token) const noexcept;
        auto InstanceDestroyed(winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> const& handler) const;
        using InstanceDestroyed_revoker = impl::event_revoker<winrt::Microsoft::ReactNative::IReactInstanceSettings, &impl::abi_t<winrt::Microsoft::ReactNative::IReactInstanceSettings>::remove_InstanceDestroyed>;
        [[nodiscard]] auto InstanceDestroyed(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Microsoft::ReactNative::InstanceDestroyedEventArgs> const& handler) const;
        auto InstanceDestroyed(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactInstanceSettings>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactInstanceSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactModuleBuilder
    {
        auto AddInitializer(winrt::Microsoft::ReactNative::InitializerDelegate const& initializer) const;
        auto AddJsiInitializer(winrt::Microsoft::ReactNative::JsiInitializerDelegate const& initializer) const;
        auto AddConstantProvider(winrt::Microsoft::ReactNative::ConstantProviderDelegate const& constantProvider) const;
        auto AddMethod(param::hstring const& name, winrt::Microsoft::ReactNative::MethodReturnType const& returnType, winrt::Microsoft::ReactNative::MethodDelegate const& method) const;
        auto AddSyncMethod(param::hstring const& name, winrt::Microsoft::ReactNative::SyncMethodDelegate const& method) const;
        auto AddEventEmitter(param::hstring const& name, winrt::Microsoft::ReactNative::EventEmitterInitializerDelegate const& emitter) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactModuleBuilder>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactModuleBuilder<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNativeHost
    {
        [[nodiscard]] auto PackageProviders() const;
        [[nodiscard]] auto InstanceSettings() const;
        auto InstanceSettings(winrt::Microsoft::ReactNative::ReactInstanceSettings const& value) const;
        auto LoadInstance() const;
        auto ReloadInstance() const;
        auto UnloadInstance() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNativeHost>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNativeHost<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNativeHostStatics
    {
        auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& reactContext) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNativeHostStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNativeHostStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNonAbiValue
    {
        auto GetPtr() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNonAbiValue>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNonAbiValue<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNotificationArgs
    {
        [[nodiscard]] auto Subscription() const;
        [[nodiscard]] auto Data() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNotificationArgs>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNotificationArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNotificationService
    {
        auto Subscribe(winrt::Microsoft::ReactNative::IReactPropertyName const& notificationName, winrt::Microsoft::ReactNative::IReactDispatcher const& dispatcher, winrt::Microsoft::ReactNative::ReactNotificationHandler const& handler) const;
        auto SendNotification(winrt::Microsoft::ReactNative::IReactPropertyName const& notificationName, winrt::Windows::Foundation::IInspectable const& sender, winrt::Windows::Foundation::IInspectable const& data) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNotificationService>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNotificationService<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNotificationServiceHelperStatics
    {
        auto CreateNotificationService() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNotificationServiceHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNotificationServiceHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactNotificationSubscription
    {
        [[nodiscard]] auto NotificationService() const;
        [[nodiscard]] auto NotificationName() const;
        [[nodiscard]] auto Dispatcher() const;
        [[nodiscard]] auto IsSubscribed() const;
        auto Unsubscribe() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactNotificationSubscription>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactNotificationSubscription<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPackageBuilder
    {
        auto AddModule(param::hstring const& moduleName, winrt::Microsoft::ReactNative::ReactModuleProvider const& moduleProvider) const;
        auto AddTurboModule(param::hstring const& moduleName, winrt::Microsoft::ReactNative::ReactModuleProvider const& moduleProvider) const;
        auto AddViewManager(param::hstring const& viewManagerName, winrt::Microsoft::ReactNative::ReactViewManagerProvider const& viewManagerProvider) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPackageBuilder>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPackageBuilder<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPackageProvider
    {
        auto CreatePackage(winrt::Microsoft::ReactNative::IReactPackageBuilder const& packageBuilder) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPackageProvider>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPackageProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPointerEventArgs
    {
        [[nodiscard]] auto Args() const;
        [[nodiscard]] auto Kind() const;
        auto Kind(winrt::Microsoft::ReactNative::PointerEventKind const& value) const;
        [[nodiscard]] auto Target() const;
        auto Target(winrt::Windows::Foundation::IInspectable const& value) const;
        [[nodiscard]] auto AllowUncaptured() const;
        auto AllowUncaptured(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPointerEventArgs>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPointerEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPropertyBag
    {
        auto Get(winrt::Microsoft::ReactNative::IReactPropertyName const& name) const;
        auto GetOrCreate(winrt::Microsoft::ReactNative::IReactPropertyName const& name, winrt::Microsoft::ReactNative::ReactCreatePropertyValue const& createValue) const;
        auto Set(winrt::Microsoft::ReactNative::IReactPropertyName const& name, winrt::Windows::Foundation::IInspectable const& value) const;
        auto CopyFrom(winrt::Microsoft::ReactNative::IReactPropertyBag const& other) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPropertyBag>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPropertyBag<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics
    {
        [[nodiscard]] auto GlobalNamespace() const;
        auto GetNamespace(param::hstring const& namespaceName) const;
        auto GetName(winrt::Microsoft::ReactNative::IReactPropertyNamespace const& ns, param::hstring const& localName) const;
        auto CreatePropertyBag() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPropertyBagHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPropertyBagHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPropertyName
    {
        [[nodiscard]] auto LocalName() const;
        [[nodiscard]] auto Namespace() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPropertyName>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPropertyName<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactPropertyNamespace
    {
        [[nodiscard]] auto NamespaceName() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactPropertyNamespace>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactPropertyNamespace<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactRootView
    {
        [[nodiscard]] auto ReactNativeHost() const;
        auto ReactNativeHost(winrt::Microsoft::ReactNative::ReactNativeHost const& value) const;
        [[nodiscard]] auto ComponentName() const;
        auto ComponentName(param::hstring const& value) const;
        [[nodiscard]] auto InitialProps() const;
        auto InitialProps(winrt::Microsoft::ReactNative::JSValueArgWriter const& value) const;
        [[nodiscard]] auto IsPerspectiveEnabled() const;
        auto IsPerspectiveEnabled(bool value) const;
        auto ReloadView() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactRootView>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactRootView<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactSettingsSnapshot
    {
        [[nodiscard]] auto UseWebDebugger() const;
        [[nodiscard]] auto UseFastRefresh() const;
        [[nodiscard]] auto UseDirectDebugger() const;
        [[nodiscard]] auto DebuggerBreakOnNextLine() const;
        [[nodiscard]] auto DebuggerPort() const;
        [[nodiscard]] auto DebugBundlePath() const;
        [[nodiscard]] auto BundleRootPath() const;
        [[nodiscard]] auto SourceBundleHost() const;
        [[nodiscard]] auto SourceBundlePort() const;
        [[nodiscard]] auto RequestInlineSourceMap() const;
        [[nodiscard]] auto JavaScriptBundleFile() const;
        [[nodiscard]] auto BundleAppId() const;
        [[nodiscard]] auto RequestDevBundle() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactSettingsSnapshot>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactSettingsSnapshot<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactViewHost
    {
        auto ReloadViewInstance() const;
        auto ReloadViewInstanceWithOptions(winrt::Microsoft::ReactNative::ReactViewOptions const& options) const;
        auto UnloadViewInstance() const;
        auto AttachViewInstance(winrt::Microsoft::ReactNative::IReactViewInstance const& viewInstance) const;
        auto DetachViewInstance() const;
        [[nodiscard]] auto ReactNativeHost() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactViewHost>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactViewHost<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactViewInstance
    {
        auto InitRootView(winrt::Microsoft::ReactNative::IReactContext const& context, winrt::Microsoft::ReactNative::ReactViewOptions const& viewOptions) const;
        auto UpdateRootView() const;
        auto UninitRootView() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactViewInstance>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactViewInstance<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IReactViewOptions
    {
        [[nodiscard]] auto ComponentName() const;
        auto ComponentName(param::hstring const& value) const;
        [[nodiscard]] auto InitialProps() const;
        auto InitialProps(winrt::Microsoft::ReactNative::JSValueArgWriter const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IReactViewOptions>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IReactViewOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo
    {
        [[nodiscard]] auto File() const;
        [[nodiscard]] auto Method() const;
        [[nodiscard]] auto Line() const;
        [[nodiscard]] auto Column() const;
        [[nodiscard]] auto Collapse() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IRedBoxErrorFrameInfo>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IRedBoxErrorFrameInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IRedBoxErrorInfo
    {
        [[nodiscard]] auto Message() const;
        [[nodiscard]] auto OriginalMessage() const;
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto ComponentStack() const;
        [[nodiscard]] auto Id() const;
        [[nodiscard]] auto Callstack() const;
        [[nodiscard]] auto ExtraData() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IRedBoxErrorInfo>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IRedBoxErrorInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IRedBoxHandler
    {
        auto ShowNewError(winrt::Microsoft::ReactNative::IRedBoxErrorInfo const& info, winrt::Microsoft::ReactNative::RedBoxErrorType const& type) const;
        [[nodiscard]] auto IsDevSupportEnabled() const;
        auto UpdateError(winrt::Microsoft::ReactNative::IRedBoxErrorInfo const& info) const;
        auto DismissRedBox() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IRedBoxHandler>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IRedBoxHandler<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IRedBoxHelper
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IRedBoxHelper>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IRedBoxHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IRedBoxHelperStatics
    {
        auto CreateDefaultHandler(winrt::Microsoft::ReactNative::ReactNativeHost const& host) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IRedBoxHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IRedBoxHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ITimer
    {
        [[nodiscard]] auto Interval() const;
        auto Interval(winrt::Windows::Foundation::TimeSpan const& value) const;
        auto Start() const;
        auto Stop() const;
        auto Tick(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Tick_revoker = impl::event_revoker<winrt::Microsoft::ReactNative::ITimer, &impl::abi_t<winrt::Microsoft::ReactNative::ITimer>::remove_Tick>;
        [[nodiscard]] auto Tick(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto Tick(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ITimer>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ITimer<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ITimer2
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ITimer2>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ITimer2<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_ITimerStatics
    {
        auto Create(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::ITimerStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_ITimerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewControl
    {
        auto GetPanel() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewControl>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewControl<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManager
    {
        [[nodiscard]] auto Name() const;
        auto CreateView() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManager>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerCreateWithProperties
    {
        auto CreateViewWithProperties(winrt::Microsoft::ReactNative::IJSValueReader const& propertyMapReader) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerCreateWithProperties>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerCreateWithProperties<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerRequiresNativeLayout
    {
        [[nodiscard]] auto RequiresNativeLayout() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerRequiresNativeLayout>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerRequiresNativeLayout<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithChildren
    {
        auto AddView(winrt::Windows::UI::Xaml::FrameworkElement const& parent, winrt::Windows::UI::Xaml::UIElement const& child, int64_t index) const;
        auto RemoveAllChildren(winrt::Windows::UI::Xaml::FrameworkElement const& parent) const;
        auto RemoveChildAt(winrt::Windows::UI::Xaml::FrameworkElement const& parent, int64_t index) const;
        auto ReplaceChild(winrt::Windows::UI::Xaml::FrameworkElement const& parent, winrt::Windows::UI::Xaml::UIElement const& oldChild, winrt::Windows::UI::Xaml::UIElement const& newChild) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithChildren>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithChildren<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithCommands
    {
        [[nodiscard]] auto Commands() const;
        auto DispatchCommand(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& commandId, winrt::Microsoft::ReactNative::IJSValueReader const& commandArgsReader) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithCommands>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithCommands<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithDropViewInstance
    {
        auto OnDropViewInstance(winrt::Windows::UI::Xaml::FrameworkElement const& view) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithDropViewInstance>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithDropViewInstance<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithExportedEventTypeConstants
    {
        [[nodiscard]] auto ExportedCustomBubblingEventTypeConstants() const;
        [[nodiscard]] auto ExportedCustomDirectEventTypeConstants() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithExportedEventTypeConstants>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithExportedEventTypeConstants<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithExportedViewConstants
    {
        [[nodiscard]] auto ExportedViewConstants() const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithExportedViewConstants>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithExportedViewConstants<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithNativeProperties
    {
        [[nodiscard]] auto NativeProps() const;
        auto UpdateProperties(winrt::Windows::UI::Xaml::FrameworkElement const& view, winrt::Microsoft::ReactNative::IJSValueReader const& propertyMapReader) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithNativeProperties>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithNativeProperties<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithOnLayout
    {
        auto OnLayout(winrt::Windows::UI::Xaml::FrameworkElement const& view, float left, float top, float width, float height) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithOnLayout>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithOnLayout<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithPointerEvents
    {
        auto OnPointerEvent(winrt::Windows::Foundation::IInspectable const& view, winrt::Microsoft::ReactNative::ReactPointerEventArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithPointerEvents>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithPointerEvents<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewManagerWithReactContext
    {
        [[nodiscard]] auto ReactContext() const;
        auto ReactContext(winrt::Microsoft::ReactNative::IReactContext const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewManagerWithReactContext>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewManagerWithReactContext<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewPanel
    {
        auto InsertAt(uint32_t index, winrt::Windows::UI::Xaml::UIElement const& value) const;
        auto RemoveAt(uint32_t index) const;
        auto Clear() const;
        [[nodiscard]] auto ViewBackground() const;
        auto ViewBackground(winrt::Windows::UI::Xaml::Media::Brush const& value) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewPanel>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewPanel<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IViewPanelStatics
    {
        [[nodiscard]] auto ViewBackgroundProperty() const;
        [[nodiscard]] auto BorderThicknessProperty() const;
        [[nodiscard]] auto BorderBrushProperty() const;
        [[nodiscard]] auto CornerRadiusProperty() const;
        [[nodiscard]] auto TopProperty() const;
        auto SetTop(winrt::Windows::UI::Xaml::UIElement const& element, double value) const;
        auto GetTop(winrt::Windows::UI::Xaml::UIElement const& element) const;
        [[nodiscard]] auto LeftProperty() const;
        auto SetLeft(winrt::Windows::UI::Xaml::UIElement const& element, double value) const;
        auto GetLeft(winrt::Windows::UI::Xaml::UIElement const& element) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IViewPanelStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IViewPanelStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IXamlHelper
    {
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IXamlHelper>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IXamlHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IXamlHelperStatics
    {
        auto BrushFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider) const;
        auto ColorFrom(winrt::Microsoft::ReactNative::JSValueArgWriter const& valueProvider) const;
        [[nodiscard]] auto ReactTagProperty() const;
        auto GetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject) const;
        auto SetReactTag(winrt::Windows::UI::Xaml::DependencyObject const& dependencyObject, int64_t tag) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IXamlHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IXamlHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IXamlUIService
    {
        auto ElementFromReactTag(int64_t reactTag) const;
        auto DispatchEvent(winrt::Windows::UI::Xaml::FrameworkElement const& view, param::hstring const& eventName, winrt::Microsoft::ReactNative::JSValueArgWriter const& eventDataArgWriter) const;
        auto GetReactRootView(winrt::Windows::UI::Xaml::FrameworkElement const& view) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IXamlUIService>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IXamlUIService<D>;
    };
    template <typename D>
    struct consume_Microsoft_ReactNative_IXamlUIServiceStatics
    {
        auto FromContext(winrt::Microsoft::ReactNative::IReactContext const& context) const;
        auto SetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::XamlRoot const& xamlRoot) const;
        auto SetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, winrt::Windows::UI::Xaml::FrameworkElement const& accessibleRoot) const;
        auto GetXamlRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
        auto GetAccessibleRoot(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
        auto GetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties) const;
        auto SetIslandWindowHandle(winrt::Microsoft::ReactNative::IReactPropertyBag const& properties, uint64_t windowHandle) const;
    };
    template <> struct consume<winrt::Microsoft::ReactNative::IXamlUIServiceStatics>
    {
        template <typename D> using type = consume_Microsoft_ReactNative_IXamlUIServiceStatics<D>;
    };
    struct struct_Microsoft_ReactNative_AccessibilityAction
    {
        void* Name;
        void* Label;
    };
    template <> struct abi<Microsoft::ReactNative::AccessibilityAction>
    {
        using type = struct_Microsoft_ReactNative_AccessibilityAction;
    };
    struct struct_Microsoft_ReactNative_DesktopWindowMessage
    {
        uint32_t Msg;
        uint64_t WParam;
        int64_t LParam;
    };
    template <> struct abi<Microsoft::ReactNative::DesktopWindowMessage>
    {
        using type = struct_Microsoft_ReactNative_DesktopWindowMessage;
    };
    struct struct_Microsoft_ReactNative_JsiBigIntRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiBigIntRef>
    {
        using type = struct_Microsoft_ReactNative_JsiBigIntRef;
    };
    struct struct_Microsoft_ReactNative_JsiObjectRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiObjectRef>
    {
        using type = struct_Microsoft_ReactNative_JsiObjectRef;
    };
    struct struct_Microsoft_ReactNative_JsiPropertyIdRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiPropertyIdRef>
    {
        using type = struct_Microsoft_ReactNative_JsiPropertyIdRef;
    };
    struct struct_Microsoft_ReactNative_JsiScopeState
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiScopeState>
    {
        using type = struct_Microsoft_ReactNative_JsiScopeState;
    };
    struct struct_Microsoft_ReactNative_JsiStringRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiStringRef>
    {
        using type = struct_Microsoft_ReactNative_JsiStringRef;
    };
    struct struct_Microsoft_ReactNative_JsiSymbolRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiSymbolRef>
    {
        using type = struct_Microsoft_ReactNative_JsiSymbolRef;
    };
    struct struct_Microsoft_ReactNative_JsiValueRef
    {
        int32_t Kind;
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiValueRef>
    {
        using type = struct_Microsoft_ReactNative_JsiValueRef;
    };
    struct struct_Microsoft_ReactNative_JsiWeakObjectRef
    {
        uint64_t Data;
    };
    template <> struct abi<Microsoft::ReactNative::JsiWeakObjectRef>
    {
        using type = struct_Microsoft_ReactNative_JsiWeakObjectRef;
    };
}
#endif
