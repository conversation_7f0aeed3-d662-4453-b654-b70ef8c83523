﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.windows.cppwinrt\2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.cppwinrt\2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ui.xaml\2.8.0\buildTransitive\Microsoft.UI.Xaml.props" Condition="Exists('$(NuGetPackageRoot)microsoft.ui.xaml\2.8.0\buildTransitive\Microsoft.UI.Xaml.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9\build\native\Microsoft.JavaScript.Hermes.props" Condition="Exists('$(NuGetPackageRoot)microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9\build\native\Microsoft.JavaScript.Hermes.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_Web_WebView2 Condition=" '$(PkgMicrosoft_Web_WebView2)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1264.42</PkgMicrosoft_Web_WebView2>
    <PkgMicrosoft_UI_Xaml Condition=" '$(PkgMicrosoft_UI_Xaml)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.ui.xaml\2.8.0</PkgMicrosoft_UI_Xaml>
    <PkgMicrosoft_JavaScript_Hermes Condition=" '$(PkgMicrosoft_JavaScript_Hermes)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.javascript.hermes\0.0.0-2505.2001-0e4bc3b9</PkgMicrosoft_JavaScript_Hermes>
  </PropertyGroup>
</Project>