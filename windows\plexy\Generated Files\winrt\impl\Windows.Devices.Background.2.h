// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Background_2_H
#define WINRT_Windows_Devices_Background_2_H
#include "winrt/impl/Windows.Devices.Background.1.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Background
{
    struct WINRT_IMPL_EMPTY_BASES DeviceServicingDetails : winrt::Windows::Devices::Background::IDeviceServicingDetails
    {
        DeviceServicingDetails(std::nullptr_t) noexcept {}
        DeviceServicingDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Background::IDeviceServicingDetails(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES DeviceUseDetails : winrt::Windows::Devices::Background::IDeviceUseDetails
    {
        DeviceUseDetails(std::nullptr_t) noexcept {}
        DeviceUseDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Background::IDeviceUseDetails(ptr, take_ownership_from_abi) {}
    };
}
#endif
