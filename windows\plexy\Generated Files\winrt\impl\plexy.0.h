// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_plexy_0_H
#define WINRT_plexy_0_H
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Markup
{
    struct IXamlMetadataProvider;
}
WINRT_EXPORT namespace winrt::plexy
{
    struct IMainPage;
    struct MainPage;
    struct XamlMetaDataProvider;
}
namespace winrt::impl
{
    template <> struct category<winrt::plexy::IMainPage>{ using type = interface_category; };
    template <> struct category<winrt::plexy::MainPage>{ using type = class_category; };
    template <> struct category<winrt::plexy::XamlMetaDataProvider>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::plexy::MainPage> = L"plexy.MainPage";
    template <> inline constexpr auto& name_v<winrt::plexy::XamlMetaDataProvider> = L"plexy.XamlMetaDataProvider";
    template <> inline constexpr auto& name_v<winrt::plexy::IMainPage> = L"plexy.IMainPage";
    template <> inline constexpr guid guid_v<winrt::plexy::IMainPage>{ 0xEB0D3FCA,0xC8C5,0x525D,{ 0xBC,0x96,0x6C,0x8E,0xF3,0xFE,0x60,0x6B } }; // EB0D3FCA-C8C5-525D-BC96-6C8EF3FE606B
    template <> struct default_interface<winrt::plexy::MainPage>{ using type = winrt::plexy::IMainPage; };
    template <> struct default_interface<winrt::plexy::XamlMetaDataProvider>{ using type = winrt::Windows::UI::Xaml::Markup::IXamlMetadataProvider; };
    template <> struct abi<winrt::plexy::IMainPage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <typename D>
    struct consume_plexy_IMainPage
    {
    };
    template <> struct consume<winrt::plexy::IMainPage>
    {
        template <typename D> using type = consume_plexy_IMainPage<D>;
    };
}
#endif
