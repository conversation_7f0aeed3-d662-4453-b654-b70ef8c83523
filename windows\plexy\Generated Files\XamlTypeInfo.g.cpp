﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "pch.h"
#include <memory>

#include "XamlTypeInfo.xaml.g.h"

#include "MainPage.h"
#include "App.h"
#include "XamlBindingInfo.xaml.g.hpp"
#include "MainPage.xaml.g.hpp"
#include "App.xaml.g.hpp"

namespace winrt::plexy::implementation
{
using IXamlMember = ::winrt::Windows::UI::Xaml::Markup::IXamlMember;
using IXamlType = ::winrt::Windows::UI::Xaml::Markup::IXamlType;
using TypeKind = ::winrt::Windows::UI::Xaml::Interop::TypeKind;

template <typename T>
::winrt::Windows::Foundation::IInspectable ActivateType()
{
    return T();
}

template <typename T>
::winrt::Windows::Foundation::IInspectable ActivateLocalType()
{
    return ::winrt::make<T>();
}

template<typename TInstance, typename TItem>
void CollectionAdd(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& item)
{
    instance.as<TInstance>().Append(::winrt::unbox_value<TItem>(item));
}

template<typename TInstance, typename TKey, typename TItem>
void DictionaryAdd(
    ::winrt::Windows::Foundation::IInspectable const& instance,
    ::winrt::Windows::Foundation::IInspectable const& key,
    ::winrt::Windows::Foundation::IInspectable const& item)
{
    instance.as<TInstance>().Insert(::winrt::unbox_value<TKey>(key), ::winrt::unbox_value<TItem>(item));
}

template<typename T>
::winrt::Windows::Foundation::IInspectable FromStringConverter(
    XamlUserType const& userType, 
    ::winrt::hstring const& input)
{
    return ::winrt::box_value(static_cast<T>(userType.CreateEnumUIntFromString(input)));
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_UseDeveloperSupport(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().UseDeveloperSupport());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_ControlsResourcesVersion(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().ControlsResourcesVersion());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_IsPerspectiveEnabled(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().IsPerspectiveEnabled());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_IsExpanded(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().IsExpanded());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_HasUnrealizedChildren(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().HasUnrealizedChildren());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_Depth(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().Depth());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_HasChildren(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().HasChildren());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_JavaScriptBundleFile(::winrt::Windows::Foundation::IInspectable const& instance)
{
   return ::winrt::box_value(::winrt::Windows::Foundation::PropertyValue::CreateString(instance.as<T>().JavaScriptBundleFile()));
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_BundleAppId(::winrt::Windows::Foundation::IInspectable const& instance)
{
   return ::winrt::box_value(::winrt::Windows::Foundation::PropertyValue::CreateString(instance.as<T>().BundleAppId()));
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_ComponentName(::winrt::Windows::Foundation::IInspectable const& instance)
{
   return ::winrt::box_value(::winrt::Windows::Foundation::PropertyValue::CreateString(instance.as<T>().ComponentName()));
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_InstanceSettings(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().InstanceSettings());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Host(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Host());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_PackageProviders(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().PackageProviders());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_ReactNativeHost(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().ReactNativeHost());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_InitialProps(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().InitialProps());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Foreground(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Foreground());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Content(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Content());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Children(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Children());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Parent(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Parent());
}

template<typename TDeclaringType, typename TValue>
void SetEnumMember_ControlsResourcesVersion(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().ControlsResourcesVersion(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_UseDeveloperSupport(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().UseDeveloperSupport(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_IsPerspectiveEnabled(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().IsPerspectiveEnabled(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_IsExpanded(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().IsExpanded(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_HasUnrealizedChildren(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().HasUnrealizedChildren(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_JavaScriptBundleFile(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    return instance.as<TDeclaringType>().JavaScriptBundleFile(::winrt::unbox_value<::winrt::hstring>(value));
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_BundleAppId(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    return instance.as<TDeclaringType>().BundleAppId(::winrt::unbox_value<::winrt::hstring>(value));
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_ComponentName(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    return instance.as<TDeclaringType>().ComponentName(::winrt::unbox_value<::winrt::hstring>(value));
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_InstanceSettings(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().InstanceSettings(value.as<TValue>());
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_ReactNativeHost(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().ReactNativeHost(value.as<TValue>());
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_InitialProps(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().InitialProps(value.as<TValue>());
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_Foreground(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().Foreground(value.as<TValue>());
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_Content(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().Content(value.as<TValue>());
}

enum TypeInfo_Flags
{
    TypeInfo_Flags_None                 = 0x00,
    TypeInfo_Flags_IsLocalType          = 0x01,
    TypeInfo_Flags_IsSystemType         = 0x02,
    TypeInfo_Flags_IsReturnTypeStub     = 0x04,
    TypeInfo_Flags_IsBindable           = 0x08,
    TypeInfo_Flags_IsMarkupExtension    = 0x10, 
};

struct TypeInfo
{
    const wchar_t* typeName{nullptr};
    const wchar_t* contentPropertyName{nullptr};
    ::winrt::Windows::Foundation::IInspectable (*activator)();
    void (*collectionAdd)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    void (*dictionaryAdd)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    ::winrt::Windows::Foundation::IInspectable (*fromStringConverter)(XamlUserType const&, ::winrt::hstring const& );
    int     baseTypeIndex;
    int     firstMemberIndex;
    int     firstEnumValueIndex;
    int     createFromStringIndex;
    TypeKind kindOfType;
    unsigned int flags;
    int boxedTypeIndex;
};


const TypeInfo TypeInfos[] = 
{
    //   0
    L"Int32", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   1
    L"String", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   2
    L"Object", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   3
    L"Boolean", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   4
    L"System.Enum", L"",
    nullptr, nullptr, nullptr, nullptr,
    7, // System.ValueType
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   5
    L"plexy.MainPage", L"",
    &ActivateLocalType<::winrt::plexy::implementation::MainPage>, nullptr, nullptr, nullptr,
    12, // Windows.UI.Xaml.Controls.Page
    0, 0, -1, TypeKind::Custom,
    TypeInfo_Flags_IsLocalType | TypeInfo_Flags_None,
    -1,
    //   6
    L"System.Delegate", L"",
    nullptr, nullptr, nullptr, nullptr,
    2, // Object
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   7
    L"System.ValueType", L"",
    nullptr, nullptr, nullptr, nullptr,
    2, // Object
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   8
    L"System.MulticastDelegate", L"",
    nullptr, nullptr, nullptr, nullptr,
    6, // System.Delegate
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   9
    L"Windows.UI.Xaml.Application", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  10
    L"Windows.UI.Xaml.Media.Brush", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  11
    L"Windows.UI.Xaml.Controls.Grid", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  12
    L"Windows.UI.Xaml.Controls.Page", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  13
    L"Windows.UI.Xaml.Controls.Panel", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  14
    L"Windows.UI.Xaml.DependencyObject", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  15
    L"Windows.UI.Xaml.ResourceDictionary", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  16
    L"Microsoft.ReactNative.ReactRootView", L"",
    &ActivateType<::winrt::Microsoft::ReactNative::ReactRootView>, nullptr, nullptr, nullptr,
    11, // Windows.UI.Xaml.Controls.Grid
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  17
    L"Windows.UI.Xaml.Controls.UserControl", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    4, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  18
    L"Microsoft.ReactNative.ReactNativeHost", L"",
    nullptr, nullptr, nullptr, nullptr,
    2, // Object
    4, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  19
    L"Microsoft.UI.Xaml.Controls.IconSource", L"",
    nullptr, nullptr, nullptr, nullptr,
    14, // Windows.UI.Xaml.DependencyObject
    4, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsBindable | TypeInfo_Flags_None,
    -1,
    //  20
    L"Microsoft.ReactNative.ReactApplication", L"",
    &ActivateType<::winrt::Microsoft::ReactNative::ReactApplication>, nullptr, nullptr, nullptr,
    9, // Windows.UI.Xaml.Application
    5, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  21
    L"Microsoft.ReactNative.JSValueArgWriter", L"",
    nullptr, nullptr, nullptr, nullptr,
    8, // System.MulticastDelegate
    11, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  22
    L"Microsoft.UI.Xaml.Controls.TreeViewNode", L"",
    &ActivateType<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, nullptr, nullptr, nullptr,
    14, // Windows.UI.Xaml.DependencyObject
    11, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsBindable | TypeInfo_Flags_None,
    -1,
    //  23
    L"Microsoft.ReactNative.ReactInstanceSettings", L"",
    nullptr, nullptr, nullptr, nullptr,
    2, // Object
    18, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  24
    L"Microsoft.ReactNative.IReactPackageProvider", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    18, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  25
    L"Microsoft.UI.Xaml.Controls.XamlControlsResources", L"",
    &ActivateType<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources>, nullptr, &DictionaryAdd<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, ::winrt::Windows::Foundation::IInspectable, ::winrt::Windows::Foundation::IInspectable>, nullptr,
    15, // Windows.UI.Xaml.ResourceDictionary
    18, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  26
    L"Microsoft.UI.Xaml.Controls.ControlsResourcesVersion", L"",
    nullptr, nullptr, nullptr, &FromStringConverter<::winrt::Microsoft::UI::Xaml::Controls::ControlsResourcesVersion>,
    4, // System.Enum
    19, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  27
    L"Windows.Foundation.Collections.IVector`1<Microsoft.UI.Xaml.Controls.TreeViewNode>", L"",
    nullptr, &CollectionAdd<::winrt::Windows::Foundation::Collections::IVector<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, ::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, nullptr, nullptr,
    -1,
    19, 2, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  28
    L"Windows.Foundation.Collections.IVector`1<Microsoft.ReactNative.IReactPackageProvider>", L"",
    nullptr, &CollectionAdd<::winrt::Windows::Foundation::Collections::IVector<::winrt::Microsoft::ReactNative::IReactPackageProvider>, ::winrt::Microsoft::ReactNative::IReactPackageProvider>, nullptr, nullptr,
    -1,
    19, 2, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  Last type here is for padding
    L"", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1, 
    19, 2, -1, TypeKind::Custom,
    TypeInfo_Flags_None,
};

constexpr uint32_t TypeInfoLookup[] = { 
      0,   //   0
      0,   //   1
      0,   //   2
      0,   //   3
      0,   //   4
      0,   //   5
      1,   //   6
      3,   //   7
      4,   //   8
      4,   //   9
      4,   //  10
      4,   //  11
      5,   //  12
      5,   //  13
      5,   //  14
      6,   //  15
      7,   //  16
      8,   //  17
      8,   //  18
      8,   //  19
      8,   //  20
      8,   //  21
      8,   //  22
      8,   //  23
      8,   //  24
      9,   //  25
      9,   //  26
      9,   //  27
     11,   //  28
     11,   //  29
     13,   //  30
     14,   //  31
     14,   //  32
     15,   //  33
     15,   //  34
     16,   //  35
     17,   //  36
     18,   //  37
     20,   //  38
     22,   //  39
     23,   //  40
     23,   //  41
     23,   //  42
     23,   //  43
     25,   //  44
     25,   //  45
     25,   //  46
     25,   //  47
     25,   //  48
     26,   //  49
     26,   //  50
     26,   //  51
     27,   //  52
     27,   //  53
     27,   //  54
     27,   //  55
     27,   //  56
     27,   //  57
     27,   //  58
     27,   //  59
     27,   //  60
     27,   //  61
     27,   //  62
     27,   //  63
     27,   //  64
     27,   //  65
     27,   //  66
     27,   //  67
     27,   //  68
     27,   //  69
     27,   //  70
     27,   //  71
     27,   //  72
     27,   //  73
     27,   //  74
     27,   //  75
     27,   //  76
     27,   //  77
     27,   //  78
     27,   //  79
     27,   //  80
     27,   //  81
     28,   //  82
     28,   //  83
     28,   //  84
     28,   //  85
     29,   //  86
};

struct EnumValueInfo
{
    const wchar_t* name{nullptr};
    int eValue;
};

const EnumValueInfo EnumValues[] =
{ 
    L"Version1", static_cast<int>(::winrt::Microsoft::UI::Xaml::Controls::ControlsResourcesVersion::Version1),
    L"Version2", static_cast<int>(::winrt::Microsoft::UI::Xaml::Controls::ControlsResourcesVersion::Version2),
};

struct MemberInfo 
{
    const wchar_t* shortName{nullptr};
    ::winrt::Windows::Foundation::IInspectable (*getter)(::winrt::Windows::Foundation::IInspectable const&);
    void (*setter)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    int typeIndex;
    int targetTypeIndex;
    bool isReadOnly;
    bool isDependencyProperty;
    bool isAttachable;
};

const MemberInfo MemberInfos[] = 
{
    //   0 - Microsoft.ReactNative.ReactRootView.ComponentName
    L"ComponentName",
    &GetReferenceTypeMember_ComponentName<::winrt::Microsoft::ReactNative::ReactRootView>,
    &SetReferenceTypeMember_ComponentName<::winrt::Microsoft::ReactNative::ReactRootView, ::winrt::hstring>,
    1, // String
    -1,
    false, false, false,
    //   1 - Microsoft.ReactNative.ReactRootView.ReactNativeHost
    L"ReactNativeHost",
    &GetReferenceTypeMember_ReactNativeHost<::winrt::Microsoft::ReactNative::ReactRootView>,
    &SetReferenceTypeMember_ReactNativeHost<::winrt::Microsoft::ReactNative::ReactRootView, ::winrt::Microsoft::ReactNative::ReactNativeHost>,
    18, // Microsoft.ReactNative.ReactNativeHost
    -1,
    false, false, false,
    //   2 - Microsoft.ReactNative.ReactRootView.IsPerspectiveEnabled
    L"IsPerspectiveEnabled",
    &GetValueTypeMember_IsPerspectiveEnabled<::winrt::Microsoft::ReactNative::ReactRootView, bool>,
    &SetValueTypeMember_IsPerspectiveEnabled<::winrt::Microsoft::ReactNative::ReactRootView, bool>,
    3, // Boolean
    -1,
    false, false, false,
    //   3 - Microsoft.ReactNative.ReactRootView.InitialProps
    L"InitialProps",
    &GetReferenceTypeMember_InitialProps<::winrt::Microsoft::ReactNative::ReactRootView>,
    &SetReferenceTypeMember_InitialProps<::winrt::Microsoft::ReactNative::ReactRootView, ::winrt::Microsoft::ReactNative::JSValueArgWriter>,
    21, // Microsoft.ReactNative.JSValueArgWriter
    -1,
    false, false, false,
    //   4 - Microsoft.UI.Xaml.Controls.IconSource.Foreground
    L"Foreground",
    &GetReferenceTypeMember_Foreground<::winrt::Microsoft::UI::Xaml::Controls::IconSource>,
    &SetReferenceTypeMember_Foreground<::winrt::Microsoft::UI::Xaml::Controls::IconSource, ::winrt::Windows::UI::Xaml::Media::Brush>,
    10, // Windows.UI.Xaml.Media.Brush
    -1,
    false, true,  false,
    //   5 - Microsoft.ReactNative.ReactApplication.UseDeveloperSupport
    L"UseDeveloperSupport",
    &GetValueTypeMember_UseDeveloperSupport<::winrt::Microsoft::ReactNative::ReactApplication, bool>,
    &SetValueTypeMember_UseDeveloperSupport<::winrt::Microsoft::ReactNative::ReactApplication, bool>,
    3, // Boolean
    -1,
    false, false, false,
    //   6 - Microsoft.ReactNative.ReactApplication.JavaScriptBundleFile
    L"JavaScriptBundleFile",
    &GetReferenceTypeMember_JavaScriptBundleFile<::winrt::Microsoft::ReactNative::ReactApplication>,
    &SetReferenceTypeMember_JavaScriptBundleFile<::winrt::Microsoft::ReactNative::ReactApplication, ::winrt::hstring>,
    1, // String
    -1,
    false, false, false,
    //   7 - Microsoft.ReactNative.ReactApplication.InstanceSettings
    L"InstanceSettings",
    &GetReferenceTypeMember_InstanceSettings<::winrt::Microsoft::ReactNative::ReactApplication>,
    &SetReferenceTypeMember_InstanceSettings<::winrt::Microsoft::ReactNative::ReactApplication, ::winrt::Microsoft::ReactNative::ReactInstanceSettings>,
    23, // Microsoft.ReactNative.ReactInstanceSettings
    -1,
    false, false, false,
    //   8 - Microsoft.ReactNative.ReactApplication.BundleAppId
    L"BundleAppId",
    &GetReferenceTypeMember_BundleAppId<::winrt::Microsoft::ReactNative::ReactApplication>,
    &SetReferenceTypeMember_BundleAppId<::winrt::Microsoft::ReactNative::ReactApplication, ::winrt::hstring>,
    1, // String
    -1,
    false, false, false,
    //   9 - Microsoft.ReactNative.ReactApplication.Host
    L"Host",
    &GetReferenceTypeMember_Host<::winrt::Microsoft::ReactNative::ReactApplication>,
    nullptr,
    18, // Microsoft.ReactNative.ReactNativeHost
    -1,
    true,  false, false,
    //  10 - Microsoft.ReactNative.ReactApplication.PackageProviders
    L"PackageProviders",
    &GetReferenceTypeMember_PackageProviders<::winrt::Microsoft::ReactNative::ReactApplication>,
    nullptr,
    28, // Windows.Foundation.Collections.IVector`1<Microsoft.ReactNative.IReactPackageProvider>
    -1,
    true,  false, false,
    //  11 - Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded
    L"IsExpanded",
    &GetValueTypeMember_IsExpanded<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    &SetValueTypeMember_IsExpanded<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    3, // Boolean
    -1,
    false, true,  false,
    //  12 - Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren
    L"HasUnrealizedChildren",
    &GetValueTypeMember_HasUnrealizedChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    &SetValueTypeMember_HasUnrealizedChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    3, // Boolean
    -1,
    false, false, false,
    //  13 - Microsoft.UI.Xaml.Controls.TreeViewNode.Content
    L"Content",
    &GetReferenceTypeMember_Content<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    &SetReferenceTypeMember_Content<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, ::winrt::Windows::Foundation::IInspectable>,
    2, // Object
    -1,
    false, true,  false,
    //  14 - Microsoft.UI.Xaml.Controls.TreeViewNode.Children
    L"Children",
    &GetReferenceTypeMember_Children<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    nullptr,
    27, // Windows.Foundation.Collections.IVector`1<Microsoft.UI.Xaml.Controls.TreeViewNode>
    -1,
    true,  false, false,
    //  15 - Microsoft.UI.Xaml.Controls.TreeViewNode.Depth
    L"Depth",
    &GetValueTypeMember_Depth<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, int32_t>,
    nullptr,
    0, // Int32
    -1,
    true,  true,  false,
    //  16 - Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren
    L"HasChildren",
    &GetValueTypeMember_HasChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    nullptr,
    3, // Boolean
    -1,
    true,  true,  false,
    //  17 - Microsoft.UI.Xaml.Controls.TreeViewNode.Parent
    L"Parent",
    &GetReferenceTypeMember_Parent<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    nullptr,
    22, // Microsoft.UI.Xaml.Controls.TreeViewNode
    -1,
    true,  false, false,
    //  18 - Microsoft.UI.Xaml.Controls.XamlControlsResources.ControlsResourcesVersion
    L"ControlsResourcesVersion",
    &GetValueTypeMember_ControlsResourcesVersion<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, ::winrt::Microsoft::UI::Xaml::Controls::ControlsResourcesVersion>,
    &SetEnumMember_ControlsResourcesVersion<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, ::winrt::Microsoft::UI::Xaml::Controls::ControlsResourcesVersion>,
    26, // Microsoft.UI.Xaml.Controls.ControlsResourcesVersion
    -1,
    false, true,  false,
};

const wchar_t* GetShortName(const wchar_t* longName)
{
    const auto separator = wcsrchr(longName, '.');
    return separator ? separator + 1: longName;
}

const TypeInfo* GetTypeInfo(::winrt::hstring const& typeName)
{
    size_t typeNameLength = typeName.size();
    if (typeNameLength < _countof(TypeInfoLookup) - 1)
    {
        const auto begin = TypeInfos + TypeInfoLookup[typeNameLength];
        const auto end = TypeInfos + TypeInfoLookup[typeNameLength + 1];
        auto pos = std::find_if(begin, end, [&typeName](TypeInfo const& elem)
        {
            return wcscmp(typeName.data(), elem.typeName) == 0;
        });
        if (pos != end)
        {
            return pos;
        }
    }
    return nullptr;
}

const MemberInfo* GetMemberInfo(::winrt::hstring const& longMemberName)
{
    const auto dotPosition = std::find(longMemberName.crbegin(), longMemberName.crend(), L'.').base();
    if (dotPosition != longMemberName.end())
    {
        const auto sizeBeforeDot = static_cast<::winrt::hstring::size_type>(dotPosition - longMemberName.begin()) - 1;
        const TypeInfo* pTypeInfo = GetTypeInfo(::winrt::hstring{longMemberName.data(), sizeBeforeDot});
        if (pTypeInfo)
        {
            const TypeInfo* pNextTypeInfo = pTypeInfo + 1;
            const auto shortMemberName = GetShortName(longMemberName.data());
            const auto begin = MemberInfos + pTypeInfo->firstMemberIndex;
            const auto end = MemberInfos + pNextTypeInfo->firstMemberIndex;
            auto info = std::find_if(begin, end,
                [shortMemberName](const MemberInfo& elem)
            {
                return wcscmp(shortMemberName, elem.shortName) == 0;
            });
            if (info != end)
            {
                return info;
            }
        }
    }
    return nullptr;
}

std::vector<::winrt::Windows::UI::Xaml::Markup::IXamlMetadataProvider> const& XamlTypeInfoProvider::OtherProviders()
{
    if (_otherProviders.empty())
    {
        _otherProviders.push_back(::winrt::Microsoft::ReactNative::XamlMetaDataProvider());
        _otherProviders.push_back(::winrt::Microsoft::UI::Xaml::XamlTypeInfo::XamlControlsXamlMetaDataProvider());
    }
    return _otherProviders;
}

IXamlType XamlTypeInfoProvider::CreateXamlType(::winrt::hstring const& typeName)
{
    const TypeInfo* pTypeInfo = GetTypeInfo(typeName);
    const TypeInfo* pNextTypeInfo = pTypeInfo + 1;
    if (!pTypeInfo || !pNextTypeInfo)
    {
        return nullptr;
    }
    else if (pTypeInfo->flags & TypeInfo_Flags_IsSystemType)
    {
        return ::winrt::make<XamlSystemBaseType>(typeName);
    }
    else
    {
        ::winrt::hstring baseName { pTypeInfo->baseTypeIndex >= 0 ? TypeInfos[pTypeInfo->baseTypeIndex].typeName : L""};
        ::winrt::hstring boxedName { pTypeInfo->boxedTypeIndex >= 0 ? TypeInfos[pTypeInfo->boxedTypeIndex].typeName : L""};
        auto userType = ::winrt::make_self<XamlUserType>(shared_from_this(), pTypeInfo->typeName, GetXamlTypeByName(baseName));
        userType->_kindOfType = pTypeInfo->kindOfType;
        userType->_activator = pTypeInfo->activator;
        userType->_collectionAdd = pTypeInfo->collectionAdd;
        userType->_dictionaryAdd = pTypeInfo->dictionaryAdd;
        userType->_fromStringConverter = pTypeInfo->fromStringConverter;
        userType->ContentPropertyName(pTypeInfo->contentPropertyName);
        userType->IsLocalType(pTypeInfo->flags & TypeInfo_Flags_IsLocalType);
        userType->IsReturnTypeStub(pTypeInfo->flags & TypeInfo_Flags_IsReturnTypeStub);
        userType->IsBindable(pTypeInfo->flags & TypeInfo_Flags_IsBindable);
        userType->IsMarkupExtension(pTypeInfo->flags & TypeInfo_Flags_IsMarkupExtension);
        userType->_createFromStringMethod = nullptr;
        userType->SetBoxedType(GetXamlTypeByName(boxedName));
        for (int i = pTypeInfo->firstMemberIndex; i < pNextTypeInfo->firstMemberIndex; ++i)
        {
            userType->AddMemberName(MemberInfos[i].shortName);
        }
        for (int i = pTypeInfo->firstEnumValueIndex; i < pNextTypeInfo->firstEnumValueIndex; ++i)
        {
            userType->AddEnumValue(EnumValues[i].name, winrt::Windows::Foundation::PropertyValue::CreateInt32(EnumValues[i].eValue));
        }
        return userType.as<IXamlType>();
    }
}

IXamlMember XamlTypeInfoProvider::CreateXamlMember(::winrt::hstring const& longMemberName)
{
    const MemberInfo* pMemberInfo = GetMemberInfo(longMemberName);
    if (!pMemberInfo)
    {
        return nullptr;
    }
    auto xamlMember = ::winrt::make_self<XamlMember>(shared_from_this(),
        pMemberInfo->shortName, TypeInfos[pMemberInfo->typeIndex].typeName);
    xamlMember->_getter = pMemberInfo->getter;
    xamlMember->_setter = pMemberInfo->setter;
    xamlMember->TargetTypeName(pMemberInfo->targetTypeIndex >= 0 ? TypeInfos[pMemberInfo->targetTypeIndex].typeName : L"");
    xamlMember->IsReadOnly(pMemberInfo->isReadOnly);
    xamlMember->IsDependencyProperty(pMemberInfo->isDependencyProperty);
    xamlMember->IsAttachable(pMemberInfo->isAttachable);

    return xamlMember.as<IXamlMember>();
}
} // namespace
