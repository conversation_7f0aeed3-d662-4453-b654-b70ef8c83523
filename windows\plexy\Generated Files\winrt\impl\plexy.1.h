// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_plexy_1_H
#define WINRT_plexy_1_H
#include "winrt/impl/plexy.0.h"
WINRT_EXPORT namespace winrt::plexy
{
    struct WINRT_IMPL_EMPTY_BASES IMainPage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMainPage>
    {
        IMainPage(std::nullptr_t = nullptr) noexcept {}
        IMainPage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
