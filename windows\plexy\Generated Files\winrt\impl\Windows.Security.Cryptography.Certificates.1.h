// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Security_Cryptography_Certificates_1_H
#define WINRT_Windows_Security_Cryptography_Certificates_1_H
#include "winrt/impl/Windows.Security.Cryptography.Certificates.0.h"
WINRT_EXPORT namespace winrt::Windows::Security::Cryptography::Certificates
{
    struct WINRT_IMPL_EMPTY_BASES ICertificate :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificate>
    {
        ICertificate(std::nullptr_t = nullptr) noexcept {}
        ICertificate(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificate2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificate2>
    {
        ICertificate2(std::nullptr_t = nullptr) noexcept {}
        ICertificate2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificate3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificate3>
    {
        ICertificate3(std::nullptr_t = nullptr) noexcept {}
        ICertificate3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateChain :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateChain>
    {
        ICertificateChain(std::nullptr_t = nullptr) noexcept {}
        ICertificateChain(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateEnrollmentManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateEnrollmentManagerStatics>
    {
        ICertificateEnrollmentManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICertificateEnrollmentManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateEnrollmentManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateEnrollmentManagerStatics2>
    {
        ICertificateEnrollmentManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        ICertificateEnrollmentManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateEnrollmentManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateEnrollmentManagerStatics3>
    {
        ICertificateEnrollmentManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        ICertificateEnrollmentManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateExtension :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateExtension>
    {
        ICertificateExtension(std::nullptr_t = nullptr) noexcept {}
        ICertificateExtension(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateFactory>
    {
        ICertificateFactory(std::nullptr_t = nullptr) noexcept {}
        ICertificateFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateKeyUsages :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateKeyUsages>
    {
        ICertificateKeyUsages(std::nullptr_t = nullptr) noexcept {}
        ICertificateKeyUsages(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateQuery :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateQuery>
    {
        ICertificateQuery(std::nullptr_t = nullptr) noexcept {}
        ICertificateQuery(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateQuery2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateQuery2>
    {
        ICertificateQuery2(std::nullptr_t = nullptr) noexcept {}
        ICertificateQuery2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateRequestProperties :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateRequestProperties>
    {
        ICertificateRequestProperties(std::nullptr_t = nullptr) noexcept {}
        ICertificateRequestProperties(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateRequestProperties2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateRequestProperties2>
    {
        ICertificateRequestProperties2(std::nullptr_t = nullptr) noexcept {}
        ICertificateRequestProperties2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateRequestProperties3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateRequestProperties3>
    {
        ICertificateRequestProperties3(std::nullptr_t = nullptr) noexcept {}
        ICertificateRequestProperties3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateRequestProperties4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateRequestProperties4>
    {
        ICertificateRequestProperties4(std::nullptr_t = nullptr) noexcept {}
        ICertificateRequestProperties4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateStore :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateStore>
    {
        ICertificateStore(std::nullptr_t = nullptr) noexcept {}
        ICertificateStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateStore2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateStore2>
    {
        ICertificateStore2(std::nullptr_t = nullptr) noexcept {}
        ICertificateStore2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateStoresStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateStoresStatics>
    {
        ICertificateStoresStatics(std::nullptr_t = nullptr) noexcept {}
        ICertificateStoresStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICertificateStoresStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICertificateStoresStatics2>
    {
        ICertificateStoresStatics2(std::nullptr_t = nullptr) noexcept {}
        ICertificateStoresStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IChainBuildingParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChainBuildingParameters>
    {
        IChainBuildingParameters(std::nullptr_t = nullptr) noexcept {}
        IChainBuildingParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IChainValidationParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IChainValidationParameters>
    {
        IChainValidationParameters(std::nullptr_t = nullptr) noexcept {}
        IChainValidationParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsAttachedSignature :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsAttachedSignature>
    {
        ICmsAttachedSignature(std::nullptr_t = nullptr) noexcept {}
        ICmsAttachedSignature(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsAttachedSignatureFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsAttachedSignatureFactory>
    {
        ICmsAttachedSignatureFactory(std::nullptr_t = nullptr) noexcept {}
        ICmsAttachedSignatureFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsAttachedSignatureStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsAttachedSignatureStatics>
    {
        ICmsAttachedSignatureStatics(std::nullptr_t = nullptr) noexcept {}
        ICmsAttachedSignatureStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsDetachedSignature :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsDetachedSignature>
    {
        ICmsDetachedSignature(std::nullptr_t = nullptr) noexcept {}
        ICmsDetachedSignature(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsDetachedSignatureFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsDetachedSignatureFactory>
    {
        ICmsDetachedSignatureFactory(std::nullptr_t = nullptr) noexcept {}
        ICmsDetachedSignatureFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsDetachedSignatureStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsDetachedSignatureStatics>
    {
        ICmsDetachedSignatureStatics(std::nullptr_t = nullptr) noexcept {}
        ICmsDetachedSignatureStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsSignerInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsSignerInfo>
    {
        ICmsSignerInfo(std::nullptr_t = nullptr) noexcept {}
        ICmsSignerInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICmsTimestampInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICmsTimestampInfo>
    {
        ICmsTimestampInfo(std::nullptr_t = nullptr) noexcept {}
        ICmsTimestampInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyAlgorithmNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyAlgorithmNamesStatics>
    {
        IKeyAlgorithmNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyAlgorithmNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyAlgorithmNamesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyAlgorithmNamesStatics2>
    {
        IKeyAlgorithmNamesStatics2(std::nullptr_t = nullptr) noexcept {}
        IKeyAlgorithmNamesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyAttestationHelperStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyAttestationHelperStatics>
    {
        IKeyAttestationHelperStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyAttestationHelperStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyAttestationHelperStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyAttestationHelperStatics2>
    {
        IKeyAttestationHelperStatics2(std::nullptr_t = nullptr) noexcept {}
        IKeyAttestationHelperStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyStorageProviderNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyStorageProviderNamesStatics>
    {
        IKeyStorageProviderNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IKeyStorageProviderNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyStorageProviderNamesStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyStorageProviderNamesStatics2>
    {
        IKeyStorageProviderNamesStatics2(std::nullptr_t = nullptr) noexcept {}
        IKeyStorageProviderNamesStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPfxImportParameters :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPfxImportParameters>
    {
        IPfxImportParameters(std::nullptr_t = nullptr) noexcept {}
        IPfxImportParameters(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStandardCertificateStoreNamesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStandardCertificateStoreNamesStatics>
    {
        IStandardCertificateStoreNamesStatics(std::nullptr_t = nullptr) noexcept {}
        IStandardCertificateStoreNamesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISubjectAlternativeNameInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISubjectAlternativeNameInfo>
    {
        ISubjectAlternativeNameInfo(std::nullptr_t = nullptr) noexcept {}
        ISubjectAlternativeNameInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISubjectAlternativeNameInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISubjectAlternativeNameInfo2>
    {
        ISubjectAlternativeNameInfo2(std::nullptr_t = nullptr) noexcept {}
        ISubjectAlternativeNameInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUserCertificateEnrollmentManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserCertificateEnrollmentManager>
    {
        IUserCertificateEnrollmentManager(std::nullptr_t = nullptr) noexcept {}
        IUserCertificateEnrollmentManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUserCertificateEnrollmentManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserCertificateEnrollmentManager2>
    {
        IUserCertificateEnrollmentManager2(std::nullptr_t = nullptr) noexcept {}
        IUserCertificateEnrollmentManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IUserCertificateStore :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUserCertificateStore>
    {
        IUserCertificateStore(std::nullptr_t = nullptr) noexcept {}
        IUserCertificateStore(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
