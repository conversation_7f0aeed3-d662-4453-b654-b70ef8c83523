// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_AllJoyn_1_H
#define WINRT_Windows_Devices_AllJoyn_1_H
#include "winrt/impl/Windows.Devices.AllJoyn.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::AllJoyn
{
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAboutData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutData>
    {
        IAllJoynAboutData(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAboutDataView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutDataView>
    {
        IAllJoynAboutDataView(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutDataView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAboutDataViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutDataViewStatics>
    {
        IAllJoynAboutDataViewStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutDataViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAcceptSessionJoiner :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoiner>
    {
        IAllJoynAcceptSessionJoiner(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoiner(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAcceptSessionJoinerEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoinerEventArgs>
    {
        IAllJoynAcceptSessionJoinerEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoinerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAcceptSessionJoinerEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoinerEventArgsFactory>
    {
        IAllJoynAcceptSessionJoinerEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoinerEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynAuthenticationCompleteEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAuthenticationCompleteEventArgs>
    {
        IAllJoynAuthenticationCompleteEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAuthenticationCompleteEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusAttachment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachment>
    {
        IAllJoynBusAttachment(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusAttachment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachment2>
    {
        IAllJoynBusAttachment2(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusAttachmentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentFactory>
    {
        IAllJoynBusAttachmentFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusAttachmentStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentStateChangedEventArgs>
    {
        IAllJoynBusAttachmentStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusAttachmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentStatics>
    {
        IAllJoynBusAttachmentStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObject>
    {
        IAllJoynBusObject(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectFactory>
    {
        IAllJoynBusObjectFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusObjectStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectStoppedEventArgs>
    {
        IAllJoynBusObjectStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynBusObjectStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectStoppedEventArgsFactory>
    {
        IAllJoynBusObjectStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynCredentials :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentials>
    {
        IAllJoynCredentials(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentials(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynCredentialsRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentialsRequestedEventArgs>
    {
        IAllJoynCredentialsRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentialsRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynCredentialsVerificationRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentialsVerificationRequestedEventArgs>
    {
        IAllJoynCredentialsVerificationRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentialsVerificationRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynMessageInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynMessageInfo>
    {
        IAllJoynMessageInfo(std::nullptr_t = nullptr) noexcept {}
        IAllJoynMessageInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynMessageInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynMessageInfoFactory>
    {
        IAllJoynMessageInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynMessageInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynProducer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducer>
    {
        IAllJoynProducer(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynProducerStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducerStoppedEventArgs>
    {
        IAllJoynProducerStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducerStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynProducerStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducerStoppedEventArgsFactory>
    {
        IAllJoynProducerStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducerStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynServiceInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfo>
    {
        IAllJoynServiceInfo(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynServiceInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoFactory>
    {
        IAllJoynServiceInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynServiceInfoRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoRemovedEventArgs>
    {
        IAllJoynServiceInfoRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynServiceInfoRemovedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoRemovedEventArgsFactory>
    {
        IAllJoynServiceInfoRemovedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoRemovedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynServiceInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoStatics>
    {
        IAllJoynServiceInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSession>
    {
        IAllJoynSession(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionJoinedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionJoinedEventArgs>
    {
        IAllJoynSessionJoinedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionJoinedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionJoinedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionJoinedEventArgsFactory>
    {
        IAllJoynSessionJoinedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionJoinedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionLostEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionLostEventArgs>
    {
        IAllJoynSessionLostEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionLostEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionLostEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionLostEventArgsFactory>
    {
        IAllJoynSessionLostEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionLostEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionMemberAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberAddedEventArgs>
    {
        IAllJoynSessionMemberAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionMemberAddedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberAddedEventArgsFactory>
    {
        IAllJoynSessionMemberAddedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberAddedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionMemberRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberRemovedEventArgs>
    {
        IAllJoynSessionMemberRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionMemberRemovedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberRemovedEventArgsFactory>
    {
        IAllJoynSessionMemberRemovedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberRemovedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynSessionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionStatics>
    {
        IAllJoynSessionStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynStatusStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynStatusStatics>
    {
        IAllJoynStatusStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynStatusStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynWatcherStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynWatcherStoppedEventArgs>
    {
        IAllJoynWatcherStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynWatcherStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAllJoynWatcherStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynWatcherStoppedEventArgsFactory>
    {
        IAllJoynWatcherStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynWatcherStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
