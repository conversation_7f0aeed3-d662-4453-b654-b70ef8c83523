// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Automation_Peers_2_H
#define WINRT_Microsoft_UI_Xaml_Automation_Peers_2_H
#include "winrt/impl/Microsoft.UI.Xaml.Controls.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Controls.Primitives.1.h"
#include "winrt/impl/Windows.UI.Xaml.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Peers.1.h"
#include "winrt/impl/Windows.UI.Xaml.Automation.Provider.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Automation.Peers.1.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Automation::Peers
{
    struct WINRT_IMPL_EMPTY_BASES AnimatedVisualPlayerAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer,
        impl::base<AnimatedVisualPlayerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AnimatedVisualPlayerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AnimatedVisualPlayerAutomationPeer(std::nullptr_t) noexcept {}
        AnimatedVisualPlayerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit AnimatedVisualPlayerAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES BreadcrumbBarItemAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer,
        impl::base<BreadcrumbBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<BreadcrumbBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        BreadcrumbBarItemAutomationPeer(std::nullptr_t) noexcept {}
        BreadcrumbBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit BreadcrumbBarItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ColorPickerSliderAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer,
        impl::base<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ISliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorPickerSliderAutomationPeer(std::nullptr_t) noexcept {}
        ColorPickerSliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ColorPickerSliderAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner);
        using impl::consume_t<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ColorPickerSliderAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ColorSpectrumAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer,
        impl::base<ColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ColorSpectrumAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ColorSpectrumAutomationPeer(std::nullptr_t) noexcept {}
        ColorSpectrumAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ColorSpectrumAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES DropDownButtonAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer,
        impl::base<DropDownButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<DropDownButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IButtonBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        DropDownButtonAutomationPeer(std::nullptr_t) noexcept {}
        DropDownButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit DropDownButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::DropDownButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ExpanderAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer,
        impl::base<ExpanderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ExpanderAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ExpanderAutomationPeer(std::nullptr_t) noexcept {}
        ExpanderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ExpanderAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::Expander const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES InfoBarAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer,
        impl::base<InfoBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<InfoBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        InfoBarAutomationPeer(std::nullptr_t) noexcept {}
        InfoBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit InfoBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::InfoBar const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer,
        impl::base<MenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarAutomationPeer(std::nullptr_t) noexcept {}
        MenuBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::MenuBar const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES MenuBarItemAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer,
        impl::base<MenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<MenuBarItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        MenuBarItemAutomationPeer(std::nullptr_t) noexcept {}
        MenuBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit MenuBarItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer,
        impl::base<NavigationViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewAutomationPeer(std::nullptr_t) noexcept {}
        NavigationViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit NavigationViewAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NavigationView const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES NavigationViewItemAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer,
        impl::base<NavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NavigationViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NavigationViewItemAutomationPeer(std::nullptr_t) noexcept {}
        NavigationViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit NavigationViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES NumberBoxAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer,
        impl::base<NumberBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<NumberBoxAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        NumberBoxAutomationPeer(std::nullptr_t) noexcept {}
        NumberBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit NumberBoxAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::NumberBox const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PersonPictureAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer,
        impl::base<PersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PersonPictureAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PersonPictureAutomationPeer(std::nullptr_t) noexcept {}
        PersonPictureAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PersonPictureAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::PersonPicture const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES PipsPagerAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer,
        impl::base<PipsPagerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<PipsPagerAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        PipsPagerAutomationPeer(std::nullptr_t) noexcept {}
        PipsPagerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit PipsPagerAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::PipsPager const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressBarAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer,
        impl::base<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::RangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IRangeBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressBarAutomationPeer(std::nullptr_t) noexcept {}
        ProgressBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ProgressBarAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ProgressBar const& owner);
        using impl::consume_t<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ProgressBarAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES ProgressRingAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer,
        impl::base<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ProgressRingAutomationPeer(std::nullptr_t) noexcept {}
        ProgressRingAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ProgressRingAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ProgressRing const& owner);
        using impl::consume_t<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IRangeValueProvider>::SetValue;
        using impl::consume_t<ProgressRingAutomationPeer, winrt::Windows::UI::Xaml::IDependencyObject>::SetValue;
    };
    struct WINRT_IMPL_EMPTY_BASES RadioButtonsAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer,
        impl::base<RadioButtonsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RadioButtonsAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RadioButtonsAutomationPeer(std::nullptr_t) noexcept {}
        RadioButtonsAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RadioButtonsAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::RadioButtons const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RatingControlAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer,
        impl::base<RatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RatingControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RatingControlAutomationPeer(std::nullptr_t) noexcept {}
        RatingControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RatingControlAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::RatingControl const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES RepeaterAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer,
        impl::base<RepeaterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RepeaterAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RepeaterAutomationPeer(std::nullptr_t) noexcept {}
        RepeaterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit RepeaterAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES SplitButtonAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer,
        impl::base<SplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<SplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IInvokeProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        SplitButtonAutomationPeer(std::nullptr_t) noexcept {}
        SplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit SplitButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::SplitButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer,
        impl::base<TabViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabViewAutomationPeer(std::nullptr_t) noexcept {}
        TabViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TabViewAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TabView const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TabViewItemAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer,
        impl::base<TabViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TabViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TabViewItemAutomationPeer(std::nullptr_t) noexcept {}
        TabViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TabViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TabViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TeachingTipAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer,
        impl::base<TeachingTipAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TeachingTipAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TeachingTipAutomationPeer(std::nullptr_t) noexcept {}
        TeachingTipAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TeachingTipAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TeachingTip const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES ToggleSplitButtonAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer,
        impl::base<ToggleSplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<ToggleSplitButtonAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Provider::IToggleProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        ToggleSplitButtonAutomationPeer(std::nullptr_t) noexcept {}
        ToggleSplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit ToggleSplitButtonAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItemAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer,
        impl::base<TreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IListViewItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewItemAutomationPeer(std::nullptr_t) noexcept {}
        TreeViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TreeViewItemAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const& owner);
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewItemDataAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer,
        impl::base<TreeViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewItemDataAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IExpandCollapseProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IVirtualizedItemProvider, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewItemDataAutomationPeer(std::nullptr_t) noexcept {}
        TreeViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer(ptr, take_ownership_from_abi) {}
        TreeViewItemDataAutomationPeer(winrt::Windows::Foundation::IInspectable const& item, winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const& parent);
    };
    struct WINRT_IMPL_EMPTY_BASES TreeViewListAutomationPeer : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer,
        impl::base<TreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::SelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::ItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::FrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::AutomationPeer, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<TreeViewListAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IListViewBaseAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::IDropTargetProvider, winrt::Windows::UI::Xaml::Automation::Peers::ISelectorAutomationPeer, winrt::Windows::UI::Xaml::Automation::Provider::ISelectionProvider, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IItemsControlAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Provider::IItemContainerProvider, winrt::Windows::UI::Xaml::Automation::Peers::IFrameworkElementAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer7, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeer9, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerProtected, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides2, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides3, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides4, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides5, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides6, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides8, winrt::Windows::UI::Xaml::Automation::Peers::IAutomationPeerOverrides9, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        TreeViewListAutomationPeer(std::nullptr_t) noexcept {}
        TreeViewListAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer(ptr, take_ownership_from_abi) {}
        explicit TreeViewListAutomationPeer(winrt::Microsoft::UI::Xaml::Controls::TreeViewList const& owner);
    };
}
#endif
