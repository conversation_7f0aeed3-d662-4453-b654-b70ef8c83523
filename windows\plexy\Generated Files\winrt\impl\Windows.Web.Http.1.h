// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Web_Http_1_H
#define WINRT_Windows_Web_Http_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Web.Http.0.h"
WINRT_EXPORT namespace winrt::Windows::Web::Http
{
    struct WINRT_IMPL_EMPTY_BASES IHttpBufferContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpBufferContentFactory>
    {
        IHttpBufferContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpBufferContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpClient :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpClient>
    {
        IHttpClient(std::nullptr_t = nullptr) noexcept {}
        IHttpClient(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpClient2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpClient2>
    {
        IHttpClient2(std::nullptr_t = nullptr) noexcept {}
        IHttpClient2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpClient3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpClient3>
    {
        IHttpClient3(std::nullptr_t = nullptr) noexcept {}
        IHttpClient3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpClientFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpClientFactory>
    {
        IHttpClientFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpClientFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpContent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpContent>,
        impl::require<winrt::Windows::Web::Http::IHttpContent, winrt::Windows::Foundation::IClosable>
    {
        IHttpContent(std::nullptr_t = nullptr) noexcept {}
        IHttpContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookie :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookie>
    {
        IHttpCookie(std::nullptr_t = nullptr) noexcept {}
        IHttpCookie(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookieFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookieFactory>
    {
        IHttpCookieFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpCookieFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpCookieManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpCookieManager>
    {
        IHttpCookieManager(std::nullptr_t = nullptr) noexcept {}
        IHttpCookieManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpFormUrlEncodedContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpFormUrlEncodedContentFactory>
    {
        IHttpFormUrlEncodedContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpFormUrlEncodedContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpGetBufferResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpGetBufferResult>
    {
        IHttpGetBufferResult(std::nullptr_t = nullptr) noexcept {}
        IHttpGetBufferResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpGetInputStreamResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpGetInputStreamResult>
    {
        IHttpGetInputStreamResult(std::nullptr_t = nullptr) noexcept {}
        IHttpGetInputStreamResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpGetStringResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpGetStringResult>
    {
        IHttpGetStringResult(std::nullptr_t = nullptr) noexcept {}
        IHttpGetStringResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMethod :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMethod>
    {
        IHttpMethod(std::nullptr_t = nullptr) noexcept {}
        IHttpMethod(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMethodFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMethodFactory>
    {
        IHttpMethodFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMethodFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMethodStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMethodStatics>
    {
        IHttpMethodStatics(std::nullptr_t = nullptr) noexcept {}
        IHttpMethodStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMultipartContent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMultipartContent>
    {
        IHttpMultipartContent(std::nullptr_t = nullptr) noexcept {}
        IHttpMultipartContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMultipartContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMultipartContentFactory>
    {
        IHttpMultipartContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMultipartContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMultipartFormDataContent :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMultipartFormDataContent>
    {
        IHttpMultipartFormDataContent(std::nullptr_t = nullptr) noexcept {}
        IHttpMultipartFormDataContent(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpMultipartFormDataContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpMultipartFormDataContentFactory>
    {
        IHttpMultipartFormDataContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpMultipartFormDataContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpRequestMessage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpRequestMessage>
    {
        IHttpRequestMessage(std::nullptr_t = nullptr) noexcept {}
        IHttpRequestMessage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpRequestMessage2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpRequestMessage2>
    {
        IHttpRequestMessage2(std::nullptr_t = nullptr) noexcept {}
        IHttpRequestMessage2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpRequestMessageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpRequestMessageFactory>
    {
        IHttpRequestMessageFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpRequestMessageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpRequestResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpRequestResult>
    {
        IHttpRequestResult(std::nullptr_t = nullptr) noexcept {}
        IHttpRequestResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpResponseMessage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpResponseMessage>
    {
        IHttpResponseMessage(std::nullptr_t = nullptr) noexcept {}
        IHttpResponseMessage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpResponseMessageFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpResponseMessageFactory>
    {
        IHttpResponseMessageFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpResponseMessageFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpStreamContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpStreamContentFactory>
    {
        IHttpStreamContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpStreamContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpStringContentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpStringContentFactory>
    {
        IHttpStringContentFactory(std::nullptr_t = nullptr) noexcept {}
        IHttpStringContentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IHttpTransportInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IHttpTransportInformation>
    {
        IHttpTransportInformation(std::nullptr_t = nullptr) noexcept {}
        IHttpTransportInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
