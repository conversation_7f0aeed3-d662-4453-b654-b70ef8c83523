// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_UI_Composition_1_H
#define WINRT_Windows_UI_Composition_1_H
#include "winrt/impl/Windows.UI.Composition.0.h"
WINRT_EXPORT namespace winrt::Windows::UI::Composition
{
    struct WINRT_IMPL_EMPTY_BASES IAmbientLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAmbientLight>
    {
        IAmbientLight(std::nullptr_t = nullptr) noexcept {}
        IAmbientLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAmbientLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAmbientLight2>
    {
        IAmbientLight2(std::nullptr_t = nullptr) noexcept {}
        IAmbientLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimationController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationController>
    {
        IAnimationController(std::nullptr_t = nullptr) noexcept {}
        IAnimationController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimationControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationControllerStatics>
    {
        IAnimationControllerStatics(std::nullptr_t = nullptr) noexcept {}
        IAnimationControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimationObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationObject>
    {
        IAnimationObject(std::nullptr_t = nullptr) noexcept {}
        IAnimationObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimationPropertyInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationPropertyInfo>
    {
        IAnimationPropertyInfo(std::nullptr_t = nullptr) noexcept {}
        IAnimationPropertyInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimationPropertyInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimationPropertyInfo2>
    {
        IAnimationPropertyInfo2(std::nullptr_t = nullptr) noexcept {}
        IAnimationPropertyInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackEasingFunction>
    {
        IBackEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IBackEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBooleanKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBooleanKeyFrameAnimation>
    {
        IBooleanKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IBooleanKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceEasingFunction>
    {
        IBounceEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IBounceEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceScalarNaturalMotionAnimation>
    {
        IBounceScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceVector2NaturalMotionAnimation>
    {
        IBounceVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBounceVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBounceVector3NaturalMotionAnimation>
    {
        IBounceVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IBounceVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICircleEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICircleEasingFunction>
    {
        ICircleEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ICircleEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorKeyFrameAnimation>
    {
        IColorKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IColorKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation>
    {
        ICompositionAnimation(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation2>
    {
        ICompositionAnimation2(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation3>
    {
        ICompositionAnimation3(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimation4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimation4>
    {
        ICompositionAnimation4(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimation4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimationBase :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationBase>
    {
        ICompositionAnimationBase(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationBase(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationFactory>
    {
        ICompositionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionAnimationGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionAnimationGroup>
    {
        ICompositionAnimationGroup(std::nullptr_t = nullptr) noexcept {}
        ICompositionAnimationGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionBackdropBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBackdropBrush>
    {
        ICompositionBackdropBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionBackdropBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionBatchCompletedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBatchCompletedEventArgs>
    {
        ICompositionBatchCompletedEventArgs(std::nullptr_t = nullptr) noexcept {}
        ICompositionBatchCompletedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBrush>
    {
        ICompositionBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionBrushFactory>
    {
        ICompositionBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionCapabilities :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCapabilities>
    {
        ICompositionCapabilities(std::nullptr_t = nullptr) noexcept {}
        ICompositionCapabilities(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionCapabilitiesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCapabilitiesStatics>
    {
        ICompositionCapabilitiesStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionCapabilitiesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClip>
    {
        ICompositionClip(std::nullptr_t = nullptr) noexcept {}
        ICompositionClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionClip2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClip2>
    {
        ICompositionClip2(std::nullptr_t = nullptr) noexcept {}
        ICompositionClip2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionClipFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionClipFactory>
    {
        ICompositionClipFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionClipFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionColorBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorBrush>
    {
        ICompositionColorBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionColorGradientStop :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorGradientStop>
    {
        ICompositionColorGradientStop(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorGradientStop(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionColorGradientStopCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionColorGradientStopCollection>
    {
        ICompositionColorGradientStopCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionColorGradientStopCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionCommitBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionCommitBatch>
    {
        ICompositionCommitBatch(std::nullptr_t = nullptr) noexcept {}
        ICompositionCommitBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionContainerShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionContainerShape>
    {
        ICompositionContainerShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionContainerShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionDrawingSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurface>
    {
        ICompositionDrawingSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionDrawingSurface2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurface2>
    {
        ICompositionDrawingSurface2(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurface2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionDrawingSurfaceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionDrawingSurfaceFactory>
    {
        ICompositionDrawingSurfaceFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionDrawingSurfaceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEasingFunction>
    {
        ICompositionEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ICompositionEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEasingFunctionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEasingFunctionFactory>
    {
        ICompositionEasingFunctionFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEasingFunctionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEasingFunctionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEasingFunctionStatics>
    {
        ICompositionEasingFunctionStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionEasingFunctionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEffectBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectBrush>
    {
        ICompositionEffectBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEffectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectFactory>
    {
        ICompositionEffectFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEffectSourceParameter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectSourceParameter>
    {
        ICompositionEffectSourceParameter(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectSourceParameter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEffectSourceParameterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEffectSourceParameterFactory>
    {
        ICompositionEffectSourceParameterFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionEffectSourceParameterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionEllipseGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionEllipseGeometry>
    {
        ICompositionEllipseGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionEllipseGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGeometricClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometricClip>
    {
        ICompositionGeometricClip(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometricClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometry>
    {
        ICompositionGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGeometryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGeometryFactory>
    {
        ICompositionGeometryFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionGeometryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrush>
    {
        ICompositionGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGradientBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrush2>
    {
        ICompositionGradientBrush2(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGradientBrushFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGradientBrushFactory>
    {
        ICompositionGradientBrushFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionGradientBrushFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGraphicsDevice :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice>
    {
        ICompositionGraphicsDevice(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGraphicsDevice2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice2>
    {
        ICompositionGraphicsDevice2(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGraphicsDevice3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice3>
    {
        ICompositionGraphicsDevice3(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionGraphicsDevice4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionGraphicsDevice4>
    {
        ICompositionGraphicsDevice4(std::nullptr_t = nullptr) noexcept {}
        ICompositionGraphicsDevice4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight>
    {
        ICompositionLight(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight2>
    {
        ICompositionLight2(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLight3>
    {
        ICompositionLight3(std::nullptr_t = nullptr) noexcept {}
        ICompositionLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLightFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLightFactory>
    {
        ICompositionLightFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionLightFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLineGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLineGeometry>
    {
        ICompositionLineGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionLineGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionLinearGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionLinearGradientBrush>
    {
        ICompositionLinearGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionLinearGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionMaskBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionMaskBrush>
    {
        ICompositionMaskBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionMaskBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionMipmapSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionMipmapSurface>
    {
        ICompositionMipmapSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionMipmapSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionNineGridBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionNineGridBrush>
    {
        ICompositionNineGridBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionNineGridBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject>
    {
        ICompositionObject(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObject2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject2>
    {
        ICompositionObject2(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObject3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject3>
    {
        ICompositionObject3(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObject4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject4>
    {
        ICompositionObject4(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObject5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObject5>
    {
        ICompositionObject5(std::nullptr_t = nullptr) noexcept {}
        ICompositionObject5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObjectFactory>
    {
        ICompositionObjectFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionObjectStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionObjectStatics>
    {
        ICompositionObjectStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionObjectStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionPath :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPath>
    {
        ICompositionPath(std::nullptr_t = nullptr) noexcept {}
        ICompositionPath(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionPathFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPathFactory>
    {
        ICompositionPathFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionPathFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionPathGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPathGeometry>
    {
        ICompositionPathGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionPathGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadow>
    {
        ICompositionProjectedShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadowCaster :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCaster>
    {
        ICompositionProjectedShadowCaster(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCaster(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadowCasterCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCasterCollection>
    {
        ICompositionProjectedShadowCasterCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCasterCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadowCasterCollectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowCasterCollectionStatics>
    {
        ICompositionProjectedShadowCasterCollectionStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowCasterCollectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadowReceiver :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowReceiver>
    {
        ICompositionProjectedShadowReceiver(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowReceiver(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionProjectedShadowReceiverUnorderedCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionProjectedShadowReceiverUnorderedCollection>
    {
        ICompositionProjectedShadowReceiverUnorderedCollection(std::nullptr_t = nullptr) noexcept {}
        ICompositionProjectedShadowReceiverUnorderedCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionPropertySet :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPropertySet>
    {
        ICompositionPropertySet(std::nullptr_t = nullptr) noexcept {}
        ICompositionPropertySet(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionPropertySet2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionPropertySet2>
    {
        ICompositionPropertySet2(std::nullptr_t = nullptr) noexcept {}
        ICompositionPropertySet2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionRadialGradientBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRadialGradientBrush>
    {
        ICompositionRadialGradientBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionRadialGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionRectangleGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRectangleGeometry>
    {
        ICompositionRectangleGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionRectangleGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionRoundedRectangleGeometry :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionRoundedRectangleGeometry>
    {
        ICompositionRoundedRectangleGeometry(std::nullptr_t = nullptr) noexcept {}
        ICompositionRoundedRectangleGeometry(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionScopedBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionScopedBatch>
    {
        ICompositionScopedBatch(std::nullptr_t = nullptr) noexcept {}
        ICompositionScopedBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShadow>
    {
        ICompositionShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositionShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionShadowFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShadowFactory>
    {
        ICompositionShadowFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionShadowFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShape>
    {
        ICompositionShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionShapeFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionShapeFactory>
    {
        ICompositionShapeFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionShapeFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSpriteShape :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSpriteShape>
    {
        ICompositionSpriteShape(std::nullptr_t = nullptr) noexcept {}
        ICompositionSpriteShape(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSupportsSystemBackdrop :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSupportsSystemBackdrop>
    {
        ICompositionSupportsSystemBackdrop(std::nullptr_t = nullptr) noexcept {}
        ICompositionSupportsSystemBackdrop(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurface>
    {
        ICompositionSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSurfaceBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush>
    {
        ICompositionSurfaceBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSurfaceBrush2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush2>
    {
        ICompositionSurfaceBrush2(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSurfaceBrush3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceBrush3>
    {
        ICompositionSurfaceBrush3(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceBrush3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionSurfaceFacade :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionSurfaceFacade>
    {
        ICompositionSurfaceFacade(std::nullptr_t = nullptr) noexcept {}
        ICompositionSurfaceFacade(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTarget :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTarget>
    {
        ICompositionTarget(std::nullptr_t = nullptr) noexcept {}
        ICompositionTarget(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTargetFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTargetFactory>
    {
        ICompositionTargetFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionTargetFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTexture :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTexture>
    {
        ICompositionTexture(std::nullptr_t = nullptr) noexcept {}
        ICompositionTexture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTextureFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTextureFactory>
    {
        ICompositionTextureFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionTextureFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTransform :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTransform>
    {
        ICompositionTransform(std::nullptr_t = nullptr) noexcept {}
        ICompositionTransform(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionTransformFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionTransformFactory>
    {
        ICompositionTransformFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionTransformFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionViewBox :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionViewBox>
    {
        ICompositionViewBox(std::nullptr_t = nullptr) noexcept {}
        ICompositionViewBox(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionVirtualDrawingSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVirtualDrawingSurface>
    {
        ICompositionVirtualDrawingSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionVirtualDrawingSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionVirtualDrawingSurfaceFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVirtualDrawingSurfaceFactory>
    {
        ICompositionVirtualDrawingSurfaceFactory(std::nullptr_t = nullptr) noexcept {}
        ICompositionVirtualDrawingSurfaceFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositionVisualSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositionVisualSurface>
    {
        ICompositionVisualSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositionVisualSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor>
    {
        ICompositor(std::nullptr_t = nullptr) noexcept {}
        ICompositor(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor2>
    {
        ICompositor2(std::nullptr_t = nullptr) noexcept {}
        ICompositor2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor3>
    {
        ICompositor3(std::nullptr_t = nullptr) noexcept {}
        ICompositor3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor4>
    {
        ICompositor4(std::nullptr_t = nullptr) noexcept {}
        ICompositor4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor5>
    {
        ICompositor5(std::nullptr_t = nullptr) noexcept {}
        ICompositor5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor6 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor6>
    {
        ICompositor6(std::nullptr_t = nullptr) noexcept {}
        ICompositor6(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor7 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor7>
    {
        ICompositor7(std::nullptr_t = nullptr) noexcept {}
        ICompositor7(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositor8 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositor8>
    {
        ICompositor8(std::nullptr_t = nullptr) noexcept {}
        ICompositor8(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositorStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorStatics>
    {
        ICompositorStatics(std::nullptr_t = nullptr) noexcept {}
        ICompositorStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositorWithBlurredWallpaperBackdropBrush :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithBlurredWallpaperBackdropBrush>
    {
        ICompositorWithBlurredWallpaperBackdropBrush(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithBlurredWallpaperBackdropBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositorWithProjectedShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithProjectedShadow>
    {
        ICompositorWithProjectedShadow(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithProjectedShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositorWithRadialGradient :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithRadialGradient>
    {
        ICompositorWithRadialGradient(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithRadialGradient(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICompositorWithVisualSurface :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICompositorWithVisualSurface>
    {
        ICompositorWithVisualSurface(std::nullptr_t = nullptr) noexcept {}
        ICompositorWithVisualSurface(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContainerVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContainerVisual>
    {
        IContainerVisual(std::nullptr_t = nullptr) noexcept {}
        IContainerVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IContainerVisualFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IContainerVisualFactory>
    {
        IContainerVisualFactory(std::nullptr_t = nullptr) noexcept {}
        IContainerVisualFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ICubicBezierEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICubicBezierEasingFunction>
    {
        ICubicBezierEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ICubicBezierEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDelegatedInkTrailVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDelegatedInkTrailVisual>
    {
        IDelegatedInkTrailVisual(std::nullptr_t = nullptr) noexcept {}
        IDelegatedInkTrailVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDelegatedInkTrailVisualStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDelegatedInkTrailVisualStatics>
    {
        IDelegatedInkTrailVisualStatics(std::nullptr_t = nullptr) noexcept {}
        IDelegatedInkTrailVisualStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDistantLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDistantLight>
    {
        IDistantLight(std::nullptr_t = nullptr) noexcept {}
        IDistantLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDistantLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDistantLight2>
    {
        IDistantLight2(std::nullptr_t = nullptr) noexcept {}
        IDistantLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropShadow :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropShadow>
    {
        IDropShadow(std::nullptr_t = nullptr) noexcept {}
        IDropShadow(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropShadow2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropShadow2>
    {
        IDropShadow2(std::nullptr_t = nullptr) noexcept {}
        IDropShadow2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IElasticEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IElasticEasingFunction>
    {
        IElasticEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IElasticEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExponentialEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExponentialEasingFunction>
    {
        IExponentialEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IExponentialEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExpressionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpressionAnimation>
    {
        IExpressionAnimation(std::nullptr_t = nullptr) noexcept {}
        IExpressionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IImplicitAnimationCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IImplicitAnimationCollection>
    {
        IImplicitAnimationCollection(std::nullptr_t = nullptr) noexcept {}
        IImplicitAnimationCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInsetClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInsetClip>
    {
        IInsetClip(std::nullptr_t = nullptr) noexcept {}
        IInsetClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation>
    {
        IKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyFrameAnimation2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation2>
    {
        IKeyFrameAnimation2(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyFrameAnimation3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimation3>
    {
        IKeyFrameAnimation3(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimation3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IKeyFrameAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKeyFrameAnimationFactory>
    {
        IKeyFrameAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IKeyFrameAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILayerVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayerVisual>
    {
        ILayerVisual(std::nullptr_t = nullptr) noexcept {}
        ILayerVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILayerVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILayerVisual2>
    {
        ILayerVisual2(std::nullptr_t = nullptr) noexcept {}
        ILayerVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ILinearEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILinearEasingFunction>
    {
        ILinearEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ILinearEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INaturalMotionAnimation>
    {
        INaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        INaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INaturalMotionAnimationFactory>
    {
        INaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        INaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPathKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPathKeyFrameAnimation>
    {
        IPathKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IPathKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight>
    {
        IPointLight(std::nullptr_t = nullptr) noexcept {}
        IPointLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight2>
    {
        IPointLight2(std::nullptr_t = nullptr) noexcept {}
        IPointLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPointLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPointLight3>
    {
        IPointLight3(std::nullptr_t = nullptr) noexcept {}
        IPointLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPowerEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerEasingFunction>
    {
        IPowerEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IPowerEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IQuaternionKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IQuaternionKeyFrameAnimation>
    {
        IQuaternionKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IQuaternionKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRectangleClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRectangleClip>
    {
        IRectangleClip(std::nullptr_t = nullptr) noexcept {}
        IRectangleClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRedirectVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRedirectVisual>
    {
        IRedirectVisual(std::nullptr_t = nullptr) noexcept {}
        IRedirectVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRenderingDeviceReplacedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRenderingDeviceReplacedEventArgs>
    {
        IRenderingDeviceReplacedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IRenderingDeviceReplacedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScalarKeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarKeyFrameAnimation>
    {
        IScalarKeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IScalarKeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarNaturalMotionAnimation>
    {
        IScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IScalarNaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IScalarNaturalMotionAnimationFactory>
    {
        IScalarNaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IScalarNaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IShapeVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IShapeVisual>
    {
        IShapeVisual(std::nullptr_t = nullptr) noexcept {}
        IShapeVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISineEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISineEasingFunction>
    {
        ISineEasingFunction(std::nullptr_t = nullptr) noexcept {}
        ISineEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpotLight :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight>
    {
        ISpotLight(std::nullptr_t = nullptr) noexcept {}
        ISpotLight(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpotLight2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight2>
    {
        ISpotLight2(std::nullptr_t = nullptr) noexcept {}
        ISpotLight2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpotLight3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpotLight3>
    {
        ISpotLight3(std::nullptr_t = nullptr) noexcept {}
        ISpotLight3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpringScalarNaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringScalarNaturalMotionAnimation>
    {
        ISpringScalarNaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringScalarNaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpringVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringVector2NaturalMotionAnimation>
    {
        ISpringVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpringVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpringVector3NaturalMotionAnimation>
    {
        ISpringVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        ISpringVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpriteVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpriteVisual>
    {
        ISpriteVisual(std::nullptr_t = nullptr) noexcept {}
        ISpriteVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISpriteVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISpriteVisual2>
    {
        ISpriteVisual2(std::nullptr_t = nullptr) noexcept {}
        ISpriteVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IStepEasingFunction :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStepEasingFunction>
    {
        IStepEasingFunction(std::nullptr_t = nullptr) noexcept {}
        IStepEasingFunction(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector2KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2KeyFrameAnimation>
    {
        IVector2KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector2KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector2NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2NaturalMotionAnimation>
    {
        IVector2NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector2NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector2NaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector2NaturalMotionAnimationFactory>
    {
        IVector2NaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IVector2NaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector3KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3KeyFrameAnimation>
    {
        IVector3KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector3KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector3NaturalMotionAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3NaturalMotionAnimation>
    {
        IVector3NaturalMotionAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector3NaturalMotionAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector3NaturalMotionAnimationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector3NaturalMotionAnimationFactory>
    {
        IVector3NaturalMotionAnimationFactory(std::nullptr_t = nullptr) noexcept {}
        IVector3NaturalMotionAnimationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVector4KeyFrameAnimation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVector4KeyFrameAnimation>
    {
        IVector4KeyFrameAnimation(std::nullptr_t = nullptr) noexcept {}
        IVector4KeyFrameAnimation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisual :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual>
    {
        IVisual(std::nullptr_t = nullptr) noexcept {}
        IVisual(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisual2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual2>
    {
        IVisual2(std::nullptr_t = nullptr) noexcept {}
        IVisual2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisual3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual3>
    {
        IVisual3(std::nullptr_t = nullptr) noexcept {}
        IVisual3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisual4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisual4>
    {
        IVisual4(std::nullptr_t = nullptr) noexcept {}
        IVisual4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualCollection>
    {
        IVisualCollection(std::nullptr_t = nullptr) noexcept {}
        IVisualCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualElement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualElement>
    {
        IVisualElement(std::nullptr_t = nullptr) noexcept {}
        IVisualElement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualElement2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualElement2>
    {
        IVisualElement2(std::nullptr_t = nullptr) noexcept {}
        IVisualElement2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualFactory>
    {
        IVisualFactory(std::nullptr_t = nullptr) noexcept {}
        IVisualFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IVisualUnorderedCollection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IVisualUnorderedCollection>
    {
        IVisualUnorderedCollection(std::nullptr_t = nullptr) noexcept {}
        IVisualUnorderedCollection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
