// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Media_Capture_Core_2_H
#define WINRT_Windows_Media_Capture_Core_2_H
#include "winrt/impl/Windows.Media.Capture.Core.1.h"
WINRT_EXPORT namespace winrt::Windows::Media::Capture::Core
{
    struct WINRT_IMPL_EMPTY_BASES VariablePhotoCapturedEventArgs : winrt::Windows::Media::Capture::Core::IVariablePhotoCapturedEventArgs
    {
        VariablePhotoCapturedEventArgs(std::nullptr_t) noexcept {}
        VariablePhotoCapturedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Capture::Core::IVariablePhotoCapturedEventArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES VariablePhotoSequenceCapture : winrt::Windows::Media::Capture::Core::IVariablePhotoSequenceCapture,
        impl::require<VariablePhotoSequenceCapture, winrt::Windows::Media::Capture::Core::IVariablePhotoSequenceCapture2>
    {
        VariablePhotoSequenceCapture(std::nullptr_t) noexcept {}
        VariablePhotoSequenceCapture(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Media::Capture::Core::IVariablePhotoSequenceCapture(ptr, take_ownership_from_abi) {}
    };
}
#endif
