// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Perception_Provider_1_H
#define WINRT_Windows_Devices_Perception_Provider_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Devices.Perception.Provider.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Perception::Provider
{
    struct WINRT_IMPL_EMPTY_BASES IKnownPerceptionFrameKindStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IKnownPerceptionFrameKindStatics>
    {
        IKnownPerceptionFrameKindStatics(std::nullptr_t = nullptr) noexcept {}
        IKnownPerceptionFrameKindStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionControlGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionControlGroup>
    {
        IPerceptionControlGroup(std::nullptr_t = nullptr) noexcept {}
        IPerceptionControlGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionControlGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionControlGroupFactory>
    {
        IPerceptionControlGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPerceptionControlGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionCorrelation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionCorrelation>
    {
        IPerceptionCorrelation(std::nullptr_t = nullptr) noexcept {}
        IPerceptionCorrelation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionCorrelationFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionCorrelationFactory>
    {
        IPerceptionCorrelationFactory(std::nullptr_t = nullptr) noexcept {}
        IPerceptionCorrelationFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionCorrelationGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionCorrelationGroup>
    {
        IPerceptionCorrelationGroup(std::nullptr_t = nullptr) noexcept {}
        IPerceptionCorrelationGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionCorrelationGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionCorrelationGroupFactory>
    {
        IPerceptionCorrelationGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPerceptionCorrelationGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFaceAuthenticationGroup :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFaceAuthenticationGroup>
    {
        IPerceptionFaceAuthenticationGroup(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFaceAuthenticationGroup(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFaceAuthenticationGroupFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFaceAuthenticationGroupFactory>
    {
        IPerceptionFaceAuthenticationGroupFactory(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFaceAuthenticationGroupFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrame :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrame>
    {
        IPerceptionFrame(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrame(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameProvider>,
        impl::require<winrt::Windows::Devices::Perception::Provider::IPerceptionFrameProvider, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionFrameProvider(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameProviderInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameProviderInfo>
    {
        IPerceptionFrameProviderInfo(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameProviderInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameProviderManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameProviderManager>,
        impl::require<winrt::Windows::Devices::Perception::Provider::IPerceptionFrameProviderManager, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionFrameProviderManager(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameProviderManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionFrameProviderManagerServiceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionFrameProviderManagerServiceStatics>
    {
        IPerceptionFrameProviderManagerServiceStatics(std::nullptr_t = nullptr) noexcept {}
        IPerceptionFrameProviderManagerServiceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionPropertyChangeRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionPropertyChangeRequest>
    {
        IPerceptionPropertyChangeRequest(std::nullptr_t = nullptr) noexcept {}
        IPerceptionPropertyChangeRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionVideoFrameAllocator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionVideoFrameAllocator>,
        impl::require<winrt::Windows::Devices::Perception::Provider::IPerceptionVideoFrameAllocator, winrt::Windows::Foundation::IClosable>
    {
        IPerceptionVideoFrameAllocator(std::nullptr_t = nullptr) noexcept {}
        IPerceptionVideoFrameAllocator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPerceptionVideoFrameAllocatorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPerceptionVideoFrameAllocatorFactory>
    {
        IPerceptionVideoFrameAllocatorFactory(std::nullptr_t = nullptr) noexcept {}
        IPerceptionVideoFrameAllocatorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
