// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Geolocation_Provider_2_H
#define WINRT_Windows_Devices_Geolocation_Provider_2_H
#include "winrt/impl/Windows.Devices.Geolocation.Provider.1.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Geolocation::Provider
{
    struct WINRT_IMPL_EMPTY_BASES GeolocationProvider : winrt::Windows::Devices::Geolocation::Provider::IGeolocationProvider
    {
        GeolocationProvider(std::nullptr_t) noexcept {}
        GeolocationProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Devices::Geolocation::Provider::IGeolocationProvider(ptr, take_ownership_from_abi) {}
        GeolocationProvider();
    };
}
#endif
