// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Devices_Sms_0_H
#define WINRT_Windows_Devices_Sms_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct EventRegistrationToken;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IBuffer;
}
WINRT_EXPORT namespace winrt::Windows::Devices::Sms
{
    enum class CellularClass : int32_t
    {
        None = 0,
        Gsm = 1,
        Cdma = 2,
    };
    enum class SmsBroadcastType : int32_t
    {
        Other = 0,
        CmasPresidential = 1,
        CmasExtreme = 2,
        CmasSevere = 3,
        CmasAmber = 4,
        CmasTest = 5,
        EUAlert1 = 6,
        EUAlert2 = 7,
        EUAlert3 = 8,
        EUAlertAmber = 9,
        EUAlertInfo = 10,
        EtwsEarthquake = 11,
        EtwsTsunami = 12,
        EtwsTsunamiAndEarthquake = 13,
        LatAlertLocal = 14,
    };
    enum class SmsDataFormat : int32_t
    {
        Unknown = 0,
        CdmaSubmit = 1,
        GsmSubmit = 2,
        CdmaDeliver = 3,
        GsmDeliver = 4,
    };
    enum class SmsDeviceStatus : int32_t
    {
        Off = 0,
        Ready = 1,
        SimNotInserted = 2,
        BadSim = 3,
        DeviceFailure = 4,
        SubscriptionNotActivated = 5,
        DeviceLocked = 6,
        DeviceBlocked = 7,
    };
    enum class SmsEncoding : int32_t
    {
        Unknown = 0,
        Optimal = 1,
        SevenBitAscii = 2,
        Unicode = 3,
        GsmSevenBit = 4,
        EightBit = 5,
        Latin = 6,
        Korean = 7,
        IA5 = 8,
        ShiftJis = 9,
        LatinHebrew = 10,
    };
    enum class SmsFilterActionType : int32_t
    {
        AcceptImmediately = 0,
        Drop = 1,
        Peek = 2,
        Accept = 3,
    };
    enum class SmsGeographicalScope : int32_t
    {
        None = 0,
        CellWithImmediateDisplay = 1,
        LocationArea = 2,
        Plmn = 3,
        Cell = 4,
    };
    enum class SmsMessageClass : int32_t
    {
        None = 0,
        Class0 = 1,
        Class1 = 2,
        Class2 = 3,
        Class3 = 4,
    };
    enum class SmsMessageType : int32_t
    {
        Binary = 0,
        Text = 1,
        Wap = 2,
        App = 3,
        Broadcast = 4,
        Voicemail = 5,
        Status = 6,
    };
    enum class SmsModemErrorCode : int32_t
    {
        Other = 0,
        MessagingNetworkError = 1,
        SmsOperationNotSupportedByDevice = 2,
        SmsServiceNotSupportedByNetwork = 3,
        DeviceFailure = 4,
        MessageNotEncodedProperly = 5,
        MessageTooLarge = 6,
        DeviceNotReady = 7,
        NetworkNotReady = 8,
        InvalidSmscAddress = 9,
        NetworkFailure = 10,
        FixedDialingNumberRestricted = 11,
    };
    struct ISmsAppMessage;
    struct ISmsBroadcastMessage;
    struct ISmsDevice2;
    struct ISmsDevice2Statics;
    struct ISmsFilterRule;
    struct ISmsFilterRuleFactory;
    struct ISmsFilterRules;
    struct ISmsFilterRulesFactory;
    struct ISmsMessageBase;
    struct ISmsMessageReceivedTriggerDetails;
    struct ISmsMessageRegistration;
    struct ISmsMessageRegistrationStatics;
    struct ISmsSendMessageResult;
    struct ISmsStatusMessage;
    struct ISmsTextMessage2;
    struct ISmsVoicemailMessage;
    struct ISmsWapMessage;
    struct SmsAppMessage;
    struct SmsBroadcastMessage;
    struct SmsDevice2;
    struct SmsFilterRule;
    struct SmsFilterRules;
    struct SmsMessageReceivedTriggerDetails;
    struct SmsMessageRegistration;
    struct SmsSendMessageResult;
    struct SmsStatusMessage;
    struct SmsTextMessage2;
    struct SmsVoicemailMessage;
    struct SmsWapMessage;
    struct SmsEncodedLength;
}
namespace winrt::impl
{
    template <> struct category<winrt::Windows::Devices::Sms::ISmsAppMessage>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsBroadcastMessage>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsDevice2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsDevice2Statics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsFilterRule>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsFilterRuleFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsFilterRules>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsFilterRulesFactory>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsMessageBase>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsMessageRegistration>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsMessageRegistrationStatics>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsSendMessageResult>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsStatusMessage>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsTextMessage2>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsVoicemailMessage>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::ISmsWapMessage>{ using type = interface_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsAppMessage>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsBroadcastMessage>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsDevice2>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsFilterRule>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsFilterRules>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsMessageReceivedTriggerDetails>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsMessageRegistration>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsSendMessageResult>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsStatusMessage>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsTextMessage2>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsVoicemailMessage>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsWapMessage>{ using type = class_category; };
    template <> struct category<winrt::Windows::Devices::Sms::CellularClass>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsBroadcastType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsDataFormat>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsDeviceStatus>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsEncoding>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsFilterActionType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsGeographicalScope>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsMessageClass>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsMessageType>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsModemErrorCode>{ using type = enum_category; };
    template <> struct category<winrt::Windows::Devices::Sms::SmsEncodedLength>{ using type = struct_category<uint32_t, uint32_t, uint32_t, uint32_t, uint32_t>; };
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsAppMessage> = L"Windows.Devices.Sms.SmsAppMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsBroadcastMessage> = L"Windows.Devices.Sms.SmsBroadcastMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsDevice2> = L"Windows.Devices.Sms.SmsDevice2";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsFilterRule> = L"Windows.Devices.Sms.SmsFilterRule";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsFilterRules> = L"Windows.Devices.Sms.SmsFilterRules";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsMessageReceivedTriggerDetails> = L"Windows.Devices.Sms.SmsMessageReceivedTriggerDetails";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsMessageRegistration> = L"Windows.Devices.Sms.SmsMessageRegistration";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsSendMessageResult> = L"Windows.Devices.Sms.SmsSendMessageResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsStatusMessage> = L"Windows.Devices.Sms.SmsStatusMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsTextMessage2> = L"Windows.Devices.Sms.SmsTextMessage2";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsVoicemailMessage> = L"Windows.Devices.Sms.SmsVoicemailMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsWapMessage> = L"Windows.Devices.Sms.SmsWapMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::CellularClass> = L"Windows.Devices.Sms.CellularClass";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsBroadcastType> = L"Windows.Devices.Sms.SmsBroadcastType";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsDataFormat> = L"Windows.Devices.Sms.SmsDataFormat";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsDeviceStatus> = L"Windows.Devices.Sms.SmsDeviceStatus";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsEncoding> = L"Windows.Devices.Sms.SmsEncoding";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsFilterActionType> = L"Windows.Devices.Sms.SmsFilterActionType";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsGeographicalScope> = L"Windows.Devices.Sms.SmsGeographicalScope";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsMessageClass> = L"Windows.Devices.Sms.SmsMessageClass";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsMessageType> = L"Windows.Devices.Sms.SmsMessageType";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsModemErrorCode> = L"Windows.Devices.Sms.SmsModemErrorCode";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::SmsEncodedLength> = L"Windows.Devices.Sms.SmsEncodedLength";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsAppMessage> = L"Windows.Devices.Sms.ISmsAppMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsBroadcastMessage> = L"Windows.Devices.Sms.ISmsBroadcastMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsDevice2> = L"Windows.Devices.Sms.ISmsDevice2";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsDevice2Statics> = L"Windows.Devices.Sms.ISmsDevice2Statics";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsFilterRule> = L"Windows.Devices.Sms.ISmsFilterRule";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsFilterRuleFactory> = L"Windows.Devices.Sms.ISmsFilterRuleFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsFilterRules> = L"Windows.Devices.Sms.ISmsFilterRules";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsFilterRulesFactory> = L"Windows.Devices.Sms.ISmsFilterRulesFactory";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsMessageBase> = L"Windows.Devices.Sms.ISmsMessageBase";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails> = L"Windows.Devices.Sms.ISmsMessageReceivedTriggerDetails";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsMessageRegistration> = L"Windows.Devices.Sms.ISmsMessageRegistration";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsMessageRegistrationStatics> = L"Windows.Devices.Sms.ISmsMessageRegistrationStatics";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsSendMessageResult> = L"Windows.Devices.Sms.ISmsSendMessageResult";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsStatusMessage> = L"Windows.Devices.Sms.ISmsStatusMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsTextMessage2> = L"Windows.Devices.Sms.ISmsTextMessage2";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsVoicemailMessage> = L"Windows.Devices.Sms.ISmsVoicemailMessage";
    template <> inline constexpr auto& name_v<winrt::Windows::Devices::Sms::ISmsWapMessage> = L"Windows.Devices.Sms.ISmsWapMessage";
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsAppMessage>{ 0xE8BB8494,0xD3A0,0x4A0A,{ 0x86,0xD7,0x29,0x10,0x33,0xA8,0xCF,0x54 } }; // E8BB8494-D3A0-4A0A-86D7-291033A8CF54
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsBroadcastMessage>{ 0x75AEBBF1,0xE4B7,0x4874,{ 0xA0,0x9C,0x29,0x56,0xE5,0x92,0xF9,0x57 } }; // 75AEBBF1-E4B7-4874-A09C-2956E592F957
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsDevice2>{ 0xBD8A5C13,0xE522,0x46CB,{ 0xB8,0xD5,0x9E,0xAD,0x30,0xFB,0x6C,0x47 } }; // BD8A5C13-E522-46CB-B8D5-9EAD30FB6C47
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsDevice2Statics>{ 0x65C78325,0x1031,0x491E,{ 0x8F,0xB6,0xEF,0x99,0x91,0xAF,0xE3,0x63 } }; // 65C78325-1031-491E-8FB6-EF9991AFE363
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsFilterRule>{ 0x40E32FAE,0xB049,0x4FBC,{ 0xAF,0xE9,0xE2,0xA6,0x10,0xEF,0xF5,0x5C } }; // 40E32FAE-B049-4FBC-AFE9-E2A610EFF55C
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsFilterRuleFactory>{ 0x00C36508,0x6296,0x4F29,{ 0x9A,0xAD,0x89,0x20,0xCE,0xBA,0x3C,0xE8 } }; // 00C36508-6296-4F29-9AAD-8920CEBA3CE8
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsFilterRules>{ 0x4E47EAFB,0x79CD,0x4881,{ 0x98,0x94,0x55,0xA4,0x13,0x5B,0x23,0xFA } }; // 4E47EAFB-79CD-4881-9894-55A4135B23FA
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsFilterRulesFactory>{ 0xA09924ED,0x6E2E,0x4530,{ 0x9F,0xDE,0x46,0x5D,0x02,0xEE,0xD0,0x0E } }; // A09924ED-6E2E-4530-9FDE-465D02EED00E
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsMessageBase>{ 0x2CF0FE30,0xFE50,0x4FC6,{ 0xAA,0x88,0x4C,0xCF,0xE2,0x7A,0x29,0xEA } }; // 2CF0FE30-FE50-4FC6-AA88-4CCFE27A29EA
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails>{ 0x2BCFCBD4,0x2657,0x4128,{ 0xAD,0x5F,0xE3,0x87,0x71,0x32,0xBD,0xB1 } }; // 2BCFCBD4-2657-4128-AD5F-E3877132BDB1
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsMessageRegistration>{ 0x1720503E,0xF34F,0x446B,{ 0x83,0xB3,0x0F,0xF1,0x99,0x23,0xB4,0x09 } }; // 1720503E-F34F-446B-83B3-0FF19923B409
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsMessageRegistrationStatics>{ 0x63A05464,0x2898,0x4778,{ 0xA0,0x3C,0x6F,0x99,0x49,0x07,0xD6,0x3A } }; // 63A05464-2898-4778-A03C-6F994907D63A
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsSendMessageResult>{ 0xDB139AF2,0x78C9,0x4FEB,{ 0x96,0x22,0x45,0x23,0x28,0x08,0x8D,0x62 } }; // DB139AF2-78C9-4FEB-9622-452328088D62
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsStatusMessage>{ 0xE6D28342,0xB70B,0x4677,{ 0x93,0x79,0xC9,0x78,0x3F,0xDF,0xF8,0xF4 } }; // E6D28342-B70B-4677-9379-C9783FDFF8F4
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsTextMessage2>{ 0x22A0D893,0x4555,0x4755,{ 0xB5,0xA1,0xE7,0xFD,0x84,0x95,0x5F,0x8D } }; // 22A0D893-4555-4755-B5A1-E7FD84955F8D
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsVoicemailMessage>{ 0x271AA0A6,0x95B1,0x44FF,{ 0xBC,0xB8,0xB8,0xFD,0xD7,0xE0,0x8B,0xC3 } }; // 271AA0A6-95B1-44FF-BCB8-B8FDD7E08BC3
    template <> inline constexpr guid guid_v<winrt::Windows::Devices::Sms::ISmsWapMessage>{ 0xCD937743,0x7A55,0x4D3B,{ 0x90,0x21,0xF2,0x2E,0x02,0x2D,0x09,0xC5 } }; // CD937743-7A55-4D3B-9021-F22E022D09C5
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsAppMessage>{ using type = winrt::Windows::Devices::Sms::ISmsAppMessage; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsBroadcastMessage>{ using type = winrt::Windows::Devices::Sms::ISmsBroadcastMessage; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsDevice2>{ using type = winrt::Windows::Devices::Sms::ISmsDevice2; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsFilterRule>{ using type = winrt::Windows::Devices::Sms::ISmsFilterRule; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsFilterRules>{ using type = winrt::Windows::Devices::Sms::ISmsFilterRules; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsMessageReceivedTriggerDetails>{ using type = winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsMessageRegistration>{ using type = winrt::Windows::Devices::Sms::ISmsMessageRegistration; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsSendMessageResult>{ using type = winrt::Windows::Devices::Sms::ISmsSendMessageResult; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsStatusMessage>{ using type = winrt::Windows::Devices::Sms::ISmsStatusMessage; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsTextMessage2>{ using type = winrt::Windows::Devices::Sms::ISmsTextMessage2; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsVoicemailMessage>{ using type = winrt::Windows::Devices::Sms::ISmsVoicemailMessage; };
    template <> struct default_interface<winrt::Windows::Devices::Sms::SmsWapMessage>{ using type = winrt::Windows::Devices::Sms::ISmsWapMessage; };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsAppMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Timestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall put_To(void*) noexcept = 0;
            virtual int32_t __stdcall get_From(void**) noexcept = 0;
            virtual int32_t __stdcall get_Body(void**) noexcept = 0;
            virtual int32_t __stdcall put_Body(void*) noexcept = 0;
            virtual int32_t __stdcall get_CallbackNumber(void**) noexcept = 0;
            virtual int32_t __stdcall put_CallbackNumber(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsDeliveryNotificationEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsDeliveryNotificationEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_RetryAttemptCount(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_RetryAttemptCount(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Encoding(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Encoding(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_PortNumber(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PortNumber(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_TeleserviceId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TeleserviceId(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ProtocolId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ProtocolId(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_BinaryBody(void**) noexcept = 0;
            virtual int32_t __stdcall put_BinaryBody(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsBroadcastMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Timestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall get_Body(void**) noexcept = 0;
            virtual int32_t __stdcall get_Channel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_GeographicalScope(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_MessageCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_UpdateNumber(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_BroadcastType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsEmergencyAlert(bool*) noexcept = 0;
            virtual int32_t __stdcall get_IsUserPopupRequested(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsDevice2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SmscAddress(void**) noexcept = 0;
            virtual int32_t __stdcall put_SmscAddress(void*) noexcept = 0;
            virtual int32_t __stdcall get_DeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_ParentDeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_AccountPhoneNumber(void**) noexcept = 0;
            virtual int32_t __stdcall get_CellularClass(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_DeviceStatus(int32_t*) noexcept = 0;
            virtual int32_t __stdcall CalculateLength(void*, struct struct_Windows_Devices_Sms_SmsEncodedLength*) noexcept = 0;
            virtual int32_t __stdcall SendMessageAndGetResultAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall add_DeviceStatusChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DeviceStatusChanged(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsDevice2Statics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDeviceSelector(void**) noexcept = 0;
            virtual int32_t __stdcall FromId(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
            virtual int32_t __stdcall FromParentId(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsFilterRule>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MessageType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ImsiPrefixes(void**) noexcept = 0;
            virtual int32_t __stdcall get_DeviceIds(void**) noexcept = 0;
            virtual int32_t __stdcall get_SenderNumbers(void**) noexcept = 0;
            virtual int32_t __stdcall get_TextMessagePrefixes(void**) noexcept = 0;
            virtual int32_t __stdcall get_PortNumbers(void**) noexcept = 0;
            virtual int32_t __stdcall get_CellularClass(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_CellularClass(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_ProtocolIds(void**) noexcept = 0;
            virtual int32_t __stdcall get_TeleserviceIds(void**) noexcept = 0;
            virtual int32_t __stdcall get_WapApplicationIds(void**) noexcept = 0;
            virtual int32_t __stdcall get_WapContentTypes(void**) noexcept = 0;
            virtual int32_t __stdcall get_BroadcastTypes(void**) noexcept = 0;
            virtual int32_t __stdcall get_BroadcastChannels(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsFilterRuleFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateFilterRule(int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsFilterRules>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ActionType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Rules(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsFilterRulesFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateFilterRules(int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsMessageBase>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MessageType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_DeviceId(void**) noexcept = 0;
            virtual int32_t __stdcall get_CellularClass(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_MessageClass(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_SimIccId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MessageType(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_TextMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_WapMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_AppMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_BroadcastMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_VoicemailMessage(void**) noexcept = 0;
            virtual int32_t __stdcall get_StatusMessage(void**) noexcept = 0;
            virtual int32_t __stdcall Drop() noexcept = 0;
            virtual int32_t __stdcall Accept() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsMessageRegistration>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall Unregister() noexcept = 0;
            virtual int32_t __stdcall add_MessageReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_MessageReceived(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsMessageRegistrationStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AllRegistrations(void**) noexcept = 0;
            virtual int32_t __stdcall Register(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsSendMessageResult>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSuccessful(bool*) noexcept = 0;
            virtual int32_t __stdcall get_MessageReferenceNumbers(void**) noexcept = 0;
            virtual int32_t __stdcall get_CellularClass(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ModemErrorCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsErrorTransient(bool*) noexcept = 0;
            virtual int32_t __stdcall get_NetworkCauseCode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_TransportFailureCause(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsStatusMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall get_From(void**) noexcept = 0;
            virtual int32_t __stdcall get_Body(void**) noexcept = 0;
            virtual int32_t __stdcall get_Status(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_MessageReferenceNumber(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ServiceCenterTimestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_DischargeTime(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsTextMessage2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Timestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall put_To(void*) noexcept = 0;
            virtual int32_t __stdcall get_From(void**) noexcept = 0;
            virtual int32_t __stdcall get_Body(void**) noexcept = 0;
            virtual int32_t __stdcall put_Body(void*) noexcept = 0;
            virtual int32_t __stdcall get_Encoding(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Encoding(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_CallbackNumber(void**) noexcept = 0;
            virtual int32_t __stdcall put_CallbackNumber(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsDeliveryNotificationEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsDeliveryNotificationEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_RetryAttemptCount(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_RetryAttemptCount(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_TeleserviceId(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_ProtocolId(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsVoicemailMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Timestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall get_Body(void**) noexcept = 0;
            virtual int32_t __stdcall get_MessageCount(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Windows::Devices::Sms::ISmsWapMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Timestamp(int64_t*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall get_From(void**) noexcept = 0;
            virtual int32_t __stdcall get_ApplicationId(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContentType(void**) noexcept = 0;
            virtual int32_t __stdcall get_BinaryBody(void**) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsAppMessage
    {
        [[nodiscard]] auto Timestamp() const;
        [[nodiscard]] auto To() const;
        auto To(param::hstring const& value) const;
        [[nodiscard]] auto From() const;
        [[nodiscard]] auto Body() const;
        auto Body(param::hstring const& value) const;
        [[nodiscard]] auto CallbackNumber() const;
        auto CallbackNumber(param::hstring const& value) const;
        [[nodiscard]] auto IsDeliveryNotificationEnabled() const;
        auto IsDeliveryNotificationEnabled(bool value) const;
        [[nodiscard]] auto RetryAttemptCount() const;
        auto RetryAttemptCount(int32_t value) const;
        [[nodiscard]] auto Encoding() const;
        auto Encoding(winrt::Windows::Devices::Sms::SmsEncoding const& value) const;
        [[nodiscard]] auto PortNumber() const;
        auto PortNumber(int32_t value) const;
        [[nodiscard]] auto TeleserviceId() const;
        auto TeleserviceId(int32_t value) const;
        [[nodiscard]] auto ProtocolId() const;
        auto ProtocolId(int32_t value) const;
        [[nodiscard]] auto BinaryBody() const;
        auto BinaryBody(winrt::Windows::Storage::Streams::IBuffer const& value) const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsAppMessage>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsAppMessage<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsBroadcastMessage
    {
        [[nodiscard]] auto Timestamp() const;
        [[nodiscard]] auto To() const;
        [[nodiscard]] auto Body() const;
        [[nodiscard]] auto Channel() const;
        [[nodiscard]] auto GeographicalScope() const;
        [[nodiscard]] auto MessageCode() const;
        [[nodiscard]] auto UpdateNumber() const;
        [[nodiscard]] auto BroadcastType() const;
        [[nodiscard]] auto IsEmergencyAlert() const;
        [[nodiscard]] auto IsUserPopupRequested() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsBroadcastMessage>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsBroadcastMessage<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsDevice2
    {
        [[nodiscard]] auto SmscAddress() const;
        auto SmscAddress(param::hstring const& value) const;
        [[nodiscard]] auto DeviceId() const;
        [[nodiscard]] auto ParentDeviceId() const;
        [[nodiscard]] auto AccountPhoneNumber() const;
        [[nodiscard]] auto CellularClass() const;
        [[nodiscard]] auto DeviceStatus() const;
        auto CalculateLength(winrt::Windows::Devices::Sms::ISmsMessageBase const& message) const;
        auto SendMessageAndGetResultAsync(winrt::Windows::Devices::Sms::ISmsMessageBase const& message) const;
        auto DeviceStatusChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Devices::Sms::SmsDevice2, winrt::Windows::Foundation::IInspectable> const& eventHandler) const;
        using DeviceStatusChanged_revoker = impl::event_revoker<winrt::Windows::Devices::Sms::ISmsDevice2, &impl::abi_t<winrt::Windows::Devices::Sms::ISmsDevice2>::remove_DeviceStatusChanged>;
        [[nodiscard]] auto DeviceStatusChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Devices::Sms::SmsDevice2, winrt::Windows::Foundation::IInspectable> const& eventHandler) const;
        auto DeviceStatusChanged(winrt::event_token const& eventCookie) const noexcept;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsDevice2>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsDevice2<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsDevice2Statics
    {
        auto GetDeviceSelector() const;
        auto FromId(param::hstring const& deviceId) const;
        auto GetDefault() const;
        auto FromParentId(param::hstring const& parentDeviceId) const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsDevice2Statics>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsDevice2Statics<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsFilterRule
    {
        [[nodiscard]] auto MessageType() const;
        [[nodiscard]] auto ImsiPrefixes() const;
        [[nodiscard]] auto DeviceIds() const;
        [[nodiscard]] auto SenderNumbers() const;
        [[nodiscard]] auto TextMessagePrefixes() const;
        [[nodiscard]] auto PortNumbers() const;
        [[nodiscard]] auto CellularClass() const;
        auto CellularClass(winrt::Windows::Devices::Sms::CellularClass const& value) const;
        [[nodiscard]] auto ProtocolIds() const;
        [[nodiscard]] auto TeleserviceIds() const;
        [[nodiscard]] auto WapApplicationIds() const;
        [[nodiscard]] auto WapContentTypes() const;
        [[nodiscard]] auto BroadcastTypes() const;
        [[nodiscard]] auto BroadcastChannels() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsFilterRule>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsFilterRule<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsFilterRuleFactory
    {
        auto CreateFilterRule(winrt::Windows::Devices::Sms::SmsMessageType const& messageType) const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsFilterRuleFactory>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsFilterRuleFactory<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsFilterRules
    {
        [[nodiscard]] auto ActionType() const;
        [[nodiscard]] auto Rules() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsFilterRules>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsFilterRules<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsFilterRulesFactory
    {
        auto CreateFilterRules(winrt::Windows::Devices::Sms::SmsFilterActionType const& actionType) const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsFilterRulesFactory>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsFilterRulesFactory<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsMessageBase
    {
        [[nodiscard]] auto MessageType() const;
        [[nodiscard]] auto DeviceId() const;
        [[nodiscard]] auto CellularClass() const;
        [[nodiscard]] auto MessageClass() const;
        [[nodiscard]] auto SimIccId() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsMessageBase>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsMessageBase<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsMessageReceivedTriggerDetails
    {
        [[nodiscard]] auto MessageType() const;
        [[nodiscard]] auto TextMessage() const;
        [[nodiscard]] auto WapMessage() const;
        [[nodiscard]] auto AppMessage() const;
        [[nodiscard]] auto BroadcastMessage() const;
        [[nodiscard]] auto VoicemailMessage() const;
        [[nodiscard]] auto StatusMessage() const;
        auto Drop() const;
        auto Accept() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsMessageReceivedTriggerDetails>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsMessageReceivedTriggerDetails<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsMessageRegistration
    {
        [[nodiscard]] auto Id() const;
        auto Unregister() const;
        auto MessageReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Devices::Sms::SmsMessageRegistration, winrt::Windows::Devices::Sms::SmsMessageReceivedTriggerDetails> const& eventHandler) const;
        using MessageReceived_revoker = impl::event_revoker<winrt::Windows::Devices::Sms::ISmsMessageRegistration, &impl::abi_t<winrt::Windows::Devices::Sms::ISmsMessageRegistration>::remove_MessageReceived>;
        [[nodiscard]] auto MessageReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Devices::Sms::SmsMessageRegistration, winrt::Windows::Devices::Sms::SmsMessageReceivedTriggerDetails> const& eventHandler) const;
        auto MessageReceived(winrt::event_token const& eventCookie) const noexcept;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsMessageRegistration>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsMessageRegistration<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsMessageRegistrationStatics
    {
        [[nodiscard]] auto AllRegistrations() const;
        auto Register(param::hstring const& id, winrt::Windows::Devices::Sms::SmsFilterRules const& filterRules) const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsMessageRegistrationStatics>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsMessageRegistrationStatics<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsSendMessageResult
    {
        [[nodiscard]] auto IsSuccessful() const;
        [[nodiscard]] auto MessageReferenceNumbers() const;
        [[nodiscard]] auto CellularClass() const;
        [[nodiscard]] auto ModemErrorCode() const;
        [[nodiscard]] auto IsErrorTransient() const;
        [[nodiscard]] auto NetworkCauseCode() const;
        [[nodiscard]] auto TransportFailureCause() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsSendMessageResult>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsSendMessageResult<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsStatusMessage
    {
        [[nodiscard]] auto To() const;
        [[nodiscard]] auto From() const;
        [[nodiscard]] auto Body() const;
        [[nodiscard]] auto Status() const;
        [[nodiscard]] auto MessageReferenceNumber() const;
        [[nodiscard]] auto ServiceCenterTimestamp() const;
        [[nodiscard]] auto DischargeTime() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsStatusMessage>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsStatusMessage<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsTextMessage2
    {
        [[nodiscard]] auto Timestamp() const;
        [[nodiscard]] auto To() const;
        auto To(param::hstring const& value) const;
        [[nodiscard]] auto From() const;
        [[nodiscard]] auto Body() const;
        auto Body(param::hstring const& value) const;
        [[nodiscard]] auto Encoding() const;
        auto Encoding(winrt::Windows::Devices::Sms::SmsEncoding const& value) const;
        [[nodiscard]] auto CallbackNumber() const;
        auto CallbackNumber(param::hstring const& value) const;
        [[nodiscard]] auto IsDeliveryNotificationEnabled() const;
        auto IsDeliveryNotificationEnabled(bool value) const;
        [[nodiscard]] auto RetryAttemptCount() const;
        auto RetryAttemptCount(int32_t value) const;
        [[nodiscard]] auto TeleserviceId() const;
        [[nodiscard]] auto ProtocolId() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsTextMessage2>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsTextMessage2<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsVoicemailMessage
    {
        [[nodiscard]] auto Timestamp() const;
        [[nodiscard]] auto To() const;
        [[nodiscard]] auto Body() const;
        [[nodiscard]] auto MessageCount() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsVoicemailMessage>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsVoicemailMessage<D>;
    };
    template <typename D>
    struct consume_Windows_Devices_Sms_ISmsWapMessage
    {
        [[nodiscard]] auto Timestamp() const;
        [[nodiscard]] auto To() const;
        [[nodiscard]] auto From() const;
        [[nodiscard]] auto ApplicationId() const;
        [[nodiscard]] auto ContentType() const;
        [[nodiscard]] auto BinaryBody() const;
        [[nodiscard]] auto Headers() const;
    };
    template <> struct consume<winrt::Windows::Devices::Sms::ISmsWapMessage>
    {
        template <typename D> using type = consume_Windows_Devices_Sms_ISmsWapMessage<D>;
    };
    struct struct_Windows_Devices_Sms_SmsEncodedLength
    {
        uint32_t SegmentCount;
        uint32_t CharacterCountLastSegment;
        uint32_t CharactersPerSegment;
        uint32_t ByteCountLastSegment;
        uint32_t BytesPerSegment;
    };
    template <> struct abi<Windows::Devices::Sms::SmsEncodedLength>
    {
        using type = struct_Windows_Devices_Sms_SmsEncodedLength;
    };
}
#endif
