// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Media_2_H
#define WINRT_Microsoft_UI_Xaml_Media_2_H
#include "winrt/impl/Windows.UI.Composition.1.h"
#include "winrt/impl/Windows.UI.Xaml.1.h"
#include "winrt/impl/Windows.UI.Xaml.Media.1.h"
#include "winrt/impl/Microsoft.UI.Xaml.Media.1.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media
{
    struct WINRT_IMPL_EMPTY_BASES AcrylicBrush : winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush,
        impl::base<AcrylicBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<AcrylicBrush, winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush2, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        AcrylicBrush(std::nullptr_t) noexcept {}
        AcrylicBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Media::IAcrylicBrush(ptr, take_ownership_from_abi) {}
        AcrylicBrush();
        [[nodiscard]] static auto BackgroundSourceProperty();
        [[nodiscard]] static auto TintColorProperty();
        [[nodiscard]] static auto TintOpacityProperty();
        [[nodiscard]] static auto TintTransitionDurationProperty();
        [[nodiscard]] static auto AlwaysUseFallbackProperty();
        [[nodiscard]] static auto TintLuminosityOpacityProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RadialGradientBrush : winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush,
        impl::base<RadialGradientBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RadialGradientBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RadialGradientBrush(std::nullptr_t) noexcept {}
        RadialGradientBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Media::IRadialGradientBrush(ptr, take_ownership_from_abi) {}
        RadialGradientBrush();
        [[nodiscard]] static auto CenterProperty();
        [[nodiscard]] static auto RadiusXProperty();
        [[nodiscard]] static auto RadiusYProperty();
        [[nodiscard]] static auto GradientOriginProperty();
        [[nodiscard]] static auto InterpolationSpaceProperty();
        [[nodiscard]] static auto MappingModeProperty();
        [[nodiscard]] static auto SpreadMethodProperty();
    };
    struct WINRT_IMPL_EMPTY_BASES RevealBackgroundBrush : winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush,
        impl::base<RevealBackgroundBrush, winrt::Microsoft::UI::Xaml::Media::RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RevealBackgroundBrush, winrt::Microsoft::UI::Xaml::Media::IRevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RevealBackgroundBrush(std::nullptr_t) noexcept {}
        RevealBackgroundBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Media::IRevealBackgroundBrush(ptr, take_ownership_from_abi) {}
        RevealBackgroundBrush();
    };
    struct WINRT_IMPL_EMPTY_BASES RevealBorderBrush : winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush,
        impl::base<RevealBorderBrush, winrt::Microsoft::UI::Xaml::Media::RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RevealBorderBrush, winrt::Microsoft::UI::Xaml::Media::IRevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RevealBorderBrush(std::nullptr_t) noexcept {}
        RevealBorderBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Media::IRevealBorderBrush(ptr, take_ownership_from_abi) {}
        RevealBorderBrush();
    };
    struct WINRT_IMPL_EMPTY_BASES RevealBrush : winrt::Microsoft::UI::Xaml::Media::IRevealBrush,
        impl::base<RevealBrush, winrt::Windows::UI::Xaml::Media::XamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::Brush, winrt::Windows::UI::Xaml::DependencyObject>,
        impl::require<RevealBrush, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBase, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseProtected, winrt::Windows::UI::Xaml::Media::IXamlCompositionBrushBaseOverrides, winrt::Windows::UI::Xaml::Media::IBrush, winrt::Windows::UI::Xaml::Media::IBrushOverrides2, winrt::Windows::UI::Composition::IAnimationObject, winrt::Windows::UI::Xaml::IDependencyObject, winrt::Windows::UI::Xaml::IDependencyObject2>
    {
        RevealBrush(std::nullptr_t) noexcept {}
        RevealBrush(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::UI::Xaml::Media::IRevealBrush(ptr, take_ownership_from_abi) {}
        [[nodiscard]] static auto ColorProperty();
        [[nodiscard]] static auto TargetThemeProperty();
        [[nodiscard]] static auto AlwaysUseFallbackProperty();
        [[nodiscard]] static auto StateProperty();
        static auto SetState(winrt::Windows::UI::Xaml::UIElement const& element, winrt::Microsoft::UI::Xaml::Media::RevealBrushState const& value);
        static auto GetState(winrt::Windows::UI::Xaml::UIElement const& element);
    };
}
#endif
