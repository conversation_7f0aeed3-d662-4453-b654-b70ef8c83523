// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Automation_Peers_0_H
#define WINRT_Microsoft_UI_Xaml_Automation_Peers_0_H
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls
{
    struct AnimatedVisualPlayer;
    struct BreadcrumbBarItem;
    struct DropDownButton;
    struct Expander;
    struct InfoBar;
    struct ItemsRepeater;
    struct MenuBar;
    struct MenuBarItem;
    struct NavigationView;
    struct NavigationViewItem;
    struct NumberBox;
    struct PersonPicture;
    struct PipsPager;
    struct ProgressBar;
    struct ProgressRing;
    struct RadioButtons;
    struct RatingControl;
    struct SplitButton;
    struct TabView;
    struct TabViewItem;
    struct TeachingTip;
    struct ToggleSplitButton;
    struct TreeViewItem;
    struct TreeViewList;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    struct ColorPickerSlider;
    struct ColorSpectrum;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Automation::Peers
{
    struct IAnimatedVisualPlayerAutomationPeer;
    struct IAnimatedVisualPlayerAutomationPeerFactory;
    struct IBreadcrumbBarItemAutomationPeer;
    struct IBreadcrumbBarItemAutomationPeerFactory;
    struct IColorPickerSliderAutomationPeer;
    struct IColorPickerSliderAutomationPeerFactory;
    struct IColorSpectrumAutomationPeer;
    struct IColorSpectrumAutomationPeerFactory;
    struct IDropDownButtonAutomationPeer;
    struct IDropDownButtonAutomationPeerFactory;
    struct IExpanderAutomationPeer;
    struct IExpanderAutomationPeerFactory;
    struct IInfoBarAutomationPeer;
    struct IInfoBarAutomationPeerFactory;
    struct IMenuBarAutomationPeer;
    struct IMenuBarAutomationPeerFactory;
    struct IMenuBarItemAutomationPeer;
    struct IMenuBarItemAutomationPeerFactory;
    struct INavigationViewAutomationPeer;
    struct INavigationViewAutomationPeerFactory;
    struct INavigationViewItemAutomationPeer;
    struct INavigationViewItemAutomationPeerFactory;
    struct INumberBoxAutomationPeer;
    struct INumberBoxAutomationPeerFactory;
    struct IPersonPictureAutomationPeer;
    struct IPersonPictureAutomationPeerFactory;
    struct IPipsPagerAutomationPeer;
    struct IPipsPagerAutomationPeerFactory;
    struct IProgressBarAutomationPeer;
    struct IProgressBarAutomationPeerFactory;
    struct IProgressRingAutomationPeer;
    struct IProgressRingAutomationPeerFactory;
    struct IRadioButtonsAutomationPeer;
    struct IRadioButtonsAutomationPeerFactory;
    struct IRatingControlAutomationPeer;
    struct IRatingControlAutomationPeerFactory;
    struct IRepeaterAutomationPeer;
    struct IRepeaterAutomationPeerFactory;
    struct ISplitButtonAutomationPeer;
    struct ISplitButtonAutomationPeerFactory;
    struct ITabViewAutomationPeer;
    struct ITabViewAutomationPeerFactory;
    struct ITabViewItemAutomationPeer;
    struct ITabViewItemAutomationPeerFactory;
    struct ITeachingTipAutomationPeer;
    struct ITeachingTipAutomationPeerFactory;
    struct IToggleSplitButtonAutomationPeer;
    struct IToggleSplitButtonAutomationPeerFactory;
    struct ITreeViewItemAutomationPeer;
    struct ITreeViewItemAutomationPeerFactory;
    struct ITreeViewItemDataAutomationPeer;
    struct ITreeViewItemDataAutomationPeerFactory;
    struct ITreeViewListAutomationPeer;
    struct ITreeViewListAutomationPeerFactory;
    struct AnimatedVisualPlayerAutomationPeer;
    struct BreadcrumbBarItemAutomationPeer;
    struct ColorPickerSliderAutomationPeer;
    struct ColorSpectrumAutomationPeer;
    struct DropDownButtonAutomationPeer;
    struct ExpanderAutomationPeer;
    struct InfoBarAutomationPeer;
    struct MenuBarAutomationPeer;
    struct MenuBarItemAutomationPeer;
    struct NavigationViewAutomationPeer;
    struct NavigationViewItemAutomationPeer;
    struct NumberBoxAutomationPeer;
    struct PersonPictureAutomationPeer;
    struct PipsPagerAutomationPeer;
    struct ProgressBarAutomationPeer;
    struct ProgressRingAutomationPeer;
    struct RadioButtonsAutomationPeer;
    struct RatingControlAutomationPeer;
    struct RepeaterAutomationPeer;
    struct SplitButtonAutomationPeer;
    struct TabViewAutomationPeer;
    struct TabViewItemAutomationPeer;
    struct TeachingTipAutomationPeer;
    struct ToggleSplitButtonAutomationPeer;
    struct TreeViewItemAutomationPeer;
    struct TreeViewItemDataAutomationPeer;
    struct TreeViewListAutomationPeer;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.NavigationViewAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IAnimatedVisualPlayerAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IAnimatedVisualPlayerAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IBreadcrumbBarItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IBreadcrumbBarItemAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IColorPickerSliderAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IColorPickerSliderAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IColorSpectrumAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IColorSpectrumAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IDropDownButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IDropDownButtonAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IExpanderAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IExpanderAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IInfoBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IInfoBarAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IMenuBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IMenuBarAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IMenuBarItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IMenuBarItemAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.INavigationViewAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.INavigationViewAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.INavigationViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.INavigationViewItemAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.INumberBoxAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.INumberBoxAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IPersonPictureAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IPersonPictureAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IPipsPagerAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IPipsPagerAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IProgressBarAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IProgressBarAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IProgressRingAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IProgressRingAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IRadioButtonsAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IRadioButtonsAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IRatingControlAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IRatingControlAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IRepeaterAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IRepeaterAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ISplitButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ISplitButtonAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITabViewAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITabViewAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITabViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITabViewItemAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITeachingTipAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITeachingTipAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.IToggleSplitButtonAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.IToggleSplitButtonAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewItemAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewItemAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewItemDataAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewItemDataAutomationPeerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewListAutomationPeer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory> = L"Microsoft.UI.Xaml.Automation.Peers.ITreeViewListAutomationPeerFactory";
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer>{ 0xF949EEB6,0xB3EA,0x58AD,{ 0xB6,0x2B,0xB7,0x25,0x5B,0xCC,0x04,0xDF } }; // F949EEB6-B3EA-58AD-B62B-B7255BCC04DF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory>{ 0xD2A49198,0x80BB,0x51D6,{ 0xB4,0x95,0x3D,0xC5,0xAA,0xB5,0x95,0x89 } }; // D2A49198-80BB-51D6-B495-3DC5AAB59589
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer>{ 0x48E81612,0x7DE0,0x5065,{ 0xB8,0x81,0x04,0xEB,0xFF,0xF9,0x04,0x97 } }; // 48E81612-7DE0-5065-B881-04EBFFF90497
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory>{ 0xDFB02146,0x405F,0x52ED,{ 0xA8,0x73,0x0E,0xD4,0x94,0x28,0x50,0xBE } }; // DFB02146-405F-52ED-A873-0ED4942850BE
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer>{ 0x793D35D4,0x4152,0x50FA,{ 0xB5,0xF4,0xF6,0xC0,0x45,0xC1,0x33,0x9D } }; // 793D35D4-4152-50FA-B5F4-F6C045C1339D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory>{ 0xE5F9093F,0x5E2C,0x5148,{ 0xB5,0xD1,0x1C,0xDA,0x4E,0xB8,0x69,0x13 } }; // E5F9093F-5E2C-5148-B5D1-1CDA4EB86913
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer>{ 0x005AC3D1,0xB031,0x58AB,{ 0x91,0x8D,0x03,0x0F,0xAB,0xAE,0xAF,0x87 } }; // 005AC3D1-B031-58AB-918D-030FABAEAF87
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory>{ 0xCFCCAE7E,0xFE0F,0x5C9C,{ 0x9D,0x1A,0x69,0xE2,0x0E,0x02,0x32,0xCF } }; // CFCCAE7E-FE0F-5C9C-9D1A-69E20E0232CF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer>{ 0x7DC37DEC,0x0A0A,0x5C98,{ 0x8A,0x6F,0x9E,0x47,0xDB,0xAB,0x2F,0x82 } }; // 7DC37DEC-0A0A-5C98-8A6F-9E47DBAB2F82
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory>{ 0x68C4BFFA,0x1685,0x5936,{ 0xB2,0x19,0x51,0x7E,0x87,0xFD,0x59,0x1F } }; // 68C4BFFA-1685-5936-B219-517E87FD591F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer>{ 0xF7527408,0xCC89,0x5B65,{ 0xBB,0xDE,0xEA,0xE6,0xD6,0x6D,0xC3,0xE5 } }; // F7527408-CC89-5B65-BBDE-EAE6D66DC3E5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory>{ 0x2024523B,0x4A40,0x5976,{ 0xAA,0xAB,0x0F,0x05,0x66,0x4F,0x74,0x94 } }; // 2024523B-4A40-5976-AAAB-0F05664F7494
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer>{ 0xAA2C40EB,0xDF80,0x5050,{ 0x92,0xC5,0x5F,0xDA,0x5A,0xBF,0xDE,0xF2 } }; // AA2C40EB-DF80-5050-92C5-5FDA5ABFDEF2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory>{ 0x5FD3E590,0x68B9,0x5C9C,{ 0xA5,0x72,0x0B,0xC1,0x01,0x67,0xCE,0x46 } }; // 5FD3E590-68B9-5C9C-A572-0BC10167CE46
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer>{ 0xAE96E710,0xB9D3,0x59DD,{ 0x97,0x3E,0x1B,0xBC,0x86,0xCF,0x0A,0xFC } }; // AE96E710-B9D3-59DD-973E-1BBC86CF0AFC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory>{ 0xD3E1CA3F,0x1702,0x5BD3,{ 0x8A,0xDB,0xE6,0xF6,0xCB,0x9E,0x75,0x31 } }; // D3E1CA3F-1702-5BD3-8ADB-E6F6CB9E7531
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer>{ 0x2DA890CD,0x0AAE,0x53B9,{ 0x8C,0x12,0x81,0x00,0x3F,0x60,0xED,0x14 } }; // 2DA890CD-0AAE-53B9-8C12-81003F60ED14
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory>{ 0x603B63C4,0xA626,0x50E6,{ 0x9C,0x1A,0x64,0x96,0x99,0xCA,0x7A,0xD6 } }; // 603B63C4-A626-50E6-9C1A-649699CA7AD6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer>{ 0x72013EAE,0xB015,0x550D,{ 0xBA,0x8D,0xA0,0x51,0x12,0xB6,0x27,0x31 } }; // 72013EAE-B015-550D-BA8D-A05112B62731
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory>{ 0x75075B03,0xA2F7,0x5869,{ 0xB2,0x3C,0x63,0xCB,0xE5,0xAC,0xC4,0x3A } }; // 75075B03-A2F7-5869-B23C-63CBE5ACC43A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer>{ 0xC7924C7A,0x739F,0x5251,{ 0x9B,0x86,0xDF,0x64,0x86,0xEB,0x08,0xA7 } }; // C7924C7A-739F-5251-9B86-DF6486EB08A7
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory>{ 0x890516D0,0x5A62,0x528B,{ 0x88,0x73,0x4F,0x71,0x40,0xB4,0x04,0x89 } }; // 890516D0-5A62-528B-8873-4F7140B40489
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer>{ 0x235BEFEB,0x6C98,0x5D35,{ 0xA2,0xE7,0x00,0x1E,0xAE,0x34,0x25,0x09 } }; // 235BEFEB-6C98-5D35-A2E7-001EAE342509
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory>{ 0x659719AC,0x4405,0x58F4,{ 0xBD,0xE2,0xEF,0x61,0xDF,0xE6,0x4C,0x21 } }; // 659719AC-4405-58F4-BDE2-EF61DFE64C21
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer>{ 0x2E71779B,0xACA4,0x52C2,{ 0x8A,0x25,0xBC,0x5F,0x1E,0xE3,0xB0,0xAE } }; // 2E71779B-ACA4-52C2-8A25-BC5F1EE3B0AE
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory>{ 0xBD1709E5,0x1940,0x56FC,{ 0xB5,0xC3,0x85,0xE4,0x57,0x09,0x51,0xCC } }; // BD1709E5-1940-56FC-B5C3-85E4570951CC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer>{ 0x93DE1BC2,0xCF84,0x5B5F,{ 0x91,0xBE,0xA7,0xC7,0x81,0xB2,0x02,0x1A } }; // 93DE1BC2-CF84-5B5F-91BE-A7C781B2021A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory>{ 0xFB5248EF,0xE835,0x5997,{ 0xBC,0x36,0xD4,0xE5,0xDB,0x4A,0x1B,0x5A } }; // FB5248EF-E835-5997-BC36-D4E5DB4A1B5A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer>{ 0x8565EAD9,0xB877,0x52C7,{ 0xA1,0x47,0x6F,0xE1,0xFE,0xE7,0x67,0xAF } }; // 8565EAD9-B877-52C7-A147-6FE1FEE767AF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory>{ 0xCEA28C0D,0xC4B3,0x5D18,{ 0xAE,0xF6,0x95,0x80,0x31,0x39,0x58,0x78 } }; // CEA28C0D-C4B3-5D18-AEF6-************
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer>{ 0x3B6952DA,0x9E44,0x52B0,{ 0x91,0xDF,0x39,0xDA,0x9D,0xC1,0xD8,0xB0 } }; // 3B6952DA-9E44-52B0-91DF-39DA9DC1D8B0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory>{ 0x650F375C,0x3B29,0x5376,{ 0xA7,0xF7,0xC7,0x80,0x82,0xB8,0x2D,0x13 } }; // 650F375C-3B29-5376-A7F7-C78082B82D13
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer>{ 0xFFF86275,0x0CDD,0x54DB,{ 0x9D,0x88,0x9C,0x0E,0x5F,0x9B,0xCB,0x4D } }; // FFF86275-0CDD-54DB-9D88-9C0E5F9BCB4D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory>{ 0xF13521D0,0x056E,0x598C,{ 0xAD,0x23,0xA7,0x15,0x00,0x49,0xD8,0x0A } }; // F13521D0-056E-598C-AD23-A7150049D80A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer>{ 0x55493EC4,0x926B,0x595A,{ 0x97,0xA3,0xA7,0xFA,0x60,0x41,0x88,0xA4 } }; // 55493EC4-926B-595A-97A3-A7FA604188A4
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory>{ 0xF87DACB6,0xE87E,0x59B3,{ 0x8A,0x40,0x33,0x1E,0xA7,0xAA,0x74,0x7D } }; // F87DACB6-E87E-59B3-8A40-331EA7AA747D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer>{ 0x03F2C315,0xFB55,0x54B2,{ 0x9A,0xAD,0x97,0x23,0xAA,0xF5,0xE2,0xCF } }; // 03F2C315-FB55-54B2-9AAD-9723AAF5E2CF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory>{ 0x04526BC7,0xFA3E,0x55FE,{ 0xA3,0x14,0x98,0x6E,0x2F,0x19,0x6A,0x2F } }; // 04526BC7-FA3E-55FE-A314-986E2F196A2F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer>{ 0x0182661C,0x0DF3,0x5C7D,{ 0x87,0x52,0x54,0x78,0x04,0xC4,0xFA,0x44 } }; // 0182661C-0DF3-5C7D-8752-547804C4FA44
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory>{ 0xE82DDC93,0x780E,0x5000,{ 0x98,0x1E,0x9B,0xE1,0x0E,0xED,0xEB,0x1F } }; // E82DDC93-780E-5000-981E-9BE10EEDEB1F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer>{ 0xEFB3F05B,0x2A25,0x5266,{ 0xA1,0xCB,0x5A,0x0A,0xA4,0x51,0xCA,0x32 } }; // EFB3F05B-2A25-5266-A1CB-5A0AA451CA32
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory>{ 0xF8D8C7CB,0x47CC,0x5DA5,{ 0xBD,0x1A,0xE2,0xE1,0xBA,0x0F,0xD2,0x4D } }; // F8D8C7CB-47CC-5DA5-BD1A-E2E1BA0FD24D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer>{ 0x58AFB1C3,0xA3FD,0x54A1,{ 0xBE,0x39,0x32,0x8D,0xD8,0xA6,0xF8,0xEC } }; // 58AFB1C3-A3FD-54A1-BE39-328DD8A6F8EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory>{ 0x00218040,0x9C0D,0x5C52,{ 0xB5,0x78,0x59,0x3B,0x80,0x90,0x47,0xB3 } }; // 00218040-9C0D-5C52-B578-593B809047B3
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer>{ 0x607994EC,0xA995,0x5B07,{ 0xB5,0x35,0x8C,0x91,0x3F,0x0B,0xC2,0x6C } }; // 607994EC-A995-5B07-B535-8C913F0BC26C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory>{ 0x71A061C1,0x3D71,0x5548,{ 0x98,0xFD,0x62,0x16,0x7F,0x24,0x60,0x85 } }; // 71A061C1-3D71-5548-98FD-62167F246085
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer>{ 0x21356952,0x4C74,0x5273,{ 0xB8,0x2D,0xE5,0xCE,0x1B,0xBC,0xD3,0x69 } }; // 21356952-4C74-5273-B82D-E5CE1BBCD369
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory>{ 0x61C214A5,0x605B,0x5E98,{ 0xB8,0x5D,0xE3,0x12,0x1D,0x23,0xED,0xAA } }; // 61C214A5-605B-5E98-B85D-E3121D23EDAA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer>{ 0x25B38166,0xB905,0x5480,{ 0x84,0x39,0xE4,0x59,0xA5,0xB7,0x7B,0x8C } }; // 25B38166-B905-5480-8439-E459A5B77B8C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory>{ 0x0C993C78,0x981F,0x5DCF,{ 0x93,0xD3,0xA2,0x17,0xAD,0x9A,0xCA,0xB6 } }; // 0C993C78-981F-5DCF-93D3-A217AD9ACAB6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer>{ 0x20F74F77,0xEDFA,0x5C71,{ 0x9D,0xEB,0x53,0x0D,0xCA,0xF9,0xC1,0x1D } }; // 20F74F77-EDFA-5C71-9DEB-530DCAF9C11D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory>{ 0x07FC8E59,0x55A2,0x58AB,{ 0x89,0x21,0x91,0xE5,0x7D,0xDF,0x11,0x9F } }; // 07FC8E59-55A2-58AB-8921-91E57DDF119F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer>{ 0x1EBF0F7F,0x6111,0x50A5,{ 0x83,0x98,0x89,0xC4,0xFD,0xD0,0xDE,0xDB } }; // 1EBF0F7F-6111-50A5-8398-89C4FDD0DEDB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory>{ 0x51332D86,0xC414,0x5E7D,{ 0xB5,0x7B,0xE4,0x79,0x98,0x3C,0x9E,0x5D } }; // 51332D86-C414-5E7D-B57B-E479983C9E5D
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::AnimatedVisualPlayerAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::BreadcrumbBarItemAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorPickerSliderAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ColorSpectrumAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::DropDownButtonAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ExpanderAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::InfoBarAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::MenuBarItemAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::NavigationViewItemAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::NumberBoxAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::PersonPictureAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::PipsPagerAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressBarAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ProgressRingAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::RadioButtonsAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::RatingControlAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::RepeaterAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::SplitButtonAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TabViewItemAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TeachingTipAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::ToggleSplitButtonAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewItemDataAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer>{ using type = winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer; };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithOwner(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IAnimatedVisualPlayerAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IAnimatedVisualPlayerAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IAnimatedVisualPlayerAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::AnimatedVisualPlayer const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IAnimatedVisualPlayerAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IAnimatedVisualPlayerAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IBreadcrumbBarItemAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IBreadcrumbBarItemAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IBreadcrumbBarItemAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::BreadcrumbBarItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IBreadcrumbBarItemAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IBreadcrumbBarItemAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IColorPickerSliderAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IColorPickerSliderAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IColorPickerSliderAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorPickerSlider const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorPickerSliderAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IColorPickerSliderAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IColorSpectrumAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IColorSpectrumAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IColorSpectrumAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::Primitives::ColorSpectrum const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IColorSpectrumAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IColorSpectrumAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IDropDownButtonAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IDropDownButtonAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IDropDownButtonAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::DropDownButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IDropDownButtonAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IDropDownButtonAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IExpanderAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IExpanderAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IExpanderAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::Expander const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IExpanderAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IExpanderAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IInfoBarAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IInfoBarAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IInfoBarAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::InfoBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IInfoBarAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IInfoBarAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::MenuBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarItemAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarItemAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarItemAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::MenuBarItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IMenuBarItemAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IMenuBarItemAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::NavigationView const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewItemAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewItemAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewItemAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::NavigationViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INavigationViewItemAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INavigationViewItemAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INumberBoxAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INumberBoxAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_INumberBoxAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::NumberBox const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::INumberBoxAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_INumberBoxAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IPersonPictureAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IPersonPictureAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IPersonPictureAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::PersonPicture const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IPersonPictureAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IPersonPictureAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IPipsPagerAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IPipsPagerAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IPipsPagerAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::PipsPager const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IPipsPagerAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IPipsPagerAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IProgressBarAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IProgressBarAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IProgressBarAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ProgressBar const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressBarAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IProgressBarAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IProgressRingAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IProgressRingAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IProgressRingAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ProgressRing const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IProgressRingAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IProgressRingAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRadioButtonsAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRadioButtonsAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRadioButtonsAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::RadioButtons const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRadioButtonsAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRadioButtonsAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRatingControlAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRatingControlAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRatingControlAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::RatingControl const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRatingControlAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRatingControlAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRepeaterAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRepeaterAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IRepeaterAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ItemsRepeater const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IRepeaterAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IRepeaterAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ISplitButtonAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ISplitButtonAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ISplitButtonAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::SplitButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ISplitButtonAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ISplitButtonAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TabView const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewItemAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewItemAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewItemAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TabViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITabViewItemAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITabViewItemAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITeachingTipAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITeachingTipAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITeachingTipAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::TeachingTip const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITeachingTipAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITeachingTipAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IToggleSplitButtonAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IToggleSplitButtonAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_IToggleSplitButtonAutomationPeerFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::Controls::ToggleSplitButton const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::IToggleSplitButtonAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_IToggleSplitButtonAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::TreeViewItem const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemDataAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemDataAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemDataAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Windows::Foundation::IInspectable const& item, winrt::Microsoft::UI::Xaml::Automation::Peers::TreeViewListAutomationPeer const& parent, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewItemDataAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewItemDataAutomationPeerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewListAutomationPeer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewListAutomationPeer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewListAutomationPeerFactory
    {
        auto CreateInstanceWithOwner(winrt::Microsoft::UI::Xaml::Controls::TreeViewList const& owner, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::Automation::Peers::ITreeViewListAutomationPeerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_Automation_Peers_ITreeViewListAutomationPeerFactory<D>;
    };
}
#endif
