// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_Automation_Peers_1_H
#define WINRT_Microsoft_UI_Xaml_Automation_Peers_1_H
#include "winrt/impl/Microsoft.UI.Xaml.Automation.Peers.0.h"
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Automation::Peers
{
    struct WINRT_IMPL_EMPTY_BASES IAnimatedVisualPlayerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimatedVisualPlayerAutomationPeer>
    {
        IAnimatedVisualPlayerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IAnimatedVisualPlayerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IAnimatedVisualPlayerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAnimatedVisualPlayerAutomationPeerFactory>
    {
        IAnimatedVisualPlayerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IAnimatedVisualPlayerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBreadcrumbBarItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBreadcrumbBarItemAutomationPeer>
    {
        IBreadcrumbBarItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IBreadcrumbBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBreadcrumbBarItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBreadcrumbBarItemAutomationPeerFactory>
    {
        IBreadcrumbBarItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IBreadcrumbBarItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPickerSliderAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderAutomationPeer>
    {
        IColorPickerSliderAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorPickerSliderAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorPickerSliderAutomationPeerFactory>
    {
        IColorPickerSliderAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IColorPickerSliderAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSpectrumAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumAutomationPeer>
    {
        IColorSpectrumAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IColorSpectrumAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IColorSpectrumAutomationPeerFactory>
    {
        IColorSpectrumAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IColorSpectrumAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropDownButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButtonAutomationPeer>
    {
        IDropDownButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IDropDownButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDropDownButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDropDownButtonAutomationPeerFactory>
    {
        IDropDownButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IDropDownButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExpanderAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpanderAutomationPeer>
    {
        IExpanderAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IExpanderAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IExpanderAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IExpanderAutomationPeerFactory>
    {
        IExpanderAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IExpanderAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfoBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfoBarAutomationPeer>
    {
        IInfoBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IInfoBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IInfoBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IInfoBarAutomationPeerFactory>
    {
        IInfoBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IInfoBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMenuBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarAutomationPeer>
    {
        IMenuBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMenuBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarAutomationPeerFactory>
    {
        IMenuBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMenuBarItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemAutomationPeer>
    {
        IMenuBarItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMenuBarItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMenuBarItemAutomationPeerFactory>
    {
        IMenuBarItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IMenuBarItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewAutomationPeer>
    {
        INavigationViewAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        INavigationViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewAutomationPeerFactory>
    {
        INavigationViewAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemAutomationPeer>
    {
        INavigationViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INavigationViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INavigationViewItemAutomationPeerFactory>
    {
        INavigationViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        INavigationViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INumberBoxAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INumberBoxAutomationPeer>
    {
        INumberBoxAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        INumberBoxAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES INumberBoxAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<INumberBoxAutomationPeerFactory>
    {
        INumberBoxAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        INumberBoxAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPersonPictureAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureAutomationPeer>
    {
        IPersonPictureAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPersonPictureAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPersonPictureAutomationPeerFactory>
    {
        IPersonPictureAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPersonPictureAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPipsPagerAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPipsPagerAutomationPeer>
    {
        IPipsPagerAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IPipsPagerAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPipsPagerAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPipsPagerAutomationPeerFactory>
    {
        IPipsPagerAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IPipsPagerAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProgressBarAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarAutomationPeer>
    {
        IProgressBarAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IProgressBarAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProgressBarAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressBarAutomationPeerFactory>
    {
        IProgressBarAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IProgressBarAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProgressRingAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingAutomationPeer>
    {
        IProgressRingAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IProgressRingAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IProgressRingAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IProgressRingAutomationPeerFactory>
    {
        IProgressRingAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IProgressRingAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadioButtonsAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonsAutomationPeer>
    {
        IRadioButtonsAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonsAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRadioButtonsAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRadioButtonsAutomationPeerFactory>
    {
        IRadioButtonsAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRadioButtonsAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRatingControlAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlAutomationPeer>
    {
        IRatingControlAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRatingControlAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRatingControlAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRatingControlAutomationPeerFactory>
    {
        IRatingControlAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRatingControlAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepeaterAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeaterAutomationPeer>
    {
        IRepeaterAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IRepeaterAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IRepeaterAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRepeaterAutomationPeerFactory>
    {
        IRepeaterAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IRepeaterAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonAutomationPeer>
    {
        ISplitButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ISplitButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISplitButtonAutomationPeerFactory>
    {
        ISplitButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ISplitButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewAutomationPeer>
    {
        ITabViewAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITabViewAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewAutomationPeerFactory>
    {
        ITabViewAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITabViewAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewItemAutomationPeer>
    {
        ITabViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITabViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITabViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITabViewItemAutomationPeerFactory>
    {
        ITabViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITabViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITeachingTipAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITeachingTipAutomationPeer>
    {
        ITeachingTipAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITeachingTipAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITeachingTipAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITeachingTipAutomationPeerFactory>
    {
        ITeachingTipAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITeachingTipAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IToggleSplitButtonAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonAutomationPeer>
    {
        IToggleSplitButtonAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IToggleSplitButtonAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IToggleSplitButtonAutomationPeerFactory>
    {
        IToggleSplitButtonAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        IToggleSplitButtonAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewItemAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemAutomationPeer>
    {
        ITreeViewItemAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewItemAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemAutomationPeerFactory>
    {
        ITreeViewItemAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewItemDataAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemDataAutomationPeer>
    {
        ITreeViewItemDataAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemDataAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewItemDataAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewItemDataAutomationPeerFactory>
    {
        ITreeViewItemDataAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewItemDataAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewListAutomationPeer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewListAutomationPeer>
    {
        ITreeViewListAutomationPeer(std::nullptr_t = nullptr) noexcept {}
        ITreeViewListAutomationPeer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES ITreeViewListAutomationPeerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITreeViewListAutomationPeerFactory>
    {
        ITreeViewListAutomationPeerFactory(std::nullptr_t = nullptr) noexcept {}
        ITreeViewListAutomationPeerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
