// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Management_Setup_1_H
#define WINRT_Windows_Management_Setup_1_H
#include "winrt/impl/Windows.Management.Setup.0.h"
WINRT_EXPORT namespace winrt::Windows::Management::Setup
{
    struct WINRT_IMPL_EMPTY_BASES IAgentProvisioningProgressReport :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAgentProvisioningProgressReport>
    {
        IAgentProvisioningProgressReport(std::nullptr_t = nullptr) noexcept {}
        IAgentProvisioningProgressReport(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentSessionConnectionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionConnectionChangedEventArgs>
    {
        IDeploymentSessionConnectionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionConnectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentSessionHeartbeatRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionHeartbeatRequestedEventArgs>
    {
        IDeploymentSessionHeartbeatRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionHeartbeatRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentSessionStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentSessionStateChangedEventArgs>
    {
        IDeploymentSessionStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IDeploymentSessionStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentWorkload :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkload>
    {
        IDeploymentWorkload(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkload(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentWorkloadBatch :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadBatch>
    {
        IDeploymentWorkloadBatch(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadBatch(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentWorkloadBatchFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadBatchFactory>
    {
        IDeploymentWorkloadBatchFactory(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadBatchFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDeploymentWorkloadFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeploymentWorkloadFactory>
    {
        IDeploymentWorkloadFactory(std::nullptr_t = nullptr) noexcept {}
        IDeploymentWorkloadFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IDevicePreparationExecutionContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDevicePreparationExecutionContext>
    {
        IDevicePreparationExecutionContext(std::nullptr_t = nullptr) noexcept {}
        IDevicePreparationExecutionContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMachineProvisioningProgressReporter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMachineProvisioningProgressReporter>
    {
        IMachineProvisioningProgressReporter(std::nullptr_t = nullptr) noexcept {}
        IMachineProvisioningProgressReporter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMachineProvisioningProgressReporterStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMachineProvisioningProgressReporterStatics>
    {
        IMachineProvisioningProgressReporterStatics(std::nullptr_t = nullptr) noexcept {}
        IMachineProvisioningProgressReporterStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
