C:\Users\<USER>\Desktop\plexy\windows\plexy\MainPage.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\MainPage.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\ReactPackageProvider.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\ReactPackageProvider.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\AutolinkedNativeModules.g.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\AutolinkedNativeModules.g.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\pch.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\pch.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\App.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\App.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\module.g.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\module.g.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSI\JsiAbiApi.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JsiAbiApi.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSI\JsiApiContext.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JsiApiContext.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSI\JsiValueHelpers.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JsiValueHelpers.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSValue.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JSValue.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSValueTreeReader.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JSValueTreeReader.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSValueTreeWriter.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JSValueTreeWriter.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ModuleRegistration.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\ModuleRegistration.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ReactPromise.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\ReactPromise.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\TurboModuleProvider.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\TurboModuleProvider.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\jsi\jsi.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\jsi.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ReactCommon\react\bridging\LongLivedObject.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\LongLivedObject.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ReactCommon\TurboModule.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\TurboModule.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ReactCommon\TurboModuleUtils.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\TurboModuleUtils.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ApiLoaders\NodeApi.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\NodeApi.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\ApiLoaders\JSRuntimeApi.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\JSRuntimeApi.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\NodeApiJsiRuntime.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\NodeApiJsiRuntime.obj
C:\Users\<USER>\Desktop\plexy\node_modules\react-native-windows\Microsoft.ReactNative.Cxx\JSI\NodeApiJsiLoader.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\NodeApiJsiLoader.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\XamlTypeInfo.g.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\XamlTypeInfo.g.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\XamlTypeInfo.Impl.g.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\XamlTypeInfo.Impl.g.obj
C:\Users\<USER>\Desktop\plexy\windows\plexy\Generated Files\XamlMetaDataProvider.cpp;C:\Users\<USER>\Desktop\plexy\windows\plexy\x64\Debug\XamlMetaDataProvider.obj
