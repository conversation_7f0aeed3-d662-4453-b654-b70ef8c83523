﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10" xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest" xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" IgnorableNamespaces="uap mp build" xmlns:build="http://schemas.microsoft.com/developer/appx/2015/build">
  <!--
    THIS PACKAGE MANIFEST FILE IS GENERATED BY THE BUILD PROCESS.

    Changes to this file will be lost when it is regenerated. To correct errors in this file, edit the source .appxmanifest file.

    For more information on package manifest files, see http://go.microsoft.com/fwlink/?LinkID=241727
  -->
  <Identity Name="7b6a8a97-8ee2-4352-a946-b71d249d3f1e" Publisher="CN=Alper" Version="1.0.0.0" ProcessorArchitecture="x64" />
  <mp:PhoneIdentity PhoneProductId="7b6a8a97-8ee2-4352-a946-b71d249d3f1e" PhonePublisherId="00000000-0000-0000-0000-000000000000" />
  <Properties>
    <DisplayName>plexy</DisplayName>
    <PublisherDisplayName>Alper</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.22621.0" />
    <PackageDependency Name="Microsoft.UI.Xaml.2.8" MinVersion="8.2207.14002.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
    <PackageDependency Name="Microsoft.VCLibs.140.00.Debug.UWPDesktop" MinVersion="14.0.33728.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
    <PackageDependency Name="Microsoft.VCLibs.140.00.Debug" MinVersion="14.0.33519.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
  </Dependencies>
  <Resources>
    <Resource Language="EN-US" />
  </Resources>
  <Applications>
    <Application Id="App" Executable="plexy.exe" EntryPoint="plexy.App">
      <uap:VisualElements DisplayName="plexy" Square150x150Logo="Assets\Square150x150Logo.png" Square44x44Logo="Assets\Square44x44Logo.png" Description="plexy" BackgroundColor="transparent">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" />
        <uap:SplashScreen Image="Assets\SplashScreen.png" />
      </uap:VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
  </Capabilities>
  <Extensions>
    <Extension Category="windows.activatableClass.inProcessServer">
      <InProcessServer>
        <Path>Microsoft.ReactNative.dll</Path>
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.DevMenuControl" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.QuirkSettings" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.DynamicAutomationPeer" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactViewOptions" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactApplication" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.JsiRuntime" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.XamlMetaDataProvider" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ViewPanel" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.DynamicValueProvider" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.XamlHelper" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.HttpSettings" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactCoreInjection" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactInstanceSettings" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactDispatcherHelper" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactPropertyBagHelper" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ViewControl" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.Timer" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactNotificationServiceHelper" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.LayoutService" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactRootView" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.XamlUIService" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.DynamicAutomationProperties" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.RedBoxHelper" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.ReactNative.ReactNativeHost" ThreadingModel="both" />
      </InProcessServer>
    </Extension>
    <Extension Category="windows.activatableClass.inProcessServer">
      <InProcessServer>
        <Path>Microsoft.Web.WebView2.Core.dll</Path>
        <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2Controller" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2CompositionController" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2ControllerWindowReference" ThreadingModel="both" />
        <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2Environment" ThreadingModel="both" />
      </InProcessServer>
    </Extension>
  </Extensions>
  <build:Metadata>
    <build:Item Name="cl.exe" Version="19.44.35211.0" />
    <build:Item Name="CodeSharingProject" Value="248F659F-DAC5-46E8-AC09-60EC9FC95053" />
    <build:Item Name="TargetFrameworkMoniker" Value="native,Version=v0.0" />
    <build:Item Name="VisualStudio" Version="17.0" />
    <build:Item Name="OperatingSystem" Version="10.0.26100.2304 (WinBuild.160101.0800)" />
    <build:Item Name="Microsoft.Build.AppxPackage.dll" Version="17.14.40364.64997" />
    <build:Item Name="ProjectGUID" Value="{6b55be3c-154b-4037-a4d4-62c1384aae29}" />
    <build:Item Name="OptimizingToolset" Value="None" />
    <build:Item Name="TargetRuntime" Value="Native" />
    <build:Item Name="Microsoft.Windows.UI.Xaml.Build.Tasks.dll" Version="0.0.0.0" />
    <build:Item Name="MakePri.exe" Version="10.0.22621.5040 (WinBuild.160101.0800)" />
  </build:Metadata>
</Package>