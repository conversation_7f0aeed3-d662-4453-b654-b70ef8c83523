﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.UI.Xaml</name>
  </assembly>
  <members>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer">
      <summary>Exposes AnimatedVisualPlayer types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer)">
      <summary>Initializes a new instance of the AnimatedVisualPlayerAutomationPeer class.</summary>
      <param name="owner">The AnimatedVisualPlayer control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer">
      <summary>Exposes BreadcrumbBar types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.BreadcrumbBarItem)">
      <summary>Initializes a new instance of the BreadcrumbBarItemAutomationPeer class.</summary>
      <param name="owner">The BreadcrumbBarItem instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer.Invoke">
      <summary>Sends a request to invoke the item associated with the automation peer.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer">
      <summary>Exposes ColorPickerSlider types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider)">
      <summary>Initializes a new instance of the ColorPickerSliderAutomationPeer class.</summary>
      <param name="owner">The ColorPickerSlider control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer">
      <summary>Exposes ColorSpectrum types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum)">
      <summary>Initializes a new instance of the ColorSpectrumAutomationPeer class.</summary>
      <param name="owner">The ColorSpectrum control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer">
      <summary>Exposes DropDownButton types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.DropDownButton)">
      <summary>Initializes a new instance of the DropDownButtonAutomationPeer class.</summary>
      <param name="owner">The DropDownButton control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer.Collapse">
      <summary>Collapses the DropDownButton.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer.Expand">
      <summary>Expands the DropDownButton.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer.ExpandCollapseState">
      <summary>Gets the state, expanded or collapsed, of the DropDownButton.</summary>
      <returns>A value of the enumeration.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer">
      <summary>Exposes Expander types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.Expander)">
      <summary>Initializes a new instance of the ExpanderAutomationPeer class.</summary>
      <param name="owner">The Expander control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer.Collapse">
      <summary>Hides the content area of the control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer.Expand">
      <summary>Displays the content area of the control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer.ExpandCollapseState">
      <summary>Gets the value of the Expander.IsExpanded" property and returns whether the Expander is currently expanded or collapsed.</summary>
      <returns>Expanded if the content area of the control is currently shown; Collapsed if the content area is hidden.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer">
      <summary>Exposes InfoBar types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.InfoBar)">
      <summary>Initializes a new instance of the InfoBarAutomationPeer class.</summary>
      <param name="owner">The InfoBar control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer">
      <summary>Exposes MenuBar types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.MenuBar)">
      <summary>Initializes a new instance of the MenuBarAutomationPeer class.</summary>
      <param name="owner">The MenuBar control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer">
      <summary>Exposes MenuBarItem types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.MenuBarItem)">
      <summary>Initializes a new instance of the MenuBarItemAutomationPeer class.</summary>
      <param name="owner">The MenuBarItem control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer.Collapse">
      <summary>Hides all nodes, controls, or content that are descendants of the MenuBarItem.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer.Expand">
      <summary>Displays all child nodes, controls, or content of the MenuBarItem.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer.Invoke">
      <summary>Sends a request to click the MenuBarItem associated with the automation peer.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer.ExpandCollapseState">
      <summary>Gets the state, expanded or collapsed, of the MenuBarItem.</summary>
      <returns>A value of the enumeration.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer">
      <summary>Exposes NavigationViewItem types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.NavigationViewItem)">
      <summary>Initializes a new instance of the NavigationViewItemAutomationPeer class.</summary>
      <param name="owner">The NavigationViewItem control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer.Collapse">
      <summary>Collapses the specified node in the tree.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer.Expand">
      <summary>Expands the specified node in the tree.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer.ExpandCollapseState">
      <summary>Gets the value of the NavigationViewItem.IsExpanded" property and returns whether the NavigationViewItem is currently expanded or collapsed.</summary>
      <returns>Expanded if the NavigationViewItem is currently expanded, Collapsed otherwise.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer">
      <summary>Exposes NumberBox types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.NumberBox)">
      <summary>Initializes a new instance of the NumberBoxAutomationPeer class.</summary>
      <param name="owner">The NumberBox control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer">
      <summary>Exposes PersonPicture types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.PersonPicture)">
      <summary>Initializes a new instance of the PersonPictureAutomationPeer class.</summary>
      <param name="owner">The PersonPicture control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer">
      <summary>Exposes PipsPager types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.PipsPager)">
      <summary>Initializes a new instance of the PipsPagerAutomationPeer class.</summary>
      <param name="owner">The PipsPager control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer">
      <summary>Exposes ProgressBar types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.ProgressBar)">
      <summary>Initializes a new instance of the ProgressBarAutomationPeer class.</summary>
      <param name="owner">The ProgressBar control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer">
      <summary>Exposes ProgressRing types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.ProgressRing)">
      <summary>Initializes a new instance of the ProgressRingAutomationPeer class.</summary>
      <param name="owner">The ProgressRing control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.SetValue(System.Double)">
      <param name="value"></param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.IsReadOnly" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.LargeChange" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.Maximum" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.Minimum" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.SmallChange" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer.Value" />
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer" />
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.RadioButtons)">
      <param name="owner"></param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer">
      <summary>Exposes RatingControl types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.RatingControl)">
      <summary>Initializes a new instance of the RatingControlAutomationPeer class.</summary>
      <param name="owner">The RatingControl control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer">
      <summary>Exposes ItemsRepeater types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.ItemsRepeater)">
      <summary>Initializes a new instance of the RepeaterAutomationPeer class.</summary>
      <param name="owner">The ItemsRepeater control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer">
      <summary>Exposes SplitButton types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.SplitButton)">
      <summary>Initializes a new instance of the SplitButtonAutomationPeer class.</summary>
      <param name="owner">The SplitButton control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer.Collapse">
      <summary>Collapses the SplitButton.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer.Expand">
      <summary>Expands the SplitButton.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer.Invoke" />
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer.ExpandCollapseState">
      <summary>Gets the state, expanded or collapsed, of the SplitButton.</summary>
      <returns>A value of the enumeration.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer">
      <summary>Exposes TabView types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.TabView)">
      <summary>Initializes a new instance of the TabViewAutomationPeer class.</summary>
      <param name="owner">The TabView control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer">
      <summary>Exposes TabViewItem types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.TabViewItem)">
      <param name="owner">The TabViewItem control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer">
      <summary>Exposes TeachingTip types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.TeachingTip)">
      <summary>Initializes a new instance of the TeachingTipAutomationPeer class.</summary>
      <param name="owner">The TeachingTip control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer">
      <summary>Exposes ToggleSplitButton types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.ToggleSplitButton)">
      <summary>Initializes a new instance of the ToggleSplitButtonAutomationPeer class.</summary>
      <param name="owner">The ToggleSplitButton control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.Collapse">
      <summary>Collapses the associated ToggleSplitButton.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.Expand">
      <summary>Expands the associated ToggleSplitButton.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.Toggle">
      <summary>Cycles through the toggle states of the ToggleSplitButton.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.ExpandCollapseState">
      <summary>Gets a value indicating the expanded or collapsed state of the associated ToggleSplitButton.</summary>
      <returns>The expanded or collapsed state of the associated ToggleSplitButton. The default value is Expanded.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer.ToggleState">
      <summary>Gets the toggle state of a ToggleSplitButton type.</summary>
      <returns>The toggle state of the ToggleSplitButton. The default value is Indeterminate.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer">
      <summary>Exposes TreeViewItem types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.TreeViewItem)">
      <summary>Initializes a new instance of the TreeViewItemAutomationPeer class.</summary>
      <param name="owner">The TreeViewItem control instance to create the peer for.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer.Collapse">
      <summary>Collapses the associated TreeViewItem.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer.Expand">
      <summary>Expands the associated TreeViewItem.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer.ExpandCollapseState">
      <summary>Gets a value indicating the expanded or collapsed state of the associated TreeViewItem.</summary>
      <returns>The expanded or collapsed state of the associated TreeViewItem. The default value is Expanded.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer">
      <summary>Exposes TreeViewItem data types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer.#ctor(System.Object,Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer)">
      <summary>Initializes a new instance of the TreeViewItemDataAutomationPeer class.</summary>
      <param name="item">The TreeViewItem.</param>
      <param name="parent">The TreeViewList parent control instance for which to create the peer.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer.Collapse">
      <summary>Collapses the associated Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer".</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer.Expand">
      <summary>Expands the associated Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer".</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer.ExpandCollapseState">
      <summary>Gets a value indicating the expanded or collapsed state of the associated TreeViewItemDataAutomationPeer.</summary>
      <returns>The expanded or collapsed state of the associated TreeViewItemDataAutomationPeer. The default value is Expanded.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer">
      <summary>Exposes TreeViewList types to Microsoft UI Automation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer.#ctor(Microsoft.UI.Xaml.Controls.TreeViewList)">
      <summary>Initializes a new instance of the TreeViewListAutomationPeer class.</summary>
      <param name="owner">The TreeViewList control instance to create the peer for.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedIcon">
      <summary>Represents an icon that displays and controls a visual that can animate in response to user interaction and visual state changes.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedIcon.#ctor">
      <summary>Initializes a new instance of the AnimatedIcon class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedIcon.GetState(Windows.UI.Xaml.DependencyObject)">
      <summary>Retrieves the value of the AnimatedIcon.State" attached property for the specified DependencyObject.</summary>
      <param name="object">The object from which the property value is retrieved.</param>
      <returns>The current value of the AnimatedIcon.State" attached property on the specified dependency object.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedIcon.SetState(Windows.UI.Xaml.DependencyObject,System.String)">
      <summary>Specifies the value of the AnimatedIcon.State" attached property for the specified DependencyObject.</summary>
      <param name="object">The object for which the property value is specified.</param>
      <param name="value">The value of the AnimatedIcon.State" attached property on the specified dependency object.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.FallbackIconSource">
      <summary>Gets or sets the static icon to use when the animated icon cannot run.</summary>
      <returns>The static icon to use when the animated icon cannot run. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.FallbackIconSourceProperty">
      <summary>Identifies the FallbackIconSource dependency property.</summary>
      <returns>The identifier for the FallbackIconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.MirroredWhenRightToLeft" />
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.MirroredWhenRightToLeftProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.Source">
      <summary>Gets or sets the animated visual shown by the AnimatedIcon object.</summary>
      <returns>The animated visual shown by the AnimatedIcon. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.SourceProperty">
      <summary>Identifies the Source dependency property.</summary>
      <returns>The identifier for the Source dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.State">
      <summary>Property that the developer sets on AnimatedIcon.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIcon.StateProperty">
      <summary>Identifies the AnimatedIcon.State" XAML attached property.</summary>
      <returns>The identifier for the AnimatedIcon.State" XAML attached property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedIconSource">
      <summary>Represents a shareable object used to create an icon that displays and controls a visual that can animate in response to user interaction and visual state changes.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedIconSource.#ctor">
      <summary>Initializes a new instance of the AnimatedIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.FallbackIconSource">
      <summary>Gets or sets the static icon to use when the animated icon cannot run.</summary>
      <returns>The static icon to use when the animated icon cannot run. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.FallbackIconSourceProperty">
      <summary>Identifies the FallbackIconSource dependency property.</summary>
      <returns>The identifier for the FallbackIconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.MirroredWhenRightToLeft" />
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.MirroredWhenRightToLeftProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.Source">
      <summary>Gets or sets the animated visual shown by the AnimatedIconSource object.</summary>
      <returns>The animated visual shown by the AnimatedIconSource. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedIconSource.SourceProperty">
      <summary>Identifies the Source dependency property.</summary>
      <returns>The identifier for the Source dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer">
      <summary>An element that displays and controls an IAnimatedVisual.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.#ctor">
      <summary>Initializes a new instance of the AnimatedVisualPlayer class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Pause">
      <summary>Pauses the currently playing animated visual, or does nothing if no play is underway.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.PlayAsync(System.Double,System.Double,System.Boolean)">
      <summary>Starts playing the loaded animated visual, or does nothing if no animated visual is loaded.</summary>
      <param name="fromProgress">The point from which to start the animation, as a value from 0 to 1.</param>
      <param name="toProgress">The point at which to finish the animation, as a value from 0 to 1.</param>
      <param name="looped">If true, the animation loops continuously between _fromProgress_ and _toProgress_. If false, the animation plays once then stops.</param>
      <returns>An async action that is completed when the play is stopped or, if _looped_ is not set, when the play reaches _toProgress_.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Resume">
      <summary>Resumes the currently paused animated visual, or does nothing if there is no animated visual loaded or the animated visual is not paused.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.SetProgress(System.Double)">
      <summary>Moves the progress of the animated visual to the given value, or does nothing if no animated visual is loaded.</summary>
      <param name="progress">A value from 0 to 1 that represents the progress of the animated visual.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Stop">
      <summary>Stops the current play, or does nothing if no play is underway.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.AutoPlay">
      <summary>Gets or sets a value that indicates whether an animated visual plays immediately when it is loaded.</summary>
      <returns>true if the animated visual plays immediately when it is loaded; otherwise false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.AutoPlayProperty">
      <summary>Identifies the AutoPlay dependency property.</summary>
      <returns>The identifier for the AutoPlay dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Diagnostics">
      <summary>Gets optional diagnostics information about the last attempt to load an animated visual.</summary>
      <returns>Diagnostics information about the last attempt to load an animated visual.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.DiagnosticsProperty">
      <summary>Identifies the Diagnostics dependency property.</summary>
      <returns>The identifier for the Diagnostics dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Duration">
      <summary>Gets the duration of the the currently loaded animated visual, or TimeSpan.Zero if no animated visual is loaded.</summary>
      <returns>The duration of the the currently loaded animated visual, or TimeSpan.Zero if no animated visual is loaded.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.DurationProperty">
      <summary>Identifies the Duration dependency property.</summary>
      <returns>The identifier for the Duration dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.FallbackContent">
      <summary>Gets or sets content to display if an animated visual fails to load.</summary>
      <returns>Content to display if an animated visual fails to load.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.FallbackContentProperty">
      <summary>Identifies the FallbackContent dependency property.</summary>
      <returns>The identifier for the FallbackContent dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.IsAnimatedVisualLoaded">
      <summary>Gets a value that indicates whether an animated visual is loaded.</summary>
      <returns>true if an animated visual is loaded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.IsAnimatedVisualLoadedProperty">
      <summary>Identifies the IsAnimatedVisualLoaded dependency property.</summary>
      <returns>The identifier for the IsAnimatedVisualLoaded dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.IsPlaying">
      <summary>Gets a value that indicates whether an animated visual is loaded and a play is underway.</summary>
      <returns>true if an animated visual is loaded and a play is underway; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.IsPlayingProperty">
      <summary>Identifies the IsPlaying dependency property.</summary>
      <returns>The identifier for the IsPlaying dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.PlaybackRate">
      <summary>Gets or sets the rate at which the animation plays.</summary>
      <returns>The rate at which the animation plays. The default is 1.0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.PlaybackRateProperty">
      <summary>Identifies the PlaybackRate dependency property.</summary>
      <returns>The identifier for the PlaybackRate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.ProgressObject">
      <summary>Gets a CompositionObject that is animated along with the progress of the AnimatedVisualPlayer.</summary>
      <returns>A CompositionObject that is animated along with the progress of the AnimatedVisualPlayer.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Source">
      <summary>Gets or sets the provider of the animated visual for the player.</summary>
      <returns>The provider of the animated visual for the player.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.SourceProperty">
      <summary>Identifies the Source dependency property.</summary>
      <returns>The identifier for the Source dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.Stretch">
      <summary>Gets or sets a value that describes how an animated visual should be stretched to fill the destination rectangle.</summary>
      <returns>A value that describes how an animated visual should be stretched to fill the destination rectangle. The default is Uniform.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer.StretchProperty">
      <summary>Identifies the Stretch dependency property.</summary>
      <returns>The identifier for the Stretch dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource">
      <summary>Represents an animation for a check mark that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedAcceptVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedAcceptVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource">
      <summary>Represents an animation for a back arrow that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedBackVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedBackVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource">
      <summary>Represents an animation for a downward facing chevron that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedChevronDownSmallVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedChevronDownSmallVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource">
      <summary>Represents an animation for a chevron that rotates from right to down that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedChevronRightDownSmallVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedChevronRightDownSmallVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource">
      <summary>Represents an animation for a chevron that rotates from up to down that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedChevronUpDownSmallVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedChevronUpDownSmallVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource">
      <summary>Represents an animation for a magnifying glass that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedFindVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedFindVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource">
      <summary>Represents an animation for a navigation menu that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedGlobalNavigationButtonVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedGlobalNavigationButtonVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource">
      <summary>Represents an animation for a settings icon that can be used as an animated icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource.#ctor">
      <summary>Initializes a new instance of the AnimatedSettingsVisualSource class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets the color of the animated visual for an AnimatedIcon.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value the animated icon is being set to.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an AnimatedSettingsVisualSource object for an AnimatedIcon.</summary>
      <param name="compositor">The Compositor used to create objects for the animated visual.</param>
      <param name="diagnostics">Diagnostics information about the IAnimatedVisualSource implementation.

This parameter is optional.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource.Markers">
      <summary>Provides a mapping from marker names to playback positions in the animated visual for an AnimatedIcon.</summary>
      <returns>The collection of marker names and values as defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.BackdropMaterial">
      <summary>Helper class to apply a backdrop material to the root of the XAML content.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.BackdropMaterial.GetApplyToRootOrPageBackground(Windows.UI.Xaml.Controls.Control)">
      <summary>Gets the value of the BackdropMaterial.ApplyToRootOrPageBackground XAML attached property for the target element.</summary>
      <param name="element">The object from which the property value is read.</param>
      <returns>The BackdropMaterial.ApplyToRootOrPageBackground XAML attached property value of the requested object.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.BackdropMaterial.SetApplyToRootOrPageBackground(Windows.UI.Xaml.Controls.Control,System.Boolean)">
      <summary>Sets the value of the BackdropMaterial.ApplyToRootOrPageBackground XAML attached property for a target element.</summary>
      <param name="element">The object to which the property value is written.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BackdropMaterial.ApplyToRootOrPageBackground">
      <summary>Applies the backdrop material to the root or background of the XAML content.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BackdropMaterial.ApplyToRootOrPageBackgroundProperty">
      <summary>Identifies the BackdropMaterial.ApplyToRootOrPageBackground XAML attached property.</summary>
      <returns>The identifier for the BackdropMaterial.ApplyToRootOrPageBackground XAML attached property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.BitmapIconSource">
      <summary>Represents an icon source that uses a bitmap as its content.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.BitmapIconSource.#ctor">
      <summary>Initializes a new instance of the BitmapIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BitmapIconSource.ShowAsMonochrome">
      <summary>Gets or sets a value that indicates whether the bitmap is shown in a single color.</summary>
      <returns>true to show the bitmap in a single color; false to show the bitmap in full color. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BitmapIconSource.ShowAsMonochromeProperty">
      <summary>Identifies the ShowAsMonochrome dependency property.</summary>
      <returns>The identifier for the ShowAsMonochrome dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BitmapIconSource.UriSource">
      <summary>Gets or sets the Uniform Resource Identifier (URI) of the bitmap to use as the icon content.</summary>
      <returns>The Uri of the bitmap to use as the icon content. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BitmapIconSource.UriSourceProperty">
      <summary>Identifies the UriSource dependency property.</summary>
      <returns>The identifier for the UriSource dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.BreadcrumbBar">
      <summary>The BreadcrumbBar control provides the direct path of pages or folders to the current location.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemClicked">
      <summary>Occurs when an item is clicked in the BreadcrumbBar.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.BreadcrumbBar.#ctor">
      <summary>Initializes a new instance of the BreadcrumbBar class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemsSource">
      <summary>Gets or sets an object source used to generate the content of the BreadcrumbBar.</summary>
      <returns>An object source used to generate the content of the BreadcrumbBar.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemsSourceProperty">
      <summary>Identifies the ItemsSource dependency property.</summary>
      <returns>The identifier for the ItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemTemplate">
      <summary>Gets or sets the data template for the BreadcrumbBarItem.</summary>
      <returns>The data template that is used to display the content of the BreadcrumbBarItem.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBar.ItemTemplateProperty">
      <summary>Identifies the ItemTemplate dependency property.</summary>
      <returns>The identifer for the ItemTemplate dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.BreadcrumbBarItem">
      <summary>Represents an item in a BreadcrumbBar control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.BreadcrumbBarItem.#ctor">
      <summary>Initializes a new instance of the BreadcrumbBarItem class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.BreadcrumbBarItemClickedEventArgs">
      <summary>Provides data for the BreadcrumbBar.ItemClicked" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBarItemClickedEventArgs.Index">
      <summary>Gets the index of the item that was clicked.</summary>
      <returns>The index of the item that was clicked.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.BreadcrumbBarItemClickedEventArgs.Item">
      <summary>Gets the Content property value of the BreadcrumbBarItem that is clicked.</summary>
      <returns>The Content property value of the BreadcrumbBarItem that is clicked.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ColorChangedEventArgs">
      <summary>Provides event data for a ColorChanged event (see ColorPicker.ColorChanged" and ColorSpectrum.ColorChanged" ).</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorChangedEventArgs.NewColor">
      <summary>Gets the color that is currently selected in the control.</summary>
      <returns>The color that is currently selected in the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorChangedEventArgs.OldColor">
      <summary>Gets the color that was previously selected in the control.</summary>
      <returns>The color that was previously selected in the control.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ColorPicker">
      <summary>Represents a control that lets a user pick a color using a color spectrum, sliders, or text input.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ColorPicker.ColorChanged">
      <summary>Occurs when the Color property has changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ColorPicker.#ctor">
      <summary>Initializes a new instance of the ColorPicker class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.Color">
      <summary>Gets or sets the current color value.</summary>
      <returns>The current color value.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.ColorProperty">
      <summary>Identifies the Color dependency property.</summary>
      <returns>The identifier for the Color dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.ColorSpectrumComponents">
      <summary>Gets or sets a value that indicates how the Hue-Saturation-Value (HSV) color components are mapped onto the ColorSpectrum.</summary>
      <returns>A value of the enumeration. The default is HueSaturation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.ColorSpectrumComponentsProperty">
      <summary>Identifies the ColorSpectrumComponents dependency property.</summary>
      <returns>The identifier for the ColorSpectrumComponents dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.ColorSpectrumShape">
      <summary>Gets or sets a value that indicates whether the ColorSpectrum is shown as a square or a circle.</summary>
      <returns>A value of the enumeration. The default is Box, which shows the spectrum as a square.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.ColorSpectrumShapeProperty">
      <summary>Identifies the ColorSpectrumShape dependency property.</summary>
      <returns>The identifier for the ColorSpectrumShape dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaEnabled">
      <summary>Gets or sets a value that indicates whether the alpha channel can be modified.</summary>
      <returns>true if the alpha channel is enabled; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaEnabledProperty">
      <summary>Identifies the IsAlphaEnabled dependency property.</summary>
      <returns>The identifier for the IsAlphaEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaSliderVisible">
      <summary>Gets or sets a value that indicates whether the slider control for the alpha channel is shown.</summary>
      <returns>true if the alpha channel slider is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaSliderVisibleProperty">
      <summary>Identifies the IsAlphaSliderVisible dependency property.</summary>
      <returns>The identifier for the IsAlphaSliderVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaTextInputVisible">
      <summary>Gets or sets a value that indicates whether the text input box for the alpha channel is shown.</summary>
      <returns>true if the alpha channel text input box is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsAlphaTextInputVisibleProperty">
      <summary>Identifies the IsAlphaTextInputVisible dependency property.</summary>
      <returns>The identifier for the IsAlphaTextInputVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorChannelTextInputVisible">
      <summary>Gets or sets a value that indicates whether the text input boxes for the color channels are shown.</summary>
      <returns>true if the color channel text input boxes are shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorChannelTextInputVisibleProperty">
      <summary>Identifies the IsColorChannelTextInputVisible dependency property.</summary>
      <returns>The identifier for the IsColorChannelTextInputVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorPreviewVisible">
      <summary>Gets or sets a value that indicates whether the color preview bar is shown.</summary>
      <returns>true if the color preview bar is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorPreviewVisibleProperty">
      <summary>Identifies the IsColorPreviewVisible dependency property.</summary>
      <returns>The identifier for the IsColorPreviewVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorSliderVisible">
      <summary>Gets or sets a value that indicates whether the slider control for the color value is shown.</summary>
      <returns>true if the color slider is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorSliderVisibleProperty">
      <summary>Identifies the IsColorSliderVisible dependency property.</summary>
      <returns>The identifier for the IsColorSliderVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorSpectrumVisible">
      <summary>Gets or sets a value that indicates whether the color spectrum control is shown.</summary>
      <returns>true if the color spectrum is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsColorSpectrumVisibleProperty">
      <summary>Identifies the IsColorSpectrumVisible dependency property.</summary>
      <returns>The identifier for the IsColorSpectrumVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsHexInputVisible">
      <summary>Gets or sets a value that indicates whether the text input box for a HEX color value is shown.</summary>
      <returns>true if the HEX color text input box is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsHexInputVisibleProperty">
      <summary>Identifies the IsHexInputVisible dependency property.</summary>
      <returns>The identifier for the IsHexInputVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsMoreButtonVisible">
      <summary>Gets or sets a value that indicates whether the 'more' button is shown.</summary>
      <returns>true if the 'more' button is shown; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.IsMoreButtonVisibleProperty">
      <summary>Identifies the IsMoreButtonVisible dependency property.</summary>
      <returns>The identifier for the IsMoreButtonVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxHue">
      <summary>Gets or sets the maximum Hue value in the range 0-359.</summary>
      <returns>The maximum Hue value in the range 0-359. The default is 359.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxHueProperty">
      <summary>Identifies the MaxHue dependency property.</summary>
      <returns>The identifier for the MaxHue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxSaturation">
      <summary>Gets or sets the maximum Saturation value in the range 0-100.</summary>
      <returns>The maximum Saturation value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxSaturationProperty">
      <summary>Identifies the MaxSaturation dependency property.</summary>
      <returns>The identifier for the MaxSaturation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxValue">
      <summary>Gets or sets the maximum Value value in the range 0-100.</summary>
      <returns>The maximum Value value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MaxValueProperty">
      <summary>Identifies the MaxValue dependency property.</summary>
      <returns>The identifier for the MaxValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinHue">
      <summary>Gets or sets the minimum Hue value in the range 0-359.</summary>
      <returns>The minimum Hue value in the range 0-359. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinHueProperty">
      <summary>Identifies the MinHue dependency property.</summary>
      <returns>The identifier for the MinHue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinSaturation">
      <summary>Gets or sets the minimum Saturation value in the range 0-100.</summary>
      <returns>The minimum Saturation value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinSaturationProperty">
      <summary>Identifies the MinSaturation dependency property.</summary>
      <returns>The identifier for the MinSaturation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinValue">
      <summary>Gets or sets the minimum Value value in the range 0-100.</summary>
      <returns>The minimum Value value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.MinValueProperty">
      <summary>Identifies the MinValue dependency property.</summary>
      <returns>The identifier for the MinValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.Orientation" />
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.OrientationProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.PreviousColor">
      <summary>Gets or sets the previous color.</summary>
      <returns>The previous color. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ColorPicker.PreviousColorProperty">
      <summary>Identifies the PreviousColor dependency property.</summary>
      <returns>The identifier for the PreviousColor dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ColorPickerHsvChannel">
      <summary>Defines constants for specifying which Hue-Saturation-Value (HSV) and Alpha channel values a slider sets in a ColorPicker control.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorPickerHsvChannel.Alpha">
      <summary>The slider controls the Alpha channel.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorPickerHsvChannel.Hue">
      <summary>The slider controls the Hue channel.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorPickerHsvChannel.Saturation">
      <summary>The slider controls the Saturation channel.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorPickerHsvChannel.Value">
      <summary>The slider controls the Value channel.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents">
      <summary>Defines constants that specify how the Hue-Saturation-Value (HSV) color components are mapped onto the ColorSpectrum.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.HueSaturation">
      <summary>Hue is mapped to the X axis. Saturation is mapped to the Y axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.HueValue">
      <summary>Hue is mapped to the X axis. Value is mapped to the Y axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.SaturationHue">
      <summary>Saturation is mapped to the X axis. Hue is mapped to the Y axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.SaturationValue">
      <summary>Saturation is mapped to the X axis. Value is mapped to the Y axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.ValueHue">
      <summary>Value is mapped to the X axis. Hue is mapped to the Y axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumComponents.ValueSaturation">
      <summary>Value is mapped to the X axis. Saturation is mapped to the Y axis.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ColorSpectrumShape">
      <summary>Defines constants that specify how the ColorSpectrum control is shown.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumShape.Box">
      <summary>The ColorSpectrum control is shown as a square.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ColorSpectrumShape.Ring">
      <summary>The ColorSpectrum control is shown as a circle.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.CommandBarFlyout">
      <summary>Represents a specialized flyout that provides layout for AppBarButton controls.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.CommandBarFlyout.#ctor">
      <summary>Initializes a new instance of the CommandBarFlyout class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.CommandBarFlyout.AlwaysExpanded">
      <summary>Gets or sets a value that indicates whether or not the CommandBarFlyout should always stay in its Expanded state and block the user from entering the Collapsed state. Defaults to false.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.CommandBarFlyout.PrimaryCommands">
      <summary>Gets the collection of primary command elements for the CommandBarFlyout.</summary>
      <returns>The collection of primary command elements for the CommandBarFlyout. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.CommandBarFlyout.SecondaryCommands">
      <summary>Gets the collection of secondary command elements for the CommandBarFlyout.</summary>
      <returns>The collection of secondary command elements for the CommandBarFlyout. The default is an empty collection.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ControlsResourcesVersion" />
    <member name="F:Microsoft.UI.Xaml.Controls.ControlsResourcesVersion.Version1" />
    <member name="F:Microsoft.UI.Xaml.Controls.ControlsResourcesVersion.Version2" />
    <member name="T:Microsoft.UI.Xaml.Controls.DropDownButton">
      <summary>Represents a button that includes a chevron to indicate a menu can be opened.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.DropDownButton.#ctor">
      <summary>Initializes a new instance of the DropDownButton class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ElementFactoryGetArgs">
      <summary>Represents the optional arguments to use when calling an implementation of the GetElement object.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ElementFactoryGetArgs.#ctor">
      <summary>Initializes a new instance of the ElementFactoryGetArgs class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ElementFactoryGetArgs.Data">
      <summary>Gets or sets the data item to prepare an element for.</summary>
      <returns>The data item to prepare an element for.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ElementFactoryGetArgs.Parent">
      <summary>Gets or sets the parent of the prepared element.</summary>
      <returns>The parent of the prepared element.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ElementFactoryRecycleArgs">
      <summary>Represents the optional arguments to use when calling an implementation of the RecycleElement object.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ElementFactoryRecycleArgs.#ctor">
      <summary>Initializes a new instance of the ElementFactoryRecycleArgs class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ElementFactoryRecycleArgs.Element">
      <summary>Gets or sets the element to recycle.</summary>
      <returns>The element to recycle.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ElementFactoryRecycleArgs.Parent">
      <summary>Gets or sets the parent of the recycled element.</summary>
      <returns>The parent of the recycled element.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ElementRealizationOptions">
      <summary>Defines constants that specify whether to suppress automatic recycling of the retrieved element or force creation of a new element.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ElementRealizationOptions.ForceCreate">
      <summary>Creation of a new element is forced.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ElementRealizationOptions.None">
      <summary>No option is specified.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ElementRealizationOptions.SuppressAutoRecycle">
      <summary>The element is ignored by the automatic recycling logic.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ExpandDirection">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Defines constants that specify in which direction a control should expand.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ExpandDirection.Down">
      <summary>The content area expands downwards from the header.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ExpandDirection.Up">
      <summary>The content area expands upwards from the header.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Expander">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Represents a control that displays a header and has a collapsible body that displays content.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.Expander.Collapsed">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Occurs when the content area of the Expander is hidden.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.Expander.Expanding">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Occurs when the content area of the Expander starts to be shown.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Expander.#ctor">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Initializes a new instance of the Expander class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.ExpandDirection">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets or sets a value that indicates the direction in which the content area expands.</summary>
      <returns>A value of the enumeration that indicates the direction in which the content area expands. The default is Down.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.ExpandDirectionProperty">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Identifies the ExpandDirection dependency property.</summary>
      <returns>The identifier for the ExpandDirection dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.Header">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets or sets the XAML content that is displayed in the header of the Expander.</summary>
      <returns>The XAML content that is displayed in the header.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.HeaderProperty">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Identifies the Header dependency property.</summary>
      <returns>The identifier for the Header dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.HeaderTemplate">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets or sets the data template for the Expander.Header".</summary>
      <returns>The data template that is used to display the header of the Expander.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.HeaderTemplateProperty">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Identifies the HeaderTemplate dependency property.</summary>
      <returns>The identifier for the HeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.HeaderTemplateSelector">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets or sets a reference to a custom DataTemplateSelector logic class that returns a template to apply to the Header.</summary>
      <returns>A reference to a custom DataTemplateSelector logic class.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.HeaderTemplateSelectorProperty">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Identifies the HeaderTemplateSelector dependency property.</summary>
      <returns>The identifier for the HeaderTemplateSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.IsExpanded">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets or sets a value that indicates whether the content area of the Expander is shown.</summary>
      <returns>true if the content area is shown; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.IsExpandedProperty">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Identifies the IsExpanded dependency property.</summary>
      <returns>The identifier for the IsExpanded dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Expander.TemplateSettings">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets an object that provides calculated values that can be referenced as TemplatedParent sources when defining templates for an Expander.</summary>
      <returns>An instance of ExpanderTemplateSettings.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ExpanderCollapsedEventArgs">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Provides data for the Expander.Collapsed" event.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ExpanderExpandingEventArgs">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Provides data for the Expander.Expanding" event.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Provides calculated values that can be referenced as TemplatedParent sources when defining templates for an Expander. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings.ContentHeight">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets the height of the Expander content.</summary>
      <returns>The height of the Expander content.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ExpanderTemplateSettings.NegativeContentHeight">
      <summary>::: moniker range="winui-3.0-preview"
&gt; [!CAUTION]
&gt; This API is in development and considered experimental in the WinUI version selected. It might be altered or unavailable in other versions.
::: moniker-end

Gets the height of the Expander content when the expand direction is negative.</summary>
      <returns>The height of the Expander content when the expand direction is negative.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.FontIconSource">
      <summary>Represents an icon source that uses a glyph from the specified font.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.FontIconSource.#ctor">
      <summary>Initializes a new instance of the FontIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontFamily">
      <summary>Gets or sets the font used to display the icon glyph.</summary>
      <returns>The font used to display the icon glyph. The default is the font family defined by the SymbolThemeFontFamily theme resource (see Remarks).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontFamilyProperty">
      <summary>Gets the identifier for the FontFamily dependency property.</summary>
      <returns>The identifier for the FontFamily dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontSize">
      <summary>Gets or sets the size of the icon glyph.</summary>
      <returns>A non-negative value that specifies the font size, measured in pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontSizeProperty">
      <summary>Gets the identifier for the FontSize dependency property.</summary>
      <returns>The identifier for the FontSize dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontStyle">
      <summary>Gets or sets the font style for the icon glyph.</summary>
      <returns>A named constant of the enumeration that specifies the style in which the icon glyph is rendered. The default is Normal.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontStyleProperty">
      <summary>Gets the identifier for the FontStyle dependency property.</summary>
      <returns>The identifier for the FontStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontWeight">
      <summary>Gets or sets the thickness of the icon glyph.</summary>
      <returns>A value that specifies the thickness of the icon glyph. The default is Normal.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.FontWeightProperty">
      <summary>Gets the identifier for the FontWeight dependency property.</summary>
      <returns>The identifier for the FontWeight dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.Glyph">
      <summary>Gets or sets the character code that identifies the icon glyph.</summary>
      <returns>The hexadecimal character code for the icon glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.GlyphProperty">
      <summary>Gets the identifier for the Glyph dependency property.</summary>
      <returns>The identifier for the Glyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.IsTextScaleFactorEnabled">
      <summary>Gets or sets a value that indicates whether automatic text enlargement, to reflect the system text size setting, is enabled.</summary>
      <returns>true if automatic text enlargement is enabled; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.IsTextScaleFactorEnabledProperty">
      <summary>Gets the identifier for the IsTextScaleFactorEnabled dependency property.</summary>
      <returns>The identifier for the IsTextScaleFactorEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.MirroredWhenRightToLeft">
      <summary>Gets or sets a value that indicates whether the icon is mirrored when its containing element's FlowDirection is RightToLeft.</summary>
      <returns>true if the icon is mirrored when the FlowDirection; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.FontIconSource.MirroredWhenRightToLeftProperty">
      <summary>Gets the identifier for the MirroredWhenRightToLeft dependency property.</summary>
      <returns>The identifier for the MirroredWhenRightToLeft dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IAnimatedVisual">
      <summary>An animated Composition.Visual" that can be used by other objects, such as an AnimatedVisualPlayer or AnimatedIcon.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IAnimatedVisual.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.IAnimatedVisual.Duration">
      <summary>Gets the duration of the animated visual.</summary>
      <returns>The size of the animated visual.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.IAnimatedVisual.RootVisual">
      <summary>Gets the root Visual of the animated visual.</summary>
      <returns>The root Visual of the animated visual.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.IAnimatedVisual.Size">
      <summary>Gets the size of the animated visual.</summary>
      <returns>The size of the animated visual.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IAnimatedVisualSource">
      <summary>An animated Visual that can be used by other objects, such as an AnimatedVisualPlayer.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IAnimatedVisualSource.TryCreateAnimatedVisual(Windows.UI.Composition.Compositor,System.Object@)">
      <summary>Attempts to create an instance of an animated visual.</summary>
      <param name="compositor">The Compositor that will be used to create objects for the animated visual.</param>
      <param name="diagnostics">An optional object containing diagnostics information about the result. The type and contents of the object depend on the implementation of IAnimatedVisualSource.</param>
      <returns>An IAnimatedVisual, or null.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2">
      <summary>An animated Visual that can be used by other objects, such as an AnimatedIcon.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2.SetColorProperty(System.String,Windows.UI.Color)">
      <summary>Sets a color for the animated visual.</summary>
      <param name="propertyName">The property name of the color as defined in the JSON file for the animated icon.</param>
      <param name="value">The color value for the propertyName.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.IAnimatedVisualSource2.Markers">
      <summary>Gets a collection that provides a mapping of marker names to playback positions in the animation.</summary>
      <returns>The collection of marker names and values defined in the JSON file for the animated icon.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IconSource">
      <summary>Represents the base class for an icon source.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IconSource.CreateIconElement" />
    <member name="P:Microsoft.UI.Xaml.Controls.IconSource.Foreground">
      <summary>Gets or sets a brush that describes the foreground color.</summary>
      <returns>The brush that paints the foreground of the control. The default is null, (a null brush) which is evaluated as Transparent for rendering. However, this value is typically set by a default system resource at runtime, which is tied to the active theme and other settings.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.IconSource.ForegroundProperty">
      <summary>Identifies the Foreground dependency property.</summary>
      <returns>The identifier for the Foreground dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IDynamicAnimatedVisualSource">
      <summary>When implemented by an IAnimatedVisualSource, indicates to the player that the current animated visual should be discarded.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.IDynamicAnimatedVisualSource.AnimatedVisualInvalidated">
      <summary>Occurs when the animated visual previously provided by the IDynamicAnimatedVisualSource should be discarded.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.IKeyIndexMapping">
      <summary>Provides methods that support mapping between an item's unique identifier and index.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IKeyIndexMapping.IndexFromKey(System.String)">
      <summary>Retrieves the index of the item that has the specified unique identifier (key).</summary>
      <param name="key">The unique identifier (key) of the item to find the index of.</param>
      <returns>The index of the item with the specified _key_.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.IKeyIndexMapping.KeyFromIndex(System.Int32)">
      <summary>Retrieves the unique identifier (key) for the item at the specified index.</summary>
      <param name="index">The index of the item to get the key for.</param>
      <returns>The unique identifier (key) for the item at the specified _index_.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ImageIcon">
      <summary>Represents an icon that uses an Image as its content.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ImageIcon.#ctor">
      <summary>Initializes a new instance of the ImageIcon class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ImageIcon.Source">
      <summary>Gets or sets the URI of the image file to use as the icon.</summary>
      <returns>The URI of the image file to use as the icon. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ImageIcon.SourceProperty">
      <summary>Identifies the Source dependency property.</summary>
      <returns>The identifier for the Source dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ImageIconSource">
      <summary>Represents an icon source that uses an image type as its content. The image types currently supported are .bmp, .gif, .jpg, .png, .wdp, and .tiff.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ImageIconSource.#ctor">
      <summary>Initializes a new instance of the ImageIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ImageIconSource.ImageSource">
      <summary>Gets or sets the URI of the image file to use as the icon source.</summary>
      <returns>The URI of the image file to use as the icon source. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ImageIconSource.ImageSourceProperty">
      <summary>Identifies the ImageSource dependency property.</summary>
      <returns>The identifier for the ImageSource dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBadge" />
    <member name="M:Microsoft.UI.Xaml.Controls.InfoBadge.#ctor" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.IconSource" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.IconSourceProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.TemplateSettings" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.TemplateSettingsProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.Value" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadge.ValueProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings" />
    <member name="M:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings.#ctor" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings.IconElement" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings.IconElementProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings.InfoBadgeCornerRadius" />
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings.InfoBadgeCornerRadiusProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBar">
      <summary>An InfoBar is an inline notification for essential app-wide messages. The InfoBar will take up space in a layout and will not cover up other content or float on top of it. It supports rich content (including titles, messages, icons, and buttons) and can be configured to be user-dismissable or persistent.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonClick">
      <summary>Occurs after the close button is clicked in the InfoBar.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.InfoBar.Closed">
      <summary>Occurs after the InfoBar is closed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.InfoBar.Closing">
      <summary>Occurs just before the InfoBar begins to close.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.InfoBar.#ctor">
      <summary>Initializes a new instance of the InfoBar class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.ActionButton">
      <summary>Gets or sets the action button of the InfoBar.</summary>
      <returns>The action button of the InfoBar. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.ActionButtonProperty">
      <summary>Identifies the ActionButton dependency property.</summary>
      <returns>The identifier for the ActionButton dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonCommand">
      <summary>Gets or sets the command to invoke when the close button is clicked in the InfoBar.</summary>
      <returns>The command to invoke when the close button is clicked in the InfoBar. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonCommandParameter">
      <summary>Gets or sets the parameter to pass to the command for the close button in the InfoBar.</summary>
      <returns>The parameter to pass to the command for the close button in the InfoBar. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonCommandParameterProperty">
      <summary>Identifies the CloseButtonCommandParameter dependency property.</summary>
      <returns>The identifier for the CloseButtonCommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonCommandProperty">
      <summary>Identifies the CloseButtonCommand dependency property.</summary>
      <returns>The identifier for the CloseButtonCommand dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonStyle">
      <summary>Gets or sets the Style to apply to the close button in the InfoBar.</summary>
      <returns>The Style to apply to the close button in the InfoBar.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.CloseButtonStyleProperty">
      <summary>Identifies the CloseButtonStyle dependency property.</summary>
      <returns>The identifier for the CloseButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.Content">
      <summary>Gets or sets the XAML Content that is displayed below the title and message in the InfoBar.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.ContentProperty">
      <summary>Identifies the Content dependency property.</summary>
      <returns>The identifier for the Content dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.ContentTemplate">
      <summary>Gets or sets the data template for the InfoBar.Content".</summary>
      <returns>The data template that is used to display the content of the InfoBar.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.ContentTemplateProperty">
      <summary>Identifies the ContentTemplate dependency property.</summary>
      <returns>The identifier for the ContentTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IconSource">
      <summary>Gets or sets the graphic content to appear alongside the title and message in the InfoBar.</summary>
      <returns>The graphic content to appear alongside the title and message in the InfoBar.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IconSourceProperty">
      <summary>Identifies the IconSource dependency property.</summary>
      <returns>The identifier for the IconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsClosable">
      <summary>Gets or sets a value that indicates whether the user can close the InfoBar.</summary>
      <returns>true if the user can close the InfoBar; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsClosableProperty">
      <summary>Identifies the IsClosable dependency property.</summary>
      <returns>The identifier for the IsClosable dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsIconVisible">
      <summary>Gets or sets a value that indicates whether the icon is visible in the InfoBar.</summary>
      <returns>true if the icon is visible; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsIconVisibleProperty">
      <summary>Identifies the IsIconVisible dependency property.</summary>
      <returns>The identifier for the IsIconVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsOpen">
      <summary>Gets or sets a value that indicates whether the InfoBar is open.</summary>
      <returns>true if the InfoBar is open; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.IsOpenProperty">
      <summary>Identifies the IsOpen dependency property.</summary>
      <returns>The identifier for the IsOpen dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.Message">
      <summary>Gets or sets the message of the InfoBar.</summary>
      <returns>The message of the InfoBar. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.MessageProperty">
      <summary>Identifies the Message dependency property.</summary>
      <returns>The identifier for the Message dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.Severity">
      <summary>Gets or sets the type of the InfoBar to apply consistent status color, icon, and assistive technology settings dependent on the criticality of the notification.</summary>
      <returns>The style of the InfoBar that indicates the criticality of the notification. The default is Informational</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.SeverityProperty">
      <summary>Identifies the Severity dependency property.</summary>
      <returns>The identifier for the Severity dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.TemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for an InfoBar. Not intended for general use.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.TemplateSettingsProperty">
      <summary>Identifies the TemplateSettings dependency property.</summary>
      <returns>The identifier for the TemplateSettings dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.Title">
      <summary>Gets or sets the title of the InfoBar.</summary>
      <returns>The title of the InfoBar. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBar.TitleProperty">
      <summary>Identifies the Title dependency property.</summary>
      <returns>The identifier for the Title dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBarClosedEventArgs">
      <summary>Provides data for the InfoBar.Closed" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBarClosedEventArgs.Reason">
      <summary>Gets a constant that specifies whether the cause of the Closed event was due to user interaction (Close button click) or programmatic closure.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBarCloseReason">
      <summary>Defines constants that indicate the cause of the InfoBar closure.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarCloseReason.CloseButton">
      <summary>The InfoBar was closed by the user clicking the close button.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarCloseReason.Programmatic">
      <summary>The InfoBar was programmatically closed.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBarClosingEventArgs">
      <summary>Provides data for the InfoBar.Closing event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBarClosingEventArgs.Cancel">
      <summary>Gets or sets a value that indicates whether the Closing event should be canceled in the InfoBar.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBarClosingEventArgs.Reason">
      <summary>Gets a constant that specifies whether the cause of the Closing event was due to user interaction (Close button click) or programmatic closure.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBarSeverity">
      <summary>Defines constants that indicate the criticality of the InfoBar that is shown.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarSeverity.Error">
      <summary>Communicates that the InfoBar is displaying information regarding an error or problem that has occurred. For assistive technologies, they will follow the behavior set in the NotificationProcessing_ImportantAll constant.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarSeverity.Informational">
      <summary>Communicates that the InfoBar is displaying general information that requires the user's attention. For assistive technologies, they will follow the behavior set in the Processing_All constant.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarSeverity.Success">
      <summary>Communicates that the InfoBar is displaying information regarding a long-running and/or background task that has completed successfully. For assistive technologies, they will follow the behavior set in the Processing_All constant.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.InfoBarSeverity.Warning">
      <summary>Communicates that the InfoBar is displaying information regarding a condition that might cause a problem in the future. For assistive technologies, they will follow the behavior set in the NotificationProcessing_ImportantAll constant.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a InfoBar.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings.#ctor">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a InfoBar.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings.IconElement">
      <summary>Gets the icon element.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings.IconElementProperty">
      <summary>Identifies the InfoBarTemplateSettings.IconElement" dependency property.</summary>
      <returns>The identifier for the InfoBarTemplateSettings.IconElement" dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsRepeater">
      <summary>Represents a data-driven collection control that incorporates a flexible layout system, custom views, and virtualization, with no default UI or interaction policies.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ItemsRepeater.ElementClearing">
      <summary>Occurs each time an element is cleared and made available to be re-used.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ItemsRepeater.ElementIndexChanged">
      <summary>Occurs for each realized UIElement when the index for the item it represents has changed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ItemsRepeater.ElementPrepared">
      <summary>Occurs each time an element is prepared for use.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsRepeater.#ctor">
      <summary>Initializes a new instance of the ItemsRepeater class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsRepeater.GetElementIndex(Windows.UI.Xaml.UIElement)">
      <summary>Retrieves the index of the item from the data source that corresponds to the specified UIElement.</summary>
      <param name="element">The element that corresponds to the item to get the index of.</param>
      <returns>The index of the item from the data source that corresponds to the specified UIElement, or -1 if the element is not supported.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsRepeater.GetOrCreateElement(System.Int32)">
      <summary>Retrieves the UIElement that corresponds to the item at the specified index in the data source.</summary>
      <param name="index">The index of the item.</param>
      <returns>A UIElement that corresponds to the item at the specified index. If the item is not realized, a new UIElement is created.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsRepeater.TryGetElement(System.Int32)">
      <summary>Retrieves the realized UIElement that corresponds to the item at the specified index in the data source.</summary>
      <param name="index">The index of the item.</param>
      <returns>The UIElement that corresponds to the item at the specified index if the item is realized, or null if the item is not realized.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.Background">
      <summary>Gets or sets a brush that provides the background of the control.</summary>
      <returns>The brush that provides the background of the control. The default is null, (a null brush) which is evaluated as Transparent for rendering.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.BackgroundProperty">
      <summary>Identifies the Background dependency property.</summary>
      <returns>The identifier for the Background dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.HorizontalCacheLength">
      <summary>Gets or sets a value that indicates the size of the buffer used to realize items when panning or scrolling horizontally.</summary>
      <returns>A non-negative value that indicates the size of the buffer as a multiple of the viewport size. The default value is determined by the system.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.HorizontalCacheLengthProperty">
      <summary>Identifies the HorizontalCacheLength dependency property.</summary>
      <returns>The identifier for the HorizontalCacheLength dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.ItemsSource">
      <summary>Gets or sets an object source used to generate the content of the ItemsRepeater.</summary>
      <returns>The object that is used to generate the content of the ItemsRepeater. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.ItemsSourceProperty">
      <summary>Identifies the ItemsSource dependency property.</summary>
      <returns>The identifier for the ItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.ItemsSourceView">
      <summary>Gets a standardized view of the supported interactions between a given ItemsSource object and the ItemsRepeater control and its associated components.</summary>
      <returns>A standardized view of the supported interactions between an ItemsSource object and the ItemsRepeater.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.ItemTemplate">
      <summary>Gets or sets the template used to display each item.</summary>
      <returns>The template that specifies the visualization of the data objects. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.ItemTemplateProperty">
      <summary>Identifies the ItemTemplate dependency property.</summary>
      <returns>The identifier for the ItemTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.Layout">
      <summary>Gets or sets the layout used to size and position elements in the ItemsRepeater.</summary>
      <returns>The layout used to size and position elements. The default is a StackLayout with vertical orientation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.LayoutProperty">
      <summary>Identifies the Layout dependency property.</summary>
      <returns>The identifier for the Layout dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.VerticalCacheLength">
      <summary>Gets or sets a value that indicates the size of the buffer used to realize items when panning or scrolling vertically.</summary>
      <returns>A non-negative value that indicates the size of the buffer as a multiple of the viewport size. The default value is determined by the system.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeater.VerticalCacheLengthProperty">
      <summary>Identifies the VerticalCacheLength dependency property.</summary>
      <returns>The identifier for the VerticalCacheLength dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementClearingEventArgs">
      <summary>Provides data for the ItemsRepeater.ElementClearing event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementClearingEventArgs.Element">
      <summary>Gets the element that is being cleared for re-use.</summary>
      <returns>The element that is being cleared.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementIndexChangedEventArgs">
      <summary>Provides data for the ItemsRepeater.ElementIndexChanged event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementIndexChangedEventArgs.Element">
      <summary>Get the element for which the index changed.</summary>
      <returns>The element for which the index changed.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementIndexChangedEventArgs.NewIndex">
      <summary>Gets the index of the element after the change.</summary>
      <returns>The index of the element after the change.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementIndexChangedEventArgs.OldIndex">
      <summary>Gets the index of the element before the change.</summary>
      <returns>The index of the element before the change.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementPreparedEventArgs">
      <summary>Provides data for the ItemsRepeater.ElementPrepared event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementPreparedEventArgs.Element">
      <summary>Gets the prepared element.</summary>
      <returns>The prepared element.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterElementPreparedEventArgs.Index">
      <summary>Gets the index of the item the element was prepared for.</summary>
      <returns>The index of the item the element was prepared for.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost">
      <summary>A helper to coordinate interaction between the ItemsRepeater and ScrollViewer.
Use the ItemsRepeaterScrollHost if your app will run on versions of Windows prior Windows 10 1809 (Build 17763).  If your app will only run on versions of Windows 1809 or higher, there is no need to use this control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost.#ctor">
      <summary>Initializes a new instance of the ItemsRepeaterScrollHost class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost.CurrentAnchor">
      <summary>The currently chosen anchor element to use for scroll anchoring.</summary>
      <returns>The most recently chosen UIElement for scroll anchoring after a layout pass, or null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost.HorizontalAnchorRatio">
      <summary>Determines the horizontal position of the ScrollViewer's _anchor point_ with respect to the viewport. By default, the ScrollViewer selects an element as its CurrentAnchor by identifying the element in its viewport nearest to the anchor point.</summary>
      <returns>A normalized value (0.0 to 1.0). The default is 0.0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost.ScrollViewer">
      <summary>Gets or sets the ScrollViewer to host.</summary>
      <returns>A ScrollViewer that contains one or more ItemsRepeater controls.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost.VerticalAnchorRatio">
      <summary>Determines the vertical position of the ScrollViewer's _anchor point_ with respect to the viewport. By default, the ScrollViewer selects an element as its CurrentAnchor by identifying the element in its viewport nearest to the anchor point.</summary>
      <returns>A normalized value (0.0 to 1.0). The default is 0.0.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ItemsSourceView">
      <summary>Represents a standardized view of the supported interactions between a given ItemsSource object and an ItemsRepeater control.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ItemsSourceView.CollectionChanged">
      <summary>Occurs when the collection has changed to indicate the reason for the change and which items changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsSourceView.#ctor(System.Object)">
      <summary>Initializes a new instance of the ItemsSourceView class for the specified data source.</summary>
      <param name="source">The data source for this view.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsSourceView.GetAt(System.Int32)">
      <summary>Retrieves the item at the specified index.</summary>
      <param name="index">The index of the item to retrieve.</param>
      <returns>The item at the specified index.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsSourceView.IndexFromKey(System.String)">
      <summary>Retrieves the index of the item that has the specified unique identifier (key).</summary>
      <param name="key">The unique identifier (key) of the item to find the index of.</param>
      <returns>The index of the item with the specified _key_.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsSourceView.IndexOf(System.Object)">
      <summary>Retrieves the index of the specified item.</summary>
      <param name="item">The object to find in the collection.</param>
      <returns>The index of the item to find, if found.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ItemsSourceView.KeyFromIndex(System.Int32)">
      <summary>Retrieves the unique identifier (key) for the item at the specified index.</summary>
      <param name="index">The index of the item to get the key for.</param>
      <returns>The unique identifier (key) for the item at the specified _index_.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsSourceView.Count">
      <summary>Gets the number of items in the collection.</summary>
      <returns>The number of items in the collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ItemsSourceView.HasKeyIndexMapping">
      <summary>Gets a value that indicates whether the items source can provide a unique key for each item.</summary>
      <returns>true if the items source can provide a unique key for each item; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Layout">
      <summary>Represents the base class for an object that sizes and arranges child elements for a host.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.Layout.ArrangeInvalidated">
      <summary>Occurs when the arrange state (layout) has been invalidated.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.Layout.MeasureInvalidated">
      <summary>Occurs when the measurement state (layout) has been invalidated.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.Arrange(Microsoft.UI.Xaml.Controls.LayoutContext,Windows.Foundation.Size)">
      <summary>Positions child elements and determines a size for a container UIElement. Container elements that support attached layouts should call this method from their layout override implementations to form a recursive layout update.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="finalSize">The final size that the container computes for the child in layout.</param>
      <returns>The actual size that is used after the element is arranged in layout.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.InitializeForContext(Microsoft.UI.Xaml.Controls.LayoutContext)">
      <summary>Initializes any per-container state the layout requires when it is attached to a UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.InvalidateArrange">
      <summary>Invalidates the arrange state (layout) for all UIElement containers that reference this layout. After the invalidation, the UIElement will have its layout updated, which occurs asynchronously.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.InvalidateMeasure">
      <summary>Invalidates the measurement state (layout) for all UIElement containers that reference this layout.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.Measure(Microsoft.UI.Xaml.Controls.LayoutContext,Windows.Foundation.Size)">
      <summary>Suggests a DesiredSize for a container element. A container element that supports attached layouts should call this method from their own MeasureOverride implementations to form a recursive layout update. The attached layout is expected to call the Measure for each of the container’s UIElement children.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="availableSize">The available space that a container can allocate to a child object. A child object can request a larger space than what is available; the provided size might be accommodated if scrolling or other resize behavior is possible in that particular container.</param>
      <returns>The size that this object determines it needs during layout, based on its calculations of the allocated sizes for child objects or based on other considerations such as a fixed container size.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Layout.UninitializeForContext(Microsoft.UI.Xaml.Controls.LayoutContext)">
      <summary>Removes any state the layout previously stored on the UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.LayoutContext">
      <summary>Represents the base class for an object that facilitates communication between an attached layout and its host container.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.LayoutContext.LayoutState">
      <summary>Gets or sets an object that represents the state of a layout.</summary>
      <returns>An object that represents the state of a layout.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.LayoutContext.LayoutStateCore">
      <summary>Implements the behavior of LayoutState in a derived or custom LayoutContext.</summary>
      <returns>The value that should be returned as LayoutState by the LayoutContext.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.MenuBar">
      <summary>Represents a specialized container that presents a set of menus in a horizontal row, typically at the top of an app window.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.MenuBar.#ctor">
      <summary>Initializes a new instance of the MenuBar class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBar.Items">
      <summary>Gets the collection of top-level menu items.</summary>
      <returns>The collection of top-level menu items.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBar.ItemsProperty">
      <summary>Identifies the Items dependency property.</summary>
      <returns>The identifier for the Items dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.MenuBarItem">
      <summary>Represents a top-level menu in a MenuBar control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.MenuBarItem.#ctor">
      <summary>Initializes a new instance of the MenuBarItem class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBarItem.Items">
      <summary>Gets the collection of commands in a MenuBar menu.</summary>
      <returns>The collection of commands in a MenuBar menu.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBarItem.ItemsProperty">
      <summary>Identifies the Items dependency property.</summary>
      <returns>The identifier for the Items dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBarItem.Title">
      <summary>Gets or sets the text label for a MenuBar menu.</summary>
      <returns>The text label for this menu. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.MenuBarItem.TitleProperty">
      <summary>Identifies the Title dependency property.</summary>
      <returns>The identifier for the Title dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.MenuBarItemFlyout">
      <summary>Represents the flyout of a MenuBarItem.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.MenuBarItemFlyout.#ctor">
      <summary>Initializes a new instance of the MenuBarItemFlyout class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationView">
      <summary>Represents a container that enables navigation of app content. It has a header, a view for the main content, and a menu pane for navigation commands.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.BackRequested">
      <summary>Occurs when the back button receives an interaction such as a click or tap.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.Collapsed">
      <summary>Occurs when a node in the tree is collapsed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.DisplayModeChanged">
      <summary>Occurs when the DisplayMode property changes.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.Expanding">
      <summary>Occurs when a node in the tree starts to expand.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.ItemInvoked">
      <summary>Occurs when an item in the menu receives an interaction such a a click or tap.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.PaneClosed">
      <summary>Occurs when the NavigationView pane is closed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.PaneClosing">
      <summary>Occurs when the NavigationView pane is closing.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.PaneOpened">
      <summary>Occurs when the NavigationView pane is opened.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.PaneOpening">
      <summary>Occurs when the NavigationView pane is opening.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NavigationView.SelectionChanged">
      <summary>Occurs when the currently selected item changes.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationView.#ctor">
      <summary>Initializes a new instance of the NavigationView class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationView.Collapse(Microsoft.UI.Xaml.Controls.NavigationViewItem)">
      <summary>Collapses the specified node in the tree.</summary>
      <param name="item">The node to be collapsed.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationView.ContainerFromMenuItem(System.Object)">
      <summary>Returns the container corresponding to the specified menu item.</summary>
      <param name="item">The menu item to retrieve the container for.</param>
      <returns>A container that corresponds to the specified menu item, if the item has a container and exists in the collection; otherwise, null.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationView.Expand(Microsoft.UI.Xaml.Controls.NavigationViewItem)">
      <summary>Expands the specified node in the tree.</summary>
      <param name="item">The node to be expanded.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemFromContainer(Windows.UI.Xaml.DependencyObject)">
      <summary>Returns the item that corresponds to the specified, generated container.</summary>
      <param name="container">The DependencyObject that corresponds to the item to be returned.</param>
      <returns>The contained item, or the container if it does not contain an item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.AlwaysShowHeader">
      <summary>Gets or sets a value that indicates whether the header is always visible.</summary>
      <returns>true if the header is always visible; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.AlwaysShowHeaderProperty">
      <summary>Identifies the AlwaysShowHeader dependency property.</summary>
      <returns>The identifier for the AlwaysShowHeader dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.AutoSuggestBox">
      <summary>Gets or sets an AutoSuggestBox to be displayed in the NavigationView.</summary>
      <returns>An AutoSuggestBox box to be displayed in the NavigationView.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.AutoSuggestBoxProperty">
      <summary>Identifies the AutoSuggestBox dependency property.</summary>
      <returns>The identifier for the AutoSuggestBox dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.CompactModeThresholdWidth">
      <summary>Gets or sets the minimum window width at which the NavigationView enters Compact display mode.</summary>
      <returns>The minimum window width at which the NavigationView enters Compact display mode. The default is 641 pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.CompactModeThresholdWidthProperty">
      <summary>Identifies the CompactModeThresholdWidth dependency property.</summary>
      <returns>The identifier for the CompactModeThresholdWidth dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.CompactPaneLength">
      <summary>Gets or sets the width of the NavigationView pane in its compact display mode.</summary>
      <returns>The width of the pane in its compact display mode. The default is 48 device-independent pixel (DIP).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.CompactPaneLengthProperty">
      <summary>Identifies the CompactPaneLength dependency property.</summary>
      <returns>The identifier for the CompactPaneLength dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ContentOverlay">
      <summary>Gets or sets a UI element that is shown at the top of the control, below the pane if PaneDisplayMode is Top.</summary>
      <returns>The element that is shown at the top of the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ContentOverlayProperty">
      <summary>Identifies the ContentOverlay dependency property.</summary>
      <returns>The identifier for the ContentOverlay dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.DisplayMode">
      <summary>Gets a value that specifies how the pane and content areas of a NavigationView are being shown.</summary>
      <returns>A value of the enumeration that specifies how the pane and content areas of a NavigationView are being shown.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.DisplayModeProperty">
      <summary>Identifies the DisplayMode dependency property.</summary>
      <returns>The identifier for the DisplayMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ExpandedModeThresholdWidth">
      <summary>Gets or sets the minimum window width at which the NavigationView enters Expanded display mode.</summary>
      <returns>The minimum window width at which the NavigationView enters Expanded display mode. The default is 1008 pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ExpandedModeThresholdWidthProperty">
      <summary>Identifies the ExpandedModeThresholdWidth dependency property.</summary>
      <returns>The identifier for the ExpandedModeThresholdWidth dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItems">
      <summary>Gets the list of objects to be used as navigation items in the footer menu.</summary>
      <returns>The collection of menu items displayed in the footer section of the NavigationView. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItemsProperty">
      <summary>Identifies the FooterMenuItems dependency property.</summary>
      <returns>The identifier for the FooterMenuItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItemsSource">
      <summary>Gets or sets the object that represents the navigation items to be used in the footer menu.</summary>
      <returns>The object that is used to generate the content of the NavigationView FooterMenuItems. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.FooterMenuItemsSourceProperty">
      <summary>Identifies the FooterMenuItemsSource dependency property.</summary>
      <returns>The identifier for the FooterMenuItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.Header">
      <summary>Gets or sets the header content.</summary>
      <returns>The header content for the NavigationView.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.HeaderProperty">
      <summary>Identifies the Header dependency property.</summary>
      <returns>The identifier for the Header dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.HeaderTemplate">
      <summary>Gets or sets the DataTemplate used to display the control's header.</summary>
      <returns>The DataTemplate used to display the control's header.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.HeaderTemplateProperty">
      <summary>Identifies the HeaderTemplate dependency property.</summary>
      <returns>The identifier for the HeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsBackButtonVisible">
      <summary>Gets or sets a value that indicates whether the back button is visible or not. Default value is "Auto", which indicates that button visibility depends on the DisplayMode setting of the NavigationView.</summary>
      <returns>A value of the enumeration that specifies the visibility of the NavigationView back button. The default is "Auto".</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsBackButtonVisibleProperty">
      <summary>Identifies the IsBackButtonVisible dependency property.</summary>
      <returns>The identifier for the IsBackButtonVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsBackEnabled">
      <summary>Gets or sets a value that indicates whether the back button is enabled or disabled.</summary>
      <returns>true if the back button is enabled; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsBackEnabledProperty">
      <summary>Identifies the IsBackEnabled dependency property.</summary>
      <returns>The identifier for the IsBackEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneOpen">
      <summary>Gets or sets a value that specifies whether the NavigationView pane is expanded to its full width.</summary>
      <returns>true if the pane is expanded to its full width; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneOpenProperty">
      <summary>Identifies the IsPaneOpen dependency property.</summary>
      <returns>The identifier for the IsPaneOpen dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneToggleButtonVisible">
      <summary>Gets or sets a value that indicates whether the menu toggle button is shown.</summary>
      <returns>true if the menu button is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneToggleButtonVisibleProperty">
      <summary>Identifies the IsPaneToggleButtonVisible dependency property.</summary>
      <returns>The identifier for the IsPaneToggleButtonVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneVisible">
      <summary>Gets or sets a value that determines whether the pane is shown.</summary>
      <returns>true is the pane is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsPaneVisibleProperty">
      <summary>Identifies the IsPaneVisible dependency property.</summary>
      <returns>The identifier for the IsPaneVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsSettingsVisible">
      <summary>Gets or sets a value that indicates whether the settings button is shown.</summary>
      <returns>true if the settings button is shown; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsSettingsVisibleProperty">
      <summary>Identifies the IsSettingsVisible dependency property.</summary>
      <returns>The identifier for the IsSettingsVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsTitleBarAutoPaddingEnabled">
      <summary>Gets or sets a value that indicates whether top padding is added to the navigation view's header when used with a custom title bar.</summary>
      <returns>true if automatic padding is enabled; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.IsTitleBarAutoPaddingEnabledProperty">
      <summary>Identifies the IsTitleBarAutoPaddingEnabled dependency property.</summary>
      <returns>The identifier for the IsTitleBarAutoPaddingEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyle">
      <summary>Gets or sets the style that is used when rendering the menu item containers.</summary>
      <returns>The style applied to the item containers. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyleProperty">
      <summary>Identifies the MenuItemContainerStyle dependency property.</summary>
      <returns>The identifier for the MenuItemContainerStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyleSelector">
      <summary>Gets or sets a reference to a custom StyleSelector values to use for the item container based on characteristics of the object being displayed.</summary>
      <returns>A custom StyleSelector logic class.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemContainerStyleSelectorProperty">
      <summary>Identifies the MenuItemContainerStyleSelector dependency property.</summary>
      <returns>The identifier for the MenuItemContainerStyleSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItems">
      <summary>Gets the collection of menu items displayed in the NavigationView.</summary>
      <returns>The collection of menu items displayed in the NavigationView. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemsProperty">
      <summary>Identifies the MenuItems dependency property.</summary>
      <returns>The identifier for the MenuItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemsSource">
      <summary>Gets or sets an object source used to generate the content of the NavigationView menu.</summary>
      <returns>The object that is used to generate the content of the NavigationView menu. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemsSourceProperty">
      <summary>Identifies the MenuItemsSource dependency property.</summary>
      <returns>The identifier for the MenuItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplate">
      <summary>Gets or sets the DataTemplate used to display each menu item.</summary>
      <returns>The template that specifies the visualization of the menu data objects. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplateProperty">
      <summary>Identifies the MenuItemTemplate dependency property.</summary>
      <returns>The identifier for the MenuItemTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplateSelector">
      <summary>Gets or sets a reference to a custom DataTemplateSelector referenced by this property returns a template to apply to items.</summary>
      <returns>A reference to a custom DataTemplateSelector logic class.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.MenuItemTemplateSelectorProperty">
      <summary>Identifies the MenuItemTemplateSelector dependency property.</summary>
      <returns>The identifier for the MenuItemTemplateSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.OpenPaneLength">
      <summary>Gets or sets the width of the NavigationView pane when it's fully expanded.</summary>
      <returns>The width of the NavigationView pane when it's fully expanded. The default is 320 device-independent pixel (DIP).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.OpenPaneLengthProperty">
      <summary>Identifies the OpenPaneLength dependency property.</summary>
      <returns>The identifier for the OpenPaneLength dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.OverflowLabelMode">
      <summary>Gets or sets a value that indicates what text label is shown for the overflow menu.</summary>
      <returns>A value of the enumeration that indicates what text label is shown for the overflow menu. The default is MoreLabel, which shows the text "More".</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.OverflowLabelModeProperty">
      <summary>Identifies the OverflowLabelMode dependency property.</summary>
      <returns>The identifier for the OverflowLabelMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneCustomContent">
      <summary>Gets or sets a UI element that is shown in the NavigationView pane.</summary>
      <returns>The element that is shown in the NavigationView pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneCustomContentProperty">
      <summary>Identifies the PaneCustomContent dependency property.</summary>
      <returns>The identifier for the PaneCustomContent dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneDisplayMode">
      <summary>Gets or sets a value that indicates how and where the NavigationView pane is shown.</summary>
      <returns>A value of the enumeration that indicates how and where the NavigationView pane is shown. The default is Auto.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneDisplayModeProperty">
      <summary>Identifies the PaneDisplayMode dependency property.</summary>
      <returns>The identifier for the PaneDisplayMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneFooter">
      <summary>Gets or sets the content for the pane footer.</summary>
      <returns>The content of the pane footer. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneFooterProperty">
      <summary>Identifies the PaneFooter dependency property.</summary>
      <returns>The identifier for the PaneFooter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneHeader">
      <summary>Gets or sets the content for the pane header.</summary>
      <returns>The content of the pane header. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneHeaderProperty">
      <summary>Identifies the PaneHeader dependency property.</summary>
      <returns>The identifier for the PaneHeader dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneTitle">
      <summary>Gets or sets the label adjacent to the menu icon when the NavigationView pane is open.</summary>
      <returns>The label adjacent to the menu icon when the pane is open. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneTitleProperty">
      <summary>Identifies the PaneTitle dependency property.</summary>
      <returns>The identifier for the PaneTitle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneToggleButtonStyle">
      <summary>Gets or sets the Style that defines the look of the menu toggle button.</summary>
      <returns>The Style that defines the look of the menu toggle button. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.PaneToggleButtonStyleProperty">
      <summary>Identifies the PaneToggleButtonStyle dependency property.</summary>
      <returns>The identifier for the PaneToggleButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SelectedItem">
      <summary>Gets or sets the selected item.</summary>
      <returns>The selected item. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SelectedItemProperty">
      <summary>Identifies the SelectedItem dependency property.</summary>
      <returns>The identifier for the SelectedItem dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SelectionFollowsFocus">
      <summary>Gets or sets a value that indicates whether item selection changes when keyboard focus changes.</summary>
      <returns>A value of the enumeration that indicates whether selection changes when keyboard focus changes. The default is Disabled.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SelectionFollowsFocusProperty">
      <summary>Identifies the SelectionFollowsFocus dependency property.</summary>
      <returns>The identifier for the SelectionFollowsFocus dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SettingsItem">
      <summary>Gets the navigation item that represents the entry point to app settings.</summary>
      <returns>The item that represents the entry point to app settings.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.SettingsItemProperty">
      <summary>Identifies the SettingsItem dependency property.</summary>
      <returns>The identifier for the SettingsItem dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ShoulderNavigationEnabled">
      <summary>Gets or sets a value that indicates when gamepad bumpers can be used to navigate the top-level navigation items in a NavigationView.</summary>
      <returns>A value of the enumeration that indicates when gamepad bumpers can be used to navigate the top-level navigation items in a NavigationView. The default is Never.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.ShoulderNavigationEnabledProperty">
      <summary>Identifies the ShoulderNavigationEnabled dependency property.</summary>
      <returns>The identifier for the ShoulderNavigationEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.TemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as TemplateBinding sources when defining templates for a NavigationView control.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationView.TemplateSettingsProperty">
      <summary>Identifies the TemplateSettings dependency property.</summary>
      <returns>The identifier for the TemplateSettings dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible">
      <summary>Defines constants that specify whether the back button is visible in NavigationView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Auto">
      <summary>The system chooses whether or not to display the back button, depending on the device/form factor. On phones, tablets, desktops, and hubs, the back button is visible. On Xbox/TV, the back button is collapsed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Collapsed">
      <summary>Do not display the back button in NavigationView, and do not reserve space for it in layout.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewBackButtonVisible.Visible">
      <summary>Display the back button in NavigationView.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewBackRequestedEventArgs">
      <summary>Provides event data for the NavigationView.BackRequested" event.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode">
      <summary>Defines constants that specify how the pane is shown in a NavigationView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Compact">
      <summary>The pane always shows as a narrow sliver which can be opened to full width.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Expanded">
      <summary>The pane stays open alongside the content.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewDisplayMode.Minimal">
      <summary>Only the menu button remains fixed. The pane shows and hides as needed.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewDisplayModeChangedEventArgs">
      <summary>Provides data for the NavigationView.DisplayModeChanged" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewDisplayModeChangedEventArgs.DisplayMode">
      <summary>Gets the new display mode.</summary>
      <returns>The new display mode.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItem">
      <summary>Represents the container for an item in a NavigationView control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationViewItem.#ctor">
      <summary>Initializes a new instance of the NavigationViewItem class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.CompactPaneLength">
      <summary>Gets the CompactPaneLength of the NavigationView that hosts this item.</summary>
      <returns>The CompactPaneLength of the NavigationView that hosts this item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.CompactPaneLengthProperty">
      <summary>Identifies the CompactPaneLength dependency property.</summary>
      <returns>The identifier for the CompactPaneLength dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.HasUnrealizedChildren">
      <summary>Gets or sets a value that indicates whether the current item has child items that haven't been shown.</summary>
      <returns>true if the tree node is expanded; otherwise, false. The default value is false.

This property is analogous to the TreeViewItem.HasUnrealizedChildren property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.HasUnrealizedChildrenProperty">
      <summary>Identifies the HasUnrealizedChildren dependency property.</summary>
      <returns>The identifier for the HasUnrealizedChildren dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.Icon">
      <summary>Gets or sets the icon to show next to the menu item text.</summary>
      <returns>The icon to show next to the menu item text. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.IconProperty">
      <summary>Identifies the Icon dependency property.</summary>
      <returns>The identifier for the Icon dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.IsChildSelected">
      <summary>Gets or sets the value that indicates whether or not descendant item is selected.</summary>
      <returns>true if descendant item is currently selected, otherwise false. The default value is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.IsChildSelectedProperty">
      <summary>Identifies the IsChildSelected dependency property.</summary>
      <returns>The identifier for the IsChildSelected dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.IsExpanded">
      <summary>Gets or sets a value that indicates whether a tree node is expanded. Ignored if there are no menu items.</summary>
      <returns>true if the tree node is expanded; otherwise, false. The default value is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.IsExpandedProperty">
      <summary>Identifies the IsExpanded dependency property.</summary>
      <returns>The identifier for the IsExpanded dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.MenuItems">
      <summary>Gets the collection of menu items displayed as children of the NavigationViewItem.</summary>
      <returns>The collection of menu items displayed as children of the NavigationViewItem.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.MenuItemsProperty">
      <summary>Identifies the MenuItems dependency property.</summary>
      <returns>The identifier for the MenuItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.MenuItemsSource">
      <summary>Gets or sets an object source used to generate the children of the NavigationViewItem.</summary>
      <returns>The object source that holds the children of the NavigationViewItem.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.MenuItemsSourceProperty">
      <summary>Identifies the MenuItemsSource dependency property.</summary>
      <returns>The identifier for the MenuItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.SelectsOnInvoked">
      <summary>Gets or sets a value that indicates whether invoking a navigation menu item also selects it.</summary>
      <returns>true if invoking a navigation menu item also selects it; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItem.SelectsOnInvokedProperty">
      <summary>Identifies the SelectsOnInvoked dependency property.</summary>
      <returns>The identifier for the SelectsOnInvoked dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemBase">
      <summary>Base class for NavigationView menu items.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemBase.IsSelected">
      <summary>Gets or sets the value that indicates whether a NavigationViewItem is selected.</summary>
      <returns>true if item is currently selected, false otherwise.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemBase.IsSelectedProperty">
      <summary>Identifies the IsSelected dependency property.</summary>
      <returns>The identifier for the IsChildSelected dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemCollapsedEventArgs">
      <summary>Provides event data for the NavigationViewItem.Collapsed" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemCollapsedEventArgs.CollapsedItem">
      <summary>Gets the object that has been collapsed after the NavigationViewItem.Collapsed event.</summary>
      <returns>The object that has been collapsed.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemCollapsedEventArgs.CollapsedItemContainer">
      <summary>Gets the container of the object that was collapsed in the NavigationViewItem.Collapsed event.</summary>
      <returns>The container of the collapsed object.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemExpandingEventArgs">
      <summary>Provides event data for the NavigationViewItem.Expanding" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemExpandingEventArgs.ExpandingItem">
      <summary>Gets the object that is expanding after the NavigationViewItem.Expanding event.</summary>
      <returns>The expanding object.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemExpandingEventArgs.ExpandingItemContainer">
      <summary>Gets the container of the expanding item after a NavigationViewItem.Expanding event.</summary>
      <returns>The container of the expanding item.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemHeader">
      <summary>Represents a header for a group of menu items in a NavigationView.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationViewItemHeader.#ctor">
      <summary>Initializes a new instance of the NavigationViewItemHeader class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs">
      <summary>Provides event data for the NavigationView.ItemInvoked" event.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs.#ctor">
      <summary>Initializes a new instance of the NavigationViewItemInvokedEventArgs class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs.InvokedItem">
      <summary>Gets a reference to the invoked item.</summary>
      <returns>The invoked item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs.InvokedItemContainer">
      <summary>Gets the container for the invoked item.</summary>
      <returns>The container for the invoked item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs.IsSettingsInvoked">
      <summary>Gets a value that indicates whether the InvokedItem is the menu item for Settings.</summary>
      <returns>true if the InvokedItem is the menu item for Settings; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs.RecommendedNavigationTransitionInfo">
      <summary>Gets the navigation transition recommended for the direction of the navigation.</summary>
      <returns>The navigation transition recommended for the direction of the navigation.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewItemSeparator">
      <summary>Represents a line that separates menu items in a NavigationView.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationViewItemSeparator.#ctor">
      <summary>Initializes a new instance of the NavigationViewItemSeparator class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode">
      <summary>Defines constants that specify the label for the overflow button in a NavigationView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode.MoreLabel">
      <summary>The text label "More" is shown next to the chevron.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewOverflowLabelMode.NoLabel">
      <summary>No text label is shown, only a chevron.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewPaneClosingEventArgs">
      <summary>Provides data for the NavigationView.PaneClosing" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewPaneClosingEventArgs.Cancel">
      <summary>Gets or sets a value that indicates whether the event should be canceled.</summary>
      <returns>true to cancel the event; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode">
      <summary>Defines constants that specify how and where the NavigationView pane is shown.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Auto">
      <summary>The pane is shown on the left side of the control, and changes between minimal, compact, and full states depending on the width of the window.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Left">
      <summary>The pane is shown on the left side of the control in its fully open state.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.LeftCompact">
      <summary>The pane is shown on the left side of the control. Only the pane icons are shown by default.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.LeftMinimal">
      <summary>The pane is shown on the left side of the control. Only the pane menu button is shown by default.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewPaneDisplayMode.Top">
      <summary>The pane is shown at the top of the control.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewSelectionChangedEventArgs">
      <summary>Provides data for the NavigationView.SelectionChanged" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewSelectionChangedEventArgs.IsSettingsSelected">
      <summary>Gets a value that indicates whether the SelectedItem is the menu item for Settings.</summary>
      <returns>true if the SelectedItem is the menu item for Settings; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewSelectionChangedEventArgs.RecommendedNavigationTransitionInfo">
      <summary>Gets the navigation transition recommended for the direction of the navigation.</summary>
      <returns>The navigation transition recommended for the direction of the navigation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewSelectionChangedEventArgs.SelectedItem">
      <summary>Gets the newly selected menu item.</summary>
      <returns>The newly selected menu item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewSelectionChangedEventArgs.SelectedItemContainer">
      <summary>Gets the container for the selected item.</summary>
      <returns>The container for the selected item.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus">
      <summary>Defines constants that specify whether item selection changes when keyboard focus changes in a NavigationView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus.Disabled">
      <summary>Selection does not change when keyboard focus changes.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewSelectionFollowsFocus.Enabled">
      <summary>Selection changes when keyboard focus changes.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled">
      <summary>Defines constants that specify when gamepad bumpers can be used to navigate the top-level navigation items in a NavigationView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.Always">
      <summary>Gamepad bumpers always navigate the top-level navigation items.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.Never">
      <summary>Gamepad bumpers never navigate the top-level navigation items.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NavigationViewShoulderNavigationEnabled.WhenSelectionFollowsFocus">
      <summary>Gamepad bumpers navigate the top-level navigation items when the SelectionFollowsFocus property is Enabled.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a NavigationView. Not intended for general use.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.#ctor">
      <summary>Initializes a new instance of the NavigationViewTemplateSettings class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.BackButtonVisibility">
      <summary>Gets the visibility of the back button.</summary>
      <returns>The visibility of the back button.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.BackButtonVisibilityProperty">
      <summary>Identifies the BackButtonVisibility dependency property.</summary>
      <returns>The identifier for the BackButtonVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.LeftPaneVisibility">
      <summary>Gets the visibility of the left pane.</summary>
      <returns>The visibility of the left pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.LeftPaneVisibilityProperty">
      <summary>Identifies the LeftPaneVisibility dependency property.</summary>
      <returns>The identifier for the LeftPaneVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.OverflowButtonVisibility">
      <summary>Gets the visibility of the overflow button.</summary>
      <returns>The visibility of the overflow button.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.OverflowButtonVisibilityProperty">
      <summary>Identifies the OverflowButtonVisibility dependency property.</summary>
      <returns>The identifier for the OverflowButtonVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.PaneToggleButtonVisibility">
      <summary>Gets the visibility of the pane toggle button.</summary>
      <returns>The visibility of the pane toggle button.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.PaneToggleButtonVisibilityProperty">
      <summary>Identifies the PaneToggleButtonVisibility dependency property.</summary>
      <returns>The identifier for the PaneToggleButtonVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.PaneToggleButtonWidth" />
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.PaneToggleButtonWidthProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.SingleSelectionFollowsFocus">
      <summary>Gets the SelectionFollowsFocus value.</summary>
      <returns>The SelectionFollowsFocus value.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.SingleSelectionFollowsFocusProperty">
      <summary>Identifies the SingleSelectionFollowsFocus dependency property.</summary>
      <returns>The identifier for the SingleSelectionFollowsFocus dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.SmallerPaneToggleButtonWidth" />
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.SmallerPaneToggleButtonWidthProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.TopPadding">
      <summary>Gets the padding value of the top pane.</summary>
      <returns>The padding value of the top pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.TopPaddingProperty">
      <summary>Identifies the TopPadding dependency property.</summary>
      <returns>The identifier for the TopPadding dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.TopPaneVisibility">
      <summary>Gets the visibility of the top pane.</summary>
      <returns>The visibility of the top pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings.TopPaneVisibilityProperty">
      <summary>Identifies the TopPaneVisibility dependency property.</summary>
      <returns>The identifier for the TopPaneVisibility dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout">
      <summary>Represents the base class for an object that sizes and arranges child elements for a host and and does not support virtualization.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout.#ctor">
      <summary>Initializes a new instance of the NonVirtualizingLayout class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout.ArrangeOverride(Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext,Windows.Foundation.Size)">
      <summary>When implemented in a derived class, provides the behavior for the "Arrange" pass of layout. Classes can override this method to define their own "Arrange" pass behavior.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="finalSize">The final area within the container that this object should use to arrange itself and its children.</param>
      <returns>The actual size that is used after the element is arranged in layout.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout.InitializeForContextCore(Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext)">
      <summary>When overridden in a derived class, initializes any per-container state the layout requires when it is attached to a UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout.MeasureOverride(Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext,Windows.Foundation.Size)">
      <summary>Provides the behavior for the "Measure" pass of the layout cycle. Classes can override this method to define their own "Measure" pass behavior.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="availableSize">The available size that this object can give to child objects. Infinity can be specified as a value to indicate that the object will size to whatever content is available.</param>
      <returns>The size that this object determines it needs during layout, based on its calculations of the allocated sizes for child objects or based on other considerations such as a fixed container size.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayout.UninitializeForContextCore(Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext)">
      <summary>When overridden in a derived class, removes any state the layout previously stored on the UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext">
      <summary>Represents the base class for layout context types that do not support virtualization.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext.#ctor">
      <summary>Initializes a new instance of the NonVirtualizingLayoutContext class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext.Children">
      <summary>Gets the collection of child UIElements from the container that provides the context.</summary>
      <returns>The collection of child elements from the container that provides the context.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext.ChildrenCore">
      <summary>Implements the behavior for getting the return value of Children in a derived or custom NonVirtualizingLayoutContext.</summary>
      <returns>The value that should be returned as Children by the NonVirtualizingLayoutContext.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NumberBox">
      <summary>Represents a control that can be used to display and edit numbers.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.NumberBox.ValueChanged">
      <summary>Occurs after the user triggers evaluation of new input by pressing the Enter key, clicking a spin button, or by changing focus.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.NumberBox.#ctor">
      <summary>Initializes a new instance of the NumberBox class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.AcceptsExpression">
      <summary>Toggles whether the control will accept and evaluate a basic formulaic expression entered as input.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.AcceptsExpressionProperty">
      <summary>Identifies the AcceptsExpression dependency property.</summary>
      <returns>The identifier for the AcceptsExpression dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Description">
      <summary>Gets or sets content that is shown below the control. The content should provide guidance about the input expected by the control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.DescriptionProperty">
      <summary>Identifies the Description dependency property.</summary>
      <returns>The identifier for the Description dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Header">
      <summary>Gets or sets the content for the control's header.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.HeaderProperty">
      <summary>Identifies the Header dependency property.</summary>
      <returns>The identifier for the Header dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.HeaderTemplate">
      <summary>Gets or sets the  DataTemplate used to display the content of the control's header.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.HeaderTemplateProperty">
      <summary>Identifies the HeaderTemplate dependency property.</summary>
      <returns>The identifier for the HeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.IsWrapEnabled">
      <summary>Toggles whether line breaking occurs if a line of text extends beyond the available width of the control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.IsWrapEnabledProperty">
      <summary>Identifies the IsWrapEnabled dependency property.</summary>
      <returns>The identifier for the IsWrapEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.LargeChange">
      <summary>Gets or sets the value that is added to or subtracted from Value when a large change is made, such as with the PageUP and PageDown keys.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.LargeChangeProperty">
      <summary>Identifies the LargeChange dependency property.</summary>
      <returns>The identifier for the LargeChange dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Maximum">
      <summary>Gets or sets the numerical maximum for Value.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.MaximumProperty">
      <summary>Identifies the Maximum dependency property.</summary>
      <returns>The identifier for the Maximum dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Minimum">
      <summary>Gets or sets the numerical minimum for Value.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.MinimumProperty">
      <summary>Identifies the Minimum dependency property.</summary>
      <returns>The identifier for the Minimum dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.NumberFormatter">
      <summary>Gets or sets the object used to specify the formatting of Value.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.NumberFormatterProperty">
      <summary>Identifies the NumberFormatter dependency property.</summary>
      <returns>The identifier for the NumberFormatter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.PlaceholderText">
      <summary>Gets or sets the text that is displayed in the control until the value is changed by a user action or some other operation.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.PlaceholderTextProperty">
      <summary>Identifies the PlaceholderText dependency property.</summary>
      <returns>The identifier for the PlaceholderText dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.PreventKeyboardDisplayOnProgrammaticFocus">
      <summary>Gets or sets a value that indicates whether the on-screen keyboard is shown when the control receives focus programmatically.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.PreventKeyboardDisplayOnProgrammaticFocusProperty">
      <summary>Identifies the PreventKeyboardDisplayOnProgrammaticFocus dependency property.</summary>
      <returns>The identifier for the PreventKeyboardDisplayOnProgrammaticFocus dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SelectionFlyout">
      <summary>Gets or sets the flyout that is shown when text is selected, or null if no flyout is shown.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SelectionFlyoutProperty">
      <summary>Identifies the SelectionFlyout dependency property.</summary>
      <returns>The identifier for the SelectionFlyout dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SelectionHighlightColor" />
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SelectionHighlightColorProperty">
      <summary>Identifies the SelectionHighlightColor dependency property.</summary>
      <returns>The identifier for the SelectionHighlightColor dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SmallChange">
      <summary>Gets or sets the value that is added to or subtracted from Value when a small change is made, such as with an arrow key or scrolling.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SmallChangeProperty">
      <summary>Identifies the SmallChange dependency property.</summary>
      <returns>The identifier for the SmallChange dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SpinButtonPlacementMode">
      <summary>Gets or sets a value that indicates the placement of buttons used to increment or decrement the Value property.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.SpinButtonPlacementModeProperty">
      <summary>Identifies the SpinButtonPlacementMode dependency property.</summary>
      <returns>The identifier for the SpinButtonPlacementMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Text">
      <summary>Gets or sets the string type representation of the Value property.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.TextProperty">
      <summary>Identifies the Text dependency property.</summary>
      <returns>The identifier for the Text dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.TextReadingOrder">
      <summary>Gets or sets a value that indicates how the reading order is determined for the NumberBox.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.TextReadingOrderProperty">
      <summary>Identifies the TextReadingOrder dependency property.</summary>
      <returns>The identifier for the TextReadingOrder dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.ValidationMode">
      <summary>Gets or sets the input validation behavior to invoke when invalid input is entered.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.ValidationModeProperty">
      <summary>Identifies the ValidationMode dependency property.</summary>
      <returns>The identifier for the ValidationMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.Value">
      <summary>Gets or sets the numeric value of a NumberBox.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBox.ValueProperty">
      <summary>Identifies the Value dependency property.</summary>
      <returns>The identifier for the Value dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode">
      <summary>Defines values that specify how the spin buttons used to increment or decrement the Value of a NumberBox are displayed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Compact">
      <summary>The spin buttons have two visual states, depending on focus. By default, the spin buttons are displayed in a compact, vertical orientation. When the Numberbox gets focus, the spin buttons expand.

:::image type="content" source="images/controls/numberbox-spinbuttonplacementmode-compact.png" alt-text="NumberBox showing both visual states for Compact spin buttons":::</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Hidden">
      <summary>The spin buttons are not displayed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NumberBoxSpinButtonPlacementMode.Inline">
      <summary>The spin buttons are displayed in an expanded, horizontal orientation.

:::image type="content" source="images/controls/numberbox-spinbuttonplacementmode-inline.png" alt-text="NumberBox with Inline spin buttons":::</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NumberBoxValidationMode">
      <summary>Defines values that specify the input validation behavior of a NumberBox when invalid input is entered.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NumberBoxValidationMode.Disabled">
      <summary>Input validation is disabled</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.NumberBoxValidationMode.InvalidInputOverwritten">
      <summary>Invalid input is replaced by NumberBox.PlaceholderText" text.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.NumberBoxValueChangedEventArgs">
      <summary>Provides event data for the NumberBox.ValueChanged" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBoxValueChangedEventArgs.NewValue">
      <summary>Contains the new Value to be set for a NumberBox.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.NumberBoxValueChangedEventArgs.OldValue">
      <summary>Contains the old Value being replaced in a NumberBox.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ParallaxSourceOffsetKind">
      <summary>Defines constants that specify how the source offset values of a ParallaxView are interpreted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ParallaxSourceOffsetKind.Absolute">
      <summary>The source start/end offset value is interpreted as an absolute value.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.ParallaxSourceOffsetKind.Relative">
      <summary>The source start/end offset value is added to the auto-computed source offset.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ParallaxView">
      <summary>Represents a container that associates the scroll position of a foreground element, such as a list, with a background element, such as an image. A 3D effect is created from each element having a different scrolling rate.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ParallaxView.#ctor">
      <summary>Initializes a new instance of the ParallaxView class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ParallaxView.RefreshAutomaticHorizontalOffsets">
      <summary>Forces the automatically computed horizontal offsets to be recomputed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ParallaxView.RefreshAutomaticVerticalOffsets">
      <summary>Forces the automatically computed vertical offsets to be recomputed.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.Child">
      <summary>Gets or sets the background content of the ParallaxView.</summary>
      <returns>The background content of the ParallaxView, typically an image. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.ChildProperty">
      <summary>Identifies the Child dependency property.</summary>
      <returns>The identifier for the Child dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalShift">
      <summary>Represents the horizontal range of motion of the child element.</summary>
      <returns>The horizontal range of motion. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalShiftProperty">
      <summary>Identifies the HorizontalShift dependency property.</summary>
      <returns>The identifier for the HorizontalShift dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceEndOffset">
      <summary>Represents the horizontal scroll offset at which the parallax motion ends.</summary>
      <returns>The horizontal scroll offset at which parallax motion ends. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceEndOffsetProperty">
      <summary>Identifies the HorizontalSourceEndOffset dependency property.</summary>
      <returns>The identifier for the HorizontalSourceEndOffset dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceOffsetKind">
      <summary>Gets or sets a value that determines how the horizontal source offset values of a ParallaxView are interpreted.</summary>
      <returns>A value of the enumeration that determines how the horizontal source offset values of a ParallaxView are interpreted.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceOffsetKindProperty">
      <summary>Identifies the HorizontalSourceOffsetKind dependency property.</summary>
      <returns>The identifier for the HorizontalSourceOffsetKind dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceStartOffset">
      <summary>Represents the horizontal scroll offset at which parallax motion starts.</summary>
      <returns>The horizontal scroll offset at which parallax motion starts. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.HorizontalSourceStartOffsetProperty">
      <summary>Identifies the HorizontalSourceStartOffset dependency property.</summary>
      <returns>The identifier for the HorizontalSourceStartOffset dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.IsHorizontalShiftClamped">
      <summary>Gets or sets a value that indicates whether the horizontal parallax ratio is clampled to a specified percentage of the source scroll velocity.</summary>
      <returns>true if the parallax ratio is clampled to a specified percentage of the source scroll velocity; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.IsHorizontalShiftClampedProperty">
      <summary>Identifies the IsHorizontalShiftClamped dependency property.</summary>
      <returns>The identifier for the IsHorizontalShiftClamped dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.IsVerticalShiftClamped">
      <summary>Gets or sets a value that indicates whether the vertical parallax ratio is clampled to a specified percentage of the source scroll velocity.</summary>
      <returns>true if the parallax ratio is clampled to a specified percentage of the source scroll velocity; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.IsVerticalShiftClampedProperty">
      <summary>Identifies the IsVerticalShiftClamped dependency property.</summary>
      <returns>The identifier for the IsVerticalShiftClamped dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.MaxHorizontalShiftRatio">
      <summary>Clamps the horizontal parallax ratio to the specified percentage of the source scroll velocity.</summary>
      <returns>The maximum percentage of the source scroll velocity. The default is 1.0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.MaxHorizontalShiftRatioProperty">
      <summary>Identifies the MaxHorizontalShiftRatio dependency property.</summary>
      <returns>The identifier for the MaxHorizontalShiftRatio dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.MaxVerticalShiftRatio">
      <summary>Clamps the vertical parallax ratio to the specified percentage of the source scroll velocity.</summary>
      <returns>The maximum percentage of the source scroll velocity. The default is 1.0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.MaxVerticalShiftRatioProperty">
      <summary>Identifies the MaxVerticalShiftRatio dependency property.</summary>
      <returns>The identifier for the MaxVerticalShiftRatio dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.Source">
      <summary>The element that either is or contains the ScrollViewer that controls the parallax operation.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.SourceProperty">
      <summary>Identifies the Source dependency property.</summary>
      <returns>The identifier for the Source dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalShift">
      <summary>Represents the vertical range of motion of the child element.</summary>
      <returns>The vertical range of motion. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalShiftProperty">
      <summary>Identifies the VerticalShift dependency property.</summary>
      <returns>The identifier for the VerticalShift dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceEndOffset">
      <summary>Represents the vertical scroll offset at which the parallax motion ends.</summary>
      <returns>The vertical scroll offset at which parallax motion ends. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceEndOffsetProperty">
      <summary>Identifies the VerticalSourceEndOffset dependency property.</summary>
      <returns>The identifier for the VerticalSourceEndOffset dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceOffsetKind">
      <summary>Gets or sets a value that determines how the vertical source offset values of a ParallaxView are interpreted.</summary>
      <returns>A value of the enumeration that determines how the vertical source offset values of a ParallaxView are interpreted.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceOffsetKindProperty">
      <summary>Identifies the VerticalSourceOffsetKind dependency property.</summary>
      <returns>The identifier for the VerticalSourceOffsetKind dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceStartOffset">
      <summary>Represents the vertical scroll offset at which parallax motion starts.</summary>
      <returns>The vertical scroll offset at which parallax motion starts. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ParallaxView.VerticalSourceStartOffsetProperty">
      <summary>Identifies the VerticalSourceStartOffset dependency property.</summary>
      <returns>The identifier for the VerticalSourceStartOffset dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PathIconSource">
      <summary>Represents an icon source that uses a vector path as its content.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.PathIconSource.#ctor">
      <summary>Initializes a new instance of the PathIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PathIconSource.Data">
      <summary>Gets or sets a Geometry.</summary>
      <returns>A description of the shape to be drawn.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PathIconSource.DataProperty">
      <summary>Identifies the Data dependency property.</summary>
      <returns>The identifier for the Data dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PersonPicture">
      <summary>Represents a control that displays the avatar image for a person, if one is available; if not, it displays the person's initials or a generic glyph.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.PersonPicture.#ctor">
      <summary>Initializes a new instance of the PersonPicture class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph to display on the badge.</summary>
      <returns>The hexadecimal character code for the badge glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeGlyphProperty">
      <summary>Identifies the BadgeGlyph dependency property.</summary>
      <returns>The identifier for the BadgeGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeImageSource">
      <summary>Gets or sets the source of an image to display on the badge.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeImageSourceProperty">
      <summary>Identifies the BadgeImageSource dependency property.</summary>
      <returns>The identifier for the BadgeImageSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeNumber">
      <summary>Gets or sets the contact number to display on the badge.</summary>
      <returns>The contact number to display on the badge.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeNumberProperty">
      <summary>Identifies the BadgeNumber dependency property.</summary>
      <returns>The identifier for the BadgeNumber dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeText">
      <summary>Gets or sets the contact text to display on the badge.</summary>
      <returns>The contact text to display on the badge.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.BadgeTextProperty">
      <summary>Identifies the BadgeText dependency property.</summary>
      <returns>The identifier for the BadgeText dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.Contact">
      <summary>Gets or sets a Contact object that contains information about the person.</summary>
      <returns>A Contact object that contains information about the person.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.ContactProperty">
      <summary>Identifies the Contact dependency property.</summary>
      <returns>The identifier for the Contact dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.DisplayName">
      <summary>Gets or sets the contact's display name.</summary>
      <returns>The contact's display name.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.DisplayNameProperty">
      <summary>Identifies the DisplayName dependency property.</summary>
      <returns>The identifier for the DisplayName dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.Initials">
      <summary>Gets or sets the contact's initials.</summary>
      <returns>The contact's initials.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.InitialsProperty">
      <summary>Identifies the Initials dependency property.</summary>
      <returns>The identifier for the Initials dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.IsGroup">
      <summary>Gets or sets a value that indicates whether the PersonPicture represents a group or an individual.</summary>
      <returns>true if the PersonPicture represents a group; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.IsGroupProperty">
      <summary>Identifies the IsGroup dependency property.</summary>
      <returns>The identifier for the IsGroup dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.PreferSmallImage">
      <summary>Gets or sets a value that indicates whether a small image is displayed rather than a large image when both are available.</summary>
      <returns>true to display a small image even when a large image is available; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.PreferSmallImageProperty">
      <summary>Identifies the PreferSmallImage dependency property.</summary>
      <returns>The identifier for the PreferSmallImage dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.ProfilePicture">
      <summary>Gets or sets the source of the contact's profile picture.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.ProfilePictureProperty">
      <summary>Identifies the ProfilePicture dependency property.</summary>
      <returns>The identifier for the ProfilePicture dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPicture.TemplateSettings" />
    <member name="T:Microsoft.UI.Xaml.Controls.PersonPictureTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a PersonPicture control. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPictureTemplateSettings.ActualImageBrush">
      <summary>Gets the image brush used on the control.</summary>
      <returns>The image brush used on the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PersonPictureTemplateSettings.ActualInitials">
      <summary>Gets the contact's initials.</summary>
      <returns>The contact's initials.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PipsPager">
      <summary>Represents a control that enables navigation within linearly paginated content using a configurable collection of glyphs, each of which represents a single "page" within a limitless range.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.PipsPager.SelectedIndexChanged">
      <summary>Occurs after the selected index changes on the PipsPager.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.PipsPager.#ctor">
      <summary>Initializes a new instance of the PipsPager class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.MaxVisiblePips">
      <summary>The maximum number of pips shown in the PipsPager at one time.</summary>
      <returns>The maximum number of pips shown in the PipsPager at one time. The default value is 5.

When possible, the PipsPager always centers itself on the selected pip.

MaxVisiblePips should be set to a positive number. If set to 0, no pages will be visible to the user.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.MaxVisiblePipsProperty">
      <summary>Identifies the MaxVisiblePips dependency property.</summary>
      <returns>The identifier for the MaxVisiblePips dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NextButtonStyle">
      <summary>Gets or sets the style to apply to the Next button.</summary>
      <returns>The style to apply to the Next button.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NextButtonStyleProperty">
      <summary>Identifies the NextButtonStyle dependency property.</summary>
      <returns>The identifier for the NextButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NextButtonVisibility">
      <summary>Gets or sets the display state of the Next button.</summary>
      <returns>The display state of the Previous button. The default value is 'Collapsed'.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NextButtonVisibilityProperty">
      <summary>Identifies the NextButtonVisibility dependency property.</summary>
      <returns>The identifier for the NextButtonVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NormalPipStyle">
      <summary>Gets or sets the style for the default, unselected pips in the PipsPager.</summary>
      <returns>The style for the default, unselected pips in the PipsPager.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NormalPipStyleProperty">
      <summary>Identifies the NormalPipStyle dependency property.</summary>
      <returns>The identifier for the NormalPipStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NumberOfPages">
      <summary>Gets or sets the maximum number of pages supported by the PipsPager.</summary>
      <returns>The maximum number of pages supported by the PipsPager. The default is -1 (infinite).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.NumberOfPagesProperty">
      <summary>Identifies the NumberOfPages dependency property.</summary>
      <returns>The identifier for the NormalPipStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.Orientation">
      <summary>Gets or sets the orientation of the pips and navigation buttons in the PipsPager.</summary>
      <returns>The orientation of the pips and navigation buttons in the PipsPager. The default is Horizontal.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.OrientationProperty">
      <summary>Identifies the Orientation dependency property.</summary>
      <returns>The identifier for the Orientation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.PreviousButtonStyle">
      <summary>Gets or sets the style to apply to the Previous button.</summary>
      <returns>The style to apply to the Previous button.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.PreviousButtonStyleProperty">
      <summary>Identifies the PreviousButtonStyle dependency property.</summary>
      <returns>The identifier for the PreviousButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.PreviousButtonVisibility">
      <summary>Gets or sets the display state of the Previous button.</summary>
      <returns>The display state of the Previous button. The default value is 'Collapsed'.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.PreviousButtonVisibilityProperty">
      <summary>Identifies the PreviousButtonVisibility dependency property.</summary>
      <returns>The identifier for the PreviousButtonVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.SelectedPageIndex">
      <summary>Gets or sets the 0 based index of the currently selected pip in the PipsPager. A pip is always selected.</summary>
      <returns>The 0 based index of the currently selected pip in the PipsPager. The default value is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.SelectedPageIndexProperty">
      <summary>Identifies the SelectedPageIndex dependency property.</summary>
      <returns>The identifier for the SelectedPageIndex dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.SelectedPipStyle">
      <summary>Gets or sets the style to apply to the selected pip in the PipsPager.</summary>
      <returns>The style to apply to the selected pip in the PipsPager.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.SelectedPipStyleProperty">
      <summary>Identifies the SelectedPipStyle dependency property.</summary>
      <returns>The identifier for the SelectedPipStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPager.TemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a PipsPager. Not intended for general use.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PipsPagerButtonVisibility">
      <summary>Defines constants that specify how the navigation buttons of the PipsPager are displayed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.PipsPagerButtonVisibility.Collapsed">
      <summary>The button is not visible and does not take up layout space.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.PipsPagerButtonVisibility.Visible">
      <summary>The navigation button is visible and enabled, but hidden when content is at one or the other extent. For example, the Previous button is hidden when the current page is the first page, and the Next button is hidden when the current page is the last page.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.PipsPagerButtonVisibility.VisibleOnPointerOver">
      <summary>The button behavior is the same as Visible except the button is visible only when the pointer cursor is over the pager, or keyboard focus is inside the pager or on a navigation button.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PipsPagerSelectedIndexChangedEventArgs">
      <summary>Provides data for the PipsPager.SelectedIndexChanged" event.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.PipsPagerTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a PipsPager.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.PipsPagerTemplateSettings.PipsPagerItems">
      <summary>Gets or sets the list of integers to represent the pips in the PipsPager.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper.GetKeepInteriorCornersSquare(Windows.UI.Xaml.Controls.AutoSuggestBox)">
      <summary>Gets whether the interior corners of the AutoSuggestBox control are square.</summary>
      <param name="autoSuggestBox">The AutoSuggestBox control.</param>
      <returns>True, if the corners are square. Otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper.SetKeepInteriorCornersSquare(Windows.UI.Xaml.Controls.AutoSuggestBox,System.Boolean)">
      <summary>Sets whether the interior corners of the AutoSuggestBox control are square.</summary>
      <param name="autoSuggestBox">The AutoSuggestBox control.</param>
      <param name="value">True, if the corners are square. Otherwise, false.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper.KeepInteriorCornersSquareProperty">
      <summary>Identifies the KeepInteriorCornersSquare dependency property. Not implemented.</summary>
      <returns>The identifier for the KeepInteriorCornersSquare dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider">
      <summary>Represents a slider in a ColorPicker control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider.#ctor">
      <summary>Initializes a new instance of the ColorPickerSlider class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider.ColorChannel">
      <summary>Gets or sets a value that indicates which color channel the slider modifies.</summary>
      <returns>A value of the enumeration that indicates which color channel the slider modifies.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider.ColorChannelProperty">
      <summary>Identifies the ColorChannel dependency property.</summary>
      <returns>The identifier for the ColorChannel dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum">
      <summary>Represents a control that lets a user choose a color from a visual spectrum.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.ColorChanged">
      <summary>Occurs when the Color property has changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.#ctor">
      <summary>Initializes a new instance of the ColorSpectrum class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.Color">
      <summary>Gets or sets the current color value.</summary>
      <returns>The current color value.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.ColorProperty">
      <summary>Identifies the Color dependency property.</summary>
      <returns>The identifier for the Color dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.Components">
      <summary>Gets or sets a value that indicates how the Hue-Saturation-Value (HSV) color components are mapped onto the ColorSpectrum.</summary>
      <returns>A value of the enumeration. The default is HueSaturation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.ComponentsProperty">
      <summary>Identifies the Components dependency property.</summary>
      <returns>The identifier for the Components dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.HsvColor">
      <summary>Gets or sets the current color value as a Vector4.</summary>
      <returns>The current HSV color value.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.HsvColorProperty">
      <summary>Identifies the HsvColor dependency property.</summary>
      <returns>The identifier for the HsvColor dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxHue">
      <summary>Gets or sets the maximum Hue value in the range 0-359.</summary>
      <returns>The maximum Hue value in the range 0-359. The default is 359.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxHueProperty">
      <summary>Identifies the MaxHue dependency property.</summary>
      <returns>The identifier for the MaxHue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxSaturation">
      <summary>Gets or sets the maximum Saturation value in the range 0-100.</summary>
      <returns>The maximum Saturation value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxSaturationProperty">
      <summary>Identifies the MaxSaturation dependency property.</summary>
      <returns>The identifier for the MaxSaturation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxValue">
      <summary>Gets or sets the maximum Value value in the range 0-100.</summary>
      <returns>The maximum Value value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MaxValueProperty">
      <summary>Identifies the MaxValue dependency property.</summary>
      <returns>The identifier for the MaxValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinHue">
      <summary>Gets or sets the minimum Hue value in the range 0-359.</summary>
      <returns>The minimum Hue value in the range 0-359. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinHueProperty">
      <summary>Identifies the MinHue dependency property.</summary>
      <returns>The identifier for the MinHue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinSaturation">
      <summary>Gets or sets the minimum Saturation value in the range 0-100.</summary>
      <returns>The minimum Saturation value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinSaturationProperty">
      <summary>Identifies the MinSaturation dependency property.</summary>
      <returns>The identifier for the MinSaturation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinValue">
      <summary>Gets or sets the minimum Value value in the range 0-100.</summary>
      <returns>The minimum Value value in the range 0-100. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.MinValueProperty">
      <summary>Identifies the MinValue dependency property.</summary>
      <returns>The identifier for the MinValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.Shape">
      <summary>Gets or sets a value that indicates whether the ColorSpectrum is shown as a square or a circle.</summary>
      <returns>A value of the enumeration. The default is Box, which shows the spectrum as a square.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum.ShapeProperty">
      <summary>Identifies the Shape dependency property.</summary>
      <returns>The identifier for the Shape dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.#ctor">
      <summary>Initializes a new instance of the ColumnMajorUniformToLargestGridLayout class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.ColumnSpacing" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.ColumnSpacingProperty">
      <summary>Identifies the ColumnSpacing dependency property.</summary>
      <returns>The identifier for the ColumnSpacing dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.MaxColumns" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.MaxColumnsProperty">
      <summary>Identifies the MaxColumns dependency property.</summary>
      <returns>The identifier for the MaxColumns dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.RowSpacing" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout.RowSpacingProperty">
      <returns>The identifier for the RowSpacing dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper">
      <summary>Provides ComboBox helper methods for the XAML compiler.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper.GetKeepInteriorCornersSquare(Windows.UI.Xaml.Controls.ComboBox)">
      <summary>Gets whether the interior corners of the ComboBox control are square.</summary>
      <param name="comboBox">The ComboBox control.</param>
      <returns>True, if the corners are square. Otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper.SetKeepInteriorCornersSquare(Windows.UI.Xaml.Controls.ComboBox,System.Boolean)">
      <summary>Sets whether the interior corners of the ComboBox control are square.</summary>
      <param name="comboBox">The ComboBox control.</param>
      <param name="value">True, if the corners are square. Otherwise, false.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper.KeepInteriorCornersSquare" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper.KeepInteriorCornersSquareProperty">
      <summary>Identifies the KeepInteriorCornersSquare dependency property. Not implemented.</summary>
      <returns>The identifier for the KeepInteriorCornersSquare dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar">
      <summary>Represents a specialized command bar used in a CommandBarFlyout.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar.#ctor">
      <summary>Initializes a new instance of the CommandBarFlyoutCommandBar class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar.FlyoutTemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as {TemplateBinding} markup extension sources when defining templates for a CommandBarFlyoutCommandBar control.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties.GetControlType(Windows.UI.Xaml.UIElement)">
      <param name="element"></param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties.SetControlType(Windows.UI.Xaml.UIElement,Windows.UI.Xaml.Automation.Peers.AutomationControlType)">
      <param name="element"></param>
      <param name="value"></param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties.ControlType" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties.ControlTypeProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a CommandBarFlyout control. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.CloseAnimationEndPosition">
      <summary>Gets the end position for the close animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ContentClipRect">
      <summary>Gets the rectangle used to clip the content.</summary>
      <returns>The rectangle used to clip the content.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.CurrentWidth">
      <summary>Gets the current width of the control.</summary>
      <returns>The current width of the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandDownAnimationEndPosition">
      <summary>Gets the end position for the expand down animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandDownAnimationHoldPosition">
      <summary>Gets the hold position for the expand down animation.</summary>
      <returns>The hold position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandDownAnimationStartPosition">
      <summary>Gets the start position for the expand down animation.</summary>
      <returns>The start position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandDownOverflowVerticalPosition">
      <summary>Gets the vertical position of the overflow when expanded down.</summary>
      <returns>The vertical position of the overflow when expanded down.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandedWidth">
      <summary>Gets the width of the control when expanded.</summary>
      <returns>The width of the control when expanded.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandUpAnimationEndPosition">
      <summary>Gets the end position for the expand up animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandUpAnimationHoldPosition">
      <summary>Gets the hold position for the expand up animation.</summary>
      <returns>The hold position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandUpAnimationStartPosition">
      <summary>Gets the start position for the expand up animation.</summary>
      <returns>The start position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.ExpandUpOverflowVerticalPosition">
      <summary>Gets the vertical position of the overflow when expanded up.</summary>
      <returns>The vertical position of the overflow when expanded up.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.OpenAnimationEndPosition">
      <summary>Gets the end position for the open animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.OpenAnimationStartPosition">
      <summary>Gets the start position for the open animation.</summary>
      <returns>The start position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.OverflowContentClipRect">
      <summary>Gets the rectangle used to clip the overflow content.</summary>
      <returns>The rectangle used to clip the overflow content.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.WidthExpansionAnimationEndPosition">
      <summary>Gets the end position for the width expansion animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.WidthExpansionAnimationStartPosition">
      <summary>Gets the start position for the width expansion animation.</summary>
      <returns>The start position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.WidthExpansionDelta">
      <summary>Gets the amount of change for the width expansion.</summary>
      <returns>The amount of change for the width expansion.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.WidthExpansionMoreButtonAnimationEndPosition">
      <summary>Gets the end position for the "more" button width expansion animation.</summary>
      <returns>The end position for the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarTemplateSettings.WidthExpansionMoreButtonAnimationStartPosition">
      <summary>Gets the start position for the "more" button width expansion animation.</summary>
      <returns>The start position for the animation.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter">
      <summary>Converts an existing CornerRadius struct to a new CornerRadius struct, with filters applied to extract only the specified fields, leaving the others set to 0.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.#ctor">
      <summary>Initializes a new instance of the CornerRadiusFilterConverter class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.Convert(System.Object,System.Type,System.Object,System.String)">
      <summary>Converts the source CornerRadius by extracting only the fields specified by the Filter and leaving others set to 0.</summary>
      <param name="value">The source CornerRadius being passed to the target.</param>
      <param name="targetType">The type of the target property. Part of the IValueConverter.Convert interface method, but not used.</param>
      <param name="parameter">An optional parameter to be used in the converter logic. Part of the IValueConverter.Convert interface method, but not used.</param>
      <param name="language">The language of the conversion. Part of the IValueConverter.Convert interface method, but not used.</param>
      <returns>The converted CornerRadius/double value to be passed to the target dependency property.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.ConvertBack(System.Object,System.Type,System.Object,System.String)">
      <summary>Not implemented.</summary>
      <param name="value">The target data being passed to the source.</param>
      <param name="targetType">The type of the target property.</param>
      <param name="parameter">An optional parameter to be used in the converter logic.</param>
      <param name="language">The language of the conversion.</param>
      <returns>The value to be passed to the source object.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.Filter">
      <summary>Gets or sets the type of the filter applied to the CornerRadiusFilterConverter.</summary>
      <returns>An enumeration value that specifies the filter type for a CornerRadiusFilterConverter.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.FilterProperty">
      <summary>Identifies the Filter dependency property.</summary>
      <returns>The identifier for the Filter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.Scale">
      <summary>Gets or sets the scale multiplier applied to the CornerRadiusFilterConverter.</summary>
      <returns>A double value that specifies the scale for the CornerRadiusFilterConverter.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter.ScaleProperty">
      <summary>Identifies the Scale dependency property.</summary>
      <returns>The identifier for the Scale dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind">
      <summary>Defines constants that specify the filter type for a CornerRadiusFilterConverter instance.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.Bottom">
      <summary>Filters BottomLeft and BottomRight values, sets TopLeft and TopRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.BottomRightValue">
      <summary>Gets the double value of BottomRight corner.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.Left">
      <summary>Filters TopLeft and BottomLeft values, sets TopRight and BottomRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.None">
      <summary>No filter applied.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.Right">
      <summary>Filters TopRight and BottomRight values, sets TopLeft and BottomLeft to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.Top">
      <summary>Filters TopLeft and TopRight values, sets BottomLeft and BottomRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterKind.TopLeftValue">
      <summary>Gets the double value of TopLeft corner.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter">
      <summary>Converts a CornerRadius to Thickness and also applies filters to extract only the specified fields, leaving the others set to 0.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.#ctor">
      <summary>Initializes a new instance of the CornerRadiusToThicknessConverter class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.Convert(System.Object,System.Type,System.Object,System.String)">
      <summary>Converts a CornerRadius value to a Thickness.</summary>
      <param name="value">The source CornerRadius being passed to the target.</param>
      <param name="targetType">The type of the target property. Part of the IValueConverter.Convert interface method, but not used.</param>
      <param name="parameter">An optional parameter to be used in the converter logic. Part of the IValueConverter.Convert interface method, but not used.</param>
      <param name="language">The language of the conversion. Part of the IValueConverter.Convert interface method, but not used.</param>
      <returns>The converted Thickness value to be passed to the target dependency property.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.ConvertBack(System.Object,System.Type,System.Object,System.String)">
      <summary>Not implemented.</summary>
      <param name="value">The target data being passed to the source.</param>
      <param name="targetType">The type of the target property.</param>
      <param name="parameter">An optional parameter to be used in the converter logic.</param>
      <param name="language">The language of the conversion.</param>
      <returns>The value to be passed to the source object.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.ConversionKind">
      <summary>Gets or sets the conversion kind that will be applied to the CornerRadiusToThicknessConverter.</summary>
      <returns>An enumeration value that specifies the conversion kind for a CornerRadiusToThicknessConverter.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.ConversionKindProperty">
      <summary>Identifies the ConversionKind dependency property.</summary>
      <returns>The identifier for the ConversionKind dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.Multiplier" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter.MultiplierProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind">
      <summary>Defines constants that specify the filter type for a CornerRadiusToThicknessConverter instance.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterBottomFromBottomLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterBottomFromBottomRight" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterLeftAndRightFromBottom">
      <summary>Filters BottomLeft and BottomRight values, sets TopLeft and TopRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterLeftAndRightFromTop">
      <summary>Filters TopLeft and TopRight values, sets BottomLeft and BottomRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterLeftFromBottomLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterLeftFromTopLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterRightFromBottomRight" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterRightFromTopRight" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterTopAndBottomFromLeft">
      <summary>Filters TopLeft and BottomLeft values, sets TopRight and BottomRight to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterTopAndBottomFromRight">
      <summary>Filters TopRight and BottomRight values, sets TopLeft and BottomLeft to 0.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterTopFromTopLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverterKind.FilterTopFromTopRight" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.IAutomationPropertiesStatics9" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.IAutomationPropertiesStatics9.GetControlType(Windows.UI.Xaml.UIElement)">
      <param name="element"></param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.IAutomationPropertiesStatics9.SetControlType(Windows.UI.Xaml.UIElement,Windows.UI.Xaml.Automation.Peers.AutomationControlType)">
      <param name="element"></param>
      <param name="value"></param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.IAutomationPropertiesStatics9.ControlType" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.IAutomationPropertiesStatics9.ControlTypeProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel">
      <summary>Represents a panel that arranges its items horizontally if there is available space, otherwise vertically.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.#ctor">
      <summary>Initializes a new instance of the InfoBarPanel class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.GetHorizontalOrientationMargin(Windows.UI.Xaml.DependencyObject)">
      <summary>Gets the HorizontalOrientationMargin from an object.</summary>
      <param name="object">The object that has an HorizontalOrientationMargin.</param>
      <returns>The object's HorizontalOrientationMargin.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.GetVerticalOrientationMargin(Windows.UI.Xaml.DependencyObject)">
      <summary>Gets the VerticalOrientationMargin from an object.</summary>
      <param name="object">The object that has an VerticalOrientationMargin.</param>
      <returns>The object's VerticalOrientationMargin.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.SetHorizontalOrientationMargin(Windows.UI.Xaml.DependencyObject,Windows.UI.Xaml.Thickness)">
      <summary>Sets the HorizontalOrientationMargin to an object.</summary>
      <param name="object">The object that the HorizontalOrientationMargin value will be set to.</param>
      <param name="value">The value of the HorizontalOrientationMargin.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.SetVerticalOrientationMargin(Windows.UI.Xaml.DependencyObject,Windows.UI.Xaml.Thickness)">
      <summary>Sets the VerticalOrientationMargin to an object.</summary>
      <param name="object">The object that the VerticalOrientationMargin value will be set to.</param>
      <param name="value">The value of the VerticalOrientationMargin.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.HorizontalOrientationMargin">
      <summary>The margin of the InfoBarPanel when its items are horizontally aligned. This property is reserved for internal use and is not intended to be used in your code.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.HorizontalOrientationMarginProperty">
      <summary>Gets the identifier for the InfoBar.HorizontalOrientationMargin dependency property.</summary>
      <returns>The identifier for the InfoBar.HorizontalOrientationMargin dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.HorizontalOrientationPadding">
      <summary>Gets and sets the distance between the edges of the InfoBarPanel and its children when the panel is oriented horizontally.</summary>
      <returns>The distance between the edges of the InfoBarPanel and its children when the panel is oriented horizontally.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.HorizontalOrientationPaddingProperty">
      <summary>Gets the identifier for the InfoBar.HorizontalOrientationPadding dependency property.</summary>
      <returns>The identifier for the InfoBar.HorizontalOrientationPadding dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.VerticalOrientationMargin">
      <summary>The margin of the InfoBarPanel when its items are vertically aligned. This property is reserved for internal use and is not intended to be used in your code.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.VerticalOrientationMarginProperty">
      <summary>Gets the identifier for the InfoBar.VerticalOrientationMargin dependency property.</summary>
      <returns>The identifier for the InfoBar.VerticalOrientationMargin dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.VerticalOrientationPadding">
      <summary>Gets and sets the distance between the edges of the InfoBarPanel and its children when the panel is oriented vertically.</summary>
      <returns>The distance between the edges of the InfoBarPanel and its children when the panel is oriented vertically.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel.VerticalOrientationPaddingProperty">
      <summary>Gets the identifier for the InfoBar.VerticalOrientationPadding dependency property.</summary>
      <returns>The identifier for the InfoBar.VerticalOrientationPadding dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.IPopup4" />
    <member name="E:Microsoft.UI.Xaml.Controls.Primitives.IPopup4.ActualPlacementChanged" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.IPopup4.ActualPlacement" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.IPopup4.DesiredPlacement" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.IPopup4.PlacementTarget" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter.#ctor" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter.ReplacementColor" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter.ReplacementColorProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter.SourceElement" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter.SourceElementProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter">
      <summary>Represents the visual elements of a NavigationViewItem.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter.#ctor">
      <summary>Initializes a new instance of the NavigationViewItemPresenter class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter.Icon">
      <summary>Gets or sets the icon in a NavigationView item.</summary>
      <returns>The NavigationView item's icon.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter.IconProperty">
      <summary>Identifies the Icon dependency property.</summary>
      <returns>The identifier for the Icon dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter.TemplateSettings" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter.TemplateSettingsProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings" />
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings.#ctor" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings.IconWidth" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings.IconWidthProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings.SmallerIconWidth" />
    <member name="P:Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings.SmallerIconWidthProperty" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.Auto" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.Bottom" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.BottomEdgeAlignedLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.BottomEdgeAlignedRight" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.Left" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.LeftEdgeAlignedBottom" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.LeftEdgeAlignedTop" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.Right" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.RightEdgeAlignedBottom" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.RightEdgeAlignedTop" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.Top" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.TopEdgeAlignedLeft" />
    <member name="F:Microsoft.UI.Xaml.Controls.Primitives.PopupPlacementMode.TopEdgeAlignedRight" />
    <member name="T:Microsoft.UI.Xaml.Controls.Primitives.TabViewListView">
      <summary>Represents the ListView corresponding to the TabStrip within the TabView.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.Primitives.TabViewListView.#ctor">
      <summary>Initializes a new instance of the TabViewListView class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ProgressBar">
      <summary>Represents a control that indicates the progress of an operation.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ProgressBar.#ctor">
      <summary>Initializes a new instance of the ProgressBar class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.IsIndeterminate">
      <summary>Gets or sets a value that indicates whether the progress bar reports generic progress with a repeating pattern or reports progress based on the Value property.</summary>
      <returns>true if the progress bar reports generic progress with a repeating pattern; false if the progress bar reports progress based on the Value property. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.IsIndeterminateProperty">
      <summary>Identifies the IsIndeterminate dependency property.</summary>
      <returns>The identifier for the IsIndeterminate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.ShowError">
      <summary>Gets or sets a value that indicates whether the progress bar should use visual states that communicate an Error state to the user.</summary>
      <returns>true if the progress bar should use visual states that communicate an Error state to the user; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.ShowErrorProperty">
      <summary>Identifies the ShowError dependency property.</summary>
      <returns>The identifier for the ShowError dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.ShowPaused">
      <summary>Gets or sets a value that indicates whether the progress bar should use visual states that communicate a Paused state to the user.</summary>
      <returns>true if the progress bar should use visual states that communicate a Paused state to the user; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.ShowPausedProperty">
      <summary>Identifies the ShowPaused dependency property.</summary>
      <returns>The identifier for the ShowPaused dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBar.TemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as TemplateBinding sources when defining templates for a ProgressBar control.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a ProgressBar control. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.ClipRect" />
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.Container2AnimationEndPosition">
      <summary>Gets the target "To" point of the secondary container animation that animates the ProgressBar.</summary>
      <returns>A double that represents the orientation-specific x- or y-value that is the target "To" point of the secondary animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.Container2AnimationStartPosition">
      <summary>Gets the "From" point of the secondary container animation that animates the ProgressBar.</summary>
      <returns>A double that represents the orientation-specific x- or y-value that is the"From" point of the secondary animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.ContainerAnimationEndPosition">
      <summary>Gets the target "To" point of the primary container animation that animates the ProgressBar.</summary>
      <returns>A double that represents the orientation-specific x- or y-value that is the target "To" point of the primary animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.ContainerAnimationMidPosition">
      <summary>Gets the target midpoint of the container animation that animates the ProgressBar.</summary>
      <returns>A double that represents the orientation-specific x- or y-value that is the midpoint of the animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.ContainerAnimationStartPosition">
      <summary>Gets the "From" point of the primary container animation that animates the ProgressBar.</summary>
      <returns>A double that represents the orientation-specific x- or y-value that is the"From" point of the primary animation.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.EllipseAnimationEndPosition">
      <summary>Gets the "To" point of the "Ellipse" animation that animates the ProgressBar.</summary>
      <returns>The "To" point of the "Ellipse" animation that animates the ProgressBar. This is internally calculated as 2/3 of the ActualWidth of the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.EllipseAnimationWellPosition">
      <summary>Gets the stopped point of the "Ellipse" animation that animates the ProgressBar.</summary>
      <returns>The stopped point of the Ellipse animation that animates the ProgressBar]. This is internally calculated as 1/3 of the [ActualWidth of the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.EllipseDiameter">
      <summary>Gets the template-defined diameter of the "Ellipse" element that is animated in a templated ProgressBar.</summary>
      <returns>The "Ellipse" element width in pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.EllipseOffset">
      <summary>Gets the template-defined offset poisition of the "Ellipse" element that is animated in a templated ProgressBar.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings.IndicatorLengthDelta">
      <summary>Gets the indicator length delta, which is useful for repositioning transitions.</summary>
      <returns>The delta in pixels.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ProgressRing">
      <summary>Represents a control that indicates the progress of an operation. The typical visual appearance is a ring-shaped "spinner".</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ProgressRing.#ctor">
      <summary>Initializes a new instance of the ProgressRing class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.IsActive">
      <summary>Gets or sets a value that indicates whether the ProgressRing is showing progress.</summary>
      <returns>True if the ProgressRing is showing progress; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.IsActiveProperty">
      <summary>Identifies the IsActive dependency property.</summary>
      <returns>The identifier for the IsActive dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.IsIndeterminate">
      <summary>Gets or sets a value that indicates whether the progress ring reports generic progress with a repeating pattern or reports progress based on the Value property.</summary>
      <returns>true if the progress ring reports generic progress with a repeating pattern; false if the progress ring reports progress based on the Value property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.IsIndeterminateProperty">
      <summary>Identifies the IsIndeterminate dependency property.</summary>
      <returns>The identifier for the IsIndeterminate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.Maximum">
      <summary>Gets or sets the highest allowed Value of the range element.</summary>
      <returns>The highest possible Value of the range element. The default is 100.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.MaximumProperty">
      <summary>Identifies the Maximum dependency property.</summary>
      <returns>The identifier for the Maximum dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.Minimum">
      <summary>Gets or sets the minimum allowed Value of the range element.</summary>
      <returns>Minimum allowed Value of the range element. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.MinimumProperty">
      <summary>Identifies the Minimum dependency property.</summary>
      <returns>The identifier for the Minimum dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.TemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as TemplateBinding sources when defining templates for a ProgressRing control.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.Value">
      <summary>Gets or sets the current magnitude of the progress ring.</summary>
      <returns>The current magnitude of the progress ring. The default is 0.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRing.ValueProperty">
      <summary>Identifies the Value dependency property.</summary>
      <returns>The identifier for the Value dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a ProgressRing control. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings.EllipseDiameter">
      <summary>Gets the template-defined diameter of the "Ellipse" element that is animated in a templated ProgressRing.</summary>
      <returns>The "Ellipse" width in pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings.EllipseOffset">
      <summary>Gets the template-defined offset position of the "Ellipse" element that is animated in a templated ProgressRing.</summary>
      <returns>The offset in pixels.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ProgressRingTemplateSettings.MaxSideLength">
      <summary>Gets the maximum bounding size of the progress ring as rendered.</summary>
      <returns>The maximum bounding size of the progress ring as rendered in pixels.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RadioButtons">
      <summary>Represents a control that shows a group of related options from which one can be selected.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.RadioButtons.SelectionChanged">
      <summary>Occurs when the currently selected item changes.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RadioButtons.#ctor">
      <summary>Initializes a new instance of the RadioButtons class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RadioButtons.ContainerFromIndex(System.Int32)">
      <summary>Returns the container for the item at the specified index within the item collection.</summary>
      <param name="index">The index of the item to retrieve.</param>
      <returns>The container for the item at the specified index within the item collection, if the item has a container; otherwise, null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.Header">
      <summary>Gets or sets the content for the group header.</summary>
      <returns>The content of the group header. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.HeaderProperty">
      <summary>Identifies the Header dependency property.</summary>
      <returns>The identifier for the Header dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.HeaderTemplate">
      <summary>Gets or sets the data template used to display the content of the control's header.</summary>
      <returns>The template that specifies the visualization of the header object. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.HeaderTemplateProperty">
      <summary>Identifies the HeaderTemplate dependency property.</summary>
      <returns>The identifier for the HeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.Items">
      <summary>Gets the collection used to generate the content of the control.</summary>
      <returns>The collection that is used to generate the content of the control, if it exists; otherwise, null. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.ItemsProperty">
      <summary>Identifies the Items dependency property.</summary>
      <returns>The identifier for the Items dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.ItemsSource">
      <summary>Gets or sets an object source used to generate the content of the control.</summary>
      <returns>The object that is used to generate the content of the control. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.ItemsSourceProperty">
      <summary>Identifies the ItemsSource dependency property.</summary>
      <returns>The identifier for the ItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.ItemTemplate">
      <summary>Gets or sets the DataTemplate used to display each item.</summary>
      <returns>The template that specifies the visualization of the data objects. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.ItemTemplateProperty">
      <summary>Identifies the ItemTemplate dependency property.</summary>
      <returns>The identifier for the ItemTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.MaxColumns">
      <summary>Gets or sets the maximum number of columns the radio buttons are arranged in.</summary>
      <returns>The maximum number of columns the radio buttons are arranged in. The default is 1.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.MaxColumnsProperty">
      <summary>Identifies the MaxColumns dependency property.</summary>
      <returns>The identifier for the MaxColumns dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.SelectedIndex">
      <summary>Gets or sets the index of the selected radio button.</summary>
      <returns>The index of the selected radio button. The default is -1, which indicates that no radio button is selected.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.SelectedIndexProperty">
      <summary>Identifies the SelectedIndex dependency property.</summary>
      <returns>The identifier for the SelectedIndex dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.SelectedItem">
      <summary>Gets or sets the selected radio button.</summary>
      <returns>The selected radio button. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioButtons.SelectedItemProperty">
      <summary>Identifies the SelectedItem dependency property.</summary>
      <returns>The identifier for the SelectedItem dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem">
      <summary>Represents a menu item that is mutually exclusive with other radio menu items in its group.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.#ctor">
      <summary>Initializes a new instance of the RadioMenuFlyoutItem class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.GetAreCheckStatesEnabled(Windows.UI.Xaml.Controls.MenuFlyoutSubItem)">
      <param name="object"></param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.SetAreCheckStatesEnabled(Windows.UI.Xaml.Controls.MenuFlyoutSubItem,System.Boolean)">
      <param name="object"></param>
      <param name="value"></param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.AreCheckStatesEnabledProperty">
      <summary>Represents whether the MenuFlyoutSubItem has RadioMenuFlyoutItems as children.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.GroupName">
      <summary>Gets or sets the name that specifies which RadioMenuFlyoutItem controls are mutually exclusive.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.GroupNameProperty">
      <summary>Identifies the GroupName dependency property.</summary>
      <returns>The identifier for the GroupName dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.IsChecked">
      <summary>Gets or sets whether the RadioMenuFlyoutItem is checked.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem.IsCheckedProperty">
      <summary>Identifies the IsChecked dependency property.</summary>
      <returns>The identifier for the IsChecked dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RatingControl">
      <summary>Represents a control that lets a user enter a star rating.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.RatingControl.ValueChanged">
      <summary>Occurs when the Value property has changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RatingControl.#ctor">
      <summary>Initializes a new instance of the RatingControl class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.Caption">
      <summary>Gets or sets the text label for the control.</summary>
      <returns>The text label for the control. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.CaptionProperty">
      <summary>Identifies the Caption dependency property.</summary>
      <returns>The identifier for Caption dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.InitialSetValue">
      <summary>Gets or sets the initial set rating value.</summary>
      <returns>The initial set rating value. The default is 1.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.InitialSetValueProperty">
      <summary>Identifies the InitialSetValue dependency property.</summary>
      <returns>The identifier for InitialSetValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.IsClearEnabled">
      <summary>Gets or sets the value that determines if the user can remove the rating.</summary>
      <returns>true if the user can remove the rating; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.IsClearEnabledProperty">
      <summary>Identifies the IsClearEnabled dependency property.</summary>
      <returns>The identifier for IsClearEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.IsReadOnly">
      <summary>Gets or sets the value that determines if the user can change the rating.</summary>
      <returns>true if the rating is read-only; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.IsReadOnlyProperty">
      <summary>Identifies the IsReadOnly dependency property.</summary>
      <returns>The identifier for IsReadOnly dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.ItemInfo">
      <summary>Gets or sets info about the visual states of the items that represent a rating.</summary>
      <returns>A RatingItemInfo-derived object that contains details about the visual states of the items that represent a rating.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.ItemInfoProperty">
      <summary>Identifies the ItemInfo dependency property.</summary>
      <returns>The identifier for ItemInfo dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.MaxRating">
      <summary>Gets or sets the maximum allowed rating value.</summary>
      <returns>The maximum allowed rating value. The default is 5.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.MaxRatingProperty">
      <summary>Identifies the MaxRating dependency property.</summary>
      <returns>The identifier for MaxRating dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.PlaceholderValue">
      <summary>Gets or sets the rating that is displayed in the control until the value is changed by a user action or some other operation.</summary>
      <returns>The rating that is displayed in the control when no value is entered. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.PlaceholderValueProperty">
      <summary>Identifies the PlaceholderValue dependency property.</summary>
      <returns>The identifier for PlaceholderValue dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.Value">
      <summary>Gets or sets the rating value.</summary>
      <returns>The rating value. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingControl.ValueProperty">
      <summary>Identifies the Value dependency property.</summary>
      <returns>The identifier for Value dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RatingItemFontInfo">
      <summary>Represents information about the visual states of font elements that represent a rating.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.#ctor">
      <summary>Initializes a new instance of the RatingItemFontInfo class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.DisabledGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element that is disabled.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.DisabledGlyphProperty">
      <summary>Identifies the DisabledGlyph dependency property.</summary>
      <returns>The identifier for DisabledGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.Glyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element that has been set by the user.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.GlyphProperty">
      <summary>Identifies the Glyph dependency property.</summary>
      <returns>The identifier for Glyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PlaceholderGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element that is showing a placeholder value.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PlaceholderGlyphProperty">
      <summary>Identifies the PlaceholderGlyph dependency property.</summary>
      <returns>The identifier for PlaceholderGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PointerOverGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element that has the pointer over it.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PointerOverGlyphProperty">
      <summary>Identifies the PointerOverGlyph dependency property.</summary>
      <returns>The identifier for PointerOverGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PointerOverPlaceholderGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element showing a placeholder value with the pointer over it.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.PointerOverPlaceholderGlyphProperty">
      <summary>Identifies the PointerOverPlaceholderGlyph dependency property.</summary>
      <returns>The identifier for PointerOverPlaceholderGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.UnsetGlyph">
      <summary>Gets or sets a Segoe MDL2 Assets font glyph that represents a rating element that has not been set.</summary>
      <returns>The hexadecimal character code for the rating element glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemFontInfo.UnsetGlyphProperty">
      <summary>Identifies the UnsetGlyph dependency property.</summary>
      <returns>The identifier for UnsetGlyph dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RatingItemImageInfo">
      <summary>Represents information about the visual states of image elements that represent a rating.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.#ctor">
      <summary>Initializes a new instance of the RatingItemImageInfo class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.DisabledImage">
      <summary>Gets or sets an image that represents a rating element that is disabled.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.DisabledImageProperty">
      <summary>Identifies the DisabledImage dependency property.</summary>
      <returns>The identifier for DisabledImage dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.Image">
      <summary>Gets or sets an image that represents a rating element that has been set by the user.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.ImageProperty">
      <summary>Identifies the Image dependency property.</summary>
      <returns>The identifier for Image dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PlaceholderImage">
      <summary>Gets or sets an image that represents a rating element that is showing a placeholder value.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PlaceholderImageProperty">
      <summary>Identifies the PlaceholderImage dependency property.</summary>
      <returns>The identifier for PlaceholderImage dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PointerOverImage">
      <summary>Gets or sets an image that represents a rating element that has the pointer over it.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PointerOverImageProperty">
      <summary>Identifies the PointerOverImage dependency property.</summary>
      <returns>The identifier for PointerOverImage dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PointerOverPlaceholderImage">
      <summary>Gets or sets an image that represents a rating element showing a placeholder value with the pointer over it.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.PointerOverPlaceholderImageProperty">
      <summary>Identifies the PointerOverPlaceholderImage dependency property.</summary>
      <returns>The identifier for PointerOverPlaceholderImage dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.UnsetImage">
      <summary>Gets or sets an image that represents a rating element that has not been set.</summary>
      <returns>An object that represents the image source file for the drawn image. Typically you set this with a BitmapImage with a stream, perhaps a stream from a storage file.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RatingItemImageInfo.UnsetImageProperty">
      <summary>Identifies the UnsetImage dependency property.</summary>
      <returns>The identifier for UnsetImage dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RatingItemInfo">
      <summary>Represents information about the visual states of the elements that represent a rating.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RatingItemInfo.#ctor">
      <summary>Initializes a new instance of the RatingItemInfo class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshContainer">
      <summary>Represents a container control that provides a RefreshVisualizer and pull-to-refresh functionality for scrollable content.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.RefreshContainer.RefreshRequested">
      <summary>Occurs when an update of the content has been initiated.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RefreshContainer.#ctor">
      <summary>Initializes a new instance of the RefreshContainer control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RefreshContainer.RequestRefresh">
      <summary>Initiates an update of the content.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshContainer.PullDirection">
      <summary>Gets or sets a value that specifies the direction to pull to initiate a refresh.</summary>
      <returns>An enumeration value that specifies the direction to pull to initiate a refresh.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshContainer.PullDirectionProperty">
      <summary>Identifies the PullDirection dependency property.</summary>
      <returns>The identifier for the PullDirection dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshContainer.Visualizer">
      <summary>Gets or sets the RefreshVisualizer for this container.</summary>
      <returns>The RefreshVisualizer for this container.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshContainer.VisualizerProperty">
      <summary>Identifies the Visualizer dependency property.</summary>
      <returns>The identifier for the Visualizer dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshInteractionRatioChangedEventArgs">
      <summary>Provides event data.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshInteractionRatioChangedEventArgs.InteractionRatio">
      <summary>Gets the interaction ratio value.</summary>
      <returns>The interaction ratio value.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshPullDirection">
      <summary>Defines constants that specify the direction to pull a RefreshContainer to initiate a refresh.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshPullDirection.BottomToTop">
      <summary>Pull from bottom to top to initiate a refresh.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshPullDirection.LeftToRight">
      <summary>Pull from left to right to initiate a refresh.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshPullDirection.RightToLeft">
      <summary>Pull from right to left to initiate a refresh.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshPullDirection.TopToBottom">
      <summary>Pull from top to bottom to initiate a refresh.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshRequestedEventArgs">
      <summary>Provides event data for RefreshRequested events.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RefreshRequestedEventArgs.GetDeferral">
      <summary>Gets a deferral object for managing the work done in the RefreshRequested event handler.</summary>
      <returns>A deferral object.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshStateChangedEventArgs">
      <summary>Provides event data for the RefreshVisualizer.RefreshStateChanged" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshStateChangedEventArgs.NewState">
      <summary>Gets a value that indicates the new state of the RefreshVisualizer.</summary>
      <returns>An enumeration value that indicates the new state of the RefreshVisualizer.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshStateChangedEventArgs.OldState">
      <summary>Gets a value that indicates the previous state of the RefreshVisualizer.</summary>
      <returns>An enumeration value that indicates the previous state of the RefreshVisualizer.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshVisualizer">
      <summary>Represents a control that provides animated state indicators for content refresh.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.RefreshVisualizer.RefreshRequested">
      <summary>Occurs when an update of the content has been initiated.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.RefreshVisualizer.RefreshStateChanged">
      <summary>Occurs when the state of the visualizer changes.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RefreshVisualizer.#ctor">
      <summary>Initializes a new instance of the RefreshVisualizer class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RefreshVisualizer.RequestRefresh">
      <summary>Initiates an update of the content.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.Content">
      <summary>Gets or sets the content of the visualizer.</summary>
      <returns>The content of the visualizer.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.ContentProperty">
      <summary>Identifies the Content dependency property.</summary>
      <returns>The identifier for the Content dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.InfoProviderProperty">
      <summary>Identifies the InfoProvider dependency property.</summary>
      <returns>The identifier for the InfoProvider dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.Orientation">
      <summary>Gets or sets a value that indicates the orientation of the visualizer.</summary>
      <returns>A value of the enumeration that indicates the orientation of the visualizer. The default is Top.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.OrientationProperty">
      <summary>Identifies the Orientation dependency property.</summary>
      <returns>The identifier for the Orientation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.State">
      <summary>Gets a value that indicates the state of the visualizer.</summary>
      <returns>A value of the enumeration that indicates the state of the visualizer.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.RefreshVisualizer.StateProperty">
      <summary>Identifies the State dependency property.</summary>
      <returns>The identifier for the State dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshVisualizerOrientation">
      <summary>Defines constants that specify the orientation of a RefreshVisualizer.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerOrientation.Auto" />
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerOrientation.Normal" />
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerOrientation.Rotate270DegreesCounterclockwise" />
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerOrientation.Rotate90DegreesCounterclockwise" />
    <member name="T:Microsoft.UI.Xaml.Controls.RefreshVisualizerState">
      <summary>Defines constants that specify the state of a RefreshVisualizer</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerState.Idle">
      <summary>The visualizer is idle.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerState.Interacting">
      <summary>The user is interacting with the visualizer.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerState.Peeking">
      <summary>The visualizer was pulled in the refresh direction from a position where a refresh is not allowed. Typically, the ScrollViewer was not at position 0 at the start of the pull.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerState.Pending">
      <summary>The visualizer is pending.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.RefreshVisualizerState.Refreshing">
      <summary>The visualizer is being refreshed.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.RevealListViewItemPresenter">
      <summary>Represents a specialized ListViewItemPresenter that supports custom visual state management.

&gt; [!NOTE]
&gt; No longer required. Please use ListViewItemPresenter in Windows 10 April 2018 Update (version 1803) and newer.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.RevealListViewItemPresenter.#ctor">
      <summary>Initializes a new instance of the RevealListViewItemPresenter class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SplitButton">
      <summary>Represents a button with two parts that can be invoked separately. One part behaves like a standard button and the other part invokes a flyout.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.SplitButton.Click">
      <summary>Occurs when a button control is clicked.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SplitButton.#ctor">
      <summary>Initializes a new instance of the SplitButton class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.Command">
      <summary>Gets or sets the command to invoke when this button is pressed.</summary>
      <returns>The command to invoke when this button is pressed. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.CommandParameter">
      <summary>Gets or sets the parameter to pass to the Command property.</summary>
      <returns>The parameter to pass to the Command property. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.CommandParameterProperty">
      <summary>Identifies the CommandParameter dependency property.</summary>
      <returns>The identifier for the CommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.CommandProperty">
      <summary>Identifies the Command dependency property.</summary>
      <returns>The identifier for the Command dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.Flyout">
      <summary>Gets or sets the flyout associated with this button.</summary>
      <returns>The flyout associated with this button. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SplitButton.FlyoutProperty">
      <summary>Identifies the Flyout dependency property.</summary>
      <returns>The identifier for the Flyout dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SplitButtonClickEventArgs">
      <summary>Provides event data for the SplitButton.Click".</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.StackLayout">
      <summary>Arranges the child elements of an ItemsRepeater into a single line that can be oriented horizontally or vertically.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.StackLayout.#ctor">
      <summary>Initializes a new instance of the StackLayout class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.StackLayout.Orientation">
      <summary>Gets or sets the axis along which items are laid out.</summary>
      <returns>One of the enumeration values that specifies the axis along which items are laid out. The default is Vertical.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.StackLayout.OrientationProperty">
      <summary>Identifies the Orientation dependency property.</summary>
      <returns>The identifier for the Orientation dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.StackLayout.Spacing">
      <summary>Gets or sets a uniform distance (in pixels) between stacked items. It is applied in the direction of the StackLayout's Orientation.</summary>
      <returns>The uniform distance (in pixels) between stacked items.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.StackLayout.SpacingProperty">
      <summary>Identifies the Spacing dependency property.</summary>
      <returns>The identifier for the Spacing dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.StylesVersion" />
    <member name="F:Microsoft.UI.Xaml.Controls.StylesVersion.Latest" />
    <member name="F:Microsoft.UI.Xaml.Controls.StylesVersion.WinUI_2dot5" />
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeBehaviorOnInvoked">
      <summary>Defines constants that specify how a SwipeControl behaves after a command is invoked.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.SwipeBehaviorOnInvoked.Auto">
      <summary>In Reveal mode, the SwipeControl closes after an item is invoked. In Execute mode, the SwipeControl remains open.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.SwipeBehaviorOnInvoked.Close">
      <summary>The SwipeControl closes after an item is invoked.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.SwipeBehaviorOnInvoked.RemainOpen">
      <summary>The SwipeControl remains open after an item is invoked.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeControl">
      <summary>Represents a container that provides access to, and the ability to invoke, contextual commands through a basic touch interaction.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeControl.#ctor">
      <summary>Initializes a new instance of the SwipeControl class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeControl.Close">
      <summary>Closes the swipe control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.BottomItems">
      <summary>Gets or sets the items that can be invoked when the control is swiped from the bottom up.</summary>
      <returns>The items that can be invoked when the control is swiped from the bottom up.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.BottomItemsProperty">
      <summary>Identifies the BottomItems dependency property.</summary>
      <returns>The identifier for the BottomItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.LeftItems">
      <summary>Gets or sets the items that can be invoked when the control is swiped from the left side.</summary>
      <returns>The items that can be invoked when the control is swiped from the left side.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.LeftItemsProperty">
      <summary>Identifies the LeftItems dependency property.</summary>
      <returns>The identifier for the LeftItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.RightItems">
      <summary>Gets or sets the items that can be invoked when the control is swiped from the right side.</summary>
      <returns>The items that can be invoked when the control is swiped from the right side.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.RightItemsProperty">
      <summary>Identifies the RightItems dependency property.</summary>
      <returns>The identifier for the RightItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.TopItems">
      <summary>Gets or sets the items that can be invoked when the control is swiped from the top down.</summary>
      <returns>The items that can be invoked when the control is swiped from the top down.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeControl.TopItemsProperty">
      <summary>Identifies the TopItems dependency property.</summary>
      <returns>The identifier for the TopItems dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeItem">
      <summary>Represents an individual command in a SwipeControl.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.SwipeItem.Invoked">
      <summary>Occurs when user interaction indicates that the command represented by this item should execute.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItem.#ctor">
      <summary>Initializes a new instance of the SwipeItem class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.Background">
      <summary>Gets or sets a brush that provides the background of the control.</summary>
      <returns>The brush that provides the background of the control.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.BackgroundProperty">
      <summary>Identifies the Background dependency property.</summary>
      <returns>The identifier for the Background dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.BehaviorOnInvoked">
      <summary>Gets or sets a value that indicates how a SwipeControl behaves after this item is invoked.</summary>
      <returns>A value that indicates how a SwipeControl behaves after this item is invoked. The default is Auto.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.BehaviorOnInvokedProperty">
      <summary>Identifies the BehaviorOnInvoked dependency property.</summary>
      <returns>The identifier for the BehaviorOnInvoked dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.Command">
      <summary>Gets or sets the command to execute when this item is invoked.</summary>
      <returns>The command to execute when this item is invoked. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.CommandParameter">
      <summary>Gets or sets the parameter to pass to the Command property.</summary>
      <returns>The parameter to pass to the Command property. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.CommandParameterProperty">
      <summary>Identifies the CommandParameter dependency property.</summary>
      <returns>The identifier for the CommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.CommandProperty">
      <summary>Identifies the Command dependency property.</summary>
      <returns>The identifier for the Command dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.Foreground">
      <summary>Gets or sets the brush that paints the text and icon of the item.</summary>
      <returns>The brush that paints the text and icon of the item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.ForegroundProperty">
      <summary>Identifies the Foreground dependency property.</summary>
      <returns>The identifier for the Foreground dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.IconSource">
      <summary>Gets or sets the graphic content of the item.</summary>
      <returns>The graphic content of the item. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.IconSourceProperty">
      <summary>Identifies the IconSource dependency property.</summary>
      <returns>The identifier for the IconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.Text">
      <summary>Gets or sets the text description displayed on the item.</summary>
      <returns>The text description displayed on the item. The default is an empty string.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItem.TextProperty">
      <summary>Identifies the Text dependency property.</summary>
      <returns>The identifier for the Text dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeItemInvokedEventArgs">
      <summary>Provides event data for the SwipeItem.Invoked" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItemInvokedEventArgs.SwipeControl">
      <summary>Gets the SwipeControl that owns the invoked item.</summary>
      <returns>The SwipeControl that owns the invoked item.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeItems">
      <summary>Represents a collection of SwipeItem objects.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.#ctor">
      <summary>Initializes a new instance of the SwipeItems class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.Append(Microsoft.UI.Xaml.Controls.SwipeItem)">
      <summary>Adds a new item to the collection.</summary>
      <param name="value">The new item to add.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.Clear">
      <summary>Removes all items from the collection.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.First">
      <summary>Returns an iterator for the items in the collection.</summary>
      <returns>The iterator object. The iterator's current position is the 0-index position, or at the collection end if the collection is empty.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.GetAt(System.UInt32)">
      <summary>Returns the item located at the specified index.</summary>
      <param name="index">The integer index for the value to retrieve.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.GetMany(System.UInt32,Microsoft.UI.Xaml.Controls.SwipeItem[])">
      <summary>Retrieves multiple elements in a single pass through the iterator.</summary>
      <param name="startIndex">The index from which to start retrieval.</param>
      <param name="items">Provides the destination for the result. Size the initial array size as a "capacity" in order to specify how many results should be retrieved.</param>
      <returns>The number of items retrieved.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.GetView">
      <summary>Gets an immutable view into the collection.</summary>
      <returns>An object representing the immutable collection view.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.IndexOf(Microsoft.UI.Xaml.Controls.SwipeItem,System.UInt32@)">
      <summary>Retrieves the index of the specified item.</summary>
      <param name="value">The value to find in the collection.</param>
      <param name="index">The index of the item to find, if found.</param>
      <returns>true if an item with the specified value was found; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.InsertAt(System.UInt32,Microsoft.UI.Xaml.Controls.SwipeItem)">
      <summary>Inserts the specified item at the specified index.</summary>
      <param name="index">The index at which to set the value.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.RemoveAt(System.UInt32)">
      <summary>Removes the item at the specified index.</summary>
      <param name="index">The index position of the item to remove.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.RemoveAtEnd">
      <summary>Removes the last item in the collection.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.ReplaceAll(Microsoft.UI.Xaml.Controls.SwipeItem[])">
      <summary>Initially clears the collection, then inserts the provided array as new items.</summary>
      <param name="items">The new collection items.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SwipeItems.SetAt(System.UInt32,Microsoft.UI.Xaml.Controls.SwipeItem)">
      <summary>Sets the value at the specified index to the value specified.</summary>
      <param name="index">The index at which to set the value.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItems.Mode">
      <summary>Gets or sets a value that indicates the effect of a swipe interaction.</summary>
      <returns>A value of the enumeration that indicates the effect of a swipe interaction. The default is Reveal.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItems.ModeProperty">
      <summary>Identifies the Mode dependency property.</summary>
      <returns>The identifier for the Mode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SwipeItems.Size">
      <summary>Gets the size (count) of the collection.</summary>
      <returns>The count of items in the collection.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SwipeMode">
      <summary>Defines constants that specify the effect of a swipe interaction.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.SwipeMode.Execute">
      <summary>A swipe executes a command.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.SwipeMode.Reveal">
      <summary>A swipe reveals a menu of commands.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.SymbolIconSource">
      <summary>Represents an icon source that uses a glyph from the Segoe MDL2 Assets font as its content.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.SymbolIconSource.#ctor">
      <summary>Initializes a new instance of the SymbolIconSource class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SymbolIconSource.Symbol">
      <summary>Gets or sets the Segoe MDL2 Assets glyph used as the icon content.</summary>
      <returns>A named constant of the numeration that specifies the Segoe MDL2 Assets glyph to use.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.SymbolIconSource.SymbolProperty">
      <summary>Identifies the Symbol dependency property.</summary>
      <returns>The identifier for the Symbol dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabView">
      <summary>The TabView control is a way to display a set of tabs and their respective content. Tab controls are useful for displaying several pages (or documents) of content while giving a user the capability to rearrange, open, or close new tabs.

**Is this the right control?**

Use a TabView to help the user manage multiple app pages or documents within the same window.

Do not use a TabView to display a static set of tabs that the user cannot rearrange, open, or close. Use a NavigationView (NavigationViewPaneDisplayMode of Top) instead.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.AddTabButtonClick">
      <summary>Occurs when the add (+) tab button has been clicked.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.SelectionChanged">
      <summary>Occurs when the currently selected tab changes.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabCloseRequested">
      <summary>Raised when the user attempts to close a Tab via clicking the x-to-close button, CTRL+F4, or mousewheel.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabDragCompleted">
      <summary>Raised when the user completes the drag action.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabDragStarting">
      <summary>Occurs when a drag operation is initiated.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabDroppedOutside">
      <summary>Occurs when the user completes a drag and drop operation by dropping a tab outside of the TabStrip area.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabItemsChanged">
      <summary>Raised when the items collection has changed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabStripDragOver">
      <summary>Occurs when the input system reports an underlying drag event with the TabStrip as the potential drop target.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabView.TabStripDrop">
      <summary>Occurs when the input system reports an underlying drop event with the TabStrip as the drop target.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TabView.#ctor">
      <summary>Initializes a new instance of the TabView class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TabView.ContainerFromIndex(System.Int32)">
      <summary>Returns the container for the item at the specified index within the collection.</summary>
      <param name="index">The index of the item to retrieve.</param>
      <returns>The container for the item at the specified index within the item collection, if the item has a container; otherwise, null.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TabView.ContainerFromItem(System.Object)">
      <summary>Returns the container corresponding to the specified item.</summary>
      <param name="item">The item to retrieve the container for.</param>
      <returns>A container that corresponds to the specified item, if the item has a container and exists in the collection; otherwise, null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AddTabButtonCommand">
      <summary>Gets or sets the command to invoke when the add (+) button is tapped.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AddTabButtonCommandParameter">
      <summary>Gets or sets the parameter to pass to the AddTabButtonCommand property.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AddTabButtonCommandParameterProperty">
      <summary>Identifies the AddTabButtonCommandParameter dependency property.</summary>
      <returns>The identifier for the AddTabButtonCommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AddTabButtonCommandProperty">
      <summary>Identifies the AddButtonCommand dependency property.</summary>
      <returns>The identifier for the AddButtonCommand dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AllowDropTabs">
      <summary>Gets or sets a value that determines whether the TabView can be a drop target for the purposes of drag-and-drop operations.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.AllowDropTabsProperty">
      <summary>Identifies the AllowDropTabs dependency property.</summary>
      <returns>The identifier for the AllowDropTabs dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CanDragTabs">
      <summary>Gets or sets a value that indicates whether tabs can be dragged as a data payload.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CanDragTabsProperty">
      <summary>Identifies the CanDragTabs dependency property.</summary>
      <returns>The identifier for the CanDragTabs dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CanReorderTabs">
      <summary>Gets or sets a value that indicates whether the tabs in the TabStrip can be reordered through user interaction.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CanReorderTabsProperty">
      <summary>Identifies the CanReorderTabs dependency property.

Identifies the CanReorderTabs dependency property.</summary>
      <returns>The identifier for the CanReorderTabs dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CloseButtonOverlayMode">
      <summary>Gets or sets a value that indicates the behavior of the close button within tabs.</summary>
      <returns>A value of the enumeration that describes the behavior of the close button within tabs. The default is Auto.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.CloseButtonOverlayModeProperty">
      <summary>Identifies the CloseButtonOverlayMode dependency property.</summary>
      <returns>The identifier for the CloseButtonOverlayMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.IsAddTabButtonVisible">
      <summary>Gets or sets whether the add (+) tab button is visible.</summary>
      <returns>Whether the add (+) tab button is visible.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.IsAddTabButtonVisibleProperty">
      <summary>Identifies the IsAddTabButtonVisible dependency property.</summary>
      <returns>The identifier for the IsAddTabButtonVisible dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.SelectedIndex">
      <summary>Gets or sets the index of the selected item.</summary>
      <returns>The index of the selected item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.SelectedIndexProperty">
      <summary>Identifies the SelectedIndex dependency property.</summary>
      <returns>The identifier for the SelectedIndex dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.SelectedItem">
      <summary>Gets or sets the selected item.</summary>
      <returns>The selected item.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.SelectedItemProperty">
      <summary>Identifies the SelectedItem dependency property.</summary>
      <returns>The identifier for the SelectedItem dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItems">
      <summary>Gets the collection used to generate the tabs within the control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemsProperty">
      <summary>Identifies the TabItems dependency property.</summary>
      <returns>The identifier for the TabItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemsSource">
      <summary>Gets or sets an object source used to generate the tabs within the TabView.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemsSourceProperty">
      <summary>Identifies the TabItemsSource dependency property.</summary>
      <returns>The identifier for the TabItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemTemplate">
      <summary>Gets or sets the DataTemplate used to display each item.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemTemplateProperty">
      <summary>Identifies the TabItemTemplate dependency property.</summary>
      <returns>The identifier for the TabItemTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemTemplateSelector">
      <summary>Gets or sets a selection object that changes the DataTemplate to apply for content, based on processing information about the content item or its container at run time.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabItemTemplateSelectorProperty">
      <summary>Identifies the TabItemTemplateSelector dependency property.</summary>
      <returns>The identifier for the TabItemTemplateSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripFooter">
      <summary>Gets or sets the content that is shown to the right of the tab strip.</summary>
      <returns>The element that is shown to the right of the tab strip.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripFooterProperty">
      <summary>Identifies the TabStripFooter dependency property.</summary>
      <returns>The identifier for the TabStripFooter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripFooterTemplate">
      <summary>Gets or sets the DataTemplate used to display the content of the TabStripFooter.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripFooterTemplateProperty">
      <summary>Identifies the TabStripFooterTemplate dependency property.</summary>
      <returns>The identifier for the TabStripFooterTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripHeader">
      <summary>Gets or sets the content that is shown to the left of the tab strip.</summary>
      <returns>The element that is shown to the left of the tab strip.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripHeaderProperty">
      <summary>Identifies the TabStripHeader dependency property.</summary>
      <returns>The identifier for the TabStripHeader dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripHeaderTemplate">
      <summary>Gets or sets the DataTemplate used to display the content of the TabStripHeader.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabStripHeaderTemplateProperty">
      <summary>Identifies the TabStripHeaderTemplate dependency property.</summary>
      <returns>The identifier for the TabStripHeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabWidthMode">
      <summary>Gets or sets how the tabs should be sized.</summary>
      <returns>The enum for how the tabs should be sized.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabView.TabWidthModeProperty">
      <summary>Identifies the TabWidthMode dependency property.</summary>
      <returns>The identifier for the TabWidthMode dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewCloseButtonOverlayMode">
      <summary>Defines constants that describe the behavior of the close button contained within each TabViewItem.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewCloseButtonOverlayMode.Always">
      <summary>The selected tab always shows the close button if it is closable. Unselected tabs always show the close button if they are closable.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewCloseButtonOverlayMode.Auto">
      <summary>Behavior is defined by the framework. Default.

This value maps to Always.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewCloseButtonOverlayMode.OnPointerOver">
      <summary>The selected tab always shows the close button if it is closable. Unselected tabs show the close button when the tab is closable and the user has their pointer over the tab.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewItem">
      <summary>Represents a single tab within a TabView.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TabViewItem.CloseRequested">
      <summary>Raised when the user attempts to close the TabViewItem via clicking the x-to-close button, CTRL+F4, or mousewheel.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TabViewItem.#ctor">
      <summary>Initializes a new instance of the TabViewItem class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.Header">
      <summary>Gets or sets the content that appears inside the tab strip to represent the tab.</summary>
      <returns>The content that appears inside the tab strip to represent the tab.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.HeaderProperty">
      <summary>Identifies the Header dependency property.</summary>
      <returns>The identifier for the Header dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.HeaderTemplate">
      <summary>Gets or sets the DataTemplate used to display the content to the right of the tab strip.</summary>
      <returns>The DataTemplate used to display the content to the right of the tab strip.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.HeaderTemplateProperty">
      <summary>Identifies the HeaderTemplate dependency property.</summary>
      <returns>The identifier for the HeaderTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.IconSource">
      <summary>Gets or sets the value for the IconSource to be displayed within the tab.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.IconSourceProperty">
      <summary>Identifies the IconSource dependency property.</summary>
      <returns>The identifier for the IconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.IsClosable">
      <summary>Gets or sets the value that determines if the tab shows a close button. The default is true.</summary>
      <returns>Determines if the tab shows a close button. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.IsClosableProperty">
      <summary>Identifies the IsClosable dependency property.</summary>
      <returns>The identifier for the IsClosable dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.TabViewTemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as {TemplateBinding} markup extension sources when defining templates for a TabViewItem control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItem.TabViewTemplateSettingsProperty">
      <summary>Identifies the TabViewTemplateSettings dependency property.</summary>
      <returns>The identifier for the TabViewTemplateSettings dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as {TemplateBinding} markup extension sources when defining templates for a TabViewItem control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings.#ctor">
      <summary>Initializes a new instance of the TabViewItemTemplateSettings class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings.IconElement">
      <summary>Gets an object that provides calculated values that can be referenced as {TemplateBinding} markup extension sources when defining templates for a TabViewItem control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings.IconElementProperty">
      <summary>Identifies the IconElement dependency property.</summary>
      <returns>The identifier for the IconElement dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewTabCloseRequestedEventArgs">
      <summary>Provides data for a tab close event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabCloseRequestedEventArgs.Item">
      <summary>Gets a value that represents the data context for the tab in which a close is being requested.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabCloseRequestedEventArgs.Tab">
      <summary>Gets the tab in which a close is being requested.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewTabDragCompletedEventArgs">
      <summary>Provides data for the TabView.TabDragCompleted" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragCompletedEventArgs.DropResult">
      <summary>Gets a value that indicates what operation was performed on the dragged data, and whether it was successful.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragCompletedEventArgs.Item">
      <summary>Gets the item that was selected for the drag action.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragCompletedEventArgs.Tab">
      <summary>Gets the TabViewItem that was selected for the drag action.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewTabDragStartingEventArgs">
      <summary>Provides data for the TabView.TabDragStarting" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragStartingEventArgs.Cancel">
      <summary>Gets or sets a value that indicates whether the drag action should be cancelled.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragStartingEventArgs.Data">
      <summary>Gets the data payload associated with a drag action.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragStartingEventArgs.Item">
      <summary>Gets the item that was selected for the drag action.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDragStartingEventArgs.Tab">
      <summary>Gets the TabViewItem that was selected for the drag action.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewTabDroppedOutsideEventArgs">
      <summary>Provides data for the TabView.TabDroppedOutside" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDroppedOutsideEventArgs.Item">
      <summary>Gets the item that was dropped outside of the TabStrip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TabViewTabDroppedOutsideEventArgs.Tab">
      <summary>Gets the TabViewItem that was dropped outside of the TabStrip.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TabViewWidthMode">
      <summary>Defines constants that specify the width of the tabs.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewWidthMode.Compact">
      <summary>Unselected tabs collapse to show only their icon. The selected tab adjusts to display the content within the tab.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewWidthMode.Equal">
      <summary>Each tab has the same width.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TabViewWidthMode.SizeToContent">
      <summary>Each tab adjusts its width to the content within the tab.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTip">
      <summary>A teaching tip is a notification flyout used to provide contextually relevant information. It supports rich content (including titles, subtitles, icons, images, and text) and can be configured for either explicit or light-dismiss.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonClick">
      <summary>Occurs after the action button is clicked.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonClick">
      <summary>Occurs after the close button is clicked.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TeachingTip.Closed">
      <summary>Occurs after the tip is closed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TeachingTip.Closing">
      <summary>Occurs just before the tip begins to close.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TeachingTip.#ctor">
      <summary>Initializes a new instance of the TeachingTip class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonCommand">
      <summary>Gets or sets the command to invoke when the action button is clicked.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonCommandParameter">
      <summary>Gets or sets the parameter to pass to the command for the action button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonCommandParameterProperty">
      <summary>Identifies the ActionButtonCommandParameter dependency property.</summary>
      <returns>The identifier for the ActionButtonCommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonCommandProperty">
      <summary>Identifies the ActionButtonCommand dependency property.</summary>
      <returns>The identifier for the ActionButtonCommand dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonContent">
      <summary>Gets or sets the text of the teaching tip's action button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonContentProperty">
      <summary>Identifies the ActionButtonContent dependency property.</summary>
      <returns>The identifier for the ActionButtonContent dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonStyle">
      <summary>Gets or sets the Style to apply to the action button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ActionButtonStyleProperty">
      <summary>Identifies the ActionButtonStyle dependency property.</summary>
      <returns>The identifier for the ActionButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonCommand">
      <summary>Gets or sets the command to invoke when the close button is clicked.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonCommandParameter">
      <summary>Gets or sets the parameter to pass to the command for the close button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonCommandParameterProperty">
      <summary>Identifies the CloseButtonCommandParameter dependency property.</summary>
      <returns>The identifier for the CloseButtonCommandParameter dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonCommandProperty">
      <summary>Identifies the CloseButtonCommand dependency property.</summary>
      <returns>The identifier for the CloseButtonCommand dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonContent">
      <summary>Gets or sets the content of the teaching tip's close button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonContentProperty">
      <summary>Identifies the CloseButtonContent dependency property.</summary>
      <returns>The identifier for the CloseButtonContent dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonStyle">
      <summary>Gets or sets the Style to apply to the teaching tip's close button.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.CloseButtonStyleProperty">
      <summary>Identifies the CloseButtonStyle dependency property.</summary>
      <returns>The identifier for the CloseButtonStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.HeroContent">
      <summary>Border-to-border graphic content displayed in the header or footer of the teaching tip. Will appear opposite of the tail in targeted teaching tips unless otherwise set.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.HeroContentPlacement">
      <summary>Placement of the hero content within the teaching tip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.HeroContentPlacementProperty">
      <summary>Identifies the HeroContentPlacement dependency property.</summary>
      <returns>The identifier for the HeroContentPlacement dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.HeroContentProperty">
      <summary>Identifies the HeroContent dependency property.</summary>
      <returns>The identifier for the HeroContent dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IconSource">
      <summary>Gets or sets the graphic content to appear alongside the title and subtitle.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IconSourceProperty">
      <summary>Identifies the IconSource dependency property.</summary>
      <returns>The identifier for the IconSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IsLightDismissEnabled">
      <summary>Enables light-dismiss functionality so that a teaching tip will dismiss when a user scrolls or interacts with other elements of the application.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IsLightDismissEnabledProperty">
      <summary>Identifies the IsLightDismissEnabled dependency property.</summary>
      <returns>The identifier for the IsLightDismissEnabled dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IsOpen">
      <summary>Gets or sets a value that indicates whether the teaching tip is open.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.IsOpenProperty">
      <summary>Identifies the IsOpen dependency property.</summary>
      <returns>The identifier for the IsOpen dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.PlacementMargin">
      <summary>Adds a margin between a targeted teaching tip and its target or between a non-targeted teaching tip and the xaml root.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.PlacementMarginProperty">
      <summary>Identifies the PlacementMargin dependency property.</summary>
      <returns>The identifier for the PlacementMargin dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.PreferredPlacement">
      <summary>Preferred placement to be used for the teaching tip. If there is not enough space to show at the preferred placement, a new placement will be automatically chosen. 
Placement is relative to its target if Target is non-null or to the parent window of the teaching tip if Target is null.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.PreferredPlacementProperty">
      <summary>Identifies the PreferredPlacement dependency property.</summary>
      <returns>The identifier for the PreferredPlacement dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ShouldConstrainToRootBounds">
      <summary>Gets or sets a value that indicates whether the teaching tip will constrain to the bounds of its xaml root.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.ShouldConstrainToRootBoundsProperty">
      <summary>Identifies the ShouldConstrainToRootBounds dependency property.</summary>
      <returns>The identifier for the ShouldConstrainToRootBounds dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.Subtitle">
      <summary>Gets or sets the subtitle of the teaching tip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.SubtitleProperty">
      <summary>Identifies the Subtitle dependency property.</summary>
      <returns>The identifier for the Subtitle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TailVisibility">
      <summary>Toggles collapse of a teaching tip's tail. Can be used to override auto behavior to make a tail visible on a non-targeted teaching tip and hidden on a targeted teaching tip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TailVisibilityProperty">
      <summary>Identifies the TailVisibility dependency property.</summary>
      <returns>The identifier for the TailVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.Target">
      <summary>Sets the target for a teaching tip to position itself relative to and point at with its tail.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TargetProperty">
      <summary>Identifies the Target dependency property.</summary>
      <returns>The identifier for the Target dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a TeachingTip. Not intended for general use.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TemplateSettingsProperty">
      <summary>Identifies the TemplateSettings dependency property.</summary>
      <returns>The identifier for the TemplateSettings dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.Title">
      <summary>Gets or sets the title of the teaching tip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTip.TitleProperty">
      <summary>Identifies the Title dependency property.</summary>
      <returns>The identifier for the Title dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipClosedEventArgs">
      <summary>Provides data for the TeachingTip.Closed" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipClosedEventArgs.Reason">
      <summary>Gets a constant that specifies whether the cause of the Closed event was due to user interaction (Close button click), light-dismissal, or programmatic closure.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipCloseReason">
      <summary>Defines constants that indicate the cause of the TeachingTip closure.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipCloseReason.CloseButton">
      <summary>The teaching tip was closed by the user clicking the close button.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipCloseReason.LightDismiss">
      <summary>The teaching tip was closed by light-dismissal.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipCloseReason.Programmatic">
      <summary>The teaching tip was programmatically closed.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipClosingEventArgs">
      <summary>Provides data for the TeachingTip.Closing" event.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TeachingTipClosingEventArgs.GetDeferral">
      <summary>Gets a deferral object for managing the work done in the Closing event handler.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipClosingEventArgs.Cancel">
      <summary>Gets or sets a value that indicates whether the Closing event should be canceled.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipClosingEventArgs.Reason">
      <summary>Gets a constant that specifies whether the cause of the Closing event was due to user interaction (Close button click), light-dismissal, or programmatic closure.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipHeroContentPlacementMode">
      <summary>Defines constants that indicate the preferred location of the HeroContent within a teaching tip.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipHeroContentPlacementMode.Auto">
      <summary>The header of the teaching tip.

The hero content might be moved to the footer to avoid intersecting with the tail of the targeted teaching tip.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipHeroContentPlacementMode.Bottom">
      <summary>The footer of the teaching tip.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipHeroContentPlacementMode.Top">
      <summary>The header of the teaching tip.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode">
      <summary>Defines constants that indicate the preferred location of the TeachingTip teaching tip.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Auto">
      <summary>Along the bottom side of the xaml root when non-targeted and above the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Bottom">
      <summary>Along the bottom side of the xaml root when non-targeted and below the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.BottomLeft">
      <summary>The bottom left corner of the xaml root when non-targeted and below the target element expanding leftward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.BottomRight">
      <summary>The bottom right corner of the xaml root when non-targeted and below the target element expanding rightward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Center">
      <summary>The center of the xaml root when non-targeted and pointing at the center of the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Left">
      <summary>Along the left side of the xaml root when non-targeted and left of the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.LeftBottom">
      <summary>The bottom left corner of the xaml root when non-targeted and left of the target element expanding downward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.LeftTop">
      <summary>The top left corner of the xaml root when non-targeted and left of the target element expanding upward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Right">
      <summary>Along the right side of the xaml root when non-targeted and right of the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.RightBottom">
      <summary>The bottom right corner of the xaml root when non-targeted and right of the target element expanding downward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.RightTop">
      <summary>The top right corner of the xaml root when non-targeted and right of the target element expanding upward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.Top">
      <summary>Along the top side of the xaml root when non-targeted and above the target element when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.TopLeft">
      <summary>The top left corner of the xaml root when non-targeted and above the target element expanding leftward when targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipPlacementMode.TopRight">
      <summary>The top right corner of the xaml root when non-targeted and above the target element expanding rightward when targeted.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipTailVisibility">
      <summary>Defines constants that specify whether a teaching tip's Tail is visible or collapsed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipTailVisibility.Auto">
      <summary>The teaching tip's tail is collapsed when non-targeted and visible when the targeted.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipTailVisibility.Collapsed">
      <summary>The teaching tip's tail is collapsed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TeachingTipTailVisibility.Visible">
      <summary>The teaching tip's tail is visible.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a TeachingTip.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.#ctor">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a TeachingTip.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.IconElement">
      <summary>Gets the icon element.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.IconElementProperty">
      <summary>Identifies the IconElement dependency property.</summary>
      <returns>The identifier for the IconElement dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.TopLeftHighlightMargin">
      <summary>Gets the thickness value of the top left highlight margin.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.TopLeftHighlightMarginProperty">
      <summary>Identifies the TopLeftHighlightMargin dependency property.</summary>
      <returns>The identifier for the TopLeftHighlightMargin dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.TopRightHighlightMargin">
      <summary>Gets the thickness value of the top right highlight margin.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings.TopRightHighlightMarginProperty">
      <summary>Identifies the TopRightHighlightMargin dependency property.</summary>
      <returns>The identifier for the TopRightHighlightMargin dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TextCommandBarFlyout">
      <summary>Represents a specialized CommandBarFlyout that contains commands for editing text.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TextCommandBarFlyout.#ctor">
      <summary>Initializes a new instance of the TextCommandBarFlyout class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ToggleSplitButton">
      <summary>Represents a button with two parts that can be invoked separately. One part behaves like a toggle button that can be on or off, while the other part invokes a flyout that contains additional options for the user to choose from.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.ToggleSplitButton.IsCheckedChanged">
      <summary>Occurs when the value of the IsChecked property is changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.ToggleSplitButton.#ctor">
      <summary>Initializes a new instance of the ToggleSplitButton class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ToggleSplitButton.IsChecked">
      <summary>Gets or sets whether the ToggleSplitButton is checked.</summary>
      <returns>true if the ToggleSplitButton is checked; false if the ToggleSplitButton is unchecked. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.ToggleSplitButton.IsCheckedProperty">
      <summary>Identifies the ToggleSplitButton.IsChecked" dependency property.</summary>
      <returns>The identifier for the ToggleSplitButton.IsChecked" dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.ToggleSplitButtonIsCheckedChangedEventArgs">
      <summary>Provides event data for the ToggleSplitButton.IsCheckedChanged" event.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeView">
      <summary>Represents a hierarchical list with expanding and collapsing nodes that contain nested items.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TreeView.Collapsed">
      <summary>Occurs when a node in the tree is collapsed.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TreeView.DragItemsCompleted">
      <summary>Occurs when a drag operation that involves one of the items in the view is ended.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TreeView.DragItemsStarting">
      <summary>Occurs when a drag operation that involves one of the items in the view is initiated.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TreeView.Expanding">
      <summary>Occurs when a node in the tree starts to expand.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TreeView.ItemInvoked">
      <summary>Occurs when an item in the tree is invoked.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.#ctor">
      <summary>Initializes a new instance of the TreeView control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.Collapse(Microsoft.UI.Xaml.Controls.TreeViewNode)">
      <summary>Collapses the specified node in the tree.</summary>
      <param name="value">The tree node to collapse.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.ContainerFromItem(System.Object)">
      <summary>Returns the container corresponding to the specified item.</summary>
      <param name="item">The item to retrieve the container for.</param>
      <returns>A container that corresponds to the specified item, if the item has a container and exists in the collection; otherwise, null.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.ContainerFromNode(Microsoft.UI.Xaml.Controls.TreeViewNode)">
      <summary>Returns the container corresponding to the specified node.</summary>
      <param name="node">The node to retrieve the container for.</param>
      <returns>A container that corresponds to the specified node, if the node has a container and exists in the collection; otherwise, null.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.Expand(Microsoft.UI.Xaml.Controls.TreeViewNode)">
      <summary>Expands the specified node in the tree.</summary>
      <param name="value">The tree node to expand.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.ItemFromContainer(Windows.UI.Xaml.DependencyObject)">
      <summary>Returns the item that corresponds to the specified, generated container.</summary>
      <param name="container">The DependencyObject that corresponds to the item to be returned.</param>
      <returns>The contained item, or the container if it does not contain an item.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.NodeFromContainer(Windows.UI.Xaml.DependencyObject)">
      <summary>Returns the TreeViewNode corresponding to the specified container.</summary>
      <param name="container">The container to retrieve the TreeViewNode for.</param>
      <returns>The node that corresponds to the specified container.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeView.SelectAll">
      <summary>Selects all nodes in the tree.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.CanDragItems">
      <summary>Gets or sets a value that indicates whether items in the view can be dragged as data payload.</summary>
      <returns>true if items in the view can be dragged as data payload; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.CanDragItemsProperty">
      <summary>Identifies the CanDragItems dependency property.</summary>
      <returns>The identifier for the CanDragItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.CanReorderItems">
      <summary>Gets or sets a value that indicates whether items in the view can be reordered through user interaction.</summary>
      <returns>true if items in the view can be reordered through user interaction; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.CanReorderItemsProperty">
      <summary>Identifies the CanReorderItems dependency property.</summary>
      <returns>The identifier for the CanReorderItems dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyle">
      <summary>Gets or sets the style that is used when rendering the item containers.</summary>
      <returns>The style applied to the item containers. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyleProperty">
      <summary>Identifies the ItemContainerStyle dependency property.</summary>
      <returns>The identifier for the ItemContainerStyle dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyleSelector">
      <summary>Gets or sets a reference to a custom StyleSelector values to use for the item container based on characteristics of the object being displayed.</summary>
      <returns>A custom StyleSelector logic class.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerStyleSelectorProperty">
      <summary>Identifies the ItemContainerStyleSelector dependency property.</summary>
      <returns>The identifier for the ItemContainerStyleSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerTransitions">
      <summary>Gets or sets the collection of Transition style elements that apply to the item containers of a TreeView.</summary>
      <returns>The collection of Transition.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemContainerTransitionsProperty">
      <summary>Identifies the ItemContainerTransitions dependency property.</summary>
      <returns>The identifier for the ItemContainerTransitions dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemsSource">
      <summary>Gets or sets an object source used to generate the content of the TreeView.</summary>
      <returns>The object that is used to generate the content of the TreeView. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemsSourceProperty">
      <summary>Identifies the ItemsSource dependency property.</summary>
      <returns>The identifier for the ItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemTemplate">
      <summary>Gets or sets the DataTemplate used to display each item.</summary>
      <returns>The template that specifies the visualization of the data objects. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemTemplateProperty">
      <summary>Identifies the ItemTemplate dependency property.</summary>
      <returns>The identifier for the ItemTemplate dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemTemplateSelector">
      <summary>Gets or sets a reference to a custom DataTemplateSelector logic class. The DataTemplateSelector referenced by this property returns a template to apply to items.</summary>
      <returns>A reference to a custom DataTemplateSelector logic class.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.ItemTemplateSelectorProperty">
      <summary>Identifies the ItemTemplateSelector dependency property.</summary>
      <returns>The identifier for the ItemTemplateSelector dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.RootNodes">
      <summary>Gets or sets the collection of root nodes of the tree.</summary>
      <returns>The collection of root nodes of the tree.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectedItem">
      <summary>Gets or sets the SelectedItem property of a TreeView.</summary>
      <returns>Gets or sets the selected item. Default value is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectedItems">
      <summary>Gets the currently selected items.</summary>
      <returns>A collection of the currently selected items. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectedNode">
      <summary>Gets or sets the node that is selected in the tree.</summary>
      <returns>The node that is selected in the tree. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectedNodes">
      <summary>Gets or sets the collection of nodes that are selected in the tree.</summary>
      <returns>The collection of nodes that are selected in the tree. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectionMode">
      <summary>Gets or sets the selection behavior for a TreeView instance.</summary>
      <returns>An enumeration value that specifies the selection behavior for a TreeView. The default is Single selection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeView.SelectionModeProperty">
      <summary>Identifies the SelectionMode dependency property.</summary>
      <returns>The identifier for the SelectionMode dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewCollapsedEventArgs">
      <summary>Provides event data for the TreeView.Collapsed" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewCollapsedEventArgs.Item">
      <summary>Gets the TreeView item that is collapsed.</summary>
      <returns>The TreeView item that is collapsed.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewCollapsedEventArgs.Node">
      <summary>Gets the TreeView node that is collapsed.</summary>
      <returns>The TreeView node that is collapsed.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewDragItemsCompletedEventArgs">
      <summary>Provides event data for the TreeView.DragItemsCompleted" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsCompletedEventArgs.DropResult">
      <summary>Gets a value that indicates what operation was performed on the dragged data, and whether it was successful.</summary>
      <returns>A value of the enumeration that indicates what operation was performed on the dragged data.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsCompletedEventArgs.Items">
      <summary>Gets the loosely typed collection of objects that are selected for the item drag action.</summary>
      <returns>A loosely typed collection of objects.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsCompletedEventArgs.NewParentItem" />
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewDragItemsStartingEventArgs">
      <summary>Provides event data for the TreeView.DragItemsStarting" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsStartingEventArgs.Cancel">
      <summary>Gets or sets a value that indicates whether the item drag action should be canceled.</summary>
      <returns>true to cancel the item drag action; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsStartingEventArgs.Data">
      <summary>Gets the data payload associated with an items drag action.</summary>
      <returns>The data payload.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewDragItemsStartingEventArgs.Items">
      <summary>Gets the loosely typed collection of objects that are selected for the item drag action.</summary>
      <returns>A loosely typed collection of objects.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewExpandingEventArgs">
      <summary>Provides event data for the TreeView.Expanding" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewExpandingEventArgs.Item">
      <summary>Gets the data item for the tree view node that is expanding.</summary>
      <returns>The data item for the tree view node that is expanding.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewExpandingEventArgs.Node">
      <summary>Gets the tree view node that is expanding.</summary>
      <returns>The tree view node that is expanding.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewItem">
      <summary>Represents the container for an item in a TreeView control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeViewItem.#ctor">
      <summary>Initializes a new instance of the TreeViewItem control.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.CollapsedGlyph">
      <summary>Gets or sets the glyph to show for a collapsed tree node.</summary>
      <returns>The glyph to show for a collapsed tree node.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.CollapsedGlyphProperty">
      <summary>Identifies the CollapsedGlyph dependency property.</summary>
      <returns>The identifier for the CollapsedGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.ExpandedGlyph">
      <summary>Gets or sets the glyph to show for an expanded tree node.</summary>
      <returns>The glyph to show for an expanded tree node.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.ExpandedGlyphProperty">
      <summary>Identifies the ExpandedGlyph dependency property.</summary>
      <returns>The identifier for the ExpandedGlyph dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphBrush">
      <summary>Gets or sets the Brush used to paint node glyphs on a TreeView.</summary>
      <returns>The Brush used to paint node glyphs on a TreeView.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphBrushProperty">
      <summary>Identifies the GlyphBrush dependency property.</summary>
      <returns>The identifier for the GlyphBrush dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphOpacity">
      <summary>Gets or sets the opacity of node glyphs on a TreeView.</summary>
      <returns>The opacity of node glyphs on a TreeView.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphOpacityProperty">
      <summary>Identifies the GlyphOpacity dependency property.</summary>
      <returns>The identifier for the GlyphOpacity dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphSize">
      <summary>Gets or sets the size of node glyphs on a TreeView.</summary>
      <returns>The opacity of size glyphs on a TreeView.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.GlyphSizeProperty">
      <summary>Identifies the GlyphSize dependency property.</summary>
      <returns>The identifier for the GlyphSize dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.HasUnrealizedChildren">
      <summary>Gets or sets a value that indicates whether the current item has child items that haven't been shown.</summary>
      <returns>true if the current item has child items that haven't been shown. Otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.HasUnrealizedChildrenProperty">
      <summary>Identifies the HasUnrealizedChildren dependency property.</summary>
      <returns>The identifier for the HasUnrealizedChildren dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.IsExpanded">
      <summary>Gets or sets a value that indicates whether a tree node is expanded.</summary>
      <returns>true if the tree node is expanded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.IsExpandedProperty">
      <summary>Identifies the IsExpanded dependency property.</summary>
      <returns>The identifier for the IsExpanded dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.ItemsSource">
      <summary>Gets or sets an object source used to generate the content of the TreeView.</summary>
      <returns>The object that is used to generate the content of the TreeViewItem. The default is null.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.ItemsSourceProperty">
      <summary>Identifies the ItemsSource dependency property.</summary>
      <returns>The identifier for the ItemsSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.TreeViewItemTemplateSettings">
      <summary>Gets an object that provides calculated values that can be referenced as {TemplateBinding} markup extension sources when defining templates for a TreeViewItem control.</summary>
      <returns>An object that provides calculated values for templates.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItem.TreeViewItemTemplateSettingsProperty">
      <summary>Identifies the TreeViewItemTemplateSettings dependency property.</summary>
      <returns>The identifier for the TreeViewItemTemplateSettings dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewItemInvokedEventArgs">
      <summary>Provides event data for the TreeView.ItemInvoked" event.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemInvokedEventArgs.Handled">
      <summary>Gets or sets a value that marks the routed event as handled. A true value for prevents most handlers along the event route from handling the same event again.</summary>
      <returns>true to mark the routed event handled. false to leave the routed event unhandled, which permits the event to potentially route further and be acted on by other handlers. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemInvokedEventArgs.InvokedItem">
      <summary>Gets the TreeView item that is invoked.</summary>
      <returns>The TreeView item that is invoked.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings">
      <summary>Provides calculated values that can be referenced as TemplatedParent sources when defining templates for a TreeViewItem control. Not intended for general use.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.#ctor">
      <summary>Initializes a new instance of the TreeViewItemTemplateSettings class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.CollapsedGlyphVisibility">
      <summary>Gets the visibilty of a collapsed glyph.</summary>
      <returns>The visibilty of a collapsed glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.CollapsedGlyphVisibilityProperty">
      <summary>Identifies the CollapsedGlyphVisibility dependency property.</summary>
      <returns>The identifier for the CollapsedGlyphVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.DragItemsCount">
      <summary>Gets the number of items being dragged.</summary>
      <returns>The number of items being dragged.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.DragItemsCountProperty">
      <summary>Identifies the DragItemsCount dependency property.</summary>
      <returns>The identifier for the DragItemsCount dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.ExpandedGlyphVisibility">
      <summary>Gets the visibilty of an expanded glyph.</summary>
      <returns>The visibilty of an expanded glyph.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.ExpandedGlyphVisibilityProperty">
      <summary>Identifies the ExpandedGlyphVisibility dependency property.</summary>
      <returns>The identifier for the ExpandedGlyphVisibility dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.Indentation">
      <summary>Gets the amount that the item is indented.</summary>
      <returns>The amount that the item is indented.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings.IndentationProperty">
      <summary>Identifies the Indentation dependency property.</summary>
      <returns>The identifier for the Indentation dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewList">
      <summary>Represents a flattened list of tree view items so that operations such as keyboard navigation and drag-and-drop can be inherited from ListView.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeViewList.#ctor">
      <summary>Initializes a new instance of the TreeViewList control.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewNode">
      <summary>Represents a node in a TreeView control.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TreeViewNode.#ctor">
      <summary>Initializes a new instance of the TreeViewNode class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.Children">
      <summary>Gets the collection of nodes that are children of the current node.</summary>
      <returns>The collection of nodes that are children of the current node. The default is an empty collection.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.Content">
      <summary>Gets or sets the data content for the current node.</summary>
      <returns>The data content for the current node.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.ContentProperty">
      <summary>Identifies the Content dependency property.</summary>
      <returns>The identifier for the Content dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.Depth">
      <summary>Gets a value that indicates how far the current node is from the root node of the tree.</summary>
      <returns>The depth of the current node from the root node of the tree.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.DepthProperty">
      <summary>Identifies the Depth dependency property.</summary>
      <returns>The identifier for the Depth dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren">
      <summary>Gets a value that indicates whether the current node has child items.</summary>
      <returns>true if the current node has child items; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildrenProperty">
      <summary>Identifies the HasChildren dependency property.</summary>
      <returns>The identifier for the HasChildren dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren">
      <summary>Gets or sets a value that indicates whether the current node has child items that haven't been shown.</summary>
      <returns>true of the current node has child items that haven't been shown; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded">
      <summary>Gets or sets a value that indicates whether the cuurent tree view node is expanded.</summary>
      <returns>true if the node is expanded; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpandedProperty">
      <summary>Identifies the IsExpanded dependency property.</summary>
      <returns>The identifier for the IsExpanded dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TreeViewNode.Parent">
      <summary>Gets or sets the node that is the parent of the current node.</summary>
      <returns>The node that is the parent of the current node.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TreeViewSelectionMode">
      <summary>Defines constants that specify the selection behavior for a TreeView instance.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.Multiple">
      <summary>The user can select multiple items.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.None">
      <summary>A user can't select items.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TreeViewSelectionMode.Single">
      <summary>A user can select a single item.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TwoPaneView">
      <summary>Represents a container with two views that size and position content in the available space, either side-by-side or top-bottom.</summary>
    </member>
    <member name="E:Microsoft.UI.Xaml.Controls.TwoPaneView.ModeChanged">
      <summary>Occurs when the Mode of the TwoPaneView has changed.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.TwoPaneView.#ctor">
      <summary>Initializes a new instance of the TwoPaneView class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.MinTallModeHeight">
      <summary>Gets or sets the minimum height at which panes are shown in tall mode.</summary>
      <returns>The minimum height at which panes are shown in tall mode. The default is 641px.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.MinTallModeHeightProperty">
      <summary>Identifies the MinTallModeHeight dependency property.</summary>
      <returns>The identifier for the MinTallModeHeight dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.MinWideModeWidth">
      <summary>Gets or sets the minimum width at which panes are shown in wide mode.</summary>
      <returns>The minimum width at which panes are shown in wide mode. The default is 641px.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.MinWideModeWidthProperty">
      <summary>Identifies the MinWideModeWidth dependency property.</summary>
      <returns>The identifier for the MinWideModeWidth dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Mode">
      <summary>Gets a value that indicates how panes are shown.</summary>
      <returns>An enumeration value that indicates how panes are shown.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.ModeProperty">
      <summary>Identifies the Mode dependency property.</summary>
      <returns>The identifier for the Mode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane1">
      <summary>Gets or sets the content of pane 1.</summary>
      <returns>The content of pane 1.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane1Length">
      <summary>Gets the calculated width (in wide mode) or height (in tall mode) of pane 1, or sets the GridLength value of pane 1.</summary>
      <returns>The GridLength that represents the width or height of the pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane1LengthProperty">
      <summary>Identifies the Pane1Length dependency property.</summary>
      <returns>The identifier for the Pane1Length dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane1Property">
      <summary>Identifies the Pane1 dependency property.</summary>
      <returns>The identifier for the Pane1 dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane2">
      <summary>Gets or sets the content of pane 2.</summary>
      <returns>The content of pane 2.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane2Length">
      <summary>Gets the calculated width (in wide mode) or height (in tall mode) of pane 2, or sets the GridLength value of pane 2.</summary>
      <returns>The GridLength that represents the width or height of the pane.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane2LengthProperty">
      <summary>Identifies the Pane2Length dependency property.</summary>
      <returns>The identifier for the Pane2Length dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.Pane2Property">
      <summary>Identifies the Pane2 dependency property.</summary>
      <returns>The identifier for the Pane2 dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.PanePriority">
      <summary>Gets or sets a value that indicates which pane has priority.</summary>
      <returns>An enumeration value that indicates which pane has priority.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.PanePriorityProperty">
      <summary>Identifies the PanePriority dependency property.</summary>
      <returns>The identifier for the PanePriority dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.TallModeConfiguration">
      <summary>Gets or sets a value that indicates how panes are shown in tall mode.</summary>
      <returns>An enumeration value that indicates how panes are shown in tall mode.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.TallModeConfigurationProperty">
      <summary>Identifies the TallModeConfiguration dependency property.</summary>
      <returns>The identifier for the TallModeConfiguration dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.WideModeConfiguration">
      <summary>Gets or sets a value that indicates how panes are shown in wide mode.</summary>
      <returns>An enumeration value that indicates how panes are shown in wide mode.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.TwoPaneView.WideModeConfigurationProperty">
      <summary>Identifies the WideModeConfiguration dependency property.</summary>
      <returns>The identifier for the WideModeConfiguration dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TwoPaneViewMode">
      <summary>Defines constants that specify how panes are shown in a TwoPaneView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewMode.SinglePane">
      <summary>Only one pane is shown.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewMode.Tall">
      <summary>Panes are shown top-bottom.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewMode.Wide">
      <summary>Panes are shown side-by-side.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TwoPaneViewPriority">
      <summary>Defines constants that specify which pane has priority in a TwoPaneView.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewPriority.Pane1">
      <summary>Pane 1 has priority.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewPriority.Pane2">
      <summary>Pane 2 has priority.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TwoPaneViewTallModeConfiguration">
      <summary>Defines constants that specify how panes are shown in a TwoPaneView in tall mode.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewTallModeConfiguration.BottomTop">
      <summary>The pane that has priority is shown on the bottom, the other pane is shown on top.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewTallModeConfiguration.SinglePane">
      <summary>Only the pane that has priority is shown, the other pane is hidden.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewTallModeConfiguration.TopBottom">
      <summary>The pane that has priority is shown on top, the other pane is shown on the bottom.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.TwoPaneViewWideModeConfiguration">
      <summary>Defines constants that specify how panes are shown in a TwoPaneView in wide mode.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewWideModeConfiguration.LeftRight">
      <summary>The pane that has priority is shown on the left, the other pane is shown on the right.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewWideModeConfiguration.RightLeft">
      <summary>The pane that has priority is shown on the right, the other pane is shown on the left.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.TwoPaneViewWideModeConfiguration.SinglePane">
      <summary>Only the pane that has priority is shown, the other pane is hidden.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.UniformGridLayout">
      <summary>Positions elements sequentially from left to right or top to bottom in a wrapping layout.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.UniformGridLayout.#ctor">
      <summary>Initializes a new instance of the UniformGridLayout class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.ItemsJustification">
      <summary>Gets or sets a value that indicates how items are aligned on the non-scrolling or non-virtualizing axis.</summary>
      <returns>An enumeration value that indicates how items are aligned. The default is Start.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.ItemsJustificationProperty">
      <summary>Identifies the ItemsJustification dependency property.</summary>
      <returns>The identifier for the ItemsJustification dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.ItemsStretch">
      <summary>Gets or sets a value that indicates how items are sized to fill the available space.</summary>
      <returns>An enumeration value that indicates how items are sized to fill the available space. The default is None.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.ItemsStretchProperty">
      <summary>Identifies the ItemsStretch dependency property.</summary>
      <returns>The identifier for the ItemsStretch dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MaximumRowsOrColumns">
      <summary>Gets or sets the maximum number of items rendered per row or column, based on the orienation of the UniformGridLayout.</summary>
      <returns>An integer indicating the maximum number of items per row (for Vertical orientation) or column (for Horizontal orientation).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MaximumRowsOrColumnsProperty">
      <summary>Identifies the MaximumRowsOrColumns dependency property.</summary>
      <returns>The identifier for the MaximumRowsOrColumns dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinColumnSpacing">
      <summary>Gets or sets the minimum space between items on the horizontal axis.</summary>
      <returns>The minimum space (in pixels) between items on the horizontal axis.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinColumnSpacingProperty">
      <summary>Identifies the MinColumnSpacing dependency property.</summary>
      <returns>The identifier for the MinColumnSpacing dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinItemHeight">
      <summary>Gets or sets the minimum height of each item.</summary>
      <returns>The minimum height (in pixels) of each item. The default is NaN, in which case the height of the first item is used as the minimum.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinItemHeightProperty">
      <summary>Identifies the MinItemHeight dependency property.</summary>
      <returns>The identifier for the MinItemHeight dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinItemWidth">
      <summary>Gets or sets the minimum width of each item.</summary>
      <returns>The minimum width (in pixels) of each item. The default is NaN, in which case the width of the first item is used as the minimum.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinItemWidthProperty">
      <summary>Identifies the MinItemWidth dependency property.</summary>
      <returns>The identifier for the MinItemWidth dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinRowSpacing">
      <summary>Gets or sets the minimum space between items on the vertical axis.</summary>
      <returns>The minimum space (in pixels) between items on the vertical axis.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.MinRowSpacingProperty">
      <summary>Identifies the MinRowSpacing dependency property.</summary>
      <returns>The identifier for the MinRowSpacing dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.Orientation">
      <summary>Gets or sets the axis along which items are laid out.</summary>
      <returns>One of the enumeration values that specifies the axis along which items are laid out. The default is Vertical.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.UniformGridLayout.OrientationProperty">
      <summary>Identifies the Orientation dependency property.</summary>
      <returns>The identifier for the Orientation dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification">
      <summary>Defines constants that specify how items are aligned on the non-scrolling or non-virtualizing axis.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.Center">
      <summary>Items are aligned in the center of the row or column, with extra space at the start and end. Spacing between items does not change.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.End">
      <summary>Items are aligned with the end of the row or column, with extra space at the start. Spacing between items does not change.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.SpaceAround">
      <summary>Items are aligned so that extra space is added evenly before and after each item.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.SpaceBetween">
      <summary>Items are aligned so that extra space is added evenly between adjacent items. No space is added at the start or end.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.SpaceEvenly" />
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsJustification.Start">
      <summary>Items are aligned with the start of the row or column, with extra space at the end. Spacing between items does not change.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsStretch">
      <summary>Defines constants that specify how items are sized to fill the available space.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsStretch.Fill">
      <summary>The item is sized to fill the available space in the non-scrolling direction. Item size in the scrolling direction is not changed.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsStretch.None">
      <summary>The item retains its natural size. Use of extra space is determined by the ItemsJustification property.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Controls.UniformGridLayoutItemsStretch.Uniform">
      <summary>The item is sized to both fill the available space in the non-scrolling direction and maintain its aspect ratio.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.VirtualizingLayout">
      <summary>Represents the base class for an object that sizes and arranges child elements for a host and supports virtualization.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.#ctor">
      <summary>Initializes a new instance of the VirtualizingLayout class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.ArrangeOverride(Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext,Windows.Foundation.Size)">
      <summary>When implemented in a derived class, provides the behavior for the "Arrange" pass of layout. Classes can override this method to define their own "Arrange" pass behavior.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="finalSize">The final area within the container that this object should use to arrange itself and its children.</param>
      <returns>The actual size that is used after the element is arranged in layout.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.InitializeForContextCore(Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext)">
      <summary>When overridden in a derived class, initializes any per-container state the layout requires when it is attached to a UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.MeasureOverride(Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext,Windows.Foundation.Size)">
      <summary>Provides the behavior for the "Measure" pass of the layout cycle. Classes can override this method to define their own "Measure" pass behavior.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="availableSize">The available size that this object can give to child objects. Infinity can be specified as a value to indicate that the object will size to whatever content is available.</param>
      <returns>The size that this object determines it needs during layout, based on its calculations of the allocated sizes for child objects or based on other considerations such as a fixed container size.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.OnItemsChangedCore(Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext,System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
      <summary>Notifies the layout when the data collection assigned to the container element (ItemsSource) has changed.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
      <param name="source">The data source.</param>
      <param name="args">Data about the collection change.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayout.UninitializeForContextCore(Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext)">
      <summary>When overridden in a derived class, removes any state the layout previously stored on the UIElement container.</summary>
      <param name="context">The context object that facilitates communication between the layout and its host container.</param>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext">
      <summary>Represents the base class for layout context types that support virtualization.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.#ctor">
      <summary>Initializes a new instance of the VirtualizingLayoutContext class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.GetItemAt(System.Int32)">
      <summary>Retrieves the data item in the source found at the specified index.</summary>
      <param name="index">The index of the data item to retrieve.</param>
      <returns>The data item at the specified index.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.GetItemAtCore(System.Int32)">
      <summary>When implemented in a derived class, retrieves the data item in the source found at the specified index.</summary>
      <param name="index">The index of the data item to retrieve.</param>
      <returns>The data item at the specified index.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.GetOrCreateElementAt(System.Int32)">
      <summary>Retrieves a UIElement that represents the data item in the source found at the specified index. By default, if an element already exists, it is returned; otherwise, a new element is created.</summary>
      <param name="index">The index of the data item to retrieve a UIElement for.</param>
      <returns>A UIElement that represents the data item.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.GetOrCreateElementAt(System.Int32,Microsoft.UI.Xaml.Controls.ElementRealizationOptions)">
      <summary>Retrieves a UIElement that represents the data item in the source found at the specified index using the specified options.</summary>
      <param name="index">The index of the data item to retrieve a UIElement for.</param>
      <param name="options">A value of ElementRealizationOptions that specifies whether to suppress automatic recycling of the retrieved element or force creation of a new element.</param>
      <returns>A UIElement that represents the data item.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.GetOrCreateElementAtCore(System.Int32,Microsoft.UI.Xaml.Controls.ElementRealizationOptions)">
      <param name="index"></param>
      <param name="options"></param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.ItemCountCore">
      <summary>When implemented in a derived class, retrieves the number of items in the data.</summary>
      <returns>The number of items in the data.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RealizationRectCore">
      <summary>When implemented in a derived class, retrieves an area that represents the viewport and buffer that the layout should fill with realized elements.</summary>
      <returns>An area that represents the viewport and buffer that the layout should fill with realized elements.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RecycleElement(Windows.UI.Xaml.UIElement)">
      <summary>Clears the specified UIElement and allows it to be either re-used or released.</summary>
      <param name="element">The element to clear.</param>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RecycleElementCore(Windows.UI.Xaml.UIElement)">
      <summary>When implemented in a derived class, clears the specified UIElement and allows it to be either re-used or released.</summary>
      <param name="element">The element to clear.</param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.ItemCount">
      <summary>Gets the number of items in the data.</summary>
      <returns>The number of items in the data.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.LayoutOrigin">
      <summary>Gets or sets the origin point for the estimated content size.</summary>
      <returns>The origin point for the estimated content size.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.LayoutOriginCore">
      <summary>Implements the behavior of LayoutOrigin in a derived or custom VirtualizingLayoutContext.</summary>
      <returns>The value that should be returned as LayoutOrigin by the VirtualizingLayoutContext.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RealizationRect">
      <summary>Gets an area that represents the viewport and buffer that the layout should fill with realized elements.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RecommendedAnchorIndex">
      <summary>Gets the recommended index from which to start the generation and layout of elements.</summary>
      <returns>The recommended index from which the layout should start.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext.RecommendedAnchorIndexCore">
      <summary>Implements the behavior for getting the return value of RecommendedAnchorIndex in a derived or custom VirtualizingLayoutContext.</summary>
      <returns>The value that should be returned as RecommendedAnchorIndex by the VirtualizingLayoutContext.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Controls.XamlControlsResources">
      <summary>Default styles for the controls in the WinUI library.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.XamlControlsResources.#ctor">
      <summary>Initializes a new instance of the XamlControlsResources class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Controls.XamlControlsResources.EnsureRevealLights(Windows.UI.Xaml.UIElement)">
      <param name="element"></param>
    </member>
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.ControlsResourcesVersion" />
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.ControlsResourcesVersionProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.UseCompactResources" />
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.UseCompactResourcesProperty" />
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.Version" />
    <member name="P:Microsoft.UI.Xaml.Controls.XamlControlsResources.VersionProperty" />
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXHasCustomActivationFactoryAttribute" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXHasCustomActivationFactoryAttribute.#ctor">
      <summary>Initializes a new instance of the MUXHasCustomActivationFactoryAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackAttribute" />
    <member name="F:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackAttribute.enable" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyChangedCallbackAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackMethodNameAttribute" />
    <member name="F:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackMethodNameAttribute.value" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyChangedCallbackMethodNameAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyChangedCallbackMethodNameAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyDefaultValueAttribute" />
    <member name="F:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyDefaultValueAttribute.value" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyDefaultValueAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyDefaultValueAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyNeedsDependencyPropertyFieldAttribute" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyNeedsDependencyPropertyFieldAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyNeedsDependencyPropertyFieldAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyTypeAttribute" />
    <member name="F:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyTypeAttribute.value" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyTypeAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyTypeAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyValidationCallbackAttribute" />
    <member name="F:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyValidationCallbackAttribute.value" />
    <member name="M:Microsoft.UI.Xaml.CustomAttributes.MUXPropertyValidationCallbackAttribute.#ctor">
      <summary>Initializes a new instance of the MUXPropertyValidationCallbackAttribute class.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Media.AcrylicBackgroundSource">
      <summary>Defines values that specify whether the brush samples from the app content or from the content behind the app window.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Media.AcrylicBackgroundSource.Backdrop">
      <summary>The brush samples from the app content.</summary>
    </member>
    <member name="F:Microsoft.UI.Xaml.Media.AcrylicBackgroundSource.HostBackdrop">
      <summary>The brush samples from the content behind the app window.</summary>
    </member>
    <member name="T:Microsoft.UI.Xaml.Media.AcrylicBrush">
      <summary>Paints an area with a semi-transparent material that uses multiple effects including blur and a noise texture.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Media.AcrylicBrush.#ctor">
      <summary>Initializes a new instance of the AcrylicBrush class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.AlwaysUseFallback">
      <summary>Gets or sets a value that specifies whether the brush is forced to the solid fallback color.</summary>
      <returns>true to always replace the acrylic material with the solid fallback color. Otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.AlwaysUseFallbackProperty">
      <summary>Identifies the AlwaysUseFallback dependency property.</summary>
      <returns>The identifier for the AlwaysUseFallback dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.BackgroundSource">
      <summary>Gets or sets a value that specifies whether the brush samples from the app content or from the content behind the app window.</summary>
      <returns>A value of the enumeration that specifies whether the brush samples from the app content or from the content behind the app window.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.BackgroundSourceProperty">
      <summary>Identifies the BackgroundSource dependency property.</summary>
      <returns>The identifier for the BackgroundSource dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintColor">
      <summary>Gets or sets the color tint for the semi-transparent acrylic material.</summary>
      <returns>The color tint for the semi-transparent acrylic material.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintColorProperty">
      <summary>Identifies the TintColor dependency property.</summary>
      <returns>The identifier for the TintColor dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintLuminosityOpacity">
      <summary>Gets or sets the degree of opacity of the color's luminosity tint.</summary>
      <returns>The opacity expressed as a value between 0 and 1.0. The default value is 1.0, which is full opacity. 0 is transparent opacity.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintLuminosityOpacityProperty">
      <summary>Identifies the TintLuminosityOpacity dependency property.</summary>
      <returns>The identifier for the TintLuminosityOpacity dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintOpacity">
      <summary>Gets or sets the degree of opacity of the color tint.</summary>
      <returns>The opacity expressed as a value between 0 and 1.0. The default value is 1.0, which is full opacity. 0 is transparent opacity.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintOpacityProperty">
      <summary>Identifies the TintOpacity dependency property.</summary>
      <returns>The identifier for the TintOpacity dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintTransitionDuration">
      <summary>Gets or sets the length of the automatic transition animation used when the TintColor changes.</summary>
      <returns>The length of the automatic transition animation used when the TintColor changes.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.AcrylicBrush.TintTransitionDurationProperty">
      <summary>Identifies the TintTransitionDuration dependency property.</summary>
      <returns>The identifier for the TintTransitionDuration dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.Media.RadialGradientBrush">
      <summary>RadialGradientBrush paints an area with a radial gradient. A center point defines the origin of the gradient, and an ellipse defines the outer bounds of the gradient.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.Media.RadialGradientBrush.#ctor">
      <summary>Initializes a new instance of the RadialGradientBrush class.</summary>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.Center">
      <summary>Gets or sets the center of the ellipse that contains the gradient.</summary>
      <returns>The center of the ellipse that contains the gradient. The default is (0.5, 0.5).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.CenterProperty">
      <summary>Identifies the Center dependency property.</summary>
      <returns>The identifier for the Center dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.GradientOrigin">
      <summary>Gets or sets the gradient's origin (relative to the top left corner).</summary>
      <returns>The gradient's origin (relative to the top left corner). The default is (0.5, 0,5).</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.GradientOriginProperty">
      <summary>Identifies the GradientOrigin dependency property.</summary>
      <returns>The identifier for the GradientOrigin dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.GradientStops">
      <summary>Gets or sets the brush's gradient stops.</summary>
      <returns>A collection of GradientStop objects that define the gradient.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.InterpolationSpace">
      <summary>Gets or sets the color space used to interpolate the gradient's colors.</summary>
      <returns>The color space used to interpolate the gradient's colors. The default is Auto.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.InterpolationSpaceProperty">
      <summary>Identifies the InterpolationSpace dependency property.</summary>
      <returns>The identifier for the InterpolationSpace dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.MappingMode">
      <summary>Gets or sets whether the gradient brush's positioning coordinates are absolute or relative to the output area.</summary>
      <returns>Defines whether Center, RadiusX, RadiusY, and GradientOrigin represent relative coordinates in the range 0 to 1 or absolute coordinates. The default is RelativeToBoundingBox.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.MappingModeProperty">
      <summary>Identifies the MappingMode dependency property.</summary>
      <returns>The identifier for the MappingMode dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.RadiusX">
      <summary>Gets or sets the X axis radius of the ellipse that contains the gradient.</summary>
      <returns>The X axis radius of the ellipse that contains the gradient. The default is 0.5.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.RadiusXProperty">
      <summary>Identifies the RadiusX dependency property.</summary>
      <returns>The identifier for the RadiusX dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.RadiusY">
      <summary>Gets or sets the Y axis radius of the ellipse that contains the gradient.</summary>
      <returns>The Y axis radius of the ellipse that contains the gradient. The default is 0.5.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.RadiusYProperty">
      <summary>Identifies the RadiusY dependency property.</summary>
      <returns>The identifier for the RadiusY dependency property.</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.SpreadMethod">
      <summary>Gets or sets the type of spread method that specifies how to draw a gradient that starts or ends inside the bounds of the object to be painted.</summary>
      <returns>The type of spread method that specifies how to draw a gradient that starts or ends inside the bounds of the object to be painted. The default is Pad</returns>
    </member>
    <member name="P:Microsoft.UI.Xaml.Media.RadialGradientBrush.SpreadMethodProperty">
      <summary>Identifies the SpreadMethod dependency property.</summary>
      <returns>The identifier for the SpreadMethod dependency property.</returns>
    </member>
    <member name="T:Microsoft.UI.Xaml.XamlContract" />
    <member name="T:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider">
      <summary>Implements XAML schema context concepts that support XAML parsing.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider.#ctor">
      <summary>Creates a new instance of the XamlControlsXamlMetadataProvider class.</summary>
    </member>
    <member name="M:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider.GetXamlType(System.String)">
      <summary>Implements XAML schema context access to underlying type mapping, based on specifying a full type name.</summary>
      <param name="fullName">The name of the class for which to return a XAML type mapping.</param>
      <returns>The schema context's implementation of the IXamlType concept.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider.GetXamlType(System.Type)">
      <summary>Implements XAML schema context access to underlying type mapping, based on providing a helper value that describes a type.</summary>
      <param name="type">The type as represented by the relevant type system or interop support type.</param>
      <returns>The schema context's implementation of the IXamlType concept.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider.GetXmlnsDefinitions">
      <summary>Gets the set of XMLNS (XAML namespace) definitions that apply to the context.</summary>
      <returns>The set of XMLNS (XAML namespace) definitions.</returns>
    </member>
    <member name="M:Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider.Initialize">
      <summary>Invokes any necessary pre-activation logic as required by the XAML schema context and its platform dependencies.</summary>
    </member>
  </members>
</doc>