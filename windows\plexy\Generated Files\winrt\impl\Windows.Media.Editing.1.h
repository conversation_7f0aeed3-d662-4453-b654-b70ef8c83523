// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Media_Editing_1_H
#define WINRT_Windows_Media_Editing_1_H
#include "winrt/impl/Windows.Media.Editing.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Editing
{
    struct WINRT_IMPL_EMPTY_BASES IBackgroundAudioTrack :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundAudioTrack>
    {
        IBackgroundAudioTrack(std::nullptr_t = nullptr) noexcept {}
        IBackgroundAudioTrack(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IBackgroundAudioTrackStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBackgroundAudioTrackStatics>
    {
        IBackgroundAudioTrackStatics(std::nullptr_t = nullptr) noexcept {}
        IBackgroundAudioTrackStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEmbeddedAudioTrack :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEmbeddedAudioTrack>
    {
        IEmbeddedAudioTrack(std::nullptr_t = nullptr) noexcept {}
        IEmbeddedAudioTrack(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaClip :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClip>
    {
        IMediaClip(std::nullptr_t = nullptr) noexcept {}
        IMediaClip(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaClipStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClipStatics>
    {
        IMediaClipStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaClipStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaClipStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaClipStatics2>
    {
        IMediaClipStatics2(std::nullptr_t = nullptr) noexcept {}
        IMediaClipStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaComposition :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaComposition>
    {
        IMediaComposition(std::nullptr_t = nullptr) noexcept {}
        IMediaComposition(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaComposition2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaComposition2>
    {
        IMediaComposition2(std::nullptr_t = nullptr) noexcept {}
        IMediaComposition2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaCompositionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaCompositionStatics>
    {
        IMediaCompositionStatics(std::nullptr_t = nullptr) noexcept {}
        IMediaCompositionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaOverlay :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlay>
    {
        IMediaOverlay(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlay(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaOverlayFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayFactory>
    {
        IMediaOverlayFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaOverlayLayer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayLayer>
    {
        IMediaOverlayLayer(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayLayer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IMediaOverlayLayerFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IMediaOverlayLayerFactory>
    {
        IMediaOverlayLayerFactory(std::nullptr_t = nullptr) noexcept {}
        IMediaOverlayLayerFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
