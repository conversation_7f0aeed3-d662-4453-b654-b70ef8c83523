// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.230706.1

#pragma once
#ifndef WINRT_Windows_Media_Import_1_H
#define WINRT_Windows_Media_Import_1_H
#include "winrt/impl/Windows.Foundation.0.h"
#include "winrt/impl/Windows.Media.Import.0.h"
WINRT_EXPORT namespace winrt::Windows::Media::Import
{
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportDeleteImportedItemsFromSourceResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportDeleteImportedItemsFromSourceResult>
    {
        IPhotoImportDeleteImportedItemsFromSourceResult(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportDeleteImportedItemsFromSourceResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportFindItemsResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportFindItemsResult>
    {
        IPhotoImportFindItemsResult(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportFindItemsResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportFindItemsResult2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportFindItemsResult2>
    {
        IPhotoImportFindItemsResult2(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportFindItemsResult2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportImportItemsResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportImportItemsResult>
    {
        IPhotoImportImportItemsResult(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportImportItemsResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportItem :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportItem>
    {
        IPhotoImportItem(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportItem(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportItem2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportItem2>
    {
        IPhotoImportItem2(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportItem2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportItemImportedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportItemImportedEventArgs>
    {
        IPhotoImportItemImportedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportItemImportedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportManagerStatics>
    {
        IPhotoImportManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportOperation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportOperation>
    {
        IPhotoImportOperation(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportOperation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSelectionChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSelectionChangedEventArgs>
    {
        IPhotoImportSelectionChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSelectionChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSession>,
        impl::require<winrt::Windows::Media::Import::IPhotoImportSession, winrt::Windows::Foundation::IClosable>
    {
        IPhotoImportSession(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSession2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSession2>
    {
        IPhotoImportSession2(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSession2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSidecar :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSidecar>
    {
        IPhotoImportSidecar(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSidecar(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSource :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSource>
    {
        IPhotoImportSource(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSource(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportSourceStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportSourceStatics>
    {
        IPhotoImportSourceStatics(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportSourceStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportStorageMedium :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportStorageMedium>
    {
        IPhotoImportStorageMedium(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportStorageMedium(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPhotoImportVideoSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPhotoImportVideoSegment>
    {
        IPhotoImportVideoSegment(std::nullptr_t = nullptr) noexcept {}
        IPhotoImportVideoSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
